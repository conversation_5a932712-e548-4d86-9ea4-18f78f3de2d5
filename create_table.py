#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建scheduling_config表
"""

import pymysql

def create_scheduling_config_table():
    try:
        conn = pymysql.connect(host='localhost', user='root', password='WWWwww123!', charset='utf8mb4')
        cursor = conn.cursor()
        
        # Check if table exists
        cursor.execute('USE aps_system')
        cursor.execute('SHOW TABLES LIKE "scheduling_config"')
        result = cursor.fetchone()
        
        if result:
            print('Table scheduling_config already exists')
            cursor.execute('SELECT * FROM scheduling_config')
            rows = cursor.fetchall()
            print(f'Found {len(rows)} rows in scheduling_config table')
            for row in rows:
                print(row)
        else:
            print('Table scheduling_config does not exist')
            print('Creating table...')
            
            # Create table
            create_sql = """CREATE TABLE scheduling_config (
              id int NOT NULL AUTO_INCREMENT,
              config_name varchar(100) NOT NULL COMMENT '配置名称',
              user_id varchar(50) DEFAULT NULL COMMENT '用户ID，NULL表示默认配置',
              tech_match_weight decimal(5,2) DEFAULT 25.00 COMMENT '技术匹配度权重(%)',
              load_balance_weight decimal(5,2) DEFAULT 20.00 COMMENT '负载均衡权重(%)',
              deadline_weight decimal(5,2) DEFAULT 25.00 COMMENT '交期紧迫度权重(%)',
              value_efficiency_weight decimal(5,2) DEFAULT 20.00 COMMENT '产值效率权重(%)',
              business_priority_weight decimal(5,2) DEFAULT 10.00 COMMENT '业务优先级权重(%)',
              minor_changeover_time int DEFAULT 45 COMMENT '小改机时间(分钟)',
              major_changeover_time int DEFAULT 120 COMMENT '大改机时间(分钟)',
              urgent_threshold int DEFAULT 8 COMMENT '紧急阈值(小时)',
              normal_threshold int DEFAULT 24 COMMENT '正常阈值(小时)',
              critical_threshold int DEFAULT 72 COMMENT '关键阈值(小时)',
              is_active tinyint(1) DEFAULT 1 COMMENT '是否激活',
              created_at timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
              updated_at timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
              PRIMARY KEY (id),
              UNIQUE KEY uk_user_config (user_id, config_name),
              KEY idx_user_id (user_id),
              KEY idx_active (is_active)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='排产算法权重配置表'"""
            
            cursor.execute(create_sql)
            
            # Insert default data
            insert_sql = """INSERT INTO scheduling_config (
              config_name, user_id, tech_match_weight, load_balance_weight, deadline_weight, 
              value_efficiency_weight, business_priority_weight, minor_changeover_time,
              major_changeover_time, urgent_threshold, normal_threshold, critical_threshold, is_active
            ) VALUES (
              'default_config', NULL, 25.00, 20.00, 25.00, 20.00, 10.00, 45, 120, 8, 24, 72, 1
            )"""
            
            cursor.execute(insert_sql)
            conn.commit()
            print('Table created and default data inserted successfully!')
            
    except Exception as e:
        print(f'Error: {e}')
    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    create_scheduling_config_table()
