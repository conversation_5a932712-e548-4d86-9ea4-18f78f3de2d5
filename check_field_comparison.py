#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.data_source_manager import DataSourceManager

def check_fields():
    """检查字段差异"""
    try:
        manager = DataSourceManager()
        
        print("=== ET_WAIT_LOT (待排产批次) 字段 ===")
        wait_result = manager.get_table_data('ET_WAIT_LOT', per_page=3)
        if wait_result.get('success'):
            wait_data = wait_result.get('data', [])
            if wait_data:
                wait_fields = list(wait_data[0].keys())
                print(f"字段数: {len(wait_fields)}")
                for i, field in enumerate(wait_fields, 1):
                    print(f"  {i:2d}. {field}")
        
        print("\n=== lotprioritydone (已排产批次) 字段 ===")
        done_result = manager.get_table_data('lotprioritydone', per_page=3)
        if done_result.get('success'):
            done_data = done_result.get('data', [])
            if done_data:
                done_fields = list(done_data[0].keys())
                print(f"字段数: {len(done_fields)}")
                for i, field in enumerate(done_fields, 1):
                    print(f"  {i:2d}. {field}")
        
        # 对比分析
        if wait_data and done_data:
            wait_set = set(wait_fields)
            done_set = set(done_fields)
            
            print(f"\n=== 字段对比 ===")
            print(f"共同字段: {len(wait_set & done_set)}个")
            print(f"待排产独有: {len(wait_set - done_set)}个")
            print(f"已排产独有: {len(done_set - wait_set)}个")
            
            print("\n待排产表独有字段:")
            for field in sorted(wait_set - done_set):
                print(f"  - {field}")
                
            print("\n已排产表独有字段:")
            for field in sorted(done_set - wait_set):
                print(f"  - {field}")
    
    except Exception as e:
        print(f"错误: {e}")

if __name__ == "__main__":
    check_fields() 