# APS排产系统测试方案

## 1. 测试目标与范围

### 1.1 测试目标
- **核心目标**：验证改进的设备匹配算法能否正确、高效地为每个待排产批次分配最佳设备
- **次要目标**：确保负载均衡、优先级排序、时间估算等功能正常工作
- **业务目标**：提高设备利用率、减少等待时间、优化生产效率

### 1.2 测试范围
- **功能测试**：设备匹配算法、负载均衡、优先级计算
- **性能测试**：大批量数据处理能力、响应时间
- **业务逻辑测试**：多种排产算法的准确性
- **数据完整性测试**：输入输出数据的正确性
- **异常处理测试**：边界条件和错误场景

### 1.3 测试环境
- **数据环境**：使用真实MySQL数据库数据
- **硬件环境**：生产环境相同配置
- **软件环境**：Python 3.8+, Flask, MySQL 5.7+

## 2. 测试数据准备

### 2.1 真实数据获取
```sql
-- 获取待排产批次（最近30天）
SELECT LOT_ID, DEVICE, PKG_PN, CHIP_ID, STAGE, GOOD_QTY, 
       WIP_STATE, PROC_STATE, HOLD_STATE, FLOW_ID, FLOW_VER, 
       RELEASE_TIME, FAC_ID, CREATE_TIME
FROM ET_WAIT_LOT 
WHERE CREATE_TIME >= DATE_SUB(NOW(), INTERVAL 30 DAY)
ORDER BY CREATE_TIME DESC
LIMIT 100;

-- 获取可用设备
SELECT EQP_ID, TESTER_ID, HANDLER_ID, STATUS, DEVICE, STAGE, 
       PKG_PN, FAC_ID, TESTER_TYPE, HANDLER_TYPE, UPH
FROM EQP_STATUS 
WHERE STATUS = '0'
ORDER BY EQP_ID;

-- 获取UPH效率数据
SELECT DEVICE, PKG_PN, STAGE, TESTER_ID, HANDLER_ID, UPH,
       UPDATE_TIME, STATUS
FROM ET_UPH_EQP 
WHERE STATUS = 'ACTIVE'
ORDER BY DEVICE, PKG_PN, STAGE;

-- 获取测试规范
SELECT DEVICE, PKG_PN, STAGE, TEST_SPEC, UPH, TEST_TIME,
       TESTER_TYPE, HANDLER_TYPE, UPDATE_TIME
FROM ET_FT_TEST_SPEC 
WHERE STATUS = 'ACTIVE'
ORDER BY DEVICE, PKG_PN;

-- 获取工艺配方
SELECT DEVICE, PKG_PN, STAGE, RECIPE_FILE, TESTER_TYPE, 
       HANDLER_TYPE, UPDATE_TIME, STATUS
FROM ET_RECIPE_FILE 
WHERE STATUS = 'ACTIVE'
ORDER BY DEVICE, PKG_PN;
```

### 2.2 测试数据集分类

#### A. 基础功能测试数据集
- **完美匹配场景**：批次与设备在产品、工序、封装完全匹配
- **部分匹配场景**：只有产品或工序匹配
- **无匹配场景**：需要使用通用设备
- **多选择场景**：多个设备都可匹配同一批次

#### B. 负载均衡测试数据集
- **设备空闲场景**：所有设备负载为0
- **设备繁忙场景**：部分设备已有高负载
- **负载不均场景**：设备负载差异较大
- **过载场景**：所有设备都接近满负载

#### C. 优先级测试数据集
- **高优先级批次**：紧急订单、VIP客户
- **普通优先级批次**：常规生产批次
- **低优先级批次**：试产、样品批次
- **混合优先级场景**：不同优先级批次混合排产

### 2.3 边界条件测试数据
- **极大批次**：GOOD_QTY > 50000
- **极小批次**：GOOD_QTY < 100
- **空数据场景**：无待排产批次或无可用设备
- **特殊字符**：LOT_ID包含特殊字符
- **时间边界**：CREATE_TIME为极值

## 3. 测试用例设计

### 3.1 设备匹配算法测试

#### 测试用例1：完美匹配验证
```python
def test_perfect_match():
    """测试完美匹配场景"""
    test_lot = {
        'LOT_ID': 'JW5116F_001',
        'DEVICE': 'JW5116F',
        'PKG_PN': 'QFN48',
        'STAGE': 'FT',
        'GOOD_QTY': 2500
    }
    
    test_equipment = {
        'TESTER_ID': 'T01',
        'HANDLER_ID': 'H01',
        'DEVICE': 'JW5116F',
        'PKG_PN': 'QFN48',
        'STAGE': 'FT',
        'STATUS': '0',
        'UPH': 1500
    }
    
    expected_score = 85  # 40+30+15 = 85分
    expected_level = '完美匹配'
    
    # 执行测试
    # 验证匹配分数是否正确
    # 验证匹配等级是否正确
```

#### 测试用例2：部分匹配验证
```python
def test_partial_match():
    """测试部分匹配场景"""
    test_cases = [
        {
            'name': '仅产品匹配',
            'lot': {'DEVICE': 'JW5116F', 'PKG_PN': 'QFN32', 'STAGE': 'FT'},
            'equipment': {'DEVICE': 'JW5116F', 'PKG_PN': 'QFN48', 'STAGE': 'CP'},
            'expected_score': 40,
            'expected_level': '中等匹配'
        },
        {
            'name': '仅工序匹配',
            'lot': {'DEVICE': 'JW5116F', 'PKG_PN': 'QFN48', 'STAGE': 'FT'},
            'equipment': {'DEVICE': 'JW7106', 'PKG_PN': 'QFN32', 'STAGE': 'FT'},
            'expected_score': 30,
            'expected_level': '低度匹配'
        }
    ]
    
    # 执行每个测试用例
    # 验证匹配分数和等级
```

#### 测试用例3：负载均衡验证
```python
def test_load_balancing():
    """测试负载均衡算法"""
    lots = [
        {'LOT_ID': 'LOT_001', 'DEVICE': 'JW5116F', 'GOOD_QTY': 2000},
        {'LOT_ID': 'LOT_002', 'DEVICE': 'JW5116F', 'GOOD_QTY': 3000},
        {'LOT_ID': 'LOT_003', 'DEVICE': 'JW5116F', 'GOOD_QTY': 1500}
    ]
    
    equipment = [
        {'TESTER_ID': 'T01', 'DEVICE': 'JW5116F', 'UPH': 1500},
        {'TESTER_ID': 'T02', 'DEVICE': 'JW5116F', 'UPH': 1500}
    ]
    
    # 预期结果：批次应均匀分配到两台设备
    # 验证设备负载差异应小于20%
```

### 3.2 业务逻辑测试

#### 测试用例4：排产算法对比
```python
def test_scheduling_algorithms():
    """测试不同排产算法"""
    test_data = get_real_production_data()
    
    algorithms = ['deadline', 'product', 'value', 'intelligent']
    results = {}
    
    for algorithm in algorithms:
        results[algorithm] = execute_scheduling(test_data, algorithm)
        
    # 验证每种算法的特点：
    # deadline: 小批次优先、交期紧急优先
    # product: 同产品聚合
    # value: 大批次优先、高价值优先
    # intelligent: 综合优化
```

#### 测试用例5：时间估算验证
```python
def test_time_estimation():
    """测试时间估算准确性"""
    test_cases = [
        {'GOOD_QTY': 1500, 'UPH': 1500, 'expected_hours': 1.0},
        {'GOOD_QTY': 3000, 'UPH': 1200, 'expected_hours': 2.5},
        {'GOOD_QTY': 5000, 'UPH': 2000, 'expected_hours': 2.5}
    ]
    
    # 验证时间估算误差小于5%
```

### 3.3 数据完整性测试

#### 测试用例6：输入数据验证
```python
def test_input_data_validation():
    """测试输入数据完整性"""
    invalid_cases = [
        {'LOT_ID': '', 'DEVICE': 'JW5116F'},  # 空LOT_ID
        {'LOT_ID': 'TEST', 'GOOD_QTY': -100},  # 负数量
        {'LOT_ID': 'TEST', 'DEVICE': None},  # 空设备
    ]
    
    # 验证系统能正确处理无效输入
    # 验证错误日志是否记录
```

#### 测试用例7：输出数据验证
```python
def test_output_data_integrity():
    """测试输出数据完整性"""
    result = execute_scheduling(test_data)
    
    # 验证输出字段完整性
    required_fields = [
        'ORDER', 'LOT_ID', 'HANDLER_ID', 'TESTER_ID', 
        'DEVICE', 'GOOD_QTY', 'match_score', 'estimated_hours'
    ]
    
    for item in result:
        for field in required_fields:
            assert field in item, f"缺少必需字段: {field}"
    
    # 验证数据逻辑正确性
    assert len(result) == len(test_data['lots'])  # 批次数量一致
    assert all(item['ORDER'] > 0 for item in result)  # 排序号正确
```

### 3.4 性能测试

#### 测试用例8：大批量数据处理
```python
def test_large_scale_processing():
    """测试大批量数据处理能力"""
    test_scales = [100, 500, 1000, 2000]
    
    for scale in test_scales:
        lots = generate_test_lots(scale)
        equipment = generate_test_equipment(50)
        
        start_time = time.time()
        result = execute_scheduling(lots, equipment)
        end_time = time.time()
        
        processing_time = end_time - start_time
        
        # 性能要求：
        # 100批次 < 1秒
        # 500批次 < 5秒  
        # 1000批次 < 15秒
        # 2000批次 < 30秒
        
        assert processing_time < get_max_time(scale)
```

#### 测试用例9：并发处理测试
```python
def test_concurrent_processing():
    """测试并发排产处理"""
    import threading
    
    def concurrent_scheduling():
        return execute_scheduling(test_data)
    
    threads = []
    results = []
    
    # 启动10个并发排产任务
    for i in range(10):
        thread = threading.Thread(target=concurrent_scheduling)
        threads.append(thread)
        thread.start()
    
    # 验证并发处理结果一致性
    # 验证无死锁和资源冲突
```

### 3.5 异常处理测试

#### 测试用例10：边界条件处理
```python
def test_edge_cases():
    """测试边界条件"""
    edge_cases = [
        {'name': '无可用设备', 'lots': test_lots, 'equipment': []},
        {'name': '无待排产批次', 'lots': [], 'equipment': test_equipment},
        {'name': '设备全部繁忙', 'lots': test_lots, 'equipment': busy_equipment},
        {'name': '超大批次', 'lots': [huge_lot], 'equipment': test_equipment}
    ]
    
    for case in edge_cases:
        try:
            result = execute_scheduling(case['lots'], case['equipment'])
            # 验证系统能正确处理边界条件
            # 验证返回合理的默认结果
        except Exception as e:
            # 验证异常处理是否正确
            assert isinstance(e, ExpectedException)
```

## 4. 测试执行计划

### 4.1 阶段1：单元测试（2天）
- **目标**：验证核心算法函数的正确性
- **内容**：设备匹配算法、负载计算、评分机制
- **标准**：所有单元测试通过率100%

### 4.2 阶段2：集成测试（3天）
- **目标**：验证模块间协作的正确性
- **内容**：数据获取、算法执行、结果生成的完整流程
- **标准**：集成功能正常，数据流转正确

### 4.3 阶段3：业务逻辑测试（3天）
- **目标**：验证业务规则的正确实现
- **内容**：4种排产算法、优先级机制、时间估算
- **标准**：业务逻辑符合需求规格

### 4.4 阶段4：性能测试（2天）
- **目标**：验证系统性能指标
- **内容**：大批量处理、响应时间、并发能力
- **标准**：满足性能要求

### 4.5 阶段5：用户验收测试（3天）
- **目标**：验证系统满足实际生产需求
- **内容**：真实数据验证、生产场景模拟
- **标准**：用户确认系统可用于生产

## 5. 测试指标与标准

### 5.1 功能指标
- **设备匹配准确率**：≥95%
- **负载均衡效果**：设备负载差异<20%
- **时间估算误差**：<5%
- **数据完整性**：100%

### 5.2 性能指标
- **处理速度**：1000批次<15秒
- **内存使用**：<500MB
- **CPU使用率**：<80%
- **并发支持**：≥10个用户同时使用

### 5.3 质量指标
- **代码覆盖率**：≥90%
- **缺陷密度**：<1个/KLOC
- **系统可用性**：≥99.9%
- **错误恢复**：<30秒

## 6. 测试工具与环境

### 6.1 测试框架
```python
# 主要测试框架
import unittest
import pytest
import mock
from unittest.mock import patch, MagicMock

# 性能测试工具
import time
import psutil
import memory_profiler

# 数据验证工具
import pandas as pd
import numpy as np
from scipy import stats
```

### 6.2 测试环境配置
```yaml
# 测试环境配置
test_environment:
  database:
    host: "localhost"
    port: 3306
    database: "aps_test"
    username: "test_user"
    password: "test_pass"
  
  redis:
    host: "localhost" 
    port: 6379
    db: 1
  
  logging:
    level: "DEBUG"
    file: "test_logs/aps_test.log"
```

### 6.3 数据备份与恢复
```bash
# 测试前数据备份
mysqldump -u root -p aps > backup/aps_backup_$(date +%Y%m%d).sql

# 测试后数据恢复
mysql -u root -p aps < backup/aps_backup_20250625.sql
```

## 7. 回归测试计划

### 7.1 自动化回归测试
- **频率**：每次代码提交
- **覆盖范围**：核心功能、关键业务逻辑
- **执行时间**：<10分钟
- **失败处理**：阻止代码合并

### 7.2 完整回归测试
- **频率**：每周一次
- **覆盖范围**：全部测试用例
- **执行时间**：<2小时
- **报告生成**：自动生成测试报告

## 8. 测试报告要求

### 8.1 测试报告内容
1. **执行摘要**：测试目标、范围、结果总览
2. **测试环境**：硬件、软件、数据环境描述
3. **测试执行**：测试用例执行情况、通过率
4. **缺陷分析**：发现的问题、严重程度、修复状态
5. **性能分析**：性能指标测试结果、瓶颈分析
6. **风险评估**：潜在风险、建议措施
7. **结论建议**：是否可以投入生产使用

### 8.2 关键指标统计
```python
# 测试结果统计
test_metrics = {
    'total_test_cases': 150,
    'passed_cases': 147,
    'failed_cases': 3,
    'pass_rate': 98.0,
    'critical_defects': 0,
    'major_defects': 2,
    'minor_defects': 5,
    'performance_score': 95.5,
    'recommendation': 'PASS - 可投入生产使用'
}
```

## 9. 风险控制措施

### 9.1 数据安全
- 使用测试数据库，避免影响生产数据
- 敏感数据脱敏处理
- 测试完成后清理临时数据

### 9.2 系统安全
- 测试环境与生产环境隔离
- 限制测试用户权限
- 监控测试过程中的系统资源使用

### 9.3 回滚计划
- 保留原始代码版本
- 准备快速回滚程序
- 建立紧急联系机制

## 10. 验收标准

### 10.1 功能验收标准
- ✅ 所有核心功能测试用例通过
- ✅ 设备匹配算法准确率≥95%
- ✅ 负载均衡效果符合预期
- ✅ 时间估算误差在可接受范围内

### 10.2 性能验收标准
- ✅ 1000批次排产在15秒内完成
- ✅ 系统可支持10个并发用户
- ✅ 内存使用在合理范围内
- ✅ 无内存泄漏问题

### 10.3 质量验收标准
- ✅ 代码覆盖率≥90%
- ✅ 无严重缺陷（Critical/Blocker）
- ✅ 主要缺陷（Major）≤3个
- ✅ 文档完整，注释清晰

### 10.4 业务验收标准
- ✅ 生产调度人员确认系统可用
- ✅ 排产结果符合生产实际需求
- ✅ 系统操作简便，易于理解
- ✅ 异常情况处理合理

## 11. 测试时间安排

| 阶段 | 开始时间 | 结束时间 | 负责人 | 关键里程碑 |
|------|----------|----------|--------|------------|
| 测试准备 | Day 1 | Day 2 | 测试团队 | 环境搭建完成 |
| 单元测试 | Day 2 | Day 4 | 开发团队 | 单元测试100%通过 |
| 集成测试 | Day 4 | Day 7 | 测试团队 | 集成功能验证完成 |
| 业务测试 | Day 7 | Day 10 | 业务团队 | 业务逻辑验证完成 |
| 性能测试 | Day 10 | Day 12 | 测试团队 | 性能指标达标 |
| 用户验收 | Day 12 | Day 15 | 用户代表 | 用户签字确认 |
| 测试总结 | Day 15 | Day 16 | 项目经理 | 测试报告发布 |

**总计：16个工作日**

---

## 💡 总结

这个测试方案确保了：

1. **全面性**：覆盖功能、性能、安全、业务各个方面
2. **严谨性**：定义明确的测试标准和验收条件  
3. **实用性**：使用真实生产数据，模拟实际使用场景
4. **可执行性**：提供具体的测试代码和执行步骤
5. **可度量性**：设定明确的量化指标和通过标准

通过这个测试方案，我们可以确保改进的排产系统能够安全、可靠地投入生产使用。 