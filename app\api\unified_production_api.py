# -*- coding: utf-8 -*-
"""
统一生产API

提供向后兼容的生产管理API接口，
内部使用统一业务服务，确保零风险迁移。

Author: AI Assistant
Date: 2025-01-14
"""

import logging
from datetime import datetime
from typing import Dict, List, Optional, Any
from flask import Blueprint, request, jsonify, current_app

from app import db
from app.services.unified_business_service import (
    unified_lot_service,
    unified_scheduling_service,
    unified_test_spec_service
)
from app.models import ProductPriorityConfig
from app.utils.response_utils import success_response, error_response

logger = logging.getLogger(__name__)

# 创建蓝图
unified_production_bp = Blueprint('unified_production', __name__, url_prefix='/api/unified/production')


@unified_production_bp.route('/wait-lots', methods=['GET'])
def get_wait_lots():
    """
    获取待排产批次数据 - 统一接口
    
    兼容原有的 /api/production/wait-lots 接口
    """
    try:
        # 获取查询参数
        filters = {}
        
        device = request.args.get('device')
        if device:
            filters['device'] = device
        
        stage = request.args.get('stage')
        if stage:
            filters['stage'] = stage
        
        wip_state = request.args.get('wip_state')
        if wip_state:
            filters['wip_state'] = wip_state
        
        priority_level = request.args.get('priority_level')
        if priority_level:
            filters['priority_level'] = priority_level
        
        min_qty = request.args.get('min_qty', type=int)
        if min_qty:
            filters['min_qty'] = min_qty
        
        max_qty = request.args.get('max_qty', type=int)
        if max_qty:
            filters['max_qty'] = max_qty
        
        limit = request.args.get('limit', type=int)
        
        # 使用统一服务获取数据
        wait_lots = unified_lot_service.get_wait_lot_data(filters, limit)
        
        return success_response(
            data=wait_lots,
            message=f"获取到 {len(wait_lots)} 条待排产批次数据（使用统一模型）"
        )
        
    except Exception as e:
        logger.error(f"❌ 获取待排产批次失败: {e}")
        return error_response(f"获取待排产批次失败: {str(e)}")


@unified_production_bp.route('/wip-lots', methods=['GET'])
def get_wip_lots():
    """
    获取在制品清单数据 - 统一接口
    """
    try:
        # 获取查询参数
        filters = {}
        
        device = request.args.get('device')
        if device:
            filters['device'] = device
        
        stage = request.args.get('stage')
        if stage:
            filters['stage'] = stage
        
        wip_state = request.args.get('wip_state')
        if wip_state:
            filters['wip_state'] = wip_state
        
        limit = request.args.get('limit', type=int)
        
        # 使用统一服务获取数据
        wip_lots = unified_lot_service.get_wip_lot_data(filters, limit)
        
        return success_response(
            data=wip_lots,
            message=f"获取到 {len(wip_lots)} 条在制品清单数据（使用统一模型）"
        )
        
    except Exception as e:
        logger.error(f"❌ 获取在制品清单失败: {e}")
        return error_response(f"获取在制品清单失败: {str(e)}")


@unified_production_bp.route('/lots/<lot_id>', methods=['GET'])
def get_lot_by_id(lot_id):
    """
    根据批次号获取批次详细信息 - 统一接口
    """
    try:
        lot_data = unified_lot_service.get_lot_by_id(lot_id)
        
        if lot_data:
            return success_response(
                data=lot_data,
                message=f"获取批次 {lot_id} 信息成功（使用统一模型）"
            )
        else:
            return error_response(f"未找到批次 {lot_id}", status_code=404)
        
    except Exception as e:
        logger.error(f"❌ 获取批次 {lot_id} 信息失败: {e}")
        return error_response(f"获取批次信息失败: {str(e)}")


@unified_production_bp.route('/lots/<lot_id>/priority', methods=['PUT'])
def update_lot_priority(lot_id):
    """
    更新批次优先级 - 统一接口
    """
    try:
        data = request.get_json()
        if not data:
            return error_response("请提供更新数据", status_code=400)
        
        priority_level = data.get('priority_level')
        priority_order = data.get('priority_order')
        
        if not priority_level:
            return error_response("请提供优先级等级", status_code=400)
        
        # 使用统一服务更新优先级
        success = unified_lot_service.update_lot_priority(lot_id, priority_level, priority_order)
        
        if success:
            return success_response(
                message=f"批次 {lot_id} 优先级更新成功（使用统一模型）"
            )
        else:
            return error_response(f"批次 {lot_id} 优先级更新失败", status_code=404)
        
    except Exception as e:
        logger.error(f"❌ 更新批次 {lot_id} 优先级失败: {e}")
        return error_response(f"更新批次优先级失败: {str(e)}")


@unified_production_bp.route('/lots/<lot_id>/equipment', methods=['PUT'])
def assign_equipment(lot_id):
    """
    为批次分配设备 - 统一接口
    """
    try:
        data = request.get_json()
        if not data:
            return error_response("请提供设备分配数据", status_code=400)
        
        handler_id = data.get('handler_id')
        tester_id = data.get('tester_id')
        
        if not handler_id or not tester_id:
            return error_response("请提供分拣机ID和测试机ID", status_code=400)
        
        # 使用统一服务分配设备
        success = unified_lot_service.assign_equipment(lot_id, handler_id, tester_id)
        
        if success:
            return success_response(
                message=f"批次 {lot_id} 设备分配成功: {handler_id}-{tester_id}（使用统一模型）"
            )
        else:
            return error_response(f"批次 {lot_id} 设备分配失败", status_code=404)
        
    except Exception as e:
        logger.error(f"❌ 为批次 {lot_id} 分配设备失败: {e}")
        return error_response(f"设备分配失败: {str(e)}")


@unified_production_bp.route('/scheduling', methods=['POST'])
def execute_scheduling():
    """
    执行排产算法 - 统一接口
    
    兼容原有的排产算法接口
    """
    try:
        data = request.get_json() or {}
        
        # 获取算法参数
        algorithm = data.get('algorithm', 'intelligent')
        optimization_target = data.get('optimization_target', 'efficiency')
        filters = data.get('filters', {})
        
        # 验证算法类型
        valid_algorithms = ['deadline', 'product', 'value', 'intelligent']
        if algorithm not in valid_algorithms:
            return error_response(f"不支持的算法类型: {algorithm}，支持的类型: {valid_algorithms}", status_code=400)
        
        # 使用统一服务执行排产
        schedule_result = unified_scheduling_service.execute_scheduling_algorithm(
            algorithm=algorithm,
            optimization_target=optimization_target,
            filters=filters
        )
        
        return success_response(
            data={
                'schedule_result': schedule_result,
                'algorithm': algorithm,
                'optimization_target': optimization_target,
                'total_lots': len(schedule_result),
                'execution_time': datetime.now().isoformat(),
                'using_unified_model': True
            },
            message=f"排产算法 {algorithm} 执行完成，生成 {len(schedule_result)} 条排产记录（使用统一模型）"
        )
        
    except Exception as e:
        logger.error(f"❌ 执行排产算法失败: {e}")
        return error_response(f"执行排产算法失败: {str(e)}")


@unified_production_bp.route('/test-specs', methods=['GET'])
def get_test_specs():
    """
    获取测试规范数据 - 统一接口
    """
    try:
        # 获取查询参数
        filters = {}
        
        device = request.args.get('device')
        if device:
            filters['device'] = device
        
        stage = request.args.get('stage')
        if stage:
            filters['stage'] = stage
        
        pkg_pn = request.args.get('pkg_pn')
        if pkg_pn:
            filters['pkg_pn'] = pkg_pn
        
        limit = request.args.get('limit', type=int)
        
        # 使用统一服务获取数据
        test_specs = unified_test_spec_service.get_test_specs(filters, limit)
        
        return success_response(
            data=test_specs,
            message=f"获取到 {len(test_specs)} 条测试规范数据（使用统一模型）"
        )
        
    except Exception as e:
        logger.error(f"❌ 获取测试规范失败: {e}")
        return error_response(f"获取测试规范失败: {str(e)}")


@unified_production_bp.route('/test-specs/<spec_id>', methods=['GET'])
def get_test_spec_by_id(spec_id):
    """
    根据规范ID获取测试规范详细信息 - 统一接口
    """
    try:
        test_spec = unified_test_spec_service.get_test_spec_by_id(spec_id)
        
        if test_spec:
            return success_response(
                data=test_spec,
                message=f"获取测试规范 {spec_id} 信息成功（使用统一模型）"
            )
        else:
            return error_response(f"未找到测试规范 {spec_id}", status_code=404)
        
    except Exception as e:
        logger.error(f"❌ 获取测试规范 {spec_id} 信息失败: {e}")
        return error_response(f"获取测试规范信息失败: {str(e)}")


@unified_production_bp.route('/statistics', methods=['GET'])
def get_production_statistics():
    """
    获取生产统计信息 - 统一接口
    """
    try:
        # 获取统计数据
        stats = {}
        
        # 待排产批次统计
        wait_lots = unified_lot_service.get_wait_lot_data()
        stats['wait_lots_count'] = len(wait_lots)
        
        # 按设备分组统计
        device_stats = {}
        for lot in wait_lots:
            device = lot.get('DEVICE', 'Unknown')
            if device not in device_stats:
                device_stats[device] = {
                    'count': 0,
                    'total_qty': 0,
                    'stages': set()
                }
            device_stats[device]['count'] += 1
            device_stats[device]['total_qty'] += lot.get('GOOD_QTY', 0)
            device_stats[device]['stages'].add(lot.get('STAGE', 'Unknown'))
        
        # 转换set为list以便JSON序列化
        for device in device_stats:
            device_stats[device]['stages'] = list(device_stats[device]['stages'])
        
        stats['device_statistics'] = device_stats
        
        # 在制品统计
        wip_lots = unified_lot_service.get_wip_lot_data()
        stats['wip_lots_count'] = len(wip_lots)
        
        # 测试规范统计
        test_specs = unified_test_spec_service.get_test_specs()
        stats['test_specs_count'] = len(test_specs)
        
        stats['last_updated'] = datetime.now().isoformat()
        stats['using_unified_model'] = True
        
        return success_response(
            data=stats,
            message="生产统计信息获取成功（使用统一模型）"
        )
        
    except Exception as e:
        logger.error(f"❌ 获取生产统计信息失败: {e}")
        return error_response(f"获取生产统计信息失败: {str(e)}")


@unified_production_bp.route('/health', methods=['GET'])
def health_check():
    """
    健康检查接口 - 验证统一模型服务状态
    """
    try:
        health_status = {
            'status': 'healthy',
            'timestamp': datetime.now().isoformat(),
            'services': {
                'unified_lot_service': 'available',
                'unified_scheduling_service': 'available',
                'unified_test_spec_service': 'available'
            },
            'database': {
                'unified_lot_model': 'connected',
                'unified_test_spec': 'connected'
            }
        }
        
        # 简单的数据库连接测试
        try:
            from app.models.unified.unified_lot_model import UnifiedLotModel
            from app.models.unified.unified_test_spec import UnifiedTestSpec
            
            lot_count = UnifiedLotModel.query.count()
            spec_count = UnifiedTestSpec.query.count()
            
            health_status['data_counts'] = {
                'unified_lots': lot_count,
                'unified_test_specs': spec_count
            }
            
        except Exception as db_error:
            health_status['status'] = 'degraded'
            health_status['database_error'] = str(db_error)
        
        return success_response(
            data=health_status,
            message="统一模型服务健康检查完成"
        )
        
    except Exception as e:
        logger.error(f"❌ 健康检查失败: {e}")
        return error_response(f"健康检查失败: {str(e)}")


# 向后兼容的路由别名
# 错误处理
@unified_production_bp.errorhandler(404)
def not_found(error):
    return error_response("接口不存在", status_code=404)


@unified_production_bp.errorhandler(500)
def internal_error(error):
    logger.error(f"内部服务器错误: {error}")
    return error_response("内部服务器错误", status_code=500)


# 注册蓝图的函数
def register_unified_production_api(app):
    """注册统一生产API蓝图"""
    app.register_blueprint(unified_production_bp)
    logger.info("✅ 统一生产API蓝图注册成功") 