# 明日APS算法优化任务清单

**日期**: 2025年1月10日  
**状态**: 待开始  
**目标**: 修复优化版算法的一致性问题

---

## 🚨 紧急修复清单

### ✅ 任务1: 修复优先级字段为空 (1-2小时)
**问题**: 所有116个批次PRIORITY显示为"n"  
**文件**: `app/services/optimized_scheduling_service.py`  
**位置**: ~第280行，排产结果构建部分

**修复代码**:
```python
# 在scheduled_lots生成后，添加以下代码
# 按设备分组重新分配优先级（确保每台设备从1开始排序）
device_groups = {}
for lot in scheduled_lots:
    handler_id = lot['HANDLER_ID']
    if handler_id not in device_groups:
        device_groups[handler_id] = []
    device_groups[handler_id].append(lot)

# 为每个设备组按综合评分排序并分配优先级
for handler_id, lots in device_groups.items():
    lots.sort(key=lambda x: x.get('comprehensive_score', 0), reverse=True)
    for i, lot in enumerate(lots, 1):
        lot['PRIORITY'] = i
```

**验证**: 运行一致性验证脚本确认PRIORITY字段有数值

### ✅ 任务2: 修复字段映射错误 (30分钟)
**问题**: PROD_ID和PO_ID字段映射错误  
**文件**: `app/services/optimized_scheduling_service.py`  
**位置**: ~第285行，scheduled_lot字典构建

**修复代码**:
```python
# 修改字段映射
'PROD_ID': lot.get('DEVICE', ''),        # 改为DEVICE而不是PROD_ID
'PO_ID': lot.get('ORDER_ID', ''),        # 改为ORDER_ID而不是PO_ID
```

### ✅ 任务3: 调查缺失的4个批次 (2-3小时)
**问题**: 优化版少了4个批次
```
YX2500001159 -> HMTS-C-002-1850
YX2500001765 -> HANK-C-001-5800  
YX2500002091 -> HCHC-C-023-6800
YX2500002117 -> HMTS-C-004-1618
```

**调试步骤**:
1. 创建专门的调试脚本追踪这4个批次
2. 比较原版和优化版的数据过滤逻辑
3. 检查设备可用性判断条件
4. 验证测试规范匹配的STAGE处理

**调试脚本**:
```python
# 创建 debug_missing_lots.py
missing_lots = ['YX2500001159', 'YX2500001765', 'YX2500002091', 'YX2500002117']
# 分别用两个算法追踪这些批次的处理路径
```

---

## 🔍 深度分析任务

### ✅ 任务4: 设备分配差异分析 (3-4小时)
**问题**: 63个批次选择了不同设备  
**典型案例**: YX2500001221 (原版:HCHC-C-021-6800, 优化版:HANK-C-001-5800)

**分析方法**:
1. 选择5-10个典型案例进行详细分析
2. 对比评分算法的每个维度得分
3. 检查缓存数据与实时数据的差异
4. 验证设备负载计算逻辑

**工具**: 修改一致性验证脚本，增加详细的评分对比

---

## 📋 验证流程

### 快速验证步骤
```bash
# 1. 运行一致性验证
python "排产结果一致性验证.py"

# 2. 检查关键指标
# - 批次数量: 应该从116提升到120
# - 优先级字段: 应该显示数字而不是"n"  
# - 字段映射: PROD_ID和PO_ID应该有值
# - 设备分配: 差异应该减少

# 3. 性能测试
python "性能对比测试.py"
```

### 成功标准
- ✅ 批次数量: 120个 (与原版一致)
- ✅ 优先级字段: 显示正确的数字值
- ✅ 字段映射: PROD_ID=DEVICE, PO_ID=ORDER_ID
- ✅ 设备分配差异: <10个批次
- ✅ 性能: 保持50-60秒执行时间

---

## 🛠️ 调试工具准备

### 1. 修改一致性验证脚本
**文件**: `排产结果一致性验证.py`
**增加功能**:
- 详细的评分对比
- 缺失批次的处理路径追踪
- 设备负载计算对比

### 2. 创建单批次调试脚本
**文件**: `debug_specific_lot.py`
**功能**: 追踪特定批次在两个算法中的完整处理路径

### 3. 字段映射验证脚本
**文件**: `verify_field_mapping.py`  
**功能**: 验证所有字段的正确映射

---

## 📊 测试数据

### 关键测试批次
```
YX2500002000: KIT匹配问题已修复的案例
YX2500001221: 设备分配差异典型案例  
YX2500001159: 缺失批次案例1
YX2500001765: 缺失批次案例2
```

### 验证基准
```
原版算法: 120批次, 123.13秒
目标结果: 120批次, 50-60秒
当前状态: 116批次, 56.50秒
```

---

## ⚠️ 注意事项

1. **备份**: 修改前备份当前的optimized_scheduling_service.py
2. **增量修复**: 一次只修复一个问题，验证后再继续
3. **性能监控**: 每次修改后确认性能不退化
4. **日志输出**: 保持详细的调试日志便于问题定位

---

## 📞 紧急联系

如果遇到严重问题无法解决:
1. 回滚到备份版本
2. 查看详细的错误日志
3. 参考原版算法的实现逻辑
4. 使用分步调试方法

---

**🎯 今日目标**: 修复前两个高优先级问题，使优化版算法达到基本可用状态 