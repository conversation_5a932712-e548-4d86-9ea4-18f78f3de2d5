import pymysql

def check_database_structure():
    """检查数据库表的实际结构"""
    
    try:
        conn = pymysql.connect(
            host='localhost',
            user='root', 
            password='Flzx3000',
            database='aps_system',
            charset='utf8mb4'
        )
        
        with conn.cursor() as cursor:
            # 检查devicepriorityconfig表结构
            print("=== aps_system.devicepriorityconfig 表结构 ===")
            cursor.execute("DESCRIBE devicepriorityconfig")
            columns = cursor.fetchall()
            
            print("实际列名和类型:")
            for col in columns:
                print(f"  {col[0]}: {col[1]} {'(主键)' if col[3] == 'PRI' else ''}")
            
            # 检查lotpriorityconfig表结构  
            print("\n=== aps_system.lotpriorityconfig 表结构 ===")
            cursor.execute("DESCRIBE lotpriorityconfig")
            columns = cursor.fetchall()
            
            print("实际列名和类型:")
            for col in columns:
                print(f"  {col[0]}: {col[1]} {'(主键)' if col[3] == 'PRI' else ''}")
                
        conn.close()
        
    except Exception as e:
        print(f"检查数据库结构失败: {e}")

if __name__ == "__main__":
    check_database_structure()
