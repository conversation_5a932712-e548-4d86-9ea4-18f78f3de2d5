#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

print("🔍 分析数据匹配问题...")

try:
    from app.services.data_source_manager import DataSourceManager
    
    dm = DataSourceManager()
    
    # 获取待排产批次数据
    wait_lots_result = dm.get_table_data('ET_WAIT_LOT', per_page=200)
    wait_lots = wait_lots_result.get('data', []) if wait_lots_result.get('success') else []
    
    # 获取测试规范数据
    specs_result = dm.get_table_data('ET_FT_TEST_SPEC', per_page=600)
    specs = specs_result.get('data', []) if specs_result.get('success') else []
    
    print(f"📋 待排产批次: {len(wait_lots)} 条")
    print(f"🔬 测试规范: {len(specs)} 条")
    
    # 分析待排产批次的DEVICE+STAGE组合
    print(f"\n=== 📊 待排产批次的DEVICE+STAGE组合 ===")
    wait_combinations = set()
    wait_samples = {}
    
    for lot in wait_lots:
        device = lot.get('DEVICE', '').strip()
        stage = lot.get('STAGE', '').strip()
        combo = f"{device}|{stage}"
        wait_combinations.add(combo)
        if combo not in wait_samples:
            wait_samples[combo] = lot.get('LOT_ID', '')
    
    print(f"待排产批次包含 {len(wait_combinations)} 种不同的DEVICE+STAGE组合:")
    for combo in sorted(list(wait_combinations)[:20]):  # 显示前20个
        device, stage = combo.split('|')
        sample_lot = wait_samples[combo]
        print(f"  {device} + {stage} (样例: {sample_lot})")
    
    if len(wait_combinations) > 20:
        print(f"  ... 还有 {len(wait_combinations) - 20} 种组合")
    
    # 分析测试规范的DEVICE+STAGE组合
    print(f"\n=== 🔬 测试规范的DEVICE+STAGE组合 (Released状态) ===")
    spec_combinations = set()
    released_specs = []
    
    for spec in specs:
        if spec.get('APPROVAL_STATE', '').strip() == 'Released':
            released_specs.append(spec)
            device = spec.get('DEVICE', '').strip()
            stage = spec.get('STAGE', '').strip()
            combo = f"{device}|{stage}"
            spec_combinations.add(combo)
    
    print(f"Released状态的测试规范: {len(released_specs)} 条")
    print(f"包含 {len(spec_combinations)} 种不同的DEVICE+STAGE组合:")
    for combo in sorted(list(spec_combinations)[:20]):  # 显示前20个
        device, stage = combo.split('|')
        print(f"  {device} + {stage}")
    
    if len(spec_combinations) > 20:
        print(f"  ... 还有 {len(spec_combinations) - 20} 种组合")
    
    # 分析匹配情况
    print(f"\n=== 🎯 匹配分析 ===")
    matched_combinations = wait_combinations & spec_combinations
    unmatched_combinations = wait_combinations - spec_combinations
    
    print(f"✅ 可以匹配的组合: {len(matched_combinations)} 种")
    for combo in sorted(list(matched_combinations)):
        device, stage = combo.split('|')
        sample_lot = wait_samples[combo]
        print(f"  {device} + {stage} (样例: {sample_lot})")
    
    print(f"\n❌ 无法匹配的组合: {len(unmatched_combinations)} 种")
    for combo in sorted(list(unmatched_combinations)[:10]):  # 显示前10个
        device, stage = combo.split('|')
        sample_lot = wait_samples[combo]
        print(f"  {device} + {stage} (样例: {sample_lot})")
    
    if len(unmatched_combinations) > 10:
        print(f"  ... 还有 {len(unmatched_combinations) - 10} 种无法匹配的组合")
    
    # 检查APPROVAL_STATE分布
    print(f"\n=== 📈 测试规范APPROVAL_STATE分布 ===")
    approval_stats = {}
    for spec in specs:
        state = spec.get('APPROVAL_STATE', '').strip()
        approval_stats[state] = approval_stats.get(state, 0) + 1
    
    for state, count in sorted(approval_stats.items()):
        percentage = count / len(specs) * 100 if specs else 0
        print(f"  {state}: {count} 条 ({percentage:.1f}%)")
    
    # 建议解决方案
    print(f"\n=== 💡 建议解决方案 ===")
    
    if len(matched_combinations) > 0:
        print(f"1. 立即修复: 有 {len(matched_combinations)} 种组合可以匹配，建议先处理这些批次")
    
    if len(unmatched_combinations) > 0:
        print(f"2. 数据补全: 有 {len(unmatched_combinations)} 种组合需要在测试规范表中补充对应的Released记录")
    
    non_released_count = len(specs) - len(released_specs)
    if non_released_count > 0:
        print(f"3. 状态更新: 有 {non_released_count} 条测试规范不是Released状态，可能需要审核通过")
    
    # 推荐批量修复策略
    print(f"\n=== 🚀 推荐修复策略 ===")
    print(f"1. 优先处理可匹配的 {len(matched_combinations)} 种组合的批次")
    print(f"2. 联系业务部门确认无法匹配的批次是否需要创建新的测试规范")
    print(f"3. 检查non-Released状态的测试规范是否可以批量审核通过")
    
except Exception as e:
    print(f"❌ 分析失败: {e}")
    import traceback
    traceback.print_exc() 