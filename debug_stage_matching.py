#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

print("🐛 调试STAGE智能匹配逻辑...")

try:
    from app.services.real_scheduling_service import RealSchedulingService
    from app.services.data_source_manager import DataSourceManager
    
    scheduler = RealSchedulingService()
    dm = DataSourceManager()
    
    # 获取具体的测试数据
    wait_lots_result = dm.get_table_data('ET_WAIT_LOT', per_page=5)
    wait_lots = wait_lots_result.get('data', [])
    
    specs_result = dm.get_table_data('ET_FT_TEST_SPEC', per_page=600)
    specs = specs_result.get('data', [])
    
    # 筛选Released测试规范
    released_specs = [spec for spec in specs if spec.get('APPROVAL_STATE', '').strip() == 'Released']
    
    print(f"📋 测试数据: {len(wait_lots)} 个批次, {len(released_specs)} 个Released测试规范")
    
    # 测试具体的批次
    test_lot = wait_lots[0] if wait_lots else None
    if test_lot:
        lot_id = test_lot.get('LOT_ID')
        lot_device = test_lot.get('DEVICE', '').strip()
        lot_stage = test_lot.get('STAGE', '').strip()
        
        print(f"\n=== 🧪 调试批次: {lot_id} ===")
        print(f"DEVICE: '{lot_device}'")
        print(f"STAGE: '{lot_stage}'")
        
        # 查找同DEVICE的所有测试规范
        matching_device_specs = []
        for spec in released_specs:
            spec_device = spec.get('DEVICE', '').strip()
            if spec_device == lot_device:
                matching_device_specs.append(spec)
        
        print(f"\n同DEVICE的测试规范 ({len(matching_device_specs)} 个):")
        for idx, spec in enumerate(matching_device_specs):
            spec_stage = spec.get('STAGE', '').strip()
            hb_pn = spec.get('HB_PN', '').strip()
            tb_pn = spec.get('TB_PN', '').strip()
            print(f"  {idx+1}. STAGE: '{spec_stage}', HB_PN: '{hb_pn}', TB_PN: '{tb_pn}'")
            
            # 测试STAGE匹配
            stage_match = scheduler._is_stage_match(lot_stage, spec_stage)
            print(f"     STAGE匹配测试: '{lot_stage}' vs '{spec_stage}' -> {stage_match}")
        
        # 手动执行配置需求获取逻辑
        print(f"\n=== 🔍 手动执行配置匹配逻辑 ===")
        
        found_match = False
        for spec in released_specs:
            spec_device = spec.get('DEVICE', '').strip()
            spec_stage = spec.get('STAGE', '').strip()
            spec_approval = spec.get('APPROVAL_STATE', '').strip()
            
            device_match = (spec_device == lot_device)
            stage_match = scheduler._is_stage_match(lot_stage, spec_stage)
            approval_match = (spec_approval == 'Released')
            
            if device_match and stage_match and approval_match:
                print(f"✅ 找到匹配!")
                print(f"   DEVICE匹配: {device_match}")
                print(f"   STAGE匹配: {stage_match} ('{lot_stage}' -> '{spec_stage}')")
                print(f"   APPROVAL匹配: {approval_match}")
                print(f"   HB_PN: {spec.get('HB_PN', '')}")
                print(f"   TB_PN: {spec.get('TB_PN', '')}")
                found_match = True
                break
        
        if not found_match:
            print(f"❌ 没有找到匹配的配置")
        
        # 测试数据源管理器的分页问题
        print(f"\n=== 🔍 检查数据源分页问题 ===")
        print(f"当前分页大小: 50")
        print(f"总测试规范数: {len(specs)}")
        print(f"是否可能因为分页导致数据不完整？")
        
        if len(specs) == 50:
            print(f"⚠️ 警告: 测试规范数据可能被分页限制了！")
            
            # 获取更多数据测试
            full_specs_result = dm.get_table_data('ET_FT_TEST_SPEC', per_page=1000)
            full_specs = full_specs_result.get('data', [])
            print(f"获取完整数据: {len(full_specs)} 条测试规范")
            
            # 重新测试匹配
            full_released_specs = [spec for spec in full_specs if spec.get('APPROVAL_STATE', '').strip() == 'Released']
            print(f"完整Released测试规范: {len(full_released_specs)} 条")
            
            found_full_match = False
            for spec in full_released_specs:
                spec_device = spec.get('DEVICE', '').strip()
                spec_stage = spec.get('STAGE', '').strip()
                spec_approval = spec.get('APPROVAL_STATE', '').strip()
                
                if (spec_device == lot_device and 
                    scheduler._is_stage_match(lot_stage, spec_stage) and
                    spec_approval == 'Released'):
                    print(f"✅ 使用完整数据找到匹配!")
                    print(f"   STAGE: '{spec_stage}'")
                    print(f"   HB_PN: {spec.get('HB_PN', '')}")
                    print(f"   TB_PN: {spec.get('TB_PN', '')}")
                    found_full_match = True
                    break
            
            if not found_full_match:
                print(f"❌ 即使使用完整数据也没有找到匹配")
        
except Exception as e:
    print(f"❌ 调试失败: {e}")
    import traceback
    traceback.print_exc() 