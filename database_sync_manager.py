#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库同步管理器
用于与其他数据库系统集成，实现待排产数据的自动同步
"""

import pymysql
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import json

logger = logging.getLogger(__name__)

class DatabaseSyncManager:
    """数据库同步管理器"""
    
    def __init__(self):
        self.mysql_config = {
            'host': 'localhost',
            'user': 'root',
            'password': 'WWWwww123!',
            'database': 'aps',
            'charset': 'utf8mb4'
        }
    
    def get_mysql_connection(self):
        """获取MySQL连接"""
        return pymysql.connect(**self.mysql_config)
    
    def sync_wait_lots_from_external(self, external_data: List[Dict]) -> Dict[str, int]:
        """
        从外部系统同步待排产批次数据到ET_WAIT_LOT表
        
        Args:
            external_data: 外部系统的批次数据列表
            
        Returns:
            同步结果统计
        """
        
        try:
            connection = self.get_mysql_connection()
            
            with connection.cursor() as cursor:
                # 数据清理和预处理
                processed_data = self._preprocess_external_data(external_data)
                
                # 批量插入或更新
                insert_count = 0
                update_count = 0
                skip_count = 0
                
                for lot_data in processed_data:
                    try:
                        # 检查是否已存在
                        cursor.execute(
                            "SELECT COUNT(*) FROM ET_WAIT_LOT WHERE LOT_ID = %s",
                            (lot_data['LOT_ID'],)
                        )
                        
                        if cursor.fetchone()[0] > 0:
                            # 更新现有记录
                            if self._update_wait_lot(cursor, lot_data):
                                update_count += 1
                            else:
                                skip_count += 1
                        else:
                            # 插入新记录
                            if self._insert_wait_lot(cursor, lot_data):
                                insert_count += 1
                            else:
                                skip_count += 1
                                
                    except Exception as e:
                        logger.error(f"同步批次 {lot_data.get('LOT_ID')} 失败: {e}")
                        skip_count += 1
                
                # 提交事务
                connection.commit()
                
                logger.info(f"数据同步完成: 插入 {insert_count}, 更新 {update_count}, 跳过 {skip_count}")
                
                return {
                    'total_processed': len(processed_data),
                    'inserted': insert_count,
                    'updated': update_count,
                    'skipped': skip_count,
                    'success': True
                }
                
        except Exception as e:
            logger.error(f"数据同步失败: {e}")
            return {
                'total_processed': 0,
                'inserted': 0,
                'updated': 0,
                'skipped': 0,
                'success': False,
                'error': str(e)
            }
        finally:
            if 'connection' in locals():
                connection.close()
    
    def _preprocess_external_data(self, external_data: List[Dict]) -> List[Dict]:
        """预处理外部数据，标准化字段格式"""
        
        processed = []
        
        for raw_data in external_data:
            # 标准化数据格式
            lot_data = {
                'LOT_ID': raw_data.get('lot_id') or raw_data.get('LOT_ID') or '',
                'LOT_TYPE': raw_data.get('lot_type') or raw_data.get('LOT_TYPE') or 'PRODUCTION',
                'GOOD_QTY': self._safe_int(raw_data.get('good_qty') or raw_data.get('GOOD_QTY'), 0),
                'PROD_ID': raw_data.get('prod_id') or raw_data.get('PROD_ID') or '',
                'DEVICE': raw_data.get('device') or raw_data.get('DEVICE') or '',
                'CHIP_ID': raw_data.get('chip_id') or raw_data.get('CHIP_ID') or '',
                'PKG_PN': raw_data.get('pkg_pn') or raw_data.get('PKG_PN') or '',
                'PO_ID': raw_data.get('po_id') or raw_data.get('PO_ID') or '',
                'STAGE': raw_data.get('stage') or raw_data.get('STAGE') or 'FT',
                'WIP_STATE': 'Released',  # 标准化为待排产状态
                'PROC_STATE': 'Wait',     # 标准化为等待状态
                'HOLD_STATE': 'NotOnHold', # 标准化为未扣留
                'FLOW_ID': raw_data.get('flow_id') or raw_data.get('FLOW_ID') or '',
                'FLOW_VER': raw_data.get('flow_ver') or raw_data.get('FLOW_VER') or '1.0.0',
                'RELEASE_TIME': self._parse_datetime(raw_data.get('release_time') or raw_data.get('RELEASE_TIME')),
                'FAC_ID': raw_data.get('fac_id') or raw_data.get('FAC_ID') or 'YX01',
                'CREATE_TIME': self._parse_datetime(raw_data.get('create_time') or raw_data.get('CREATE_TIME'))
            }
            
            # 数据验证
            if lot_data['LOT_ID'] and lot_data['DEVICE'] and lot_data['GOOD_QTY'] > 0:
                processed.append(lot_data)
            else:
                logger.warning(f"跳过无效数据: {raw_data}")
        
        return processed
    
    def _insert_wait_lot(self, cursor, lot_data: Dict) -> bool:
        """插入新的待排产批次"""
        try:
            insert_query = """
                INSERT INTO ET_WAIT_LOT (
                    LOT_ID, LOT_TYPE, GOOD_QTY, PROD_ID, DEVICE, CHIP_ID, PKG_PN, PO_ID,
                    STAGE, WIP_STATE, PROC_STATE, HOLD_STATE, FLOW_ID, FLOW_VER,
                    RELEASE_TIME, FAC_ID, CREATE_TIME
                ) VALUES (
                    %(LOT_ID)s, %(LOT_TYPE)s, %(GOOD_QTY)s, %(PROD_ID)s, %(DEVICE)s,
                    %(CHIP_ID)s, %(PKG_PN)s, %(PO_ID)s, %(STAGE)s, %(WIP_STATE)s,
                    %(PROC_STATE)s, %(HOLD_STATE)s, %(FLOW_ID)s, %(FLOW_VER)s,
                    %(RELEASE_TIME)s, %(FAC_ID)s, %(CREATE_TIME)s
                )
            """
            
            cursor.execute(insert_query, lot_data)
            return True
            
        except Exception as e:
            logger.error(f"插入批次 {lot_data['LOT_ID']} 失败: {e}")
            return False
    
    def _update_wait_lot(self, cursor, lot_data: Dict) -> bool:
        """更新现有的待排产批次"""
        try:
            update_query = """
                UPDATE ET_WAIT_LOT SET
                    LOT_TYPE = %(LOT_TYPE)s,
                    GOOD_QTY = %(GOOD_QTY)s,
                    PROD_ID = %(PROD_ID)s,
                    DEVICE = %(DEVICE)s,
                    CHIP_ID = %(CHIP_ID)s,
                    PKG_PN = %(PKG_PN)s,
                    PO_ID = %(PO_ID)s,
                    STAGE = %(STAGE)s,
                    WIP_STATE = %(WIP_STATE)s,
                    PROC_STATE = %(PROC_STATE)s,
                    HOLD_STATE = %(HOLD_STATE)s,
                    FLOW_ID = %(FLOW_ID)s,
                    FLOW_VER = %(FLOW_VER)s,
                    RELEASE_TIME = %(RELEASE_TIME)s,
                    FAC_ID = %(FAC_ID)s
                WHERE LOT_ID = %(LOT_ID)s
            """
            
            cursor.execute(update_query, lot_data)
            return True
            
        except Exception as e:
            logger.error(f"更新批次 {lot_data['LOT_ID']} 失败: {e}")
            return False
    
    def get_wait_lots_status(self) -> Dict:
        """获取待排产批次状态统计"""
        try:
            connection = self.get_mysql_connection()
            
            with connection.cursor() as cursor:
                # 总数统计
                cursor.execute("SELECT COUNT(*) FROM ET_WAIT_LOT")
                total_count = cursor.fetchone()[0]
                
                # 待排产数量
                cursor.execute("""
                    SELECT COUNT(*) FROM ET_WAIT_LOT 
                    WHERE WIP_STATE = 'Released' 
                    AND PROC_STATE = 'Wait' 
                    AND HOLD_STATE = 'NotOnHold'
                """)
                schedulable_count = cursor.fetchone()[0]
                
                # 按状态分组统计
                cursor.execute("""
                    SELECT WIP_STATE, PROC_STATE, COUNT(*) 
                    FROM ET_WAIT_LOT 
                    GROUP BY WIP_STATE, PROC_STATE
                """)
                
                status_breakdown = {}
                for row in cursor.fetchall():
                    key = f"{row[0]}_{row[1]}"
                    status_breakdown[key] = row[2]
                
                # 设备相关统计
                cursor.execute("SELECT COUNT(*) FROM EQP_STATUS WHERE STATUS IN ('0', 'IDLE')")
                available_equipment = cursor.fetchone()[0]
                
                return {
                    'total_lots': total_count,
                    'schedulable_lots': schedulable_count,
                    'available_equipment': available_equipment,
                    'status_breakdown': status_breakdown,
                    'last_updated': datetime.now().isoformat()
                }
                
        except Exception as e:
            logger.error(f"获取状态统计失败: {e}")
            return {'error': str(e)}
        finally:
            if 'connection' in locals():
                connection.close()
    
    def clear_old_wait_lots(self, days_old: int = 30) -> int:
        """清理过期的待排产批次数据"""
        try:
            connection = self.get_mysql_connection()
            
            with connection.cursor() as cursor:
                # 删除指定天数前的数据
                cutoff_date = datetime.now() - timedelta(days=days_old)
                
                cursor.execute("""
                    DELETE FROM ET_WAIT_LOT 
                    WHERE CREATE_TIME < %s 
                    AND (PROC_STATE != 'Wait' OR WIP_STATE != 'Released')
                """, (cutoff_date,))
                
                deleted_count = cursor.rowcount
                connection.commit()
                
                logger.info(f"清理了 {deleted_count} 条过期的待排产批次数据")
                return deleted_count
                
        except Exception as e:
            logger.error(f"清理过期数据失败: {e}")
            return 0
        finally:
            if 'connection' in locals():
                connection.close()
    
    def _safe_int(self, value, default=0) -> int:
        """安全转换为整数"""
        try:
            if value is None:
                return default
            return int(float(value))
        except (ValueError, TypeError):
            return default
    
    def _parse_datetime(self, value) -> Optional[str]:
        """解析时间字符串"""
        if not value:
            return datetime.now().isoformat()
        
        if isinstance(value, datetime):
            return value.isoformat()
        
        try:
            # 尝试解析多种时间格式
            formats = [
                '%Y-%m-%d %H:%M:%S',
                '%Y-%m-%d',
                '%Y/%m/%d %H:%M:%S',
                '%Y/%m/%d'
            ]
            
            for fmt in formats:
                try:
                    dt = datetime.strptime(str(value), fmt)
                    return dt.isoformat()
                except ValueError:
                    continue
            
            # 如果都解析不了，返回当前时间
            return datetime.now().isoformat()
            
        except Exception:
            return datetime.now().isoformat()

# 使用示例
if __name__ == "__main__":
    sync_manager = DatabaseSyncManager()
    
    # 获取状态统计
    status = sync_manager.get_wait_lots_status()
    print("📊 当前数据库状态:")
    print(json.dumps(status, indent=2, ensure_ascii=False))
    
    # 示例：同步外部数据
    # external_data = [
    #     {
    #         'lot_id': 'EXT001',
    #         'device': 'EXTERNAL_DEVICE_1',
    #         'stage': 'FT',
    #         'good_qty': 5000,
    #         'prod_id': 'PROD_EXT_001',
    #         'create_time': '2025-06-26 10:00:00'
    #     }
    # ]
    # 
    # result = sync_manager.sync_wait_lots_from_external(external_data)
    # print(f"同步结果: {result}") 