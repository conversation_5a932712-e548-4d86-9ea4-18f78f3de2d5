2025-06-17 21:31:22,783 WARNING: 第6行数据处理失败: invalid literal for int() with base 10: 'n' [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\api_v2\production\routes.py:703]
2025-06-17 21:31:22,799 WARNING: 第7行数据处理失败: invalid literal for int() with base 10: 'n' [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\api_v2\production\routes.py:703]
2025-06-17 21:31:22,807 INFO: 成功导入 3 条记录到 devicepriorityconfig 表 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\api_v2\production\routes.py:709]
2025-06-17 21:31:22,808 WARNING: 文件 devicepriorityconfig.xlsx 导入时有 3 个警告 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\api_v2\production\routes.py:729]
2025-06-17 21:31:25,145 INFO: 从MySQL获取到 13 条产品优先级配置数据 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:1574]
2025-06-17 21:32:25,286 ERROR: 检查会话状态失败: 'User' object has no attribute 'id' [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\api_v2\auth\routes.py:63]
2025-06-17 21:32:25,319 INFO: 📊 从MySQL获取到 169 条待排产批次数据 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:366]
2025-06-17 21:32:30,777 ERROR: 检查会话状态失败: 'User' object has no attribute 'id' [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\api_v2\auth\routes.py:63]
2025-06-17 21:32:30,803 INFO: 从MySQL获取到 13 条产品优先级配置数据 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:1574]
2025-06-17 21:33:56,278 INFO: 尝试登录用户: admin [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\auth\routes.py:24]
2025-06-17 21:33:56,281 INFO: 找到用户: admin, 角色: admin, 密码哈希: pbkdf2:sha256:600000$5b3oIzxKyFuBWizM$eaaab87f30981563f8519ad8cb7e8669fa5b6280e439fcd17b1845cdd1c4f56c [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\auth\routes.py:33]
2025-06-17 21:33:56,846 INFO: 密码验证结果: True [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\auth\routes.py:35]
2025-06-17 21:33:56,847 INFO: 用户 admin 登录成功 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\auth\routes.py:42]
2025-06-17 21:33:58,895 INFO: 从MySQL获取到 62 条设备状态数据 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:679]
2025-06-17 21:33:58,896 INFO: 🏭 从MySQL获取到 62 条设备状态数据 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:440]
2025-06-17 21:34:02,991 INFO: 从MySQL获取到 62 条设备状态数据 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:679]
2025-06-17 21:34:02,992 INFO: 🏭 从MySQL获取到 62 条设备状态数据 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:440]
2025-06-17 21:34:05,593 INFO: 从MySQL获取到 184 条测试规范数据 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:584]
2025-06-17 21:34:05,598 INFO: 🔬 从MySQL获取到 184 条测试规范数据 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:392]
2025-06-17 21:34:09,731 INFO: 从MySQL获取到 184 条测试规范数据 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:584]
2025-06-17 21:34:09,733 INFO: 🔬 从MySQL获取到 184 条测试规范数据 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:392]
2025-06-17 21:34:12,303 INFO: 从MySQL获取到 1759 条UPH数据 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:722]
2025-06-17 21:34:12,305 INFO: ⚡ 从MySQL获取到 1759 条UPH数据 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:464]
2025-06-17 21:34:16,407 INFO: 从MySQL获取到 1759 条UPH数据 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:722]
2025-06-17 21:34:16,408 INFO: ⚡ 从MySQL获取到 1759 条UPH数据 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:464]
2025-06-17 21:34:18,965 INFO: 从MySQL获取到 124 条TCC_INV数据 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:985]
2025-06-17 21:34:23,079 INFO: 从MySQL获取到 124 条TCC_INV数据 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:985]
2025-06-17 21:34:25,677 INFO: 从MySQL获取到 1000 条CT数据 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:1013]
2025-06-17 21:34:29,790 INFO: 从MySQL获取到 1000 条CT数据 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:1013]
2025-06-17 21:34:32,340 INFO: 📊 从MySQL获取到 169 条待排产批次数据 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:366]
2025-06-17 21:34:36,452 INFO: 📊 从MySQL获取到 169 条待排产批次数据 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:366]
2025-06-17 21:34:39,025 INFO: 从MySQL获取到 2 条已排产批次数据 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:1697]
2025-06-17 21:34:43,134 INFO: 从MySQL获取到 2 条已排产批次数据 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:1697]
2025-06-17 21:34:45,712 INFO: 从MySQL获取到 13 条产品优先级配置数据 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:1574]
2025-06-17 21:34:49,874 ERROR: 创建devicepriorityconfig记录失败: (1292, "Incorrect datetime value: '08:00:00' for column 'from_time' at row 1") [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:1274]
2025-06-17 21:34:51,933 INFO: 从MySQL获取到 13 条产品优先级配置数据 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:1574]
2025-06-17 21:34:54,485 INFO: 从MySQL获取到 19 条批次优先级配置数据 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:1604]
2025-06-17 21:34:58,580 INFO: 成功创建lotpriorityconfig记录，ID: 20 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:1266]
2025-06-17 21:35:00,640 INFO: 从MySQL获取到 20 条批次优先级配置数据 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:1604]
2025-06-17 21:35:05,247 INFO: 尝试登录用户: admin [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\auth\routes.py:24]
2025-06-17 21:35:05,249 INFO: 找到用户: admin, 角色: admin, 密码哈希: pbkdf2:sha256:600000$5b3oIzxKyFuBWizM$eaaab87f30981563f8519ad8cb7e8669fa5b6280e439fcd17b1845cdd1c4f56c [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\auth\routes.py:33]
2025-06-17 21:35:05,807 INFO: 密码验证结果: True [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\auth\routes.py:35]
2025-06-17 21:35:05,807 INFO: 用户 admin 登录成功 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\auth\routes.py:42]
2025-06-17 21:35:07,865 ERROR: 创建devicepriorityconfig记录失败: (1292, "Incorrect datetime value: '08:00:00' for column 'from_time' at row 1") [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:1274]
2025-06-17 21:35:09,935 INFO: 成功创建lotpriorityconfig记录，ID: 21 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:1266]
2025-06-17 21:35:11,984 INFO: 成功更新lotpriorityconfig记录，主键: 21，影响行数: 1 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:1355]
2025-06-17 21:35:14,038 INFO: 成功删除lotpriorityconfig记录，主键: 21，影响行数: 1 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:1414]
2025-06-17 21:35:18,631 INFO: 尝试登录用户: admin [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\auth\routes.py:24]
