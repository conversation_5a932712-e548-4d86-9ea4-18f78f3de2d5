#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试算法权重配置API功能
"""

import requests
import json
import time

# API基础URL
BASE_URL = "http://127.0.0.1:5000"
LOGIN_URL = f"{BASE_URL}/auth/login"
WEIGHTS_API_URL = f"{BASE_URL}/api/production/algorithm-weights"

def login_and_get_session():
    """登录并获取会话"""
    session = requests.Session()
    
    # 首先获取登录页面（可能需要CSRF token）
    login_page = session.get(f"{BASE_URL}/auth/login")
    
    # 登录
    login_data = {
        'username': 'admin',
        'password': 'admin'
    }
    
    response = session.post(LOGIN_URL, data=login_data, allow_redirects=False)
    
    if response.status_code in [200, 302]:
        print("✅ 登录成功")
        return session
    else:
        print(f"❌ 登录失败: {response.status_code}")
        print(f"响应内容: {response.text[:200]}")
        return None

def test_get_weights(session):
    """测试获取权重配置"""
    print("\n📋 测试获取权重配置...")
    
    try:
        response = session.get(WEIGHTS_API_URL)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 获取权重配置成功")
            print(f"响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
            return data
        else:
            print(f"❌ 获取权重配置失败: {response.status_code}")
            print(f"响应内容: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return None

def test_save_weights(session):
    """测试保存权重配置"""
    print("\n💾 测试保存权重配置...")
    
    # 测试权重数据
    test_weights = {
        'tech_match_weight': 30.0,
        'load_balance_weight': 15.0,
        'deadline_weight': 30.0,
        'value_efficiency_weight': 20.0,
        'business_priority_weight': 5.0,
        'minor_changeover_time': 50,
        'major_changeover_time': 130,
        'urgent_threshold': 10,
        'normal_threshold': 20,
        'critical_threshold': 80
    }
    
    try:
        response = session.post(
            WEIGHTS_API_URL,
            json=test_weights,
            headers={'Content-Type': 'application/json'}
        )
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 保存权重配置成功")
            print(f"响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
            return True
        else:
            print(f"❌ 保存权重配置失败: {response.status_code}")
            print(f"响应内容: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def test_invalid_weights(session):
    """测试无效权重数据"""
    print("\n🚫 测试无效权重数据...")
    
    # 权重总和不为100%的数据
    invalid_weights = {
        'tech_match_weight': 50.0,
        'load_balance_weight': 20.0,
        'deadline_weight': 20.0,
        'value_efficiency_weight': 20.0,
        'business_priority_weight': 20.0  # 总和130%
    }
    
    try:
        response = session.post(
            WEIGHTS_API_URL,
            json=invalid_weights,
            headers={'Content-Type': 'application/json'}
        )
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 400:
            data = response.json()
            print("✅ 正确拒绝了无效权重数据")
            print(f"错误信息: {data.get('message', '')}")
            return True
        else:
            print(f"❌ 应该拒绝无效数据但没有: {response.status_code}")
            print(f"响应内容: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 开始测试算法权重配置API...")
    
    # 登录
    session = login_and_get_session()
    if not session:
        print("❌ 无法登录，测试终止")
        return
    
    # 等待一下确保登录完成
    time.sleep(1)
    
    # 测试获取权重配置
    original_weights = test_get_weights(session)
    
    # 测试保存权重配置
    save_success = test_save_weights(session)
    
    # 测试无效权重数据
    invalid_test_success = test_invalid_weights(session)
    
    # 再次获取权重配置，验证保存是否成功
    if save_success:
        print("\n🔄 验证权重配置是否已保存...")
        updated_weights = test_get_weights(session)
        
        if updated_weights and updated_weights.get('success'):
            weights_data = updated_weights.get('weights', {})
            if weights_data.get('tech_match_weight') == 30.0:
                print("✅ 权重配置保存验证成功")
            else:
                print("❌ 权重配置保存验证失败")
    
    # 总结
    print("\n📊 测试总结:")
    print(f"  - 获取权重配置: {'✅' if original_weights else '❌'}")
    print(f"  - 保存权重配置: {'✅' if save_success else '❌'}")
    print(f"  - 无效数据验证: {'✅' if invalid_test_success else '❌'}")
    
    if original_weights and save_success and invalid_test_success:
        print("\n🎉 所有测试通过！算法权重配置功能正常工作。")
    else:
        print("\n⚠️ 部分测试失败，请检查相关功能。")

if __name__ == "__main__":
    main()
