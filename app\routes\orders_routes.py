#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
订单管理路由
"""

from flask import Blueprint, render_template, request, jsonify, redirect, url_for
from flask_login import login_required, current_user
from app import db
from app.models import OrderData

bp = Blueprint('orders', __name__, url_prefix='/orders')

@bp.route('/semi-auto')
@login_required
def orders_semi_auto():
    """手动导入订单页面"""
    return render_template('orders/orders_semi_auto.html')

@bp.route('/auto')
@login_required
def orders_auto():
    """订单处理中心页面"""
    return render_template('orders/orders_auto.html')

@bp.route('/summary-preview')
@login_required
def summary_preview():
    """订单汇总预览页面"""
    # 权限检查 - 汇总预览权限ID 38
    if not current_user.has_permission(38):
        return redirect(url_for('auth.login'))
    return render_template('orders/summary_preview.html')

@bp.route('/preview-summary')
@login_required
def preview_summary_page():
    """汇总表预览页面（新窗口打开）"""
    # 权限检查 - 汇总预览权限ID 38
    if not current_user.has_permission(38):
        return redirect(url_for('auth.login'))
    
    summary_type = request.args.get('type', 'all')
    
    # 获取对应类型的订单数据
    query = db.session.query(OrderData)
    
    if summary_type == 'engineering':
        query = query.filter_by(classification='engineering')
        title = 'FT工程订单汇总预览'
    elif summary_type == 'production':
        query = query.filter_by(classification='production')
        title = 'FT量产订单汇总预览'
    else:
        title = '全部订单汇总预览'
    
    orders = query.order_by(OrderData.delivery_date.asc()).all()
    
    return render_template('orders/summary_preview_detail.html', 
                         orders=orders, 
                         summary_type=summary_type,
                         title=title) 

@bp.route('/high-concurrency-test')
@login_required
def high_concurrency_test():
    """高并发邮件处理测试控制台"""
    return render_template('orders/high_concurrency_test.html') 