#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

print("🔍 调试特定批次的配置需求...")

try:
    from app.services.real_scheduling_service import RealSchedulingService
    from app.services.data_source_manager import DataSourceManager
    
    scheduler = RealSchedulingService()
    dm = DataSourceManager()
    
    # 获取特定批次
    wait_lots_result = dm.get_table_data('ET_WAIT_LOT', per_page=200)
    wait_lots = wait_lots_result.get('data', [])
    
    target_lot = None
    for lot in wait_lots:
        if lot.get('LOT_ID') == 'YX2500002099':
            target_lot = lot
            break
    
    if not target_lot:
        print("❌ 未找到批次 YX2500002099")
        exit()
    
    print(f"✅ 找到批次 YX2500002099:")
    for key, value in target_lot.items():
        print(f"  {key}: {value}")
    
    # 获取配置需求
    requirements = scheduler.get_lot_configuration_requirements(target_lot)
    if requirements:
        print(f"\n📋 批次配置需求:")
        for key, value in requirements.items():
            print(f"  {key}: {value}")
        
        # 测试与HANK-C-001-5800的兼容性
        equipment_result = dm.get_table_data('EQP_STATUS', per_page=100)
        all_equipment = equipment_result.get('data', [])
        
        hank_equipment = None
        for eq in all_equipment:
            if eq.get('HANDLER_ID') == 'HANK-C-001-5800':
                hank_equipment = eq
                break
        
        if hank_equipment:
            print(f"\n🏭 HANK-C-001-5800 设备信息:")
            print(f"  HANDLER_TYPE: {hank_equipment.get('HANDLER_TYPE')}")
            print(f"  HANDLER_CONFIG: {hank_equipment.get('HANDLER_CONFIG')}")
            print(f"  STATUS: {hank_equipment.get('STATUS')}")
            print(f"  DEVICE: {hank_equipment.get('DEVICE')}")
            
            # 测试兼容性
            eqp_type = hank_equipment.get('HANDLER_TYPE', '').strip()
            eqp_config = hank_equipment.get('HANDLER_CONFIG', '').strip()
            req_config = requirements.get('HANDLER_CONFIG', '').strip()
            req_stage = requirements.get('STAGE', '').strip()
            
            print(f"\n🧪 兼容性测试:")
            print(f"  设备类型: {eqp_type}")
            print(f"  设备配置: {eqp_config}")
            print(f"  需求配置: {req_config}")
            print(f"  需求阶段: {req_stage}")
            
            compatible = scheduler._is_equipment_type_compatible(eqp_type, eqp_config, req_config, req_stage)
            print(f"  兼容性结果: {'✅ 兼容' if compatible else '❌ 不兼容'}")
            
            if not compatible:
                print(f"  ✅ 正确！烧录机不应该处理这个测试批次")
            else:
                print(f"  ❌ 错误！兼容性检查有问题")
                
        # 查找适合的设备
        suitable_equipment = scheduler.find_suitable_equipment(target_lot, requirements)
        print(f"\n🎯 适合的设备列表:")
        for idx, eq_info in enumerate(suitable_equipment[:5]):
            equipment = eq_info.get('equipment', {})
            handler_id = equipment.get('HANDLER_ID', '')
            handler_type = equipment.get('HANDLER_TYPE', '')
            match_type = eq_info.get('match_type', '')
            score = eq_info.get('comprehensive_score', 0)
            print(f"  {idx+1}. {handler_id} ({handler_type}) - {match_type} - 评分:{score:.2f}")
    else:
        print(f"❌ 无法获取批次配置需求")
    
except Exception as e:
    print(f"❌ 调试失败: {e}")
    import traceback
    traceback.print_exc() 