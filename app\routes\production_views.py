"""
生产管理视图路由 (精简版)
"""
from flask import Blueprint, render_template
from flask_login import login_required

production_views_bp = Blueprint('production_views', __name__,
                               template_folder='templates')

@production_views_bp.route('/production/auto')
@login_required
def production_auto():
    """自动排产页面"""
    return render_template('production/auto.html')

@production_views_bp.route('/production/semi-auto')
@login_required 
def production_semi_auto():
    """手动排产页面"""
    return render_template('production/semi_auto.html')



@production_views_bp.route('/production/priority-settings')
@login_required
def production_priority_settings():
    """优先级设定页面（旧版，保留兼容性）"""
    return render_template('production/priority_settings.html')

@production_views_bp.route('/production/device-priority')
@login_required
def production_device_priority():
    """产品优先级配置页面"""
    return render_template('production/device_priority.html')

@production_views_bp.route('/production/lot-priority')
@login_required
def production_lot_priority():
    """批次优先级配置页面"""
    return render_template('production/lot_priority.html')

@production_views_bp.route('/production/wait-lots')
@login_required
def wait_lots():
    """待排产批次页面"""
    return render_template('production/wait_lots.html')

@production_views_bp.route('/production/done-lots')
@login_required
def done_lots():
    """已排产批次页面"""
    return render_template('production/done_lots.html')

@production_views_bp.route('/production/algorithm')
@login_required
def production_algorithm():
    """算法设置页面"""
    return render_template('production/algorithm.html')
