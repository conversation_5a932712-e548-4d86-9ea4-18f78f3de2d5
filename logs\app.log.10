2025-06-17 20:49:34,747 WARNING: ⚠️ API v2相关蓝图注册失败: cannot import name 'data_sources' from 'app.routes' (unknown location) [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\__init__.py:275]
2025-06-17 20:49:34,751 INFO: ✅ 性能监控已启用 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\__init__.py:321]
2025-06-17 20:49:34,753 WARNING: ⚠️ 缓存管理器初始化失败: No module named 'redis' [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\__init__.py:334]
2025-06-17 20:49:40,115 INFO: 尝试登录用户: admin [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\auth\routes.py:24]
2025-06-17 20:49:40,117 INFO: 找到用户: admin, 角色: admin, 密码哈希: pbkdf2:sha256:600000$5b3oIzxKyFuBWizM$eaaab87f30981563f8519ad8cb7e8669fa5b6280e439fcd17b1845cdd1c4f56c [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\auth\routes.py:33]
2025-06-17 20:49:40,642 INFO: 密码验证结果: True [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\auth\routes.py:35]
2025-06-17 20:49:40,642 INFO: 用户 admin 登录成功 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\auth\routes.py:42]
2025-06-17 20:49:41,749 ERROR: 检查会话状态失败: 'User' object has no attribute 'id' [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\api_v2\auth\routes.py:63]
2025-06-17 20:49:45,452 ERROR: 检查会话状态失败: 'User' object has no attribute 'id' [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\api_v2\auth\routes.py:63]
2025-06-17 20:49:45,459 INFO: 成功连接到MySQL数据库: aps [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\api\routes.py:92]
2025-06-17 20:49:48,952 ERROR: 检查会话状态失败: 'User' object has no attribute 'id' [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\api_v2\auth\routes.py:63]
2025-06-17 20:49:48,978 INFO: ✅ MySQL数据源可用 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:172]
2025-06-17 20:49:48,986 INFO: 📊 从MySQL获取到 171 条待排产批次数据 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:221]
2025-06-17 20:49:50,766 INFO: 📊 从MySQL获取到 171 条待排产批次数据 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:221]
2025-06-17 20:49:59,032 INFO: 📊 从MySQL获取到 171 条待排产批次数据 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:221]
2025-06-17 20:50:03,636 INFO: 📊 从MySQL获取到 171 条待排产批次数据 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:221]
2025-06-17 20:50:23,807 ERROR: 检查会话状态失败: 'User' object has no attribute 'id' [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\api_v2\auth\routes.py:63]
2025-06-17 20:50:23,838 INFO: 从MySQL获取到 62 条设备状态数据 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:534]
2025-06-17 20:50:23,839 INFO: 🏭 从MySQL获取到 62 条设备状态数据 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:295]
2025-06-17 20:50:26,074 INFO: 从MySQL获取到 62 条设备状态数据 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:534]
2025-06-17 20:50:26,076 INFO: 🏭 从MySQL获取到 62 条设备状态数据 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:295]
2025-06-17 20:50:32,431 ERROR: 检查会话状态失败: 'User' object has no attribute 'id' [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\api_v2\auth\routes.py:63]
2025-06-17 20:50:32,553 INFO: 从MySQL获取到 185 条测试规范数据 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:439]
2025-06-17 20:50:32,556 INFO: 🔬 从MySQL获取到 185 条测试规范数据 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:247]
2025-06-17 20:50:34,662 ERROR: 检查会话状态失败: 'User' object has no attribute 'id' [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\api_v2\auth\routes.py:63]
2025-06-17 20:50:34,681 INFO: 从MySQL获取到 124 条TCC_INV数据 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:840]
2025-06-17 20:50:36,993 ERROR: 检查会话状态失败: 'User' object has no attribute 'id' [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\api_v2\auth\routes.py:63]
2025-06-17 20:50:37,061 INFO: 从MySQL获取到 1000 条CT数据 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:868]
2025-06-17 20:52:55,184 ERROR: 更新CT记录失败: (1292, "Incorrect datetime value: 'Tue, 17 Jun 2025 17:17:22 GMT' for column 'created_at' at row 1") [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:1195]
2025-06-17 20:53:23,976 INFO: 从MySQL获取到 1000 条CT数据 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:868]
2025-06-17 20:53:31,983 ERROR: 检查会话状态失败: 'User' object has no attribute 'id' [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\api_v2\auth\routes.py:63]
2025-06-17 20:53:32,010 INFO: 从MySQL获取到 1759 条UPH数据 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:577]
2025-06-17 20:53:32,011 INFO: ⚡ 从MySQL获取到 1759 条UPH数据 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:319]
2025-06-17 20:53:47,579 ERROR: 更新ET_UPH_EQP记录失败: (1292, "Incorrect datetime value: 'Tue, 17 Jun 2025 17:17:34 GMT' for column 'created_at' at row 1") [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:1195]
2025-06-17 20:54:01,607 ERROR: 检查会话状态失败: 'User' object has no attribute 'id' [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\api_v2\auth\routes.py:63]
2025-06-17 20:54:01,610 INFO: 📊 从MySQL获取到 171 条待排产批次数据 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:221]
2025-06-17 20:54:20,756 ERROR: 检查会话状态失败: 'User' object has no attribute 'id' [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\api_v2\auth\routes.py:63]
2025-06-17 20:54:20,846 INFO: 从MySQL获取到 185 条测试规范数据 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:439]
2025-06-17 20:54:20,849 INFO: 🔬 从MySQL获取到 185 条测试规范数据 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:247]
2025-06-17 20:54:31,627 ERROR: 创建et_ft_test_spec记录失败: (1292, "Incorrect datetime value: '' for column 'created_at' at row 1") [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:1117]
2025-06-17 20:55:22,318 ERROR: 检查会话状态失败: 'User' object has no attribute 'id' [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\api_v2\auth\routes.py:63]
2025-06-17 20:55:22,327 INFO: 📊 从MySQL获取到 171 条待排产批次数据 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:221]
2025-06-17 20:55:48,706 ERROR: 检查会话状态失败: 'User' object has no attribute 'id' [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\api_v2\auth\routes.py:63]
2025-06-17 20:55:48,709 INFO: 从MySQL获取到 3 条已排产批次数据 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:1510]
2025-06-17 20:55:53,493 INFO: 成功批量删除lotprioritydone记录，删除数量: 1 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:1314]
2025-06-17 20:55:53,822 INFO: 从MySQL获取到 2 条已排产批次数据 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:1510]
2025-06-17 20:55:56,719 ERROR: 检查会话状态失败: 'User' object has no attribute 'id' [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\api_v2\auth\routes.py:63]
