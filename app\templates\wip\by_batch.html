{% extends "base.html" %}

{% block head %}
{{ super() }}
<script src="{{ url_for('static', filename='js/base/api-client-v2.js') }}"></script>
<style>
:root {
    --primary-color: #b72424;
    --secondary-color: #d73027;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
}

.navbar-brand, .nav-link.active {
    color: var(--primary-color) !important;
}

.card-header {
    background-color: var(--primary-color);
    color: white;
}
</style>
{% endblock %}

{% block title %}批次WIP跟踪{% endblock %}

{% block extra_head %}
<style>
:root {
    --primary-color: #b72424;
    --secondary-color: #d73027;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
}

.navbar-brand, .nav-link.active {
    color: var(--primary-color) !important;
}

.card-header {
    background-color: var(--primary-color);
    color: white;
}
</style>
{% endblock %}

{% block extra_css %}
<!-- DataTables CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='vendor/bootstrap/bootstrap.min.css') }}">
<style>
    .preview-area {
        margin-bottom: 1rem;
        padding: 1.25rem;
        background-color: #ffffff;
        border: 1px solid #dee2e6;
        border-radius: 6px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.06);
    }
    
    /* 高级筛选面板样式 */
    .advanced-filter-panel {
        background-color: white;
        border: 1px solid #dee2e6;
        border-radius: 6px;
        padding: 15px;
        margin-bottom: 15px;
        box-shadow: 0 1px 3px rgba(0,0,0,0.08);
    }
    
    .filter-row {
        display: flex;
        align-items: end;
        gap: 10px;
        margin-bottom: 10px;
        padding: 8px;
        background-color: #f8f9fa;
        border-radius: 4px;
        border: 1px solid #e9ecef;
    }
    
    .filter-field {
        flex: 1;
        min-width: 160px;
    }
    
    .filter-operator {
        flex: 0 0 100px;
    }
    
    .filter-value {
        flex: 1;
        min-width: 160px;
    }
    
    .filter-actions {
        flex: 0 0 80px;
        display: flex;
        gap: 3px;
    }
    
    .btn-sm {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
    }
    
    .collapse-toggle {
        cursor: pointer;
        user-select: none;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 6px 0;
        border-bottom: 1px solid #dee2e6;
        margin-bottom: 10px;
    }
    
    .collapse-toggle:hover {
        color: var(--theme-color);
    }
    
    .collapse-icon {
        transition: transform 0.3s ease;
    }
    
    .collapse-icon.rotated {
        transform: rotate(180deg);
    }
    
    .table-responsive {
        max-height: 70vh;
        overflow-y: auto;
        border: 1px solid #dee2e6;
        border-radius: 4px;
    }
    
    .table th {
        white-space: nowrap;
        min-width: 100px;
        position: relative;
        padding-right: 20px;
    }
    
    .table td {
        white-space: nowrap;
    }
    
    .selectable-row {
        cursor: pointer;
    }
    
    .selectable-row.selected {
        background-color: #fff1f0 !important;
    }
    
    .selectable-row:hover {
        background-color: #f8f9fa;
    }
    
    .sort-icon {
        position: absolute;
        right: 5px;
        cursor: pointer;
        color: #999;
    }
    
    .sort-icon.active {
        color: #b72424;
    }
    
    .batch-actions {
        margin-bottom: 10px;
    }
    
    .batch-actions .btn {
        margin-right: 5px;
    }
    
    .table {
        font-size: 0.8rem;
        line-height: 1.2;
    }
    
    .quick-filter-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 6px;
        margin-bottom: 10px;
    }
    
    .filter-tag {
        background-color: var(--theme-color);
        color: white;
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 0.8rem;
        display: flex;
        align-items: center;
        gap: 5px;
    }
    
    .filter-tag .remove-filter {
        cursor: pointer;
        margin-left: 5px;
        font-weight: bold;
    }
    
    .filter-tag .remove-filter:hover {
        background-color: rgba(255,255,255,0.2);
        border-radius: 50%;
    }
    
    .loading-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(255, 255, 255, 0.8);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 1000;
    }
    

    
    /* 优化小尺寸表单标签 */
    .form-label-sm {
        font-size: 0.8rem;
        font-weight: 500;
        margin-bottom: 0.25rem;
    }
    
    /* 高级筛选标题样式优化 */
    .advanced-filter-panel h6 {
        font-size: 0.95rem;
        margin-bottom: 0;
    }
    
    /* 数据预览区域标题强调 */
    .preview-area h6 {
        font-size: 1.1rem;
        font-weight: 600;
        color: #2c3e50;
    }
    
    #loadingIndicator {
        padding: 30px;
        text-align: center;
    }
    .spinner {
        margin: 0 auto;
        width: 70px;
        text-align: center;
    }
    .spinner > div {
        width: 18px;
        height: 18px;
        background-color: #333;
        border-radius: 100%;
        display: inline-block;
        animation: sk-bouncedelay 1.4s infinite ease-in-out both;
    }
    .spinner .bounce1 {
        animation-delay: -0.32s;
    }
    .spinner .bounce2 {
        animation-delay: -0.16s;
    }
    @keyframes sk-bouncedelay {
        0%, 80%, 100% { 
            transform: scale(0);
        } 40% { 
            transform: scale(1.0);
        }
    }
    
    /* 统一的通知样式 */
    .notification-message {
        position: fixed;
        top: 20px;
        right: 20px;
        background-color: #5cb85c;
        color: white;
        padding: 15px;
        border-radius: 4px;
        display: flex;
        align-items: center;
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        z-index: 1050;
        max-width: 400px;
        animation: fadeInOut 5s forwards;
    }
    
    .notification-icon {
        margin-right: 10px;
        font-size: 20px;
    }
    
    .notification-text {
        flex-grow: 1;
    }
    
    .notification-close {
        background: none;
        border: none;
        color: white;
        font-size: 20px;
        cursor: pointer;
        padding: 0 5px;
    }
    
    .notification-close:hover {
        opacity: 0.8;
    }
    
    @keyframes fadeInOut {
        0% { opacity: 0; }
        10% { opacity: 1; }
        90% { opacity: 1; }
        100% { opacity: 0; display: none; }
    }
    
    /* 根据排产管理页面的样式，修改按钮颜色 */
    .btn-primary {
        background-color: #b72424;
        border-color: #b72424;
    }
    .btn-primary:hover {
        background-color: #b72424;
        border-color: #b72424;
    }
    .btn-primary:focus {
        background-color: #b72424;
        border-color: #b72424;
        box-shadow: 0 0 0 0.25rem rgba(245, 34, 45, 0.25);
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-3">
        <div class="col">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="card-title mb-0">批次WIP跟踪</h5>
                        <div>
                            <button type="button" class="btn btn-outline-primary me-2" onclick="refreshData()">
                                <i class="fas fa-sync-alt me-1"></i>刷新
                            </button>
                            <button type="button" class="btn btn-success me-2" onclick="exportData()">
                                <i class="fas fa-file-excel me-1"></i>导出
                            </button>
                        </div>
                    </div>
                    
                    <!-- 高级筛选面板 -->
                    <div class="advanced-filter-panel">
                        <div class="collapse-toggle" onclick="toggleAdvancedFilter()">
                            <h6 class="mb-0">
                                <i class="fas fa-filter me-2"></i>高级筛选
                                <small class="text-muted ms-2">支持多条件组合查询</small>
                            </h6>
                            <i class="fas fa-chevron-down collapse-icon" id="collapseIcon"></i>
                        </div>
                        
                        <div id="advancedFilterContent" class="collapse show">
                            <!-- 当前筛选条件标签 -->
                            <div class="quick-filter-tags" id="filterTags"></div>
                            
                            <!-- 筛选条件列表 -->
                            <div id="filterConditions">
                                <!-- 默认的第一个筛选条件 -->
                                <div class="filter-row" data-index="0">
                                    <div class="filter-field">
                                        <label class="form-label form-label-sm">字段</label>
                                        <select class="form-select form-select-sm" name="field" id="defaultFieldSelect">
                                            <option value="">请选择字段</option>
                                            <!-- 字段选项将通过JavaScript动态生成 -->
                                        </select>
                                    </div>
                                    <div class="filter-operator">
                                        <label class="form-label form-label-sm">操作符</label>
                                        <select class="form-select form-select-sm" name="operator">
                                            <option value="contains">包含</option>
                                            <option value="equals">等于</option>
                                            <option value="starts_with">开始于</option>
                                            <option value="ends_with">结束于</option>
                                            <option value="not_equals">不等于</option>
                                            <option value="is_empty">为空</option>
                                            <option value="is_not_empty">不为空</option>
                                        </select>
                                    </div>
                                    <div class="filter-value">
                                        <label class="form-label form-label-sm">值</label>
                                        <input type="text" class="form-control form-control-sm" name="value" placeholder="输入筛选值">
                                    </div>
                                    <div class="filter-actions">
                                        <label class="form-label form-label-sm">&nbsp;</label>
                                        <div>
                                            <button type="button" class="btn btn-sm btn-success" onclick="addFilterCondition()" title="添加条件">
                                                <i class="fas fa-plus"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm btn-danger" onclick="removeFilterCondition(0)" title="删除条件">
                                                <i class="fas fa-minus"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 筛选操作按钮 -->
                            <div class="d-flex justify-content-between align-items-center mt-2">
                                <div>
                                    <button type="button" class="btn btn-primary btn-sm" onclick="applyAdvancedFilter()">
                                        <i class="fas fa-search me-1"></i>应用筛选
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary btn-sm ms-2" onclick="clearAllFilters()">
                                        <i class="fas fa-times me-1"></i>清除筛选
                                    </button>
                                    <button type="button" class="btn btn-outline-info btn-sm ms-2" onclick="saveFilterPreset()">
                                        <i class="fas fa-save me-1"></i>保存预设
                                    </button>
                                </div>
                                <div class="d-flex gap-2">
                                    <select class="form-select form-select-sm" id="filterPresets" onchange="loadFilterPreset()" style="width: 180px;">
                                        <option value="">选择筛选预设...</option>
                                    </select>
                                    <button type="button" class="btn btn-outline-danger btn-sm" onclick="deleteFilterPreset()" title="删除选中的预设">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 预览区域 -->
                    <div class="preview-area">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h6 class="mb-0">WIP批次数据预览</h6>
                            <div>
                                <span class="badge bg-primary me-2" id="recordCount">0 条记录</span>
                                <select class="form-select form-select-sm d-inline-block" style="width: auto;" id="pageSize" onchange="changePageSize()">
                                    <option value="25">25 条/页</option>
                                    <option value="50" selected>50 条/页</option>
                                    <option value="100">100 条/页</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="table-responsive" style="position: relative;">
                            <div class="loading-overlay" id="loadingOverlay" style="display: none;">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">加载中...</span>
                                </div>
                            </div>
                            
                            <table class="table table-sm table-hover table-striped" id="wipTable">
                                <thead class="table-light">
                                    <tr id="tableHeaders">
                                        <!-- 表头将通过JavaScript动态生成 -->
                                    </tr>
                                </thead>
                                <tbody id="tableBody">
                                    <tr>
                                        <td colspan="20" class="text-center">加载中...</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- 分页导航 -->
                        <nav class="mt-3" aria-label="数据分页">
                            <ul class="pagination justify-content-center" id="pagination">
                                <!-- 分页按钮将通过JavaScript动态生成 -->
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 添加统一的通知容器 -->
<div id="notification-container"></div>
{% endblock %}

{% block extra_js %}
<!-- jQuery -->
<script src="{{ url_for('static', filename='jquery/jquery.min.js') }}">
    // 🚀 推荐使用新的API v2客户端 (支持自动降级)
    // 示例: 
    // const result = await apsAPI.resources.getTableData('tableName', {page: 1, per_page: 50});
    // if (result.success) { console.log(result.data); }
    
    // 原有fetch代码保持不变，确保向后兼容
    </script>
<!-- DataTables -->
<script src="{{ url_for('static', filename='jquery/jquery.min.js') }}"></script>
         <script src="{{ url_for('static', filename='vendor/bootstrap/bootstrap.bundle.min.js') }}"></script>

<script>
// WIP批次数据API配置
const WIP_API_CONFIG = {
    baseUrl: '/api/v2/tables/wip_lot',
    database: 'aps',  // 确保连接到aps主业务数据库
    displayFields: [
        'LOT_ID', 'DEVICE', 'STAGE', 'WIP_STATE', 'PROC_STATE', 
        'LOT_QTY', 'GOOD_QTY', 'NG_QTY', 'PRIORITY_LEVEL', 
        'PLAN_DUE_DATE', 'created_at'
    ],
    searchFields: ['LOT_ID', 'DEVICE', 'STAGE', 'WIP_STATE']
};

// 加载WIP批次数据
function loadWipData() {
    showLoading(true);
    
    fetch(`${WIP_API_CONFIG.baseUrl}/data?database=${WIP_API_CONFIG.database}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                renderWipTable(data.data);
                updateRecordCount(data.data.length);
            } else {
                showError('加载WIP批次数据失败: ' + (data.error || '未知错误'));
            }
        })
        .catch(error => {
            showError('网络错误: ' + error.message);
        })
        .finally(() => {
            showLoading(false);
        });
}

// 渲染WIP表格
function renderWipTable(data) {
    const tableBody = document.getElementById('tableBody');
    const tableHeaders = document.getElementById('tableHeaders');
    
    if (!data || data.length === 0) {
        tableBody.innerHTML = '<tr><td colspan="20" class="text-center">暂无数据</td></tr>';
        return;
    }
    
    // 渲染表头
    tableHeaders.innerHTML = WIP_API_CONFIG.displayFields.map(field => 
        `<th>${getFieldDisplayName(field)}</th>`
    ).join('');
    
    // 渲染数据行
    tableBody.innerHTML = data.map(record => 
        `<tr>
            ${WIP_API_CONFIG.displayFields.map(field => 
                `<td>${formatFieldValue(record[field], field)}</td>`
            ).join('')}
        </tr>`
    ).join('');
}

// 获取字段显示名称
function getFieldDisplayName(field) {
    const fieldNames = {
        'LOT_ID': '批次号',
        'DEVICE': '产品型号', 
        'STAGE': '工序',
        'WIP_STATE': 'WIP状态',
        'PROC_STATE': '工艺状态',
        'LOT_QTY': '批次数量',
        'GOOD_QTY': '良品数量',
        'NG_QTY': '不良数量',
        'PRIORITY_LEVEL': '优先级',
        'PLAN_DUE_DATE': '计划交期',
        'created_at': '创建时间'
    };
    return fieldNames[field] || field;
}

// 格式化字段值
function formatFieldValue(value, field) {
    if (value === null || value === undefined) {
        return '-';
    }
    
    if (['PLAN_DUE_DATE', 'created_at'].includes(field)) {
        return new Date(value).toLocaleString('zh-CN');
    }
    
    return value;
}

// 显示加载状态
function showLoading(show) {
    const overlay = document.getElementById('loadingOverlay');
    if (overlay) {
        overlay.style.display = show ? 'flex' : 'none';
    }
}

// 显示错误信息
function showError(message) {
    console.error(message);
    // 可以添加更好的错误显示UI
    alert('错误: ' + message);
}

// 更新记录数量
function updateRecordCount(count) {
    const countElement = document.getElementById('recordCount');
    if (countElement) {
        countElement.textContent = `${count} 条记录`;
    }
}

// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    loadWipData();
    
    // 添加刷新按钮事件
    const refreshBtn = document.getElementById('refreshBtn');
    if (refreshBtn) {
        refreshBtn.addEventListener('click', loadWipData);
    }
});
</script>
{% endblock %} 