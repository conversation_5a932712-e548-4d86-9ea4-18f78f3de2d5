#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
菜单渲染工具 - 实现高性能的服务端菜单渲染
"""

from flask import current_app
from app.config.menu_config import MENU_CONFIG, MENU_ID_MAP
from markupsafe import Markup


class MenuRenderer:
    """菜单渲染器 - 负责将菜单配置转换为HTML"""
    
    def __init__(self, user):
        self.user = user
        self.user_permissions = set(user.get_permissions()) if user.is_authenticated else set()
        self.is_admin = user.role == 'admin' if user.is_authenticated else False
    
    def render_menu(self):
        """渲染完整菜单HTML"""
        if not self.user.is_authenticated:
            return Markup("")
        
        menu_items = []
        for menu_config in MENU_CONFIG:
            menu_html = self._render_menu_item(menu_config, is_top_level=True)
            if menu_html:
                menu_items.append(menu_html)
        
        return Markup('\n'.join(menu_items))
    
    def _render_menu_item(self, menu_config, is_top_level=False):
        """渲染单个菜单项"""
        menu_id = MENU_ID_MAP.get(menu_config['code'])
        
        # 权限检查
        if not self._has_permission(menu_id):
            return None
        
        # 处理子菜单
        children_html = []
        if menu_config.get('children'):
            for child_config in menu_config['children']:
                child_html = self._render_menu_item(child_config, is_top_level=False)
                if child_html:
                    children_html.append(child_html)
        
        # 如果没有子菜单且没有路由，不显示
        if not children_html and not menu_config.get('route'):
            return None
        
        # 构建菜单项HTML
        return self._build_menu_html(menu_config, children_html, is_top_level)
    
    def _has_permission(self, menu_id):
        """检查用户是否有权限访问菜单"""
        if not menu_id:
            return False
        return self.is_admin or menu_id in self.user_permissions
    
    def _build_menu_html(self, menu_config, children_html, is_top_level):
        """构建菜单HTML结构"""
        menu_id = MENU_ID_MAP.get(menu_config['code'])
        route = menu_config.get('route')
        icon = menu_config.get('icon', '')
        name = menu_config['name']
        has_children = len(children_html) > 0
        
        # 构建链接类名
        link_classes = ['nav-link']
        if has_children:
            link_classes.append('has-children')
        
        # 构建图标HTML
        icon_html = f'<i class="{icon}" style="margin-right: 8px;"></i>' if icon else ''
        
        # 构建箭头HTML
        arrow_html = '<i class="fas fa-chevron-right arrow"></i>' if has_children else ''
        
        # 处理链接：如果有子菜单且没有路由，使用JavaScript阻止默认行为
        if has_children and not route:
            # 父级菜单：只展开/收起子菜单，不跳转
            link_html = f'''
        <a href="javascript:void(0)" 
           class="{' '.join(link_classes)}" 
           data-menu-id="{menu_id}"
           data-is-top-level="{str(is_top_level).lower()}"
           onclick="toggleSubmenu(this, event)">
            <span class="d-flex align-items-center">
                {icon_html}{name}
            </span>
            {arrow_html}
        </a>'''
        else:
            # 叶子菜单：正常跳转
            link_html = f'''
        <a href="{route or '#'}" 
           class="{' '.join(link_classes)}" 
           data-menu-id="{menu_id}"
           data-is-top-level="{str(is_top_level).lower()}">
            <span class="d-flex align-items-center">
                {icon_html}{name}
            </span>
            {arrow_html}
        </a>'''
        
        # 构建子菜单HTML
        submenu_html = ''
        if children_html:
            submenu_content = '\n'.join(children_html)
            submenu_html = f'''
        <div class="submenu">
            {submenu_content}
        </div>'''
        
        # 返回完整的菜单项HTML
        return f'''
    <div class="nav-item" data-menu-id="{menu_id}">
        {link_html}
        {submenu_html}
    </div>'''


def get_user_menu_html(user):
    """获取用户菜单HTML - 供模板使用"""
    renderer = MenuRenderer(user)
    return renderer.render_menu()


def get_user_menu_data(user):
    """获取用户菜单数据 - 供API使用"""
    if not user.is_authenticated:
        return []
    
    user_permissions = set(user.get_permissions())
    is_admin = user.role == 'admin'
    
    def build_menu_data(menu_config):
        menu_id = MENU_ID_MAP.get(menu_config['code'])
        
        # 权限检查
        if not menu_id or (not is_admin and menu_id not in user_permissions):
            return None
        
        # 处理子菜单
        children = []
        if menu_config.get('children'):
            for child_config in menu_config['children']:
                child_data = build_menu_data(child_config)
                if child_data:
                    children.append(child_data)
        
        # 如果没有子菜单且没有路由，不显示
        if not children and not menu_config.get('route'):
            return None
        
        return {
            'id': menu_id,
            'code': menu_config['code'],
            'name': menu_config['name'],
            'icon': menu_config.get('icon', ''),
            'route': menu_config.get('route'),
            'order': menu_config.get('order', 0),
            'children': children
        }
    
    menu_data = []
    for menu_config in MENU_CONFIG:
        menu_item = build_menu_data(menu_config)
        if menu_item:
            menu_data.append(menu_item)
    
    return menu_data


class MenuBuilder:
    """菜单构建器 - 提供便捷的菜单创建和管理功能"""
    
    def __init__(self):
        self.menu_items = []
        self.current_id = max(MENU_ID_MAP.values()) + 1 if MENU_ID_MAP else 1
    
    def add_main_menu(self, code, name, icon=None, route=None, order=1):
        """添加主菜单"""
        menu_item = {
            'code': code,
            'name': name,
            'icon': icon or 'fas fa-folder',
            'route': route,
            'order': order,
            'children': []
        }
        self.menu_items.append(menu_item)
        return self
    
    def add_sub_menu(self, parent_code, code, name, route, icon=None, order=1):
        """添加子菜单"""
        parent_menu = self._find_menu_by_code(parent_code)
        if not parent_menu:
            raise ValueError(f"Parent menu '{parent_code}' not found")
        
        sub_menu = {
            'code': code,
            'name': name,
            'icon': icon or 'fas fa-file',
            'route': route,
            'order': order
        }
        
        parent_menu['children'].append(sub_menu)
        return self
    
    def add_nested_menu(self, parent_code, code, name, icon=None, order=1):
        """添加嵌套菜单（可以包含子菜单的菜单）"""
        parent_menu = self._find_menu_by_code(parent_code)
        if not parent_menu:
            raise ValueError(f"Parent menu '{parent_code}' not found")
        
        nested_menu = {
            'code': code,
            'name': name,
            'icon': icon or 'fas fa-folder-open',
            'route': None,
            'order': order,
            'children': []
        }
        
        parent_menu['children'].append(nested_menu)
        return self
    
    def _find_menu_by_code(self, code):
        """根据代码查找菜单项"""
        def search_menu(items):
            for item in items:
                if item['code'] == code:
                    return item
                if item.get('children'):
                    result = search_menu(item['children'])
                    if result:
                        return result
            return None
        
        return search_menu(self.menu_items)
    
    def generate_config(self):
        """生成菜单配置代码"""
        def format_menu_item(item, indent=0):
            spaces = '    ' * indent
            children_str = ''
            
            if item.get('children'):
                children_list = []
                for child in sorted(item['children'], key=lambda x: x.get('order', 0)):
                    children_list.append(format_menu_item(child, indent + 3))
                children_str = f",\n{spaces}    'children': [\n" + ',\n'.join(children_list) + f"\n{spaces}    ]"
            
            return f"""{spaces}{{
{spaces}    'code': '{item['code']}',
{spaces}    'name': '{item['name']}',
{spaces}    'icon': '{item.get('icon', '')}',
{spaces}    'route': {repr(item.get('route'))},
{spaces}    'order': {item.get('order', 1)}{children_str}
{spaces}}}"""
        
        menu_list = []
        for item in sorted(self.menu_items, key=lambda x: x.get('order', 0)):
            menu_list.append(format_menu_item(item, 1))
        
        return f"MENU_CONFIG = [\n" + ',\n'.join(menu_list) + "\n]"
    
    def generate_id_map(self):
        """生成菜单ID映射代码"""
        id_map = {}
        current_id = 1
        
        def assign_ids(items):
            nonlocal current_id
            for item in items:
                id_map[item['code']] = current_id
                current_id += 1
                if item.get('children'):
                    assign_ids(item['children'])
        
        assign_ids(self.menu_items)
        
        lines = ["MENU_ID_MAP = {"]
        for code, menu_id in id_map.items():
            lines.append(f"    '{code}': {menu_id},")
        lines.append("}")
        
        return '\n'.join(lines)


# 使用示例和便捷函数
def create_sample_menu():
    """创建示例菜单 - 展示如何便捷地创建菜单"""
    builder = MenuBuilder()
    
    # 创建生产管理菜单
    builder.add_main_menu('production', '生产管理', 'fas fa-industry', order=1)
    builder.add_sub_menu('production', 'production_schedule', '生产排产', '/production/schedule', 'fas fa-calendar-alt')
    builder.add_sub_menu('production', 'production_monitor', '生产监控', '/production/monitor', 'fas fa-eye')
    
    # 创建系统管理菜单
    builder.add_main_menu('system', '系统管理', 'fas fa-cogs', order=2)
    builder.add_sub_menu('system', 'system_users', '用户管理', '/system/users', 'fas fa-users')
    builder.add_sub_menu('system', 'system_settings', '系统设置', '/system/settings', 'fas fa-cog')
    
    return builder


def quick_add_menu(parent_code, code, name, route, icon='fas fa-file'):
    """快速添加菜单项到现有配置"""
    # 这个函数可以用于运行时动态添加菜单
    # 实际使用时需要结合配置文件更新机制
    pass 