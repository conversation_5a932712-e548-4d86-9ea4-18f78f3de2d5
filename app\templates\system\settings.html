{% extends 'base.html' %}

{% block title %}AI助手设置 - AEC-FT ICP{% endblock %}

{% block page_title %}AI助手设置{% endblock %}

{% block extra_css %}
<style>
    :root {
        --primary-color: #b72424;
        --secondary-color: #d73027;
        --success-color: #28a745;
        --warning-color: #ffc107;
        --danger-color: #dc3545;
        --info-color: #17a2b8;
    }

    .settings-card {
        margin-bottom: 1.5rem;
        transition: all 0.3s ease;
        border: 1px solid #e9ecef;
    }
    
    .settings-card:hover {
        box-shadow: 0 6px 20px rgba(0,0,0,0.1);
        transform: translateY(-2px);
    }
    
    .settings-header {
        margin-bottom: 1.5rem;
        border-bottom: 2px solid var(--primary-color);
        padding-bottom: 1rem;
        color: var(--primary-color);
    }
    
    .settings-section {
        margin-bottom: 2rem;
    }
    
    .form-switch {
        display: flex;
        align-items: center;
    }
    
    .form-switch .form-check-input {
        margin-right: 10px;
        width: 3em;
        height: 1.5em;
    }
    
    .setting-description {
        color: #6c757d;
        margin-top: 0.5rem;
        font-size: 0.9rem;
    }
    
    .prompt-editor {
        min-height: 200px;
        font-family: 'Courier New', monospace;
        font-size: 0.9rem;
        border: 2px solid #e9ecef;
        border-radius: 8px;
        transition: border-color 0.3s ease;
    }
    
    .prompt-editor:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 0.2rem rgba(183, 36, 36, 0.25);
    }
    
    .nav-tabs .nav-link {
        border: none;
        color: #495057;
        padding: 0.75rem 1.5rem;
        margin-right: 0.5rem;
        border-radius: 8px 8px 0 0;
        transition: all 0.3s ease;
    }
    
    .nav-tabs .nav-link:hover {
        background-color: rgba(183, 36, 36, 0.1);
        color: var(--primary-color);
    }
    
    .nav-tabs .nav-link.active {
        color: white;
        background-color: var(--primary-color);
        border-bottom: 2px solid var(--primary-color);
    }
    
    .status-badge {
        display: inline-block;
        padding: 0.35em 0.8em;
        font-size: 0.75em;
        font-weight: 700;
        line-height: 1;
        text-align: center;
        white-space: nowrap;
        vertical-align: baseline;
        border-radius: 0.5rem;
        margin-left: 0.5rem;
        transition: all 0.3s ease;
    }

    .status-badge:hover {
        transform: scale(1.05);
    }

    .btn {
        transition: all 0.3s ease;
        border-radius: 6px;
    }

    .btn:hover {
        transform: translateY(-2px);
    }

    .btn-primary {
        background-color: var(--primary-color);
        border-color: var(--primary-color);
    }

    .btn-primary:hover {
        background-color: var(--secondary-color);
        border-color: var(--secondary-color);
        box-shadow: 0 4px 12px rgba(183, 36, 36, 0.3);
    }

    .card-header {
        background-color: var(--primary-color);
        color: white;
        border-radius: 8px 8px 0 0;
    }

    .form-control:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 0.2rem rgba(183, 36, 36, 0.25);
    }

    .alert {
        border-radius: 8px;
        border: none;
    }

    .table {
        border-radius: 8px;
        overflow: hidden;
    }

    .spinner-border-sm {
        width: 1rem;
        height: 1rem;
    }

    /* 自定义滚动条 */
    .task-logs {
        max-height: 300px;
        overflow-y: auto;
        font-family: 'Courier New', monospace;
        font-size: 0.9rem;
        background-color: #f8f9fa;
        padding: 1rem;
        border-radius: 8px;
        border: 1px solid #e9ecef;
    }

    .task-logs::-webkit-scrollbar {
        width: 8px;
    }

    .task-logs::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 4px;
    }

    .task-logs::-webkit-scrollbar-thumb {
        background: var(--primary-color);
        border-radius: 4px;
    }

    .task-logs::-webkit-scrollbar-thumb:hover {
        background: var(--secondary-color);
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
        .nav-tabs .nav-link {
            padding: 0.5rem 1rem;
            font-size: 0.9rem;
        }
        
        .settings-header {
            font-size: 1.5rem;
        }
        
        .prompt-editor {
            min-height: 150px;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card settings-card">
                <div class="card-body">
                    <h4 class="settings-header">
                        <i class="fas fa-robot me-2"></i>AI助手设置
                    </h4>
                    
                    <!-- 设置导航标签 -->
                    <ul class="nav nav-tabs mb-4" id="settingsTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="ai-config-tab" data-bs-toggle="tab" data-bs-target="#ai-config" 
                                    type="button" role="tab" aria-controls="ai-config" aria-selected="true">
                                <i class="fas fa-brain me-1"></i>AI模型配置
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="chatbot-tab" data-bs-toggle="tab" data-bs-target="#chatbot" 
                                    type="button" role="tab" aria-controls="chatbot" aria-selected="false">
                                <i class="fas fa-comment-dots me-1"></i>Dify聊天机器人
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="scheduler-tab" data-bs-toggle="tab" data-bs-target="#scheduler" 
                                    type="button" role="tab" aria-controls="scheduler" aria-selected="false">
                                <i class="fas fa-clock me-1"></i>定时任务管理
                            </button>
                        </li>
                    </ul>
                    
                    <!-- 设置内容 -->
                    <div class="tab-content" id="settingsTabContent">
                        <!-- AI模型配置 -->
                        <div class="tab-pane fade show active" id="ai-config" role="tabpanel" aria-labelledby="ai-config-tab">
                            <div class="settings-section">
                                <h5><i class="fas fa-cogs me-2"></i>AI助手核心配置</h5>
                                <hr>
                                
                                <form id="aiConfigForm">
                                    <!-- AI功能开关 -->
                                    <div class="mb-4">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="enableAI" name="ai_enabled">
                                            <label class="form-check-label" for="enableAI">启用AI助手功能</label>
                                            <span class="status-badge" id="aiStatus">检查中...</span>
                                        </div>
                                        <p class="setting-description">控制AI助手的全局开关，关闭后AI相关功能将不可用。</p>
                                    </div>

                                    <!-- 数据库集成说明 -->
                                    <div class="mb-4">
                                        <div class="alert alert-info">
                                            <i class="fas fa-info-circle me-2"></i>
                                            <strong>数据库配置已迁移</strong>：数据库查询和集成功能已迁移到
                                            <a href="/system/database-config" class="alert-link">数据库配置管理</a> 页面，
                                            请前往该页面的"AI集成"标签进行配置。
                                        </div>
                                    </div>

                                    <!-- 智能推荐功能 -->
                                    <div class="mb-4">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="enableSmartRecommendation" name="smart_recommendation_enabled">
                                            <label class="form-check-label" for="enableSmartRecommendation">启用智能推荐</label>
                                        </div>
                                        <p class="setting-description">基于历史数据和当前状态，提供排产和资源分配建议。</p>
                                    </div>
                                    
                                    <!-- 模型参数设置 -->
                                    <div class="mb-4">
                                        <h6 class="mb-3"><i class="fas fa-sliders-h me-2"></i>模型参数</h6>
                                        
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="temperature" class="form-label">创造性温度 (Temperature)</label>
                                                    <div class="d-flex align-items-center">
                                                        <input type="range" class="form-range flex-grow-1 me-2" id="temperature" 
                                                               name="temperature" min="0" max="1" step="0.1" value="0.3">
                                                        <span id="temperatureValue" class="badge bg-primary" style="width: 50px; text-align: center;">0.3</span>
                                                    </div>
                                                    <div class="form-text">控制AI回复的创造性，值越低回复越确定性，建议0.1-0.5。</div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="maxTokens" class="form-label">最大生成长度</label>
                                                    <input type="number" class="form-control" id="maxTokens" name="max_tokens" 
                                                           min="100" max="4000" step="100" value="1000">
                                                    <div class="form-text">控制AI回复的最大长度，建议800-2000。</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- 系统提示词设置 -->
                                    <div class="mb-4">
                                        <h6 class="mb-3"><i class="fas fa-file-text me-2"></i>系统提示词</h6>
                                        
                                        <div class="mb-3">
                                            <label for="systemPrompt" class="form-label">AI助手系统提示词</label>
                                            <textarea class="form-control prompt-editor" id="systemPrompt" name="system_prompt" rows="8"
                                                      placeholder="定义AI助手的角色、行为和回答风格..."></textarea>
                                            <div class="form-text">定义AI助手在车规芯片终测调度平台中的专业角色和回答风格。</div>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <button type="button" class="btn btn-outline-secondary" id="resetPromptBtn">
                                                <i class="fas fa-undo me-1"></i>恢复默认提示词
                                            </button>
                                            <button type="button" class="btn btn-outline-info ms-2" id="previewPromptBtn">
                                                <i class="fas fa-eye me-1"></i>预览效果
                                            </button>
                                        </div>
                                    </div>
                                    
                                    <!-- API配置状态 -->
                                    <div class="mb-4">
                                        <h6 class="mb-3"><i class="fas fa-plug me-2"></i>API连接状态</h6>
                                        <div class="alert alert-info" id="apiStatus">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <span>
                                                    <i class="fas fa-info-circle me-2"></i>API连接状态
                                                </span>
                                                <button type="button" class="btn btn-sm btn-outline-info" id="checkApiStatusBtn">
                                                    <i class="fas fa-sync-alt me-1"></i>检查连接
                                                </button>
                                            </div>
                                            <div class="mt-2" id="apiStatusDetails">
                                                正在检查API连接状态...
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="d-flex gap-2">
                                        <button type="button" class="btn btn-primary" id="saveAIConfigBtn">
                                            <i class="fas fa-save me-1"></i>保存AI配置
                                        </button>
                                        <button type="button" class="btn btn-outline-secondary" id="testAIBtn">
                                            <i class="fas fa-flask me-1"></i>测试AI功能
                                        </button>
                                        <button type="button" class="btn btn-outline-info" id="resetAIConfigBtn">
                                            <i class="fas fa-undo me-1"></i>重置为默认
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                        
                        <!-- Dify聊天机器人设置 -->
                        <div class="tab-pane fade" id="chatbot" role="tabpanel" aria-labelledby="chatbot-tab">
                            <div class="settings-section">
                                <h5><i class="fas fa-comments me-2"></i>Dify聊天机器人配置</h5>
                                <hr>
                                
                                <form id="chatbotSettingsForm">
                                    <div class="mb-4">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="enableChatbot" name="enable_chatbot" 
                                                   data-bs-toggle="collapse" data-bs-target="#chatbotSettings">
                                            <label class="form-check-label" for="enableChatbot">启用页面聊天机器人</label>
                                            <span class="status-badge" id="chatbotStatus">未启用</span>
                                        </div>
                                        <p class="setting-description">在页面右下角显示聊天机器人图标，用户可以随时咨询问题。</p>
                                    </div>
                                    
                                    <div class="collapse" id="chatbotSettings">
                                        <div class="card">
                                            <div class="card-body">
                                                <div class="row">
                                                    <div class="col-md-6">
                                                        <div class="mb-3">
                                                            <label for="chatbotToken" class="form-label">
                                                                <i class="fas fa-key me-1"></i>机器人Token
                                                            </label>
                                                            <input type="text" class="form-control" id="chatbotToken" name="chatbot_token" 
                                                                   value="uV72gGRdNz0eP7ac" placeholder="输入Dify机器人Token">
                                                            <div class="form-text">从Dify平台获取的聊天机器人唯一标识符。</div>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <div class="mb-3">
                                                            <label for="chatbotServer" class="form-label">
                                                                <i class="fas fa-server me-1"></i>服务器地址
                                                            </label>
                                                            <input type="text" class="form-control" id="chatbotServer" name="chatbot_server" 
                                                                   value="http://************" placeholder="http://your-dify-server.com">
                                                            <div class="form-text">Dify服务器的完整URL地址。</div>
                                                        </div>
                                                    </div>
                                                </div>
                                                
                                                <div class="row">
                                                    <div class="col-md-6">
                                                        <div class="mb-3">
                                                            <label for="chatbotColor" class="form-label">
                                                                <i class="fas fa-palette me-1"></i>按钮颜色
                                                            </label>
                                                            <input type="color" class="form-control form-control-color" id="chatbotColor" 
                                                                   name="chatbot_color" value="#b72424">
                                                            <div class="form-text">聊天机器人按钮的主题颜色。</div>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <div class="mb-3">
                                                            <label class="form-label">
                                                                <i class="fas fa-code me-1"></i>集成方式
                                                            </label>
                                                            <div class="form-check">
                                                                <input class="form-check-input" type="radio" name="integration_type" 
                                                                       id="integrationScript" value="script" checked>
                                                                <label class="form-check-label" for="integrationScript">
                                                                    脚本集成（推荐）
                                                                </label>
                                                            </div>
                                                            <div class="form-check">
                                                                <input class="form-check-input" type="radio" name="integration_type" 
                                                                       id="integrationIframe" value="iframe">
                                                                <label class="form-check-label" for="integrationIframe">
                                                                    IFrame集成
                                                                </label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- 聊天机器人预览 -->
                                                <div class="mb-3">
                                                    <h6><i class="fas fa-eye me-2"></i>预览效果</h6>
                                                    <div class="alert alert-light border" style="min-height: 100px; position: relative;">
                                                        <div class="text-muted text-center">
                                                            <i class="fas fa-comment-dots fa-2x mb-2"></i>
                                                            <p>聊天机器人将显示在页面右下角</p>
                                                        </div>
                                                        <div id="chatbotPreview" style="position: absolute; bottom: 10px; right: 10px;">
                                                            <button type="button" class="btn btn-sm rounded-circle" 
                                                                    style="width: 50px; height: 50px; background-color: #b72424; color: white;">
                                                                <i class="fas fa-comments"></i>
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="mt-3">
                                        <button type="button" class="btn btn-primary" id="saveChatbotBtn">
                                            <i class="fas fa-save me-1"></i>保存聊天机器人设置
                                        </button>
                                        <button type="button" class="btn btn-outline-info ms-2" id="testChatbotBtn">
                                            <i class="fas fa-flask me-1"></i>测试连接
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                        
                        <!-- 定时任务管理 -->
                        <div class="tab-pane fade" id="scheduler" role="tabpanel" aria-labelledby="scheduler-tab">
                            <div class="settings-section">
                                <h5><i class="fas fa-tasks me-2"></i>全局定时任务管理</h5>
                                <hr>
                                
                                <!-- 全局控制 -->
                                <div class="mb-4">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="enableGlobalScheduler" name="global_scheduler_enabled">
                                        <label class="form-check-label" for="enableGlobalScheduler">启用全局定时任务</label>
                                        <span class="status-badge" id="globalSchedulerStatus">检查中...</span>
                                    </div>
                                    <p class="setting-description">控制系统中所有定时任务的全局开关。关闭后，所有定时任务将停止运行。</p>
                                </div>

                                <!-- 任务状态概览 -->
                                <div class="mb-4">
                                    <h6><i class="fas fa-list me-2"></i>定时任务状态概览</h6>
                                    <div class="table-responsive">
                                        <table class="table table-striped table-hover">
                                            <thead class="table-dark">
                                                <tr>
                                                    <th><i class="fas fa-tag me-1"></i>任务类型</th>
                                                    <th><i class="fas fa-circle me-1"></i>状态</th>
                                                    <th><i class="fas fa-list-ol me-1"></i>配置数量</th>
                                                    <th><i class="fas fa-clock me-1"></i>下次运行</th>
                                                    <th><i class="fas fa-tools me-1"></i>操作</th>
                                                </tr>
                                            </thead>
                                            <tbody id="schedulerTaskList">
                                                <tr>
                                                    <td colspan="5" class="text-center">
                                                        <div class="spinner-border spinner-border-sm me-2" role="status"></div>
                                                        正在加载任务状态...
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>

                                <!-- 操作按钮 -->
                                <div class="d-flex gap-2 mb-4">
                                    <button type="button" class="btn btn-success" id="saveSchedulerSettingsBtn">
                                        <i class="fas fa-save me-1"></i>保存设置
                                    </button>
                                    <button type="button" class="btn btn-info" id="refreshSchedulerStatusBtn">
                                        <i class="fas fa-sync me-1"></i>刷新状态
                                    </button>
                                    <button type="button" class="btn btn-warning" id="restartAllSchedulersBtn">
                                        <i class="fas fa-redo me-1"></i>重启所有任务
                                    </button>
                                </div>

                                <!-- 任务日志查看 -->
                                <div class="mb-4">
                                    <h6><i class="fas fa-file-alt me-2"></i>定时任务日志</h6>
                                    <div class="card">
                                        <div class="card-body p-0">
                                            <div id="schedulerLogs" class="task-logs">
                                                <div class="text-center text-muted">
                                                    <i class="fas fa-spinner fa-spin me-2"></i>正在加载日志...
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 成功提示Toast -->
<div class="position-fixed bottom-0 end-0 p-3" style="z-index: 1055">
    <div id="successToast" class="toast" role="alert" aria-live="assertive" aria-atomic="true" data-bs-delay="3000">
        <div class="toast-header bg-success text-white">
            <i class="fas fa-check-circle me-2"></i>
            <strong class="me-auto">成功</strong>
            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast"></button>
        </div>
        <div class="toast-body" id="successToastBody">
            设置已成功保存！
        </div>
    </div>
</div>

<!-- AI测试结果模态框 -->
<div class="modal fade" id="aiTestModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title">
                    <i class="fas fa-flask me-2"></i>AI功能测试
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="aiTestResult">
                    <div class="text-center p-4">
                        <div class="spinner-border text-primary" role="status"></div>
                        <p class="mt-2">正在测试AI功能，请稍候...</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // ===============================
    // 通用工具函数
    // ===============================
    
    // 显示成功提示
    function showSuccessToast(message = '操作成功完成！') {
        document.getElementById('successToastBody').textContent = message;
        const toast = new bootstrap.Toast(document.getElementById('successToast'));
        toast.show();
    }
    
    // 显示错误提示
    function showErrorAlert(message, title = '错误') {
        alert(`${title}: ${message}`);
    }
    
    // 格式化日期时间
    function formatDateTime(dateString) {
        if (!dateString) return '未知';
        const date = new Date(dateString);
        return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
    }
    
    // 更新状态徽章
    function updateStatusBadge(elementId, status, text) {
        const element = document.getElementById(elementId);
        if (!element) return;
        
        element.className = 'status-badge';
        switch (status) {
            case 'success':
                element.classList.add('bg-success', 'text-white');
                break;
            case 'warning':
                element.classList.add('bg-warning', 'text-dark');
                break;
            case 'error':
                element.classList.add('bg-danger', 'text-white');
                break;
            case 'info':
                element.classList.add('bg-info', 'text-white');
                break;
            default:
                element.classList.add('bg-secondary', 'text-white');
        }
        element.textContent = text;
    }
    
    // ===============================
    // AI模型配置功能
    // ===============================
    
    // 默认系统提示词
    const defaultSystemPrompt = `你是车规芯片终测智能调度平台(AEC-FT ICP)的专业AI助手，专注于帮助用户解决生产排程、订单管理、资源分配等方面的问题。

**你的核心能力：**
1. 基于MySQL数据库的实时数据查询和分析
2. 生产排程优化建议和算法推荐
3. 设备资源配置和UPH分析
4. 订单状态跟踪和进度预测
5. WIP在制品管理和异常识别

**回答原则：**
- 仅基于数据库中的真实数据回答问题，不编造信息
- 如果数据不足，明确告知用户并建议数据补充方案
- 使用专业术语但保持表达清晰易懂
- 优先提供可执行的具体建议和操作步骤
- 对于数值和状态信息，尽可能使用表格或列表格式展示

**专业领域：**
- 车规级芯片终测工艺流程
- 自动化测试设备调度优化
- 生产计划排程算法
- 质量控制和异常处理
- 设备效率和产能分析`;

    // 获取UI元素
    const temperatureSlider = document.getElementById('temperature');
    const temperatureValue = document.getElementById('temperatureValue');
    const systemPromptTextarea = document.getElementById('systemPrompt');
    const saveAIConfigBtn = document.getElementById('saveAIConfigBtn');
    const testAIBtn = document.getElementById('testAIBtn');
    const resetAIConfigBtn = document.getElementById('resetAIConfigBtn');
    const resetPromptBtn = document.getElementById('resetPromptBtn');
    const checkApiStatusBtn = document.getElementById('checkApiStatusBtn');
    
    // 温度滑块值更新
    if (temperatureSlider && temperatureValue) {
        temperatureSlider.addEventListener('input', function() {
            temperatureValue.textContent = this.value;
        });
    }
    
    // 加载AI配置
    function loadAIConfig() {
        fetch('/api/ai-settings')
            .then(response => response.json())
            .then(data => {
                if (data.database) {
                    const dbConfig = data.database;
                    
                    // 更新AI功能开关
                    document.getElementById('enableAI').checked = dbConfig.enabled || false;
                    updateStatusBadge('aiStatus', dbConfig.enabled ? 'success' : 'warning', 
                                    dbConfig.enabled ? '已启用' : '已禁用');
                    
                                    // 注意：数据库查询功能已迁移到数据库配置页面
                    
                    // 智能推荐功能
                    document.getElementById('enableSmartRecommendation').checked = dbConfig.prioritize_database || false;
                    
                    // 模型参数
                    if (dbConfig.model) {
                        if (dbConfig.model.temperature !== undefined && temperatureSlider) {
                            temperatureSlider.value = dbConfig.model.temperature;
                            temperatureValue.textContent = dbConfig.model.temperature;
                        }
                        
                        if (dbConfig.model.max_tokens !== undefined) {
                            document.getElementById('maxTokens').value = dbConfig.model.max_tokens;
                        }
                    }
                    
                    // 系统提示词
                    if (dbConfig.system_prompt && systemPromptTextarea) {
                        systemPromptTextarea.value = dbConfig.system_prompt;
                    } else if (systemPromptTextarea) {
                        systemPromptTextarea.value = defaultSystemPrompt;
                    }
                } else {
                    console.warn('加载AI配置时数据格式异常:', data);
                    // 使用默认值
                    document.getElementById('enableAI').checked = true;
                    // 数据库查询功能已迁移到数据库配置页面
                    document.getElementById('enableSmartRecommendation').checked = true;
                    if (temperatureSlider) {
                        temperatureSlider.value = 0.3;
                        temperatureValue.textContent = 0.3;
                    }
                    document.getElementById('maxTokens').value = 1000;
                    if (systemPromptTextarea) {
                        systemPromptTextarea.value = defaultSystemPrompt;
                    }
                    updateStatusBadge('aiStatus', 'success', '已启用');
                }
            })
            .catch(error => {
                console.error('加载AI配置失败:', error);
                updateStatusBadge('aiStatus', 'error', '加载失败');
            });
    }
    
    // 保存AI配置
    function saveAIConfig() {
        const aiEnabled = document.getElementById('enableAI').checked;
        const smartRecommendationEnabled = document.getElementById('enableSmartRecommendation').checked;
        
        const config = {
            database: {
                enabled: aiEnabled,
                auto_db_path: "instance/aps.db",
                prioritize_database: smartRecommendationEnabled,
                type: "mysql", // 数据库类型现在在数据库配置页面管理
                model: {
                    temperature: parseFloat(temperatureSlider?.value || 0.3),
                    max_tokens: parseInt(document.getElementById('maxTokens').value || 1000)
                },
                system_prompt: systemPromptTextarea?.value || defaultSystemPrompt
            }
        };
        
        fetch('/api/ai-settings', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(config)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showSuccessToast('AI配置已保存成功！');
                updateStatusBadge('aiStatus', aiEnabled ? 'success' : 'warning', 
                                aiEnabled ? '已启用' : '已禁用');
            } else {
                showErrorAlert(data.message || '保存失败');
            }
        })
        .catch(error => {
            console.error('保存AI配置失败:', error);
            showErrorAlert('保存失败: ' + error.message);
        });
    }
    
    // 测试AI功能
    function testAIFunction() {
        const modal = new bootstrap.Modal(document.getElementById('aiTestModal'));
        const resultDiv = document.getElementById('aiTestResult');
        
        modal.show();
        
        // 显示加载状态
        resultDiv.innerHTML = `
            <div class="text-center p-4">
                <div class="spinner-border text-primary" role="status"></div>
                <p class="mt-2">正在测试AI功能，请稍候...</p>
            </div>
        `;
        
        // 发送测试请求
        fetch('/api/ai-test', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                type: 'database' // 默认使用数据库模式
            })
        })
        .then(response => response.json())
        .then(data => {
            let resultHTML = '';
            
            if (data.success && data.test_result) {
                const result = data.test_result;
                resultHTML = `
                    <div class="alert alert-success">
                        <h6><i class="fas fa-check-circle me-2"></i>AI功能测试成功！</h6>
                    </div>
                    <div class="mb-3">
                        <h6>测试结果:</h6>
                        <div class="p-3 bg-light rounded border">
                            ${result.message || '测试完成'}
                        </div>
                    </div>
                `;
                
                if (result.details) {
                    resultHTML += `
                        <div class="mb-3">
                            <h6>详细信息:</h6>
                            <ul class="list-group">
                                <li class="list-group-item d-flex justify-content-between">
                                    <span>模型版本:</span>
                                    <span class="badge bg-info">${result.details.model || '未知'}</span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between">
                                    <span>系统版本:</span>
                                    <span class="badge bg-secondary">${result.details.version || 'v2.0'}</span>
                                </li>
                                ${result.details.database ? `
                                    <li class="list-group-item d-flex justify-content-between">
                                        <span>数据库:</span>
                                        <span class="badge bg-success">${result.details.database}</span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between">
                                        <span>数据量:</span>
                                        <span class="badge bg-primary">${result.details.table_count || 0} 条记录</span>
                                    </li>
                                ` : ''}
                                <li class="list-group-item d-flex justify-content-between">
                                    <span>测试时间:</span>
                                    <span class="badge bg-muted">${new Date(result.details.timestamp).toLocaleString()}</span>
                                </li>
                            </ul>
                        </div>
                    `;
                }
            } else {
                resultHTML = `
                    <div class="alert alert-danger">
                        <h6><i class="fas fa-exclamation-triangle me-2"></i>AI功能测试失败</h6>
                        <p class="mb-0">${data.error || '未知错误'}</p>
                    </div>
                `;
                
                if (data.suggestions) {
                    resultHTML += `
                        <div class="alert alert-info">
                            <h6>建议:</h6>
                            <ul class="mb-0">
                                ${data.suggestions.map(s => `<li>${s}</li>`).join('')}
                            </ul>
                        </div>
                    `;
                }
            }
            
            resultDiv.innerHTML = resultHTML;
        })
        .catch(error => {
            console.error('测试AI功能失败:', error);
            resultDiv.innerHTML = `
                <div class="alert alert-danger">
                    <h6><i class="fas fa-exclamation-triangle me-2"></i>测试请求失败</h6>
                    <p class="mb-0">网络错误: ${error.message}</p>
                </div>
            `;
        });
    }
    
    // 重置AI配置
    function resetAIConfig() {
        if (confirm('确定要重置为默认AI配置吗？所有自定义设置将丢失。')) {
            document.getElementById('enableAI').checked = true;
            document.getElementById('enableDatabaseQuery').checked = true;
            document.getElementById('enableSmartRecommendation').checked = false;
            
            if (temperatureSlider && temperatureValue) {
                temperatureSlider.value = 0.3;
                temperatureValue.textContent = '0.3';
            }
            
            document.getElementById('maxTokens').value = 1000;
            
            if (systemPromptTextarea) {
                systemPromptTextarea.value = defaultSystemPrompt;
            }
            
            showSuccessToast('AI配置已重置为默认值');
        }
    }
    
    // 检查API状态
    function checkApiStatus() {
        const statusDiv = document.getElementById('apiStatusDetails');
        statusDiv.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>正在检查API连接...';
        
        fetch('/api/ai-status')
            .then(response => response.json())
            .then(data => {
                let statusHTML = '';
                
                if (data.success) {
                    statusHTML = `
                        <div class="row">
                            <div class="col-md-6">
                                <div class="d-flex justify-content-between">
                                    <span>API状态:</span>
                                    <span class="badge bg-success">正常</span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="d-flex justify-content-between">
                                    <span>模型版本:</span>
                                    <span class="badge bg-info">${data.model_version || '未知'}</span>
                                </div>
                            </div>
                        </div>
                    `;
                    
                    if (data.database_status) {
                        statusHTML += `
                            <div class="row mt-2">
                                <div class="col-12">
                                    <div class="d-flex justify-content-between">
                                        <span>数据库连接:</span>
                                        <span class="badge bg-${data.database_status.connected ? 'success' : 'danger'}">
                                            ${data.database_status.connected ? '已连接' : '未连接'}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        `;
                    }
                } else {
                    statusHTML = `
                        <div class="text-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            ${data.error || 'API连接失败'}
                        </div>
                    `;
                }
                
                statusDiv.innerHTML = statusHTML;
            })
            .catch(error => {
                console.error('检查API状态失败:', error);
                statusDiv.innerHTML = `
                    <div class="text-danger">
                        <i class="fas fa-times me-2"></i>
                        检查失败: ${error.message}
                    </div>
                `;
            });
    }
    
    // 绑定AI配置事件
    if (saveAIConfigBtn) saveAIConfigBtn.addEventListener('click', saveAIConfig);
    if (testAIBtn) testAIBtn.addEventListener('click', testAIFunction);
    if (resetAIConfigBtn) resetAIConfigBtn.addEventListener('click', resetAIConfig);
    if (resetPromptBtn) {
        resetPromptBtn.addEventListener('click', function() {
            if (systemPromptTextarea) {
                systemPromptTextarea.value = defaultSystemPrompt;
                showSuccessToast('系统提示词已重置为默认值');
            }
        });
    }
    if (checkApiStatusBtn) checkApiStatusBtn.addEventListener('click', checkApiStatus);
    
    // ===============================
    // Dify聊天机器人配置功能
    // ===============================
    
    const enableChatbot = document.getElementById('enableChatbot');
    const saveChatbotBtn = document.getElementById('saveChatbotBtn');
    const testChatbotBtn = document.getElementById('testChatbotBtn');
    const chatbotColorInput = document.getElementById('chatbotColor');
    const chatbotPreview = document.getElementById('chatbotPreview');
    
    // 加载聊天机器人设置
    function loadChatbotSettings() {
        // 从服务器加载设置 - 使用新的API端点
        fetch('/api/v2/system/ai-settings')
            .then(response => response.json())
            .then(data => {
                if (data.chatbot) {
                    const settings = data.chatbot;
                    
                    document.getElementById('enableChatbot').checked = settings.enabled || false;
                    document.getElementById('chatbotToken').value = settings.token || 'uV72gGRdNz0eP7ac';
                    document.getElementById('chatbotServer').value = settings.server || 'http://************';
                    document.getElementById('chatbotColor').value = settings.color || '#b72424';
                    
                    const integrationType = settings.integration_type || 'script';
                    const radioBtn = document.querySelector(`input[name="integration_type"][value="${integrationType}"]`);
                    if (radioBtn) radioBtn.checked = true;
                    
                    // 更新状态和预览
                    updateChatbotStatus(settings.enabled);
                    updateChatbotPreview(settings.color);
                    
                    // 如果启用了，展开设置面板
                    if (settings.enabled) {
                        document.getElementById('chatbotSettings').classList.add('show');
                    }
                } else {
                    console.warn('未找到聊天机器人设置，使用默认值');
                    // 使用默认设置
                    document.getElementById('enableChatbot').checked = false;
                    document.getElementById('chatbotToken').value = 'uV72gGRdNz0eP7ac';
                    document.getElementById('chatbotServer').value = 'http://************';
                    document.getElementById('chatbotColor').value = '#b72424';
                    updateChatbotStatus(false);
                    updateChatbotPreview('#b72424');
                }
            })
            .catch(error => {
                console.error('加载聊天机器人设置失败:', error);
                // 降级到本地存储
                const savedSettings = localStorage.getItem('chatbotSettings');
                if (savedSettings) {
                    const settings = JSON.parse(savedSettings);
                    document.getElementById('enableChatbot').checked = settings.enabled || false;
                    document.getElementById('chatbotToken').value = settings.token || '';
                    document.getElementById('chatbotServer').value = settings.server || 'http://************';
                    document.getElementById('chatbotColor').value = settings.color || '#b72424';
                    updateChatbotStatus(settings.enabled);
                    updateChatbotPreview(settings.color);
                }
            });
    }
    
    // 保存聊天机器人设置
    function saveChatbotSettings() {
        const settings = {
            enabled: document.getElementById('enableChatbot').checked,
            token: document.getElementById('chatbotToken').value,
            server: document.getElementById('chatbotServer').value,
            color: document.getElementById('chatbotColor').value,
            integrationType: document.querySelector('input[name="integration_type"]:checked').value
        };
        
        // 保存到本地存储
        localStorage.setItem('chatbotSettings', JSON.stringify(settings));
        
        // 同时保存到服务器 - 使用新的API端点
        fetch('/api/v2/system/ai-settings', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ 
                chatbot: {
                    enabled: settings.enabled,
                    token: settings.token,
                    server: settings.server,
                    color: settings.color,
                    integration_type: settings.integrationType
                }
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showSuccessToast('Dify聊天机器人设置已保存！');
                updateChatbotStatus(settings.enabled);
            } else {
                showErrorAlert(data.message || '保存失败');
            }
        })
        .catch(error => {
            console.error('保存聊天机器人设置失败:', error);
            // 即使服务器保存失败，本地存储仍然有效
            showSuccessToast('聊天机器人设置已保存到本地！');
            updateChatbotStatus(settings.enabled);
        });
    }
    
    // 测试聊天机器人连接
    function testChatbotConnection() {
        const server = document.getElementById('chatbotServer').value;
        const token = document.getElementById('chatbotToken').value;
        
        if (!server || !token) {
            showErrorAlert('请先填写服务器地址和Token');
            return;
        }
        
        // 先保存当前设置，然后测试连接
        const tempSettings = {
            enabled: true,
            token: token,
            server: server,
            color: document.getElementById('chatbotColor').value,
            integration_type: document.querySelector('input[name="integration_type"]:checked').value
        };
        
        // 临时保存设置用于测试
        fetch('/api/v2/system/ai-settings', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ 
                chatbot: tempSettings
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // 设置保存成功，现在测试连接
                return fetch('/api/dify-proxy/test-connection', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' }
                });
            } else {
                throw new Error('保存设置失败: ' + (data.message || '未知错误'));
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                showSuccessToast(`Dify连接测试成功！服务器: ${data.server}`);
            } else {
                showErrorAlert(data.message || '连接测试失败');
            }
        })
        .catch(error => {
            console.error('测试连接失败:', error);
            showErrorAlert('测试连接失败: ' + error.message);
        });
    }
    
    // 更新聊天机器人状态
    function updateChatbotStatus(enabled) {
        updateStatusBadge('chatbotStatus', enabled ? 'success' : 'secondary', 
                         enabled ? '已启用' : '未启用');
    }
    
    // 更新聊天机器人预览
    function updateChatbotPreview(color) {
        if (chatbotPreview) {
            const button = chatbotPreview.querySelector('button');
            if (button) {
                button.style.backgroundColor = color || '#b72424';
            }
        }
    }
    
    // 绑定聊天机器人事件
    if (enableChatbot) {
        enableChatbot.addEventListener('change', function() {
            updateChatbotStatus(this.checked);
        });
    }
    
    if (chatbotColorInput) {
        chatbotColorInput.addEventListener('change', function() {
            updateChatbotPreview(this.value);
        });
    }
    
    if (saveChatbotBtn) saveChatbotBtn.addEventListener('click', saveChatbotSettings);
    if (testChatbotBtn) testChatbotBtn.addEventListener('click', testChatbotConnection);
    
    // ===============================
    // 定时任务管理功能
    // ===============================
    
    const enableGlobalScheduler = document.getElementById('enableGlobalScheduler');
    const saveSchedulerSettingsBtn = document.getElementById('saveSchedulerSettingsBtn');
    const refreshSchedulerStatusBtn = document.getElementById('refreshSchedulerStatusBtn');
    const restartAllSchedulersBtn = document.getElementById('restartAllSchedulersBtn');
    const schedulerTaskList = document.getElementById('schedulerTaskList');
    const schedulerLogs = document.getElementById('schedulerLogs');
    
    // 加载全局定时任务设置
    function loadGlobalSchedulerConfig() {
        fetch('/api/global-scheduler/config')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    enableGlobalScheduler.checked = data.config.enabled;
                    updateGlobalSchedulerStatus();
                    loadSchedulerTaskStatus();
                }
            })
            .catch(error => {
                console.error('加载全局定时任务配置失败:', error);
                updateStatusBadge('globalSchedulerStatus', 'error', '加载失败');
            });
    }
    
    // 更新全局定时任务状态显示
    function updateGlobalSchedulerStatus() {
        const isEnabled = enableGlobalScheduler.checked;
        updateStatusBadge('globalSchedulerStatus', isEnabled ? 'success' : 'warning', 
                         isEnabled ? '已启用' : '已禁用');
    }
    
    // 加载各个定时任务的状态
    function loadSchedulerTaskStatus() {
        schedulerTaskList.innerHTML = '<tr><td colspan="5" class="text-center"><i class="fas fa-spinner fa-spin me-2"></i>加载中...</td></tr>';
        
        Promise.all([
            fetch('/api/sync/status').then(r => r.json()).catch(() => ({ success: false })),
            fetch('/api/v2/system/email-scheduler').then(r => r.json()).catch(() => ({ success: false }))
        ]).then(([syncStatus, emailStatus]) => {
            let html = '';
            
            // 数据同步任务
            const syncEnabled = syncStatus.success && syncStatus.status?.enabled === 'true';
            html += `
                <tr>
                    <td><i class="fas fa-sync-alt me-2 text-primary"></i>数据同步</td>
                    <td><span class="badge ${syncEnabled ? 'bg-success' : 'bg-secondary'}">${
                        syncEnabled ? '运行中' : '已停止'
                    }</span></td>
                    <td>1</td>
                    <td>${syncStatus.success ? (syncStatus.status?.next_sync_time || '未知') : '-'}</td>
                    <td>
                        <button class="btn btn-sm btn-outline-primary" onclick="controlSchedulerTask('sync', '${syncEnabled ? 'stop' : 'start'}')">
                            <i class="fas fa-${syncEnabled ? 'stop' : 'play'} me-1"></i>${syncEnabled ? '停止' : '启动'}
                        </button>
                    </td>
                </tr>
            `;
            
            // 邮件附件任务
            if (emailStatus.success) {
                const emailData = emailStatus.status;
                const isRunning = emailData.running && emailData.enabled;
                html += `
                    <tr>
                        <td><i class="fas fa-envelope me-2 text-info"></i>邮件附件获取</td>
                        <td><span class="badge ${isRunning ? 'bg-success' : 'bg-secondary'}">${
                            isRunning ? '运行中' : '已停止'
                        }</span></td>
                        <td>${emailData.configs_count || 0}</td>
                        <td>${isRunning ? '按配置定时运行' : '-'}</td>
                        <td>
                            <button class="btn btn-sm btn-outline-primary" onclick="controlSchedulerTask('email', '${isRunning ? 'stop' : 'start'}')">
                                <i class="fas fa-${isRunning ? 'stop' : 'play'} me-1"></i>${isRunning ? '停止' : '启动'}
                            </button>
                        </td>
                    </tr>
                `;
            } else {
                html += `
                    <tr>
                        <td><i class="fas fa-envelope me-2 text-warning"></i>邮件附件获取</td>
                        <td><span class="badge bg-warning">错误</span></td>
                        <td>-</td>
                        <td>-</td>
                        <td>
                            <button class="btn btn-sm btn-outline-danger" disabled>
                                <i class="fas fa-exclamation-triangle me-1"></i>错误
                            </button>
                        </td>
                    </tr>
                `;
            }
            
            schedulerTaskList.innerHTML = html;
        }).catch(error => {
            console.error('加载任务状态失败:', error);
            schedulerTaskList.innerHTML = '<tr><td colspan="5" class="text-center text-danger">加载失败</td></tr>';
        });
    }
    
    // 控制单个定时任务
    function controlSchedulerTask(taskType, action) {
        let apiUrl = '';
        let payload = { operation: action };
        
        if (taskType === 'sync') {
            apiUrl = '/api/sync/' + action;
            payload = { user_id: 'admin' };
        } else if (taskType === 'email') {
            apiUrl = '/api/v2/system/email-scheduler';
            payload = { operation: action };
        }
        
        fetch(apiUrl, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(payload)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success || data.status === 'success') {
                const taskName = taskType === 'sync' ? '数据同步' : '邮件附件';
                const actionName = action === 'start' ? '启动' : '停止';
                
                // 显示成功消息，包含同步信息
                if (taskType === 'email' && data.message) {
                    showSuccessToast(`${taskName}任务${actionName}成功 - ${data.message}`);
                } else {
                    showSuccessToast(`${taskName}任务${actionName}成功`);
                }
                
                // 延迟刷新状态，给系统时间完成同步
                setTimeout(() => {
                    loadSchedulerTaskStatus();
                    // 如果是邮件任务，也刷新邮箱配置状态
                    if (taskType === 'email') {
                        refreshEmailConfigStatus();
                    }
                }, 1500);
            } else {
                showErrorAlert(`操作失败: ${data.message || data.error || '未知错误'}`);
            }
        })
        .catch(error => {
            console.error('控制任务失败:', error);
            showErrorAlert('操作失败: ' + error.message);
        });
    }

    // 刷新邮箱配置状态（如果页面上有邮箱配置管理）
    function refreshEmailConfigStatus() {
        // 检查是否有邮箱配置相关的元素
        const emailConfigSection = document.querySelector('#emailConfigSection');
        if (emailConfigSection && typeof loadEmailConfigs === 'function') {
            console.log('刷新邮箱配置状态...');
            loadEmailConfigs();
        }
    }
    
    // 保存全局定时任务设置
    function saveGlobalSchedulerSettings() {
        const config = {
            enabled: enableGlobalScheduler.checked
        };
        
        fetch('/api/global-scheduler/config', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ config: config })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showSuccessToast('全局定时任务设置已保存');
                updateGlobalSchedulerStatus();
                
                if (!config.enabled) {
                    setTimeout(() => {
                        loadSchedulerTaskStatus();
                    }, 1000);
                }
            } else {
                showErrorAlert('保存失败: ' + data.error);
            }
        })
        .catch(error => {
            console.error('保存设置失败:', error);
            showErrorAlert('保存失败: ' + error.message);
        });
    }
    
    // 重启所有定时任务
    function restartAllSchedulers() {
        if (confirm('确定要重启所有定时任务吗？这将暂时中断正在进行的任务。')) {
            fetch('/api/global-scheduler/restart-all', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showSuccessToast('所有定时任务已重启');
                    setTimeout(() => {
                        loadSchedulerTaskStatus();
                    }, 2000);
                } else {
                    showErrorAlert('重启失败: ' + data.error);
                }
            })
            .catch(error => {
                console.error('重启失败:', error);
                showErrorAlert('重启失败: ' + error.message);
            });
        }
    }
    
    // 加载定时任务日志
    function loadSchedulerLogs() {
        fetch('/api/global-scheduler/logs?limit=100')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const logs = data.logs || [];
                    if (logs.length > 0) {
                        schedulerLogs.innerHTML = logs.join('\n');
                        schedulerLogs.scrollTop = schedulerLogs.scrollHeight;
                    } else {
                        schedulerLogs.innerHTML = '<div class="text-muted text-center p-3">暂无日志记录</div>';
                    }
                } else {
                    schedulerLogs.innerHTML = `<div class="text-danger text-center p-3">加载日志失败: ${data.error}</div>`;
                }
            })
            .catch(error => {
                console.error('加载日志失败:', error);
                schedulerLogs.innerHTML = `<div class="text-danger text-center p-3">加载日志失败: ${error.message}</div>`;
            });
    }
    
    // 绑定定时任务管理事件
    if (enableGlobalScheduler) enableGlobalScheduler.addEventListener('change', updateGlobalSchedulerStatus);
    if (saveSchedulerSettingsBtn) saveSchedulerSettingsBtn.addEventListener('click', saveGlobalSchedulerSettings);
    if (refreshSchedulerStatusBtn) {
        refreshSchedulerStatusBtn.addEventListener('click', () => {
            loadSchedulerTaskStatus();
            loadSchedulerLogs();
        });
    }
    if (restartAllSchedulersBtn) restartAllSchedulersBtn.addEventListener('click', restartAllSchedulers);
    
    // 将控制函数暴露到全局作用域
    window.controlSchedulerTask = controlSchedulerTask;
    
    // ===============================
    // 页面初始化
    // ===============================
    
    // 页面加载时初始化所有功能
    loadAIConfig();
    loadChatbotSettings();
    loadGlobalSchedulerConfig();
    loadSchedulerLogs();
    checkApiStatus();
    
    // 定期刷新日志（仅在定时任务标签页激活时）
    setInterval(() => {
        const activeTab = document.querySelector('.nav-link.active');
        if (activeTab && activeTab.id === 'scheduler-tab') {
            loadSchedulerLogs();
        }
    }, 10000); // 每10秒刷新一次
});
</script>
{% endblock %} 