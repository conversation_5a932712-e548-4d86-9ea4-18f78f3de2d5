2025-06-17 20:55:56,728 INFO: 📊 从MySQL获取到 171 条待排产批次数据 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:221]
2025-06-17 20:56:02,687 ERROR: 批量删除et_wait_lot记录失败: (1292, "Truncated incorrect DOUBLE value: 'YX2500002197'") [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:1322]
2025-06-17 20:56:09,694 ERROR: 检查会话状态失败: 'User' object has no attribute 'id' [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\api_v2\auth\routes.py:63]
2025-06-17 20:56:09,697 INFO: 从MySQL获取到 2 条已排产批次数据 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:1510]
2025-06-17 20:56:13,904 INFO: 从MySQL获取到 2 条已排产批次数据 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:1510]
2025-06-17 20:56:32,895 ERROR: 检查会话状态失败: 'User' object has no attribute 'id' [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\api_v2\auth\routes.py:63]
2025-06-17 20:56:37,250 ERROR: 检查会话状态失败: 'User' object has no attribute 'id' [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\api_v2\auth\routes.py:63]
2025-06-17 20:56:37,256 INFO: 从MySQL获取到 16 条产品优先级配置数据 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:1387]
2025-06-17 20:56:43,341 INFO: 成功批量删除devicepriorityconfig记录，删除数量: 1 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:1314]
2025-06-17 20:56:43,658 INFO: 从MySQL获取到 15 条产品优先级配置数据 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:1387]
2025-06-17 20:56:57,588 INFO: 成功批量删除devicepriorityconfig记录，删除数量: 4 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:1314]
2025-06-17 20:56:57,917 INFO: 从MySQL获取到 11 条产品优先级配置数据 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:1387]
2025-06-17 20:57:05,120 INFO: 从MySQL获取到 11 条产品优先级配置数据 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:1387]
2025-06-17 21:01:38,047 ERROR: 检查会话状态失败: 'User' object has no attribute 'id' [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\api_v2\auth\routes.py:63]
2025-06-17 21:06:38,030 ERROR: 检查会话状态失败: 'User' object has no attribute 'id' [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\api_v2\auth\routes.py:63]
2025-06-17 21:11:02,742 INFO: 应用启动 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\__init__.py:219]
2025-06-17 21:11:02,743 INFO: 已重新加载 0 个邮箱配置（应用实例未设置） [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\utils\scheduler.py:119]
2025-06-17 21:11:02,761 INFO: 邮件附件定时调度器已启动 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\utils\scheduler.py:74]
2025-06-17 21:11:02,792 INFO: 邮件附件定时任务调度器已启动 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\__init__.py:234]
2025-06-17 21:11:02,801 INFO: 全局定时任务已禁用，邮件调度器跟随禁用 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\utils\scheduler.py:41]
2025-06-17 21:11:02,802 INFO: 检测到全局定时任务已禁用，停止邮件调度器 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\utils\scheduler.py:199]
2025-06-17 21:11:02,939 INFO: APScheduler调度器配置完成: timezone=Asia/Shanghai, max_workers=10 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\scheduler_service.py:98]
2025-06-17 21:11:02,970 INFO: 从数据库加载了 0 个任务 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\scheduler_service.py:178]
2025-06-17 21:11:02,971 INFO: APScheduler调度器启动成功 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\scheduler_service.py:120]
2025-06-17 21:11:02,972 INFO: ✅ APScheduler统一调度器启动成功 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\__init__.py:246]
2025-06-17 21:11:02,973 INFO: 统一模型未启用，使用传统模型 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\compat\model_injector.py:212]
2025-06-17 21:11:02,974 INFO: API兼容性层初始化完成 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\compat\api_compatibility.py:116]
2025-06-17 21:11:02,974 INFO: 兼容层初始化完成 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\__init__.py:264]
2025-06-17 21:11:02,975 WARNING: ⚠️ API v2相关蓝图注册失败: cannot import name 'data_sources' from 'app.routes' (unknown location) [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\__init__.py:275]
2025-06-17 21:11:02,976 INFO: ✅ 性能监控已启用 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\__init__.py:321]
2025-06-17 21:11:02,978 WARNING: ⚠️ 缓存管理器初始化失败: No module named 'redis' [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\__init__.py:334]
2025-06-17 21:11:06,103 ERROR: 检查会话状态失败: 'User' object has no attribute 'id' [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\api_v2\auth\routes.py:63]
2025-06-17 21:11:16,025 ERROR: 检查会话状态失败: 'User' object has no attribute 'id' [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\api_v2\auth\routes.py:63]
2025-06-17 21:11:16,037 INFO: 📊 从MySQL获取到 171 条待排产批次数据 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:221]
2025-06-17 21:14:24,606 INFO: 尝试登录用户: admin [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\auth\routes.py:24]
2025-06-17 21:14:24,609 INFO: 找到用户: admin, 角色: admin, 密码哈希: pbkdf2:sha256:600000$5b3oIzxKyFuBWizM$eaaab87f30981563f8519ad8cb7e8669fa5b6280e439fcd17b1845cdd1c4f56c [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\auth\routes.py:33]
2025-06-17 21:14:24,903 INFO: 密码验证结果: True [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\auth\routes.py:35]
2025-06-17 21:14:24,904 INFO: 用户 admin 登录成功 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\auth\routes.py:42]
2025-06-17 21:14:25,917 ERROR: 检查会话状态失败: 'User' object has no attribute 'id' [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\api_v2\auth\routes.py:63]
2025-06-17 21:14:31,196 ERROR: 检查会话状态失败: 'User' object has no attribute 'id' [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\api_v2\auth\routes.py:63]
2025-06-17 21:14:31,226 INFO: 从MySQL获取到 62 条设备状态数据 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:534]
2025-06-17 21:14:31,228 INFO: 🏭 从MySQL获取到 62 条设备状态数据 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:295]
2025-06-17 21:14:43,818 INFO: 从MySQL获取到 62 条设备状态数据 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:534]
2025-06-17 21:14:43,820 INFO: 🏭 从MySQL获取到 62 条设备状态数据 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:295]
2025-06-17 21:14:48,551 INFO: 从MySQL获取到 62 条设备状态数据 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:534]
2025-06-17 21:14:48,553 INFO: 🏭 从MySQL获取到 62 条设备状态数据 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:295]
2025-06-17 21:14:56,973 ERROR: 检查会话状态失败: 'User' object has no attribute 'id' [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\api_v2\auth\routes.py:63]
2025-06-17 21:14:57,092 INFO: 从MySQL获取到 185 条测试规范数据 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:439]
2025-06-17 21:14:57,095 INFO: 🔬 从MySQL获取到 185 条测试规范数据 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:247]
