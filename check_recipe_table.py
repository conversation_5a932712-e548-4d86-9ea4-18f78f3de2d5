#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

print("检查配方表的各种可能名称...")

try:
    from app.services.data_source_manager import DataSourceManager
    
    dm = DataSourceManager()
    
    # 尝试各种可能的表名
    possible_tables = [
        'ET_RECIPE_FILE', 'et_recipe_file', 
        'RECIPE_FILE', 'recipe_file',
        'ET_RECIPE', 'et_recipe',
        'recipe', 'RECIPE',
        'recipes', 'RECIPES'
    ]
    
    print(f"\n=== 配方表名称检查 ===")
    
    found_tables = []
    
    for table_name in possible_tables:
        try:
            result = dm.get_table_data(table_name)
            if result.get('success'):
                data = result.get('data', [])
                print(f"✅ {table_name}: {len(data)} 条数据")
                found_tables.append(table_name)
                
                # 显示字段信息
                if data and len(data) > 0:
                    fields = list(data[0].keys())
                    kit_related = [f for f in fields if 'KIT' in f.upper() or 'SOCKET' in f.upper()]
                    device_related = [f for f in fields if 'DEVICE' in f.upper() or 'STAGE' in f.upper() or 'PKG' in f.upper()]
                    print(f"   KIT相关字段: {kit_related}")
                    print(f"   设备相关字段: {device_related}")
                    
            else:
                print(f"❌ {table_name}: 失败")
                
        except Exception as e:
            print(f"❌ {table_name}: 异常 - {str(e)}")
    
    if not found_tables:
        print("\n⚠️  没有找到任何配方表!")
        print("可能的解决方案:")
        print("1. 配方信息可能在其他表中")
        print("2. 可能需要从Excel文件中导入")
        print("3. 可能配方信息就在ET_FT_TEST_SPEC中")
        
        # 检查ET_FT_TEST_SPEC是否有KIT相关信息
        print("\n=== 检查ET_FT_TEST_SPEC中的配方信息 ===")
        specs_result = dm.get_table_data('ET_FT_TEST_SPEC')
        if specs_result.get('success'):
            data = specs_result.get('data', [])[:3]
            for idx, spec in enumerate(data):
                print(f"\n测试规范 {idx+1}:")
                for key, value in spec.items():
                    if any(keyword in key.upper() for keyword in ['KIT', 'SOCKET', 'RECIPE', 'PROGRAM']):
                        print(f"  {key}: {value}")
    else:
        print(f"\n✅ 找到配方表: {found_tables}")
        
except Exception as e:
    print(f"错误: {e}")
    import traceback
    traceback.print_exc() 