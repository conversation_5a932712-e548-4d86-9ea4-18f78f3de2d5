#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
已排产批次API接口
提供lotprioritydone表的数据查询、操作接口
"""

import logging
from flask import Blueprint, request, jsonify
from sqlalchemy import text
from app import db

logger = logging.getLogger(__name__)

# 创建蓝图
done_lots_bp = Blueprint('done_lots_api', __name__)

# 兼容性导出
done_lots_api = done_lots_bp

@done_lots_api.route('/api/v2/production/done-lots', methods=['GET'])
def get_lotprioritydone_data():
    """
    获取已排产表数据
    支持分页、筛选、排序
    """
    try:
        # 获取请求参数
        table = request.args.get('table', 'lotprioritydone')
        page = int(request.args.get('page', 1))
        size = int(request.args.get('size', 50))
        
        # 只处理lotprioritydone表的请求
        if table != 'lotprioritydone':
            return jsonify({
                'success': False,
                'message': f'不支持的表名: {table}'
            }), 400
        
        # 计算偏移量
        offset = (page - 1) * size
        
        # 查询总数
        count_query = text("SELECT COUNT(*) FROM lotprioritydone")
        total_result = db.session.execute(count_query)
        total = total_result.scalar() or 0
        
        # 查询数据
        data_query = text("""
            SELECT 
                id, PRIORITY, HANDLER_ID, LOT_ID, LOT_TYPE, GOOD_QTY,
                PROD_ID, DEVICE, CHIP_ID, PKG_PN, PO_ID, STAGE,
                WIP_STATE, PROC_STATE, HOLD_STATE, FLOW_ID, FLOW_VER,
                RELEASE_TIME, FAC_ID, CREATE_TIME,
                comprehensive_score, processing_time, changeover_time,
                algorithm_version, match_type, priority_score
            FROM lotprioritydone 
            ORDER BY PRIORITY ASC, CREATE_TIME DESC
            LIMIT :size OFFSET :offset
        """)
        
        result = db.session.execute(data_query, {'size': size, 'offset': offset})
        
        # 构建返回数据
        records = []
        for row in result.fetchall():
            records.append({
                'id': row[0],  # 使用id作为ID
                'PRIORITY': row[1] or '',
                'HANDLER_ID': row[2] or '',
                'LOT_ID': row[3] or '',
                'LOT_TYPE': row[4] or '',
                'GOOD_QTY': row[5] or 0,
                'PROD_ID': row[6] or '',
                'DEVICE': row[7] or '',
                'CHIP_ID': row[8] or '',
                'PKG_PN': row[9] or '',
                'PO_ID': row[10] or '',
                'STAGE': row[11] or '',
                'WIP_STATE': row[12] or '',
                'PROC_STATE': row[13] or '',
                'HOLD_STATE': row[14] or 0,
                'FLOW_ID': row[15] or '',
                'FLOW_VER': row[16] or '',
                'RELEASE_TIME': row[17] or '',
                'FAC_ID': row[18] or '',
                'CREATE_TIME': row[19] or '',
                'comprehensive_score': row[20] or 0.0,  # 综合评分
                'processing_time': row[21] or 0.0,      # 预计加工时间
                'changeover_time': row[22] or 0.0,      # 改机时间
                'algorithm_version': row[23] or '',     # 算法版本
                'match_type': row[24] or '',            # 匹配类型
                'priority_score': row[25] or 0.0        # 优先级评分
            })
        
        # 计算分页信息
        total_pages = (total + size - 1) // size
        
        logger.info(f"📊 已排产表查询: 第{page}页, {size}条/页, 共{total}条记录")
        
        return jsonify({
            'success': True,
            'data': records,
            'pagination': {
                'page': page,
                'size': size,
                'total': total,
                'total_pages': total_pages
            }
        })
        
    except Exception as e:
        logger.error(f"❌ 获取已排产数据失败: {e}")
        return jsonify({
            'success': False,
            'message': f'查询失败: {str(e)}'
        }), 500

@done_lots_api.route('/api/v2/production/done-lots/columns', methods=['GET'])
def get_lotprioritydone_columns():
    """获取已排产表的列信息"""
    try:
        table = request.args.get('table', 'lotprioritydone')
        
        if table != 'lotprioritydone':
            return jsonify({
                'success': False,
                'message': f'不支持的表名: {table}'
            }), 400
        
        # 定义已排产表的列信息（包含算法扩展字段）
        columns = [
            {'name': 'PRIORITY', 'label': '执行优先级', 'type': 'number', 'sortable': True},
            {'name': 'comprehensive_score', 'label': '综合评分', 'type': 'number', 'sortable': True},
            {'name': 'HANDLER_ID', 'label': '分拣机ID', 'type': 'string', 'sortable': True},
            {'name': 'LOT_ID', 'label': '批次号', 'type': 'string', 'sortable': True},
            {'name': 'LOT_TYPE', 'label': '批次类型', 'type': 'string', 'sortable': True},
            {'name': 'GOOD_QTY', 'label': '良品数量', 'type': 'number', 'sortable': True},
            {'name': 'PROD_ID', 'label': '产品ID', 'type': 'string', 'sortable': True},
            {'name': 'DEVICE', 'label': '器件名称', 'type': 'string', 'sortable': True},
            {'name': 'CHIP_ID', 'label': '芯片ID', 'type': 'string', 'sortable': True},
            {'name': 'PKG_PN', 'label': '封装', 'type': 'string', 'sortable': True},
            {'name': 'PO_ID', 'label': '订单号', 'type': 'string', 'sortable': True},
            {'name': 'STAGE', 'label': '工序', 'type': 'string', 'sortable': True},
            {'name': 'processing_time', 'label': '预计加工时间(h)', 'type': 'number', 'sortable': True},
            {'name': 'changeover_time', 'label': '改机时间(min)', 'type': 'number', 'sortable': True},
            {'name': 'WIP_STATE', 'label': 'WIP状态', 'type': 'string', 'sortable': True},
            {'name': 'PROC_STATE', 'label': '流程状态', 'type': 'string', 'sortable': True},
            {'name': 'HOLD_STATE', 'label': '扣留状态', 'type': 'number', 'sortable': True},
            {'name': 'FLOW_ID', 'label': '流程ID', 'type': 'string', 'sortable': True},
            {'name': 'FLOW_VER', 'label': '流程版本', 'type': 'string', 'sortable': True},
            {'name': 'RELEASE_TIME', 'label': '释放时间', 'type': 'datetime', 'sortable': True},
            {'name': 'FAC_ID', 'label': '工厂ID', 'type': 'string', 'sortable': True},
            {'name': 'CREATE_TIME', 'label': '创建时间', 'type': 'datetime', 'sortable': True},
            {'name': 'priority_score', 'label': '优先级评分', 'type': 'number', 'sortable': True},
            {'name': 'algorithm_version', 'label': '算法版本', 'type': 'string', 'sortable': True},
            {'name': 'match_type', 'label': '匹配类型', 'type': 'string', 'sortable': True}
        ]
        
        return jsonify({
            'success': True,
            'columns': columns
        })
        
    except Exception as e:
        logger.error(f"❌ 获取列信息失败: {e}")
        return jsonify({
            'success': False,
            'message': f'获取列信息失败: {str(e)}'
        }), 500

@done_lots_api.route('/api/v2/production/done-lots/move-to-waiting', methods=['POST'])
def move_lots_to_waiting():
    """将已排产批次移回待排产状态"""
    try:
        data = request.get_json()
        lot_ids = data.get('ids', [])
        
        if not lot_ids:
            return jsonify({
                'success': False,
                'message': '请选择要移动的批次'
            }), 400
        
        # 获取要移动的批次信息
        placeholders = ','.join([':id_%d' % i for i in range(len(lot_ids))])
        params = {'id_%d' % i: lot_id for i, lot_id in enumerate(lot_ids)}
        
        select_query = text(f"""
            SELECT LOT_ID, DEVICE, STAGE, GOOD_QTY, PKG_PN, CHIP_ID,
                   FLOW_ID, FLOW_VER, FAC_ID, RELEASE_TIME
            FROM lotprioritydone 
            WHERE PRIORITY IN ({placeholders})
        """)
        
        result = db.session.execute(select_query, params)
        lots_to_move = result.fetchall()
        
        if not lots_to_move:
            return jsonify({
                'success': False,
                'message': '未找到指定的批次'
            }), 404
        
        # 将批次信息重新插入到ET_WAIT_LOT表
        moved_count = 0
        for lot in lots_to_move:
            try:
                insert_query = text("""
                    INSERT INTO ET_WAIT_LOT (
                        LOT_ID, DEVICE, STAGE, GOOD_QTY, PKG_PN, CHIP_ID,
                        WIP_STATE, PROC_STATE, HOLD_STATE, FLOW_ID, FLOW_VER,
                        FAC_ID, RELEASE_TIME, CREATE_TIME
                    ) VALUES (
                        :lot_id, :device, :stage, :good_qty, :pkg_pn, :chip_id,
                        'WAIT', 'UNASSIGNED', 0, :flow_id, :flow_ver,
                        :fac_id, :release_time, NOW()
                    )
                """)
                
                db.session.execute(insert_query, {
                    'lot_id': lot[0],
                    'device': lot[1],
                    'stage': lot[2],
                    'good_qty': lot[3],
                    'pkg_pn': lot[4],
                    'chip_id': lot[5],
                    'flow_id': lot[6],
                    'flow_ver': lot[7],
                    'fac_id': lot[8],
                    'release_time': lot[9]
                })
                moved_count += 1
                
            except Exception as e:
                logger.warning(f"移动批次 {lot[0]} 失败: {e}")
                continue
        
        # 从已排产表中删除这些批次
        if moved_count > 0:
            delete_query = text(f"""
                DELETE FROM lotprioritydone 
                WHERE PRIORITY IN ({placeholders})
            """)
            db.session.execute(delete_query, params)
        
        db.session.commit()
        
        logger.info(f"✅ 成功移动 {moved_count} 个批次到待排产状态")
        
        return jsonify({
            'success': True,
            'message': f'成功移动 {moved_count} 个批次到待排产状态',
            'moved_count': moved_count
        })
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"❌ 移动批次失败: {e}")
        return jsonify({
            'success': False,
            'message': f'移动失败: {str(e)}'
        }), 500

@done_lots_api.route('/api/v2/production/done-lots/delete', methods=['POST'])
def delete_lotprioritydone_records():
    """删除已排产记录"""
    try:
        data = request.get_json()
        table = data.get('table', 'lotprioritydone')
        ids = data.get('ids', [])
        
        if table != 'lotprioritydone':
            return jsonify({
                'success': False,
                'message': f'不支持的表名: {table}'
            }), 400
        
        if not ids:
            return jsonify({
                'success': False,
                'message': '请选择要删除的记录'
            }), 400
        
        # 构建删除语句
        placeholders = ','.join([':id_%d' % i for i in range(len(ids))])
        params = {'id_%d' % i: record_id for i, record_id in enumerate(ids)}
        
        delete_query = text(f"""
            DELETE FROM lotprioritydone 
            WHERE PRIORITY IN ({placeholders})
        """)
        
        result = db.session.execute(delete_query, params)
        deleted_count = result.rowcount
        
        db.session.commit()
        
        logger.info(f"✅ 成功删除 {deleted_count} 条已排产记录")
        
        return jsonify({
            'success': True,
            'message': f'成功删除 {deleted_count} 条记录',
            'deleted_count': deleted_count
        })
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"❌ 删除已排产记录失败: {e}")
        return jsonify({
            'success': False,
            'message': f'删除失败: {str(e)}'
        }), 500 