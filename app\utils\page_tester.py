#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
页面测试工具 - 自动测试11个表页面的功能
验证增删改查、筛选、导出等功能是否正常工作
"""

import requests
import json
import logging
from typing import Dict, List, Optional
from urllib.parse import urljoin

logger = logging.getLogger(__name__)

class PageTester:
    """页面功能测试器"""
    
    def __init__(self, base_url: str = "http://localhost:5000"):
        self.base_url = base_url
        self.api_base = urljoin(base_url, "/api/v3")
        self.target_tables = [
            'eqp_status', 'et_uph_eqp', 'et_ft_test_spec', 'ct', 'tcc_inv',
            'wip_lot', 'et_wait_lot', 'et_recipe_file', 'devicepriorityconfig',
            'lotpriorityconfig', 'lotprioritydone'
        ]
        self.session = requests.Session()
    
    def test_all_pages(self) -> Dict:
        """测试所有页面功能"""
        results = {
            'success': True,
            'tested': [],
            'failed': [],
            'summary': {},
            'details': {}
        }
        
        logger.info("🧪 开始测试所有页面功能...")
        
        for table_name in self.target_tables:
            try:
                result = self.test_single_page(table_name)
                results['details'][table_name] = result
                
                if result['overall_success']:
                    results['tested'].append(table_name)
                    logger.info(f"✅ {table_name} 页面测试通过")
                else:
                    results['failed'].append({
                        'table': table_name,
                        'failures': result['failures']
                    })
                    logger.error(f"❌ {table_name} 页面测试失败: {len(result['failures'])}个问题")
                    
            except Exception as e:
                results['failed'].append({
                    'table': table_name,
                    'error': str(e)
                })
                logger.error(f"❌ {table_name} 页面测试异常: {e}")
        
        results['summary'] = {
            'total': len(self.target_tables),
            'passed': len(results['tested']),
            'failed': len(results['failed'])
        }
        
        if results['failed']:
            results['success'] = False
        
        logger.info(f"📊 测试完成: {results['summary']}")
        return results
    
    def test_single_page(self, table_name: str) -> Dict:
        """测试单个页面的所有功能"""
        result = {
            'table_name': table_name,
            'tests': {},
            'failures': [],
            'overall_success': True
        }
        
        # 测试项目列表
        test_cases = [
            ('page_load', self._test_page_load),
            ('table_structure', self._test_table_structure),
            ('data_fetch', self._test_data_fetch),
            ('search_function', self._test_search_function),
            ('filter_function', self._test_filter_function),
            ('sort_function', self._test_sort_function),
            ('pagination', self._test_pagination),
            ('export_function', self._test_export_function)
        ]
        
        for test_name, test_func in test_cases:
            try:
                test_result = test_func(table_name)
                result['tests'][test_name] = test_result
                
                if not test_result['success']:
                    result['failures'].append({
                        'test': test_name,
                        'error': test_result['error']
                    })
                    result['overall_success'] = False
                    
            except Exception as e:
                result['tests'][test_name] = {
                    'success': False,
                    'error': str(e)
                }
                result['failures'].append({
                    'test': test_name,
                    'error': str(e)
                })
                result['overall_success'] = False
        
        return result
    
    def _test_page_load(self, table_name: str) -> Dict:
        """测试页面加载"""
        try:
            url = urljoin(self.base_url, f"/api/v3/universal/{table_name}")
            response = self.session.get(url, timeout=10)
            
            if response.status_code == 200:
                # 检查页面内容
                content = response.text
                required_elements = [
                    'universal_resource_v3.html',
                    table_name,
                    'API_BASE',
                    'TABLE_NAME'
                ]
                
                missing_elements = [elem for elem in required_elements if elem not in content]
                
                if missing_elements:
                    return {
                        'success': False,
                        'error': f'页面缺少必要元素: {missing_elements}'
                    }
                
                return {
                    'success': True,
                    'status_code': response.status_code,
                    'content_length': len(content)
                }
            else:
                return {
                    'success': False,
                    'error': f'页面加载失败，状态码: {response.status_code}'
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': f'页面加载异常: {str(e)}'
            }
    
    def _test_table_structure(self, table_name: str) -> Dict:
        """测试表结构获取"""
        try:
            url = f"{self.api_base}/tables/{table_name}/structure"
            response = self.session.get(url, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    return {
                        'success': True,
                        'columns_count': len(data.get('columns', [])),
                        'table_info': data.get('table_info', {})
                    }
                else:
                    return {
                        'success': False,
                        'error': data.get('error', '获取表结构失败')
                    }
            else:
                return {
                    'success': False,
                    'error': f'API调用失败，状态码: {response.status_code}'
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': f'表结构测试异常: {str(e)}'
            }
    
    def _test_data_fetch(self, table_name: str) -> Dict:
        """测试数据获取"""
        try:
            url = f"{self.api_base}/tables/{table_name}/data"
            params = {'page': 1, 'per_page': 10}
            response = self.session.get(url, params=params, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    return {
                        'success': True,
                        'total_records': data.get('total', 0),
                        'returned_records': len(data.get('data', [])),
                        'columns': data.get('columns', [])
                    }
                else:
                    return {
                        'success': False,
                        'error': data.get('error', '获取数据失败')
                    }
            else:
                return {
                    'success': False,
                    'error': f'数据获取失败，状态码: {response.status_code}'
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': f'数据获取测试异常: {str(e)}'
            }
    
    def _test_search_function(self, table_name: str) -> Dict:
        """测试搜索功能"""
        try:
            url = f"{self.api_base}/tables/{table_name}/data"
            params = {'page': 1, 'per_page': 10, 'search': 'test'}
            response = self.session.get(url, params=params, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                return {
                    'success': data.get('success', False),
                    'search_results': len(data.get('data', [])),
                    'error': data.get('error') if not data.get('success') else None
                }
            else:
                return {
                    'success': False,
                    'error': f'搜索功能测试失败，状态码: {response.status_code}'
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': f'搜索功能测试异常: {str(e)}'
            }
    
    def _test_filter_function(self, table_name: str) -> Dict:
        """测试筛选功能"""
        try:
            # 简单的筛选测试
            filters = json.dumps([{
                'field': 'id',
                'operator': 'gt',
                'value': '0'
            }])
            
            url = f"{self.api_base}/tables/{table_name}/data"
            params = {'page': 1, 'per_page': 10, 'filters': filters}
            response = self.session.get(url, params=params, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                return {
                    'success': data.get('success', False),
                    'filtered_results': len(data.get('data', [])),
                    'error': data.get('error') if not data.get('success') else None
                }
            else:
                return {
                    'success': False,
                    'error': f'筛选功能测试失败，状态码: {response.status_code}'
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': f'筛选功能测试异常: {str(e)}'
            }
    
    def _test_sort_function(self, table_name: str) -> Dict:
        """测试排序功能"""
        try:
            url = f"{self.api_base}/tables/{table_name}/data"
            params = {'page': 1, 'per_page': 10, 'sort_by': 'id', 'sort_order': 'desc'}
            response = self.session.get(url, params=params, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                return {
                    'success': data.get('success', False),
                    'sorted_results': len(data.get('data', [])),
                    'error': data.get('error') if not data.get('success') else None
                }
            else:
                return {
                    'success': False,
                    'error': f'排序功能测试失败，状态码: {response.status_code}'
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': f'排序功能测试异常: {str(e)}'
            }
    
    def _test_pagination(self, table_name: str) -> Dict:
        """测试分页功能"""
        try:
            # 测试第二页
            url = f"{self.api_base}/tables/{table_name}/data"
            params = {'page': 2, 'per_page': 5}
            response = self.session.get(url, params=params, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                return {
                    'success': data.get('success', False),
                    'page_results': len(data.get('data', [])),
                    'current_page': data.get('page', 0),
                    'total_pages': data.get('pages', 0),
                    'error': data.get('error') if not data.get('success') else None
                }
            else:
                return {
                    'success': False,
                    'error': f'分页功能测试失败，状态码: {response.status_code}'
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': f'分页功能测试异常: {str(e)}'
            }
    
    def _test_export_function(self, table_name: str) -> Dict:
        """测试导出功能"""
        try:
            url = f"{self.api_base}/tables/{table_name}/export"
            params = {'format': 'csv'}
            response = self.session.get(url, params=params, timeout=30)
            
            if response.status_code == 200:
                # 检查响应头
                content_type = response.headers.get('content-type', '')
                if 'csv' in content_type or 'text' in content_type:
                    return {
                        'success': True,
                        'content_type': content_type,
                        'content_length': len(response.content)
                    }
                else:
                    return {
                        'success': False,
                        'error': f'导出格式不正确: {content_type}'
                    }
            else:
                return {
                    'success': False,
                    'error': f'导出功能测试失败，状态码: {response.status_code}'
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': f'导出功能测试异常: {str(e)}'
            }
    
    def generate_test_report(self, results: Dict) -> str:
        """生成测试报告"""
        report = f"""
# 页面功能测试报告

## 测试概览
- 总计测试: {results['summary']['total']} 个页面
- 测试通过: {results['summary']['passed']} 个页面
- 测试失败: {results['summary']['failed']} 个页面
- 成功率: {(results['summary']['passed'] / results['summary']['total'] * 100):.1f}%

## 测试详情

### ✅ 通过的页面
"""
        
        for table_name in results['tested']:
            report += f"- {table_name}\n"
        
        if results['failed']:
            report += "\n### ❌ 失败的页面\n"
            for failure in results['failed']:
                table_name = failure['table']
                report += f"\n#### {table_name}\n"
                if 'failures' in failure:
                    for fail in failure['failures']:
                        report += f"- {fail['test']}: {fail['error']}\n"
                elif 'error' in failure:
                    report += f"- 异常: {failure['error']}\n"
        
        return report

# 便捷函数
def test_all_pages(base_url: str = "http://localhost:5000"):
    """测试所有页面的便捷函数"""
    tester = PageTester(base_url)
    return tester.test_all_pages()

def test_single_page(table_name: str, base_url: str = "http://localhost:5000"):
    """测试单个页面的便捷函数"""
    tester = PageTester(base_url)
    return tester.test_single_page(table_name)

if __name__ == "__main__":
    # 命令行执行
    import sys
    
    base_url = sys.argv[1] if len(sys.argv) > 1 else "http://localhost:5000"
    
    if len(sys.argv) > 2:
        table_name = sys.argv[2]
        result = test_single_page(table_name, base_url)
        print(json.dumps(result, ensure_ascii=False, indent=2))
    else:
        result = test_all_pages(base_url)
        tester = PageTester(base_url)
        report = tester.generate_test_report(result)
        print(report)
