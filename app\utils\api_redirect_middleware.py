
"""
API重定向中间件 - 将废弃的API自动重定向到v2版本
"""
from flask import request, redirect, url_for

class APIRedirectMiddleware:
    def __init__(self, app=None):
        self.app = app
        self.redirects = {'/api/production/': '/api/v2/production/', '/api/status': '/api/v2/system/health', '/api/logs': '/api/v2/system/logs', '/api/system_logs': '/api/v2/system/logs', '/api/ai-settings': '/api/v2/system/ai-settings', '/api/global-scheduler/': '/api/v2/system/scheduler/'}
        if app:
            self.init_app(app)
    
    def init_app(self, app):
        app.before_request(self.handle_redirect)
    
    def handle_redirect(self):
        path = request.path
        
        # 检查是否需要重定向
        for old_pattern, new_pattern in self.redirects.items():
            if path.startswith(old_pattern) and not path.startswith('/api/v2/'):
                new_path = path.replace(old_pattern, new_pattern, 1)
                return redirect(new_path, code=301)  # 永久重定向
        
        return None
