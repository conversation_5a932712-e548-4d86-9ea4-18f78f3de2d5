#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

print("🔍 调试设备分配逻辑错误...")

try:
    from app.services.real_scheduling_service import RealSchedulingService
    from app.services.data_source_manager import DataSourceManager
    
    scheduler = RealSchedulingService()
    dm = DataSourceManager()
    
    print("=== 🏭 检查EQP_STATUS表中的设备数据 ===")
    equipment_result = dm.get_table_data('EQP_STATUS', per_page=100)
    all_equipment = equipment_result.get('data', []) if equipment_result.get('success') else []
    
    print(f"设备总数: {len(all_equipment)}")
    
    # 查找HANK-C-001-5800设备
    hank_equipment = None
    for eq in all_equipment:
        if eq.get('HANDLER_ID') == 'HANK-C-001-5800':
            hank_equipment = eq
            break
    
    if hank_equipment:
        print(f"\n✅ 找到HANK-C-001-5800设备:")
        for key, value in hank_equipment.items():
            print(f"  {key}: {value}")
    else:
        print(f"\n❌ 在EQP_STATUS表中没有找到HANK-C-001-5800设备")
        
        # 显示实际的设备ID
        print(f"\n实际的设备ID列表 (前20个):")
        for idx, eq in enumerate(all_equipment[:20]):
            handler_id = eq.get('HANDLER_ID', '')
            tester_id = eq.get('TESTER_ID', '')
            device = eq.get('DEVICE', '')
            status = eq.get('STATUS', '')
            print(f"  {idx+1}. HANDLER_ID='{handler_id}', TESTER_ID='{tester_id}', DEVICE='{device}', STATUS='{status}'")
    
    # 检查find_suitable_equipment方法
    print(f"\n=== 🧪 测试find_suitable_equipment方法 ===")
    
    # 获取一个测试批次
    wait_lots_result = dm.get_table_data('ET_WAIT_LOT', per_page=1)
    wait_lots = wait_lots_result.get('data', [])
    
    if wait_lots:
        test_lot = wait_lots[0]
        lot_id = test_lot.get('LOT_ID')
        print(f"测试批次: {lot_id}")
        
        # 获取批次配置需求
        requirements = scheduler.get_lot_configuration_requirements(test_lot)
        if requirements:
            print(f"批次配置需求:")
            for key, value in requirements.items():
                print(f"  {key}: {value}")
            
            # 测试find_suitable_equipment
            suitable_equipment = scheduler.find_suitable_equipment(test_lot, requirements)
            print(f"\nfind_suitable_equipment返回结果:")
            for idx, eq in enumerate(suitable_equipment[:5]):
                equipment_info = eq.get('equipment', {})
                handler_id = equipment_info.get('HANDLER_ID', '')
                score = eq.get('comprehensive_score', 0)
                match_type = eq.get('match_type', '')
                print(f"  {idx+1}. HANDLER_ID='{handler_id}', 评分={score:.2f}, 类型='{match_type}'")
            
            # 特别检查是否返回了HANK-C-001-5800
            for eq in suitable_equipment:
                equipment_info = eq.get('equipment', {})
                if equipment_info.get('HANDLER_ID') == 'HANK-C-001-5800':
                    print(f"\n⚠️ 发现问题！find_suitable_equipment返回了HANK-C-001-5800:")
                    print(f"   完整信息: {eq}")
                    print(f"   设备详情: {equipment_info}")
                    break
        else:
            print(f"❌ 无法获取批次配置需求")
    
    # 检查equipment_score方法的逻辑
    print(f"\n=== 🔍 检查设备评分逻辑 ===")
    
    # 看看空闲设备是如何被处理的
    idle_equipment = [eq for eq in all_equipment if eq.get('STATUS', '').strip().upper() == 'IDLE']
    print(f"空闲设备数量: {len(idle_equipment)}")
    
    if idle_equipment:
        print(f"\n前5个空闲设备:")
        for idx, eq in enumerate(idle_equipment[:5]):
            handler_id = eq.get('HANDLER_ID', '')
            tester_id = eq.get('TESTER_ID', '')
            device = eq.get('DEVICE', '')
            hb_pn = eq.get('HB_PN', '')
            tb_pn = eq.get('TB_PN', '')
            kit_pn = eq.get('KIT_PN', '')
            print(f"  {idx+1}. HANDLER_ID='{handler_id}', DEVICE='{device}'")
            print(f"     HB_PN='{hb_pn}', TB_PN='{tb_pn}', KIT_PN='{kit_pn}'")
    
    # 检查generate_equipment_id方法
    print(f"\n=== 🔍 检查generate_equipment_id方法 ===")
    
    # 直接调用这个方法看看它返回什么
    if hasattr(scheduler, '_generate_equipment_id'):
        for i in range(5):
            generated_id = scheduler._generate_equipment_id(f"TEST_DEVICE_{i}", "TEST_STAGE")
            print(f"  生成的设备ID {i+1}: {generated_id}")
    
except Exception as e:
    print(f"❌ 调试失败: {e}")
    import traceback
    traceback.print_exc() 