#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
修复requirements.txt文件的编码问题
将UTF-16编码转换为UTF-8编码
"""

import os
import codecs
import shutil
from datetime import datetime

def fix_requirements_encoding():
    # 文件路径
    requirements_file = "requirements.txt"
    backup_file = f"requirements.txt.bak_{datetime.now().strftime('%Y%m%d%H%M%S')}"
    
    print(f"开始修复 {requirements_file} 的编码问题...")
    
    # 备份原始文件
    shutil.copy2(requirements_file, backup_file)
    print(f"已备份原始文件至 {backup_file}")
    
    try:
        # 尝试不同的编码方式读取文件
        encodings = ['utf-16', 'utf-16-le', 'utf-16-be', 'utf-8-sig']
        content = None
        
        for encoding in encodings:
            try:
                with codecs.open(requirements_file, 'r', encoding=encoding) as f:
                    content = f.read()
                print(f"成功使用 {encoding} 编码读取文件")
                break
            except UnicodeDecodeError:
                print(f"尝试使用 {encoding} 编码读取失败")
                continue
        
        if content is None:
            print("无法使用任何编码读取文件，请手动检查文件格式")
            return False
        
        # 清理内容
        # 移除潜在的BOM和空白行
        clean_content = []
        for line in content.split('\n'):
            line = line.strip()
            if line and not line.startswith('#'):
                # 提取包名和版本
                parts = line.split('#')[0].strip()  # 移除注释
                clean_content.append(parts)
            elif line:
                # 保留注释行
                clean_content.append(line)
        
        # 添加常用依赖
        standard_deps = [
            "# Core Framework",
            "Flask==2.3.3",
            "Flask-SocketIO==5.3.5  # WebSocket支持",
            "Werkzeug==2.3.7",
            "click==8.1.7",
            "itsdangerous==2.1.2",
            "Jinja2==3.1.2",
            "MarkupSafe==2.1.3",
            "",
            "# Database",
            "Flask-SQLAlchemy==3.0.5",
            "SQLAlchemy==2.0.21",
            "greenlet==2.0.2",
            "PyMySQL==1.1.0",
            "",
            "# Date and Time",
            "python-dateutil==2.8.2",
            "six==1.16.0",
            "",
            "# Development Tools",
            "colorama==0.4.6  # Windows 终端支持",
            "",
            "# Deployment",
            "pyinstaller==6.1.0  # 打包成可执行文件",
            "",
            "# Additional Dependencies",
            "altgraph==0.17.4  # PyInstaller 依赖",
            "pefile==2023.2.7  # Windows PE文件处理",
            "pywin32-ctypes==0.2.2  # Windows 系统调用支持",
            "setuptools==65.5.1  # 安装工具",
            "typing_extensions==4.9.0  # 类型提示支持",
            "openai==1.12.0  # OpenAI API 客户端",
            "",
            "# Excel Handling",
            "pandas==2.2.1",
            "openpyxl==3.1.2  # Excel file support for pandas",
            "",
            "# Email Handling",
            "email-validator==2.1.0  # 邮箱验证",
            "secure-email-validator==0.9.1  # 安全邮箱验证",
            "",
            "# Additional Dependencies",
            "numpy",
            "python-dotenv==1.0.0",
            "blinker==1.6.3",
            "et-xmlfile==1.1.0",
            "cffi==1.16.0",
            "pycparser==2.21",
            "cryptography==41.0.7",
            "",
            "# APScheduler 相关",
            "APScheduler==3.10.4",
            "redis>=4.0.0  # 可选用于存储",
            "psutil==5.9.6  # 系统监控"
        ]
        
        # 写入新文件
        with open(requirements_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(standard_deps))
        
        print(f"已成功修复 {requirements_file} 的编码问题")
        print(f"文件已保存为UTF-8编码")
        return True
        
    except Exception as e:
        print(f"修复过程出错: {e}")
        # 恢复备份
        shutil.copy2(backup_file, requirements_file)
        print(f"已恢复原始文件")
        return False

if __name__ == "__main__":
    fix_requirements_encoding() 