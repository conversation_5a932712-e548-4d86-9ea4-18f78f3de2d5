#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

print("🔍 深入分析DEVICE字段匹配问题...")

try:
    from app.services.data_source_manager import DataSourceManager
    
    dm = DataSourceManager()
    
    # 获取待排产批次数据（完整数据）
    wait_lots_result = dm.get_table_data('ET_WAIT_LOT', per_page=200)
    wait_lots = wait_lots_result.get('data', []) if wait_lots_result.get('success') else []
    
    # 获取测试规范数据（完整数据）
    specs_result = dm.get_table_data('ET_FT_TEST_SPEC', per_page=600)
    specs = specs_result.get('data', []) if specs_result.get('success') else []
    
    print(f"📋 待排产批次: {len(wait_lots)} 条")
    print(f"🔬 测试规范: {len(specs)} 条")
    
    # 筛选Released状态的测试规范
    released_specs = [spec for spec in specs if spec.get('APPROVAL_STATE', '').strip() == 'Released']
    print(f"📋 Released状态的测试规范: {len(released_specs)} 条")
    
    # 分析待排产批次的DEVICE
    print(f"\n=== 📊 待排产批次DEVICE分析 ===")
    wait_devices = set()
    for lot in wait_lots:
        device = lot.get('DEVICE', '').strip()
        if device:
            wait_devices.add(device)
    
    print(f"待排产批次包含 {len(wait_devices)} 种不同的DEVICE:")
    for device in sorted(list(wait_devices)[:15]):
        print(f"  {device}")
    
    if len(wait_devices) > 15:
        print(f"  ... 还有 {len(wait_devices) - 15} 种DEVICE")
    
    # 分析测试规范的DEVICE  
    print(f"\n=== 🔬 测试规范DEVICE分析 ===")
    spec_devices = set()
    for spec in released_specs:
        device = spec.get('DEVICE', '').strip()
        if device:
            spec_devices.add(device)
    
    print(f"Released测试规范包含 {len(spec_devices)} 种不同的DEVICE:")
    for device in sorted(list(spec_devices)[:15]):
        print(f"  {device}")
    
    if len(spec_devices) > 15:
        print(f"  ... 还有 {len(spec_devices) - 15} 种DEVICE")
    
    # 分析DEVICE匹配情况
    print(f"\n=== 🎯 DEVICE匹配分析 ===")
    matched_devices = wait_devices & spec_devices
    unmatched_devices = wait_devices - spec_devices
    
    print(f"✅ 可以匹配的DEVICE: {len(matched_devices)} 种")
    for device in sorted(list(matched_devices)[:10]):
        print(f"  {device}")
    
    print(f"\n❌ 无法匹配的DEVICE: {len(unmatched_devices)} 种")
    for device in sorted(list(unmatched_devices)[:10]):
        print(f"  {device}")
        
        # 尝试找相似的DEVICE
        similar_devices = []
        device_base = device.split('_')[0] if '_' in device else device
        for spec_device in spec_devices:
            spec_base = spec_device.split('_')[0] if '_' in spec_device else spec_device
            if device_base == spec_base or device in spec_device or spec_device in device:
                similar_devices.append(spec_device)
        
        if similar_devices:
            print(f"    可能的匹配: {similar_devices[:3]}")
    
    if len(unmatched_devices) > 10:
        print(f"  ... 还有 {len(unmatched_devices) - 10} 种无法匹配的DEVICE")
    
    # 具体测试一个批次
    print(f"\n=== 🧪 具体批次分析 ===")
    if wait_lots:
        test_lot = wait_lots[0]
        lot_device = test_lot.get('DEVICE', '').strip()
        lot_stage = test_lot.get('STAGE', '').strip()
        
        print(f"测试批次: {test_lot.get('LOT_ID')}")
        print(f"DEVICE: '{lot_device}'")
        print(f"STAGE: '{lot_stage}'")
        
        # 查找可能的匹配
        possible_matches = []
        for spec in released_specs:
            spec_device = spec.get('DEVICE', '').strip()
            spec_stage = spec.get('STAGE', '').strip()
            
            # 检查是否有任何相似性
            if lot_device == spec_device:
                possible_matches.append((spec_device, spec_stage, "完全匹配DEVICE"))
            elif lot_device in spec_device or spec_device in lot_device:
                possible_matches.append((spec_device, spec_stage, "部分匹配DEVICE"))
        
        print(f"\n可能的匹配 (前10个):")
        for spec_device, spec_stage, match_type in possible_matches[:10]:
            print(f"  {spec_device} + {spec_stage} ({match_type})")
        
        if not possible_matches:
            print("  ❌ 没有找到任何可能的匹配")
    
    # 统计分析
    print(f"\n=== 📈 统计分析 ===")
    device_match_rate = len(matched_devices) / len(wait_devices) * 100 if wait_devices else 0
    print(f"DEVICE匹配率: {device_match_rate:.1f}%")
    
    # 推荐解决方案
    print(f"\n=== 💡 推荐解决方案 ===")
    if len(matched_devices) > 0:
        print(f"1. 立即可用: {len(matched_devices)} 种DEVICE有匹配，建议优先处理这些批次")
    
    if len(unmatched_devices) > 0:
        print(f"2. 数据补全: {len(unmatched_devices)} 种DEVICE需要在测试规范表中补充")
        print(f"3. 可能需要建立DEVICE映射表处理命名差异")
    
    if device_match_rate < 50:
        print(f"4. 建议联系业务部门确认DEVICE命名规范和数据完整性")
        
except Exception as e:
    print(f"❌ 分析失败: {e}")
    import traceback
    traceback.print_exc() 