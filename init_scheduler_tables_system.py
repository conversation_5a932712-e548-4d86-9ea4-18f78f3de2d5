#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
在aps_system数据库中初始化调度器表
解决"邮件调度器未初始化"的问题

Author: AI Assistant
Date: 2025-06-22
"""

import os
import sys
import logging
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models import SchedulerJob, SchedulerJobLog, SchedulerConfig
from config import Config

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)-8s | %(name)s | %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('logs/scheduler_init_system.log', encoding='utf-8')
    ]
)

logger = logging.getLogger(__name__)

def init_scheduler_tables_in_system_db():
    """在aps_system数据库中初始化调度器表"""
    try:
        logger.info("开始在aps_system数据库中初始化调度器表...")
        
        # 创建Flask应用
        app, socketio = create_app(Config)
        
        with app.app_context():
            # 检查系统数据库连接
            try:
                # 测试系统数据库连接
                system_engine = db.engines['system']
                with system_engine.connect() as connection:
                    connection.execute(db.text('SELECT 1'))
                logger.info("✅ aps_system数据库连接正常")
            except Exception as e:
                logger.error(f"❌ aps_system数据库连接失败: {e}")
                return False
            
            # 在系统数据库中创建调度器相关表
            try:
                # 创建调度器相关的表（绑定到system数据库）
                SchedulerJob.__table__.create(system_engine, checkfirst=True)
                logger.info("✅ 在aps_system中创建 scheduler_jobs 表")
                
                SchedulerJobLog.__table__.create(system_engine, checkfirst=True)
                logger.info("✅ 在aps_system中创建 scheduler_job_logs 表")
                
                SchedulerConfig.__table__.create(system_engine, checkfirst=True)
                logger.info("✅ 在aps_system中创建 scheduler_config 表")
                
                # 创建APScheduler自己的作业存储表（在系统数据库中）
                with system_engine.connect() as connection:
                    connection.execute(db.text('''
                        CREATE TABLE IF NOT EXISTS apscheduler_jobs (
                            id VARCHAR(191) NOT NULL PRIMARY KEY,
                            next_run_time DOUBLE,
                            job_state LONGBLOB NOT NULL
                        )
                    '''))
                    connection.commit()
                logger.info("✅ 在aps_system中创建 apscheduler_jobs 表")
                
            except Exception as e:
                logger.error(f"❌ 在aps_system中创建表失败: {e}")
                return False
            
            # 初始化默认配置（在系统数据库中）
            try:
                default_configs = [
                    ('scheduler_enabled', 'true', '调度器是否启用'),
                    ('timezone', 'Asia/Shanghai', '时区设置'),
                    ('max_workers', '10', '最大工作线程数'),
                    ('coalesce', 'true', '是否合并错过的作业'),
                    ('max_instances', '3', '作业最大并发实例数'),
                    ('job_defaults_misfire_grace_time', '30', '作业错过执行的宽限时间(秒)')
                ]
                
                for key, value, description in default_configs:
                    existing = SchedulerConfig.query.filter_by(key=key).first()
                    if not existing:
                        config = SchedulerConfig(
                            key=key,
                            value=value,
                            description=description,
                            updated_by='system_init'
                        )
                        db.session.add(config)
                        logger.info(f"✅ 在aps_system中添加默认配置: {key} = {value}")
                
                db.session.commit()
                logger.info("✅ aps_system数据库中的默认配置初始化完成")
                
            except Exception as e:
                logger.error(f"❌ 在aps_system中初始化默认配置失败: {e}")
                db.session.rollback()
                return False
            
            # 验证系统数据库中的表创建
            try:
                from sqlalchemy import inspect
                system_inspector = inspect(system_engine)
                system_tables = system_inspector.get_table_names()
                
                required_tables = ['scheduler_jobs', 'scheduler_job_logs', 'scheduler_config', 'apscheduler_jobs']
                missing_tables = [table for table in required_tables if table not in system_tables]
                
                if missing_tables:
                    logger.warning(f"⚠️ aps_system数据库中以下表未创建: {missing_tables}")
                else:
                    logger.info("✅ aps_system数据库中所有调度器表创建成功")
                
                # 验证配置数据
                config_count = SchedulerConfig.query.count()
                logger.info(f"✅ aps_system数据库中调度器配置记录数: {config_count}")
                
                # 显示数据库信息
                logger.info(f"✅ 系统数据库URI: {app.config['SQLALCHEMY_BINDS']['system']}")
                
            except Exception as e:
                logger.error(f"❌ 验证aps_system数据库表创建失败: {e}")
                return False
        
        logger.info("🎉 aps_system数据库中调度器表初始化完成！")
        return True
        
    except Exception as e:
        logger.error(f"❌ 在aps_system数据库中初始化调度器表失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def test_scheduler_service_with_system_db():
    """测试调度器服务（使用系统数据库）"""
    try:
        logger.info("开始测试调度器服务（系统数据库模式）...")
        
        # 创建Flask应用
        app, socketio = create_app(Config)
        
        with app.app_context():
            from app.services.scheduler_service import scheduler_service
            
            # 测试调度器初始化
            logger.info(f"调度器实例: {scheduler_service}")
            logger.info(f"调度器运行状态: {scheduler_service.is_running}")
            
            # 检查调度器配置
            enabled = SchedulerConfig.get_config('scheduler_enabled', 'false')
            logger.info(f"调度器配置状态: {enabled}")
            
            # 如果调度器未运行，尝试启动
            if not scheduler_service.is_running:
                logger.info("尝试启动调度器...")
                result = scheduler_service.start()
                logger.info(f"调度器启动结果: {result}")
                
                if result:
                    logger.info("✅ 调度器启动成功")
                    # 获取调度器状态
                    jobs = scheduler_service.get_all_jobs()
                    logger.info(f"当前任务数量: {len(jobs)}")
                    
                    # 显示作业存储信息
                    logger.info(f"APScheduler作业存储配置: 使用aps_system数据库")
                else:
                    logger.error("❌ 调度器启动失败")
                    return False
            else:
                logger.info("✅ 调度器已在运行")
        
        logger.info("🎉 调度器服务测试完成（系统数据库模式）！")
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试调度器服务失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

if __name__ == '__main__':
    print("=== 调度器数据库表初始化工具（aps_system模式）===")
    print()
    
    # 确保日志目录存在
    os.makedirs('logs', exist_ok=True)
    
    # 步骤1: 在aps_system数据库中初始化调度器表
    print("步骤1: 在aps_system数据库中初始化调度器表...")
    if init_scheduler_tables_in_system_db():
        print("✅ aps_system数据库表初始化成功")
    else:
        print("❌ aps_system数据库表初始化失败")
        sys.exit(1)
    
    print()
    
    # 步骤2: 测试调度器服务
    print("步骤2: 测试调度器服务（系统数据库模式）...")
    if test_scheduler_service_with_system_db():
        print("✅ 调度器服务测试成功")
    else:
        print("❌ 调度器服务测试失败")
        sys.exit(1)
    
    print()
    print("🎉 调度器初始化完成（aps_system模式）！现在可以重启应用，调度器应该能正常工作。")
    print()
    print("建议操作:")
    print("1. 重启Flask应用")
    print("2. 访问系统设置页面检查调度器状态")
    print("3. 查看日志文件: logs/scheduler_init_system.log")
    print()
    print("重要提醒:")
    print("- 调度器相关表现在正确创建在aps_system数据库中")
    print("- APScheduler作业存储也使用aps_system数据库")
    print("- 用户和权限数据与调度器数据在同一个系统数据库中") 