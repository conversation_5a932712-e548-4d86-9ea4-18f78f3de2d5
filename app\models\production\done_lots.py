"""
已排产批次数据模型
对应lotprioritydone.xlsx文件
"""
from app import db
from datetime import datetime

class DoneLot(db.Model):
    """已排产批次模型 - 对应lotprioritydone表"""
    __tablename__ = 'lotprioritydone'
    __table_args__ = {'extend_existing': True}
    
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    lot_id = db.Column(db.String(50), nullable=False, comment='批次号')
    device = db.Column(db.String(100), nullable=True, comment='产品名称')
    stage = db.Column(db.String(20), nullable=True, comment='工序')
    quantity = db.Column(db.Integer, nullable=True, comment='数量')
    pkg_pn = db.Column(db.String(100), nullable=True, comment='封装料号')
    chip_id = db.Column(db.String(100), nullable=True, comment='芯片ID')
    priority = db.Column(db.Integer, nullable=True, comment='优先级')
    scheduled_start_time = db.Column(db.DateTime, nullable=True, comment='排产开始时间')
    scheduled_end_time = db.Column(db.DateTime, nullable=True, comment='排产结束时间')
    actual_start_time = db.Column(db.DateTime, nullable=True, comment='实际开始时间')
    actual_end_time = db.Column(db.DateTime, nullable=True, comment='实际结束时间')
    equipment_id = db.Column(db.String(50), nullable=True, comment='设备ID')
    status = db.Column(db.String(20), nullable=True, default='SCHEDULED', comment='状态')
    completion_rate = db.Column(db.Float, nullable=True, comment='完成率')
    user = db.Column(db.String(50), nullable=True, comment='操作用户')
    refresh_time = db.Column(db.DateTime, nullable=True, comment='刷新时间')
    created_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow, comment='创建时间')
    updated_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow, comment='更新时间')
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'lot_id': self.lot_id,
            'device': self.device,
            'stage': self.stage,
            'quantity': self.quantity,
            'pkg_pn': self.pkg_pn,
            'chip_id': self.chip_id,
            'priority': self.priority,
            'scheduled_start_time': self.scheduled_start_time.isoformat() if self.scheduled_start_time else None,
            'scheduled_end_time': self.scheduled_end_time.isoformat() if self.scheduled_end_time else None,
            'actual_start_time': self.actual_start_time.isoformat() if self.actual_start_time else None,
            'actual_end_time': self.actual_end_time.isoformat() if self.actual_end_time else None,
            'equipment_id': self.equipment_id,
            'status': self.status,
            'completion_rate': self.completion_rate,
            'user': self.user,
            'refresh_time': self.refresh_time.isoformat() if self.refresh_time else None,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
    
    def __repr__(self):
        return f'<DoneLot {self.lot_id}>' 