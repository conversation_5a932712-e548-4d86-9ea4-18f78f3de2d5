# Redis配置指南

## 问题描述
APS系统中的某些功能（如事件总线、任务管理、实时通信）依赖Redis服务。

## 解决方案

### 方案一：安装Redis模块（推荐）
```bash
pip install redis
```

### 方案二：安装并启动Redis服务器
1. 下载Redis：https://redis.io/download
2. Windows用户可以使用：https://github.com/microsoftarchive/redis/releases
3. 启动Redis服务器：
   ```bash
   redis-server
   ```

### 方案三：使用云Redis服务
- 阿里云Redis
- 腾讯云Redis
- AWS ElastiCache

## 配置Redis连接
在系统设置中配置Redis连接字符串：
```
redis://localhost:6379/0
```

## 功能影响
如果没有Redis，以下功能会受到影响：
- 实时事件通知
- 任务队列管理
- WebSocket实时推送
- 用户会话管理

## 临时解决方案
系统已自动启用模拟模式，核心功能不受影响。
