# APS排产逻辑详细说明文档

## 概述
APS（Advanced Production Scheduling）车规芯片终测智能调度平台的排产系统是一个复杂的多层架构，通过智能算法将待排产批次转换为已排产批次。本文档详细分析排产逻辑的每个环节。

## 1. 系统架构概览

### 1.1 核心数据表
```
输入表：
- ET_WAIT_LOT：待排产批次表
- ET_FT_TEST_SPEC：测试规范表
- ET_RECIPE_FILE：工艺配方表  
- EQP_STATUS：设备状态表
- ET_UPH_EQP：设备效率表
- devicepriorityconfig：设备优先级配置表
- lotpriorityconfig：批次优先级配置表

输出表：
- lotprioritydone：已排产批次表（最终结果）
```

### 1.2 核心服务模块
- `IntelligentSchedulingService`：智能排产服务
- `ManualSchedulingService`：手动排产服务  
- `DataSourceManager`：数据源管理器
- `DoneLot` Model：已排产批次数据模型

## 2. 排产流程详细分析

### 2.1 排产入口 - API层

#### 文件：`app/api_v2/production/routes.py`
```python
@production_bp.route('/scheduling/execute', methods=['POST'])
@login_required
def execute_intelligent_scheduling():
    """执行智能排产"""
    data = request.get_json()
    algorithm = data.get('algorithm', 'intelligent')
    optimization_target = data.get('optimization_target', 'balanced')
    
    # 执行排产
    result = scheduling_service.execute_intelligent_scheduling({
        'algorithm': algorithm,
        'optimization_target': optimization_target
    })
```

**说明：**
- 支持多种排产算法：`deadline`、`product`、`value`、`intelligent`
- 支持优化目标：`time`、`balanced`、`efficiency`
- 返回排产结果和执行状态

### 2.2 核心排产引擎

#### 文件：`app/services/intelligent_scheduling_service.py`

**主要函数：**
```python
def execute_intelligent_scheduling(self, algorithm: str = 'intelligent', 
                                 optimization_target: str = 'balanced') -> Dict[str, Any]:
```

**执行步骤：**

1. **数据源初始化**
```python
from app.services.data_source_manager import DataSourceManager
data_manager = DataSourceManager()
data_source_status = data_manager.get_data_source_status()
```

2. **数据收集阶段（6个核心步骤）**

   **步骤1：获取待排产批次**
   ```python
   wait_lots, wait_lots_source = data_manager.get_wait_lot_data()
   ```
   - 查询 `ET_WAIT_LOT` 表
   - 筛选条件：`GOOD_QTY > 0` 且 `LOT_ID IS NOT NULL`
   - 排序：按 `CREATE_TIME ASC`

   **步骤2：获取测试规范**
   ```python
   test_specs, test_specs_source = data_manager.get_test_spec_data()
   ```
   - 查询 `ET_FT_TEST_SPEC` 表
   - 建立多级匹配键：设备+工序+封装、设备+工序、设备

   **步骤3：获取工艺配方**
   ```python
   recipe_files, recipe_files_source = data_manager.get_recipe_file_data()
   ```
   - 查询 `ET_RECIPE_FILE` 表
   - 提供设备配方信息

   **步骤4：获取设备状态**
   ```python
   equipment_status, equipment_status_source = data_manager.get_equipment_status_data()
   ```
   - 查询 `EQP_STATUS` 表
   - 筛选可用设备

   **步骤5：获取UPH数据**
   ```python
   uph_data, uph_data_source = data_manager.get_uph_data()
   ```
   - 查询 `ET_UPH_EQP` 表
   - 计算生产效率

   **步骤6：获取优先级配置**
   ```python
   priority_configs, priority_configs_source = data_manager.get_priority_configs()
   ```
   - 查询 `devicepriorityconfig` 和 `lotpriorityconfig` 表

3. **综合排产算法执行**
```python
schedule_result = self._execute_comprehensive_scheduling(
    wait_lots, test_specs, recipe_files, equipment_status, 
    uph_data, priority_configs, algorithm, optimization_target
)
```

### 2.3 智能匹配算法详细分析

#### 函数：`_execute_comprehensive_scheduling`

**核心逻辑：**

1. **数据匹配**
```python
for lot in wait_lots:
    matched_data = self._match_comprehensive_data(
        lot, test_specs, recipe_files, equipment_status, uph_data
    )
```

2. **优先级计算**
```python
priority_score = self._calculate_comprehensive_priority(
    lot, priority_configs, matched_data, algorithm
)
```

3. **时间估算**
```python
estimated_completion = self._calculate_estimated_completion(
    lot, matched_data['uph_info'], matched_data['test_spec']
)
```

4. **排序和设备分配**
```python
sorted_lots = self._sort_by_comprehensive_algorithm(
    enriched_lots, algorithm, optimization_target
)
```

### 2.4 匹配逻辑详解

#### 函数：`_match_comprehensive_data`

**匹配策略：**
```python
def _find_best_match(self, data_dict: Dict, keys: List[str], default: Dict) -> Dict:
    """多级匹配策略"""
    for key in keys:
        if key in data_dict:
            return data_dict[key]
    return default
```

**匹配键优先级：**
1. 完全匹配：`{DEVICE}|{STAGE}|{PKG_PN}|{CHIP_ID}`
2. 三要素匹配：`{DEVICE}|{STAGE}|{PKG_PN}`
3. 二要素匹配：`{DEVICE}|{STAGE}`
4. 单要素匹配：`{DEVICE}`

### 2.5 最终结果生成

#### 函数：`_generate_final_schedule_result`

**输出格式（按 lotprioritydone 表结构）：**
```python
schedule_item = {
    'ORDER': index + 1,                           # 排产顺序
    'HANDLER_ID': best_equipment.get('HANDLER_ID'), # 设备ID
    'LOT_ID': lot['LOT_ID'],                     # 批次号
    'LOT_TYPE': 'PRODUCTION',                    # 批次类型
    'GOOD_QTY': lot['GOOD_QTY'],                # 良品数量
    'PROD_ID': lot.get('DEVICE', ''),           # 产品ID
    'DEVICE': lot['DEVICE'],                     # 设备名称
    'CHIP_ID': lot['CHIP_ID'],                  # 芯片ID
    'PKG_PN': lot['PKG_PN'],                    # 封装料号
    'STAGE': lot['STAGE'],                       # 工序
    'RELEASE_TIME': estimated_start_time,        # 发布时间
    'FAC_ID': lot.get('FAC_ID', 'YX'),          # 工厂ID
    'CREATE_TIME': datetime.now(),               # 创建时间
    # 扩展信息
    'priority_score': lot['priority_score'],     # 优先级分数
    'estimated_hours': estimated_hours,          # 预计耗时
    'uph': lot['uph_info']['UPH'],              # 每小时产量
}
```

## 3. 数据源管理机制

### 3.1 智能数据源切换

#### 文件：`app/services/data_source_manager.py`

**核心特性：**
- MySQL 优先，Excel 备用
- 自动检测数据源可用性
- 透明的数据源切换

**数据获取策略：**
```python
def get_wait_lot_data(self) -> Tuple[List[Dict], str]:
    """获取待排产批次数据"""
    try:
        # 优先从MySQL获取
        if self.mysql_available:
            data = self._get_wait_lot_from_mysql()
            if data:
                return data, 'MySQL'
        
        # 备用Excel数据源
        if self.excel_available:
            data = self._get_wait_lot_from_excel()
            return data, 'Excel'
    except Exception as e:
        logger.error(f"数据获取失败: {e}")
    
    return [], 'None'
```

### 3.2 数据查询细节

#### MySQL查询示例（待排产批次）：
```sql
SELECT 
    LOT_ID, DEVICE, STAGE, GOOD_QTY, PKG_PN, CHIP_ID,
    CREATE_TIME, FAC_ID, FLOW_ID, FLOW_VER,
    WIP_STATE, PROC_STATE, HOLD_STATE
FROM `v_et_wait_lot_unified`
WHERE GOOD_QTY > 0
  AND LOT_ID IS NOT NULL 
  AND LOT_ID != ''
ORDER BY CREATE_TIME ASC
```

#### 设备状态查询：
```sql
SELECT 
    EQP_ID, HANDLER_ID, TESTER_ID, STATUS, 
    CAPABILITY, LOCATION, UPDATE_TIME
FROM `v_eqp_status_unified`
WHERE STATUS = 'Available' OR STATUS = 'Idle'
```

## 4. 手动排产逻辑

### 4.1 手动排产服务

#### 文件：`app/services/manual_scheduling_service.py`

**执行流程：**
```python
def execute_manual_scheduling(self, algorithm: str = 'intelligent', 
                             optimization_target: str = 'balanced', 
                             user_id: str = 'system') -> Dict[str, Any]:
```

**步骤：**
1. 创建排产历史记录
2. 获取待排产批次（直接从MySQL）
3. 获取可用设备资源
4. 获取UPH数据和测试规范
5. 智能匹配和排序
6. 生成排产记录
7. 保存到 lotprioritydone 表

### 4.2 设备匹配算法

```python
def _find_compatible_equipment(self, lot: Dict, equipment_list: List[Dict]) -> List[Dict]:
    """查找兼容设备"""
    compatible = []
    for equipment in equipment_list:
        # 检查设备能力
        if self._check_equipment_capability(equipment, lot):
            compatible.append(equipment)
    return compatible
```

## 5. 已排产批次管理

### 5.1 数据模型

#### 文件：`app/models/production/done_lots.py`

```python
class DoneLot(db.Model):
    """已排产批次模型"""
    __tablename__ = 'lotprioritydone'
    
    id = db.Column(db.Integer, primary_key=True)
    lot_id = db.Column(db.String(50), nullable=False)
    device = db.Column(db.String(100), nullable=True)
    stage = db.Column(db.String(20), nullable=True)
    quantity = db.Column(db.Integer, nullable=True)
    priority = db.Column(db.Integer, nullable=True)
    scheduled_start_time = db.Column(db.DateTime, nullable=True)
    equipment_id = db.Column(db.String(50), nullable=True)
    status = db.Column(db.String(20), default='SCHEDULED')
    completion_rate = db.Column(db.Float, nullable=True)
```

### 5.2 数据库表结构

#### 表：`lotprioritydone`

```sql
CREATE TABLE `lotprioritydone` (
  `id` int NOT NULL AUTO_INCREMENT,
  `PRIORITY` varchar(50) DEFAULT NULL COMMENT '优先级',
  `HANDLER_ID` varchar(100) DEFAULT NULL COMMENT '操作员ID',
  `LOT_ID` varchar(100) DEFAULT NULL COMMENT '批次ID',
  `LOT_TYPE` varchar(50) DEFAULT NULL COMMENT '批次类型',
  `GOOD_QTY` int DEFAULT NULL COMMENT '良品数量',
  `DEVICE` varchar(100) DEFAULT NULL COMMENT '设备',
  `STAGE` varchar(50) DEFAULT NULL COMMENT '阶段',
  `RELEASE_TIME` datetime DEFAULT NULL COMMENT '发布时间',
  `CREATE_TIME` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_lot_id` (`LOT_ID`),
  KEY `idx_device` (`DEVICE`),
  KEY `idx_priority` (`PRIORITY`)
);
```

### 5.3 API接口

#### 文件：`app/api_v2/production/done_lots_api.py`

**查询接口：**
```python
@done_lots_api.route('/api/v2/resources', methods=['GET'])
def get_lotprioritydone_data():
    """获取已排产表数据"""
    query = text("""
        SELECT 
            PRIORITY, HANDLER_ID, LOT_ID, LOT_TYPE, GOOD_QTY,
            PROD_ID, DEVICE, CHIP_ID, PKG_PN, STAGE,
            RELEASE_TIME, FAC_ID, CREATE_TIME
        FROM lotprioritydone 
        ORDER BY PRIORITY ASC, CREATE_TIME DESC
        LIMIT :size OFFSET :offset
    """)
```

**批次移动接口：**
```python
@done_lots_api.route('/api/v2/production/done-lots/move-to-waiting', methods=['POST'])
def move_lots_to_waiting():
    """将已排产批次移回待排产状态"""
```

## 6. 前端展示层

### 6.1 已排产批次页面

#### 文件：`app/templates/production/done_lots.html`

**核心功能：**
- 数据表格展示
- 分页导航
- 批次操作（移回待排产）
- 数据导出

**JavaScript功能：**
```javascript
function moveLotsToWaiting(ids) {
    fetch('/api/v2/production/done-lots/move-to-waiting', {
        method: 'POST',
        headers: {'Content-Type': 'application/json'},
        body: JSON.stringify({ ids: ids })
    })
}
```

### 6.2 自动排产页面

#### 文件：`app/templates/production/auto.html`

**主要功能：**
- 排产算法选择
- 实时进度显示
- 结果展示和导出

## 7. 优先级配置机制

### 7.1 设备优先级配置

#### 表：`devicepriorityconfig`
```sql
CREATE TABLE `devicepriorityconfig` (
  `id` int NOT NULL AUTO_INCREMENT,
  `device` varchar(100) DEFAULT NULL COMMENT '设备名称',
  `stage` varchar(50) DEFAULT NULL COMMENT '工序',
  `priority` int DEFAULT NULL COMMENT '优先级',
  `capability` varchar(100) DEFAULT NULL COMMENT '设备能力',
  `from_time` datetime DEFAULT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间'
);
```

### 7.2 批次优先级配置

#### 表：`lotpriorityconfig`
```sql
CREATE TABLE `lotpriorityconfig` (
  `id` int NOT NULL AUTO_INCREMENT,
  `device` varchar(100) DEFAULT NULL COMMENT '产品',
  `stage` varchar(50) DEFAULT NULL COMMENT '工序',
  `priority` int DEFAULT NULL COMMENT '优先级'
);
```

## 8. 算法策略分析

### 8.1 算法类型

1. **deadline（交期优先）**
   - 按交期紧急程度排序
   - 优先安排交期紧的批次

2. **product（产品优先）**
   - 按产品重要性排序
   - 考虑产品价值和客户等级

3. **value（价值优先）**
   - 按产值/利润排序
   - 优先安排高价值批次

4. **intelligent（智能算法）**
   - 综合考虑多因素
   - 平衡效率、交期、价值

### 8.2 优化目标

1. **time（时间优先）**
   - 最小化总完成时间
   - 快速排产

2. **balanced（平衡模式）**
   - 平衡各项指标
   - 综合最优

3. **efficiency（效率优先）**
   - 最大化设备利用率
   - 减少设备空闲

## 9. 数据流程图

```
待排产批次(ET_WAIT_LOT) → 数据源管理器 → 智能匹配算法
        ↓                      ↓              ↓
测试规范(ET_FT_TEST_SPEC) → 综合排产引擎 → 优先级计算
        ↓                      ↓              ↓
设备状态(EQP_STATUS) → 设备分配算法 → 时间估算
        ↓                      ↓              ↓
UPH数据(ET_UPH_EQP) → 排序算法 → 已排产结果
        ↓                      ↓              ↓
优先级配置 → 最终生成 → lotprioritydone表
```

## 10. 关键性能指标

### 10.1 排产质量指标
- 设备利用率
- 交期达成率
- 批次完成率
- 优先级匹配度

### 10.2 系统性能指标
- 排产算法执行时间
- 数据查询响应时间
- 并发处理能力

## 11. 错误处理和容错机制

### 11.1 数据源容错
- MySQL异常时自动切换Excel
- 数据格式异常处理
- 缺失数据补充策略

### 11.2 算法容错
- 设备不足时的降级策略
- 无匹配规范时的默认处理
- 计算异常时的兜底方案

## 🚨 **重大问题发现：设备分配逻辑缺陷**

### **您发现的问题完全正确！**

经过深入分析，我发现当前系统存在**严重的设备分配缺陷**：

#### **❌ 错误的逻辑（当前代码问题）**

在 `IntelligentSchedulingService._generate_final_schedule_result()` 函数第753行：

```python
'HANDLER_ID': best_equipment.get('HANDLER_ID', f'H{(index % 10) + 1:02d}') if best_equipment else f'H{(index % 10) + 1:02d}',
```

**问题分析：**
1. **缺乏真正的设备匹配**：如果没有找到最佳设备，就简单地按 `index % 10` 循环分配
2. **结果错误**：所有批次按顺序分配给 H01→H02→H03...→H10→H01...
3. **没有负载均衡**：完全忽略设备的实际工作负载
4. **缺乏智能决策**：不考虑批次与设备的匹配度

### **✅ 正确的排产逻辑应该是：**

#### **1. 设备匹配评分算法** 
**每个批次都应该通过科学的评分系统选择最佳设备：**

```python
设备匹配分数 = 产品匹配(40分) + 工序匹配(30分) + 封装匹配(15分) + 设备类型匹配(15分)
```

#### **2. 负载均衡策略**
**综合考虑匹配度和设备负载：**

```python
最终评分 = 匹配分数 × 0.7 + 负载权重 × 0.3
```

#### **3. 具体批次示例**

假设批次 `JW5116F_001`：
- **DEVICE**: JW5116F  
- **STAGE**: FT
- **PKG_PN**: QFN48
- **GOOD_QTY**: 2500

**设备A (T01-H01)**：
- 产品完全匹配 +40分
- 工序完全匹配 +30分  
- 封装匹配 +15分
- 当前负载 2小时
- **综合评分**: 88.9分 ✅

**设备B (T02-H02)**：
- 产品不匹配 +0分
- 工序匹配 +30分
- 封装不匹配 +0分  
- 当前负载 0小时
- **综合评分**: 51分

**正确结果**：选择设备A，因为综合评分更高

#### **4. 同设备批次排序**

当多个批次分配到同一设备时：

- **deadline算法**：紧急度 > 小批次优先 > 时间
- **value算法**：价值 > 大批次优先 > 时间  
- **intelligent算法**：综合分数 > 匹配质量 > 时间

### **🔧 根本问题和解决方案**

**当前系统的核心缺陷：**
1. ❌ 设备匹配算法不完善
2. ❌ 缺乏负载均衡机制
3. ❌ 优先级排序过于简化
4. ❌ 没有真正的智能决策

**修复建议：**
1. ✅ 实现完整的设备匹配评分算法
2. ✅ 添加设备负载跟踪和均衡逻辑
3. ✅ 完善同设备批次的优先级排序
4. ✅ 增加匹配质量验证和反馈机制

### **📊 正确的排产结果示例**

```python
# 3个批次的正确分配结果
已排产结果 = [
    {
        'ORDER': 1,
        'LOT_ID': 'JW5116F_001', 
        'HANDLER_ID': 'H01',      # 匹配度最高的设备
        'TESTER_ID': 'T01',
        'match_score': 85,        # 高匹配度
        'estimated_hours': 1.67
    },
    {
        'ORDER': 2, 
        'LOT_ID': 'JW5116F_002',
        'HANDLER_ID': 'H01',      # 同产品，排在H01的第二位
        'TESTER_ID': 'T01', 
        'match_score': 85,
        'estimated_hours': 1.20
    },
    {
        'ORDER': 3,
        'LOT_ID': 'JW7106_001',
        'HANDLER_ID': 'H02',      # 不同产品，分配给匹配的H02
        'TESTER_ID': 'T02',
        'match_score': 85,
        'estimated_hours': 2.67
    }
]

设备负载 = {
    'T01-H01': 2.87小时,     # 1.67 + 1.20
    'T02-H02': 2.67小时      # 2.67  
}
```

这才是**真正的智能排产逻辑**！您发现的问题揭示了系统设计的根本缺陷。

## 12. 总结

APS排产系统**应该**通过以下关键步骤实现智能排产：

1. **数据收集**：从6个核心表获取完整信息
2. **智能匹配**：多级匹配策略确保最佳配对
3. **科学评分**：设备匹配分数 + 负载均衡权重
4. **智能分配**：真正的设备选择和时间规划
5. **优化排序**：同设备批次的二次优先级排序
6. **结果生成**：标准化输出到已排产表

**但目前系统存在严重缺陷，需要重新设计设备分配算法！** 