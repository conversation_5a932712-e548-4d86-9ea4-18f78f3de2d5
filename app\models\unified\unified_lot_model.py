"""
统一批次管理模型

整合LOT_WIP、WIP_LOT、ET_WAIT_LOT三个冗余表的功能，
提供统一的批次数据管理接口。

设计原则：
1. 以ET_WAIT_LOT为基础（使用频率最高）
2. 整合LOT_WIP的设备分配功能
3. 通过JSON字段存储WIP_LOT的扩展数据
4. 保持向后兼容性
"""

from datetime import datetime
from app import db
import json


class UnifiedLotModel(db.Model):
    """统一批次管理模型"""
    __tablename__ = 'unified_lot_management'
    
    # 核心标识
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    LOT_ID = db.Column(db.String(50), nullable=False, unique=True, index=True, comment='批次号')
    
    # 产品信息 (来自三表共同字段)
    DEVICE = db.Column(db.String(50), nullable=True, index=True, comment='产品型号')
    CHIP_ID = db.Column(db.String(50), nullable=True, comment='芯片ID')
    PKG_PN = db.Column(db.String(50), nullable=True, comment='封装型号')
    PROD_ID = db.Column(db.String(50), nullable=True, comment='产品ID')
    
    # 工艺信息
    STAGE = db.Column(db.String(50), nullable=True, index=True, comment='工序')
    FLOW_ID = db.Column(db.String(50), nullable=True, comment='流程ID')
    FLOW_VER = db.Column(db.Integer, nullable=True, comment='流程版本')
    
    # 数量信息
    GOOD_QTY = db.Column(db.Integer, nullable=True, comment='良品数量')
    LOT_QTY = db.Column(db.Integer, nullable=True, comment='批次数量')
    LOT_IN_QTY = db.Column(db.Integer, nullable=True, comment='批次入库数量')
    LOT_OUT_QTY = db.Column(db.Integer, nullable=True, comment='批次出库数量')
    NG_QTY = db.Column(db.Integer, nullable=True, comment='不良品数量')
    
    # 状态信息
    WIP_STATE = db.Column(db.String(50), nullable=True, comment='在制品状态')
    PROC_STATE = db.Column(db.String(50), nullable=True, comment='工艺状态')
    HOLD_STATE = db.Column(db.String(50), nullable=True, comment='暂停状态')
    LOT_TYPE = db.Column(db.String(50), nullable=True, comment='批次类型')
    
    # 设备分配 (来自LOT_WIP)
    HANDLER_ID = db.Column(db.String(50), nullable=True, comment='分选机ID')
    TESTER_ID = db.Column(db.String(50), nullable=True, comment='测试机ID')
    EQP_ID = db.Column(db.String(50), nullable=True, comment='设备ID')
    
    # 优先级和调度
    PRIORITY_LEVEL = db.Column(db.String(20), default='medium', comment='优先级等级(high/medium/low)')
    PRIORITY_ORDER = db.Column(db.Integer, default=999, comment='优先级排序(数字越小优先级越高)')
    HOT_TYPE = db.Column(db.String(50), nullable=True, comment='热点类型/优先级')
    
    # 时间信息
    RELEASE_TIME = db.Column(db.String(50), nullable=True, comment='释放时间')
    PLAN_START_DATE = db.Column(db.DateTime, nullable=True, comment='计划开始日期')
    PLAN_DUE_DATE = db.Column(db.DateTime, nullable=True, comment='计划交期')
    JOB_START_TIME = db.Column(db.String(50), nullable=True, comment='作业开始时间')
    JOB_END_TIME = db.Column(db.String(50), nullable=True, comment='作业结束时间')
    
    # 订单信息
    PO_ID = db.Column(db.String(50), nullable=True, comment='采购单ID')
    WORK_ORDER_ID = db.Column(db.String(50), nullable=True, comment='工单ID')
    WORK_ORDER_VER = db.Column(db.Integer, nullable=True, comment='工单版本')
    
    # 工厂信息
    FAC_ID = db.Column(db.String(50), nullable=True, comment='工厂ID')
    SUB_FAC = db.Column(db.String(50), nullable=True, comment='子工厂')
    AREA_ID = db.Column(db.String(50), nullable=True, comment='区域ID')
    
    # 批次关系
    ROOT_LOT_ID = db.Column(db.String(50), nullable=True, comment='根批次ID')
    PARENT_LOT_ID = db.Column(db.String(50), nullable=True, comment='父批次ID')
    CHILD_LOT_ID = db.Column(db.String(50), nullable=True, comment='子批次ID')
    CUST_LOT_ID = db.Column(db.String(50), nullable=True, comment='客户批次ID')
    
    # 扩展信息 (JSON格式存储WIP_LOT的额外字段)
    extended_data = db.Column(db.Text, nullable=True, comment='扩展数据JSON')
    
    # 数据源跟踪
    source_table = db.Column(db.String(20), nullable=True, comment='数据来源表')
    migration_status = db.Column(db.String(20), default='pending', comment='迁移状态')
    data_version = db.Column(db.Integer, default=1, comment='数据版本')
    
    # 审计字段
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='创建时间')
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='更新时间')
    created_by = db.Column(db.String(64), nullable=True, comment='创建人')
    updated_by = db.Column(db.String(64), nullable=True, comment='更新人')
    
    # 索引定义
    __table_args__ = (
        db.Index('idx_unified_lot_device_stage', 'DEVICE', 'STAGE'),
        db.Index('idx_unified_lot_priority', 'PRIORITY_LEVEL', 'PRIORITY_ORDER'),
        db.Index('idx_unified_lot_status', 'WIP_STATE', 'PROC_STATE'),
        db.Index('idx_unified_lot_equipment', 'HANDLER_ID', 'TESTER_ID'),
        db.Index('idx_unified_lot_time', 'PLAN_DUE_DATE'),
        {'comment': '统一批次管理表 - 整合LOT_WIP、WIP_LOT、ET_WAIT_LOT功能'}
    )
    
    def __repr__(self):
        return f'<UnifiedLotModel {self.LOT_ID}>'
    
    def to_dict(self):
        """转换为字典格式"""
        result = {}
        for column in self.__table__.columns:
            value = getattr(self, column.name)
            if value is not None:
                if isinstance(value, datetime):
                    result[column.name] = value.isoformat()
                else:
                    result[column.name] = value
        
        # 解析扩展数据
        if self.extended_data:
            try:
                extended = json.loads(self.extended_data)
                result['extended_data_parsed'] = extended
            except (json.JSONDecodeError, TypeError):
                result['extended_data_parsed'] = {}
        
        return result
    
    def set_extended_data(self, data_dict):
        """设置扩展数据"""
        if data_dict:
            self.extended_data = json.dumps(data_dict, ensure_ascii=False)
        else:
            self.extended_data = None
    
    def get_extended_data(self):
        """获取扩展数据"""
        if self.extended_data:
            try:
                return json.loads(self.extended_data)
            except (json.JSONDecodeError, TypeError):
                return {}
        return {}
    
    def update_from_et_wait_lot(self, et_wait_lot_record):
        """从ET_WAIT_LOT记录更新数据"""
        self.LOT_ID = et_wait_lot_record.LOT_ID
        self.LOT_TYPE = et_wait_lot_record.LOT_TYPE
        self.GOOD_QTY = et_wait_lot_record.GOOD_QTY
        self.PROD_ID = et_wait_lot_record.PROD_ID
        self.DEVICE = et_wait_lot_record.DEVICE
        self.CHIP_ID = et_wait_lot_record.CHIP_ID
        self.PKG_PN = et_wait_lot_record.PKG_PN
        self.PO_ID = et_wait_lot_record.PO_ID
        self.STAGE = et_wait_lot_record.STAGE
        self.WIP_STATE = et_wait_lot_record.WIP_STATE
        self.PROC_STATE = et_wait_lot_record.PROC_STATE
        self.HOLD_STATE = et_wait_lot_record.HOLD_STATE
        self.FLOW_ID = et_wait_lot_record.FLOW_ID
        self.FLOW_VER = et_wait_lot_record.FLOW_VER
        self.RELEASE_TIME = et_wait_lot_record.RELEASE_TIME
        self.FAC_ID = et_wait_lot_record.FAC_ID
        self.source_table = 'v_et_wait_lot_unified'
    
    def update_from_lot_wip(self, lot_wip_record):
        """从LOT_WIP记录更新数据"""
        self.LOT_ID = lot_wip_record.LOT_ID
        self.STAGE = lot_wip_record.STAGE
        self.PROD_ID = lot_wip_record.PROD_ID
        self.PKG_PN = lot_wip_record.PKG_PN
        self.HANDLER_ID = lot_wip_record.HANDLER_ID
        self.TESTER_ID = lot_wip_record.TESTER_ID
        self.DEVICE = lot_wip_record.DEVICE
        self.CHIP_ID = lot_wip_record.CHIP_ID
        if hasattr(lot_wip_record, 'ORDER_INDEX'):
            self.PRIORITY_ORDER = lot_wip_record.ORDER_INDEX
        self.source_table = 'LOT_WIP'
    
    def update_from_wip_lot(self, wip_lot_record):
        """从WIP_LOT记录更新数据"""
        # 更新核心字段
        self.LOT_ID = wip_lot_record.LOT_ID
        self.LOT_TYPE = wip_lot_record.LOT_TYPE
        self.LOT_QTY = wip_lot_record.LOT_QTY
        self.WIP_STATE = wip_lot_record.WIP_STATE
        self.PROC_STATE = wip_lot_record.PROC_STATE
        self.HOLD_STATE = wip_lot_record.HOLD_STATE
        self.DEVICE = wip_lot_record.DEVICE
        self.CHIP_ID = wip_lot_record.CHIP_ID
        self.PKG_PN = wip_lot_record.PKG_PN
        self.STAGE = wip_lot_record.STAGE
        self.GOOD_QTY = wip_lot_record.GOOD_QTY
        self.LOT_IN_QTY = wip_lot_record.LOT_IN_QTY
        self.LOT_OUT_QTY = wip_lot_record.LOT_OUT_QTY
        self.NG_QTY = wip_lot_record.NG_QTY
        self.HOT_TYPE = wip_lot_record.HOT_TYPE
        self.EQP_ID = wip_lot_record.EQP_ID
        self.AREA_ID = wip_lot_record.AREA_ID
        self.ROOT_LOT_ID = wip_lot_record.ROOT_LOT_ID
        self.PARENT_LOT_ID = wip_lot_record.PARENT_LOT_ID
        self.CHILD_LOT_ID = wip_lot_record.CHILD_LOT_ID
        self.CUST_LOT_ID = wip_lot_record.CUST_LOT_ID
        self.JOB_START_TIME = wip_lot_record.JOB_START_TIME
        self.JOB_END_TIME = wip_lot_record.JOB_END_TIME
        self.WORK_ORDER_ID = wip_lot_record.WORK_ORDER_ID
        self.WORK_ORDER_VER = wip_lot_record.WORK_ORDER_VER
        self.PO_ID = wip_lot_record.PO_ID
        self.FAC_ID = wip_lot_record.FAC_ID
        self.SUB_FAC = wip_lot_record.SUB_FAC
        
        # 将其他字段存储到扩展数据中
        extended_fields = {}
        for column in wip_lot_record.__table__.columns:
            if column.name not in [
                'id', 'LOT_ID', 'LOT_TYPE', 'LOT_QTY', 'WIP_STATE', 'PROC_STATE', 
                'HOLD_STATE', 'DEVICE', 'CHIP_ID', 'PKG_PN', 'STAGE', 'GOOD_QTY',
                'LOT_IN_QTY', 'LOT_OUT_QTY', 'NG_QTY', 'HOT_TYPE', 'EQP_ID',
                'AREA_ID', 'ROOT_LOT_ID', 'PARENT_LOT_ID', 'CHILD_LOT_ID',
                'CUST_LOT_ID', 'JOB_START_TIME', 'JOB_END_TIME', 'WORK_ORDER_ID',
                'WORK_ORDER_VER', 'PO_ID', 'FAC_ID', 'SUB_FAC'
            ]:
                value = getattr(wip_lot_record, column.name)
                if value is not None:
                    extended_fields[column.name] = str(value)
        
        if extended_fields:
            self.set_extended_data(extended_fields)
        
        self.source_table = 'v_wip_lot_unified'
    
    @classmethod
    def find_by_lot_id(cls, lot_id):
        """根据批次号查找记录"""
        return cls.query.filter_by(LOT_ID=lot_id).first()
    
    @classmethod
    def find_by_device_stage(cls, device, stage):
        """根据产品和工序查找记录"""
        return cls.query.filter_by(DEVICE=device, STAGE=stage).all()
    
    @classmethod
    def find_pending_lots(cls, limit=100):
        """查找待处理的批次"""
        return cls.query.filter(
            cls.WIP_STATE.in_(['WAIT', 'READY', 'PENDING']),
            cls.HOLD_STATE.is_(None)
        ).order_by(cls.PRIORITY_ORDER.asc(), cls.PLAN_DUE_DATE.asc()).limit(limit).all()
    
    @classmethod
    def find_by_priority(cls, priority_level):
        """根据优先级查找批次"""
        return cls.query.filter_by(PRIORITY_LEVEL=priority_level).order_by(
            cls.PRIORITY_ORDER.asc()
        ).all()
    
    def calculate_priority_score(self):
        """计算优先级分数（用于排序）"""
        base_score = 1000
        
        # 优先级等级权重
        priority_weights = {
            'high': 100,
            'medium': 50,
            'low': 10
        }
        
        score = base_score - priority_weights.get(self.PRIORITY_LEVEL, 50)
        score -= self.PRIORITY_ORDER if self.PRIORITY_ORDER else 999
        
        # 交期紧急度
        if self.PLAN_DUE_DATE:
            days_to_due = (self.PLAN_DUE_DATE - datetime.utcnow()).days
            if days_to_due < 0:  # 已过期
                score -= 500
            elif days_to_due < 3:  # 3天内到期
                score -= 200
            elif days_to_due < 7:  # 7天内到期
                score -= 100
        
        return score 