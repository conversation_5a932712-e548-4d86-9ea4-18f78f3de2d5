{"version": "0.2.0", "configurations": [{"name": "Launch Edge", "request": "launch", "type": "msedge", "url": "http://localhost:8080", "webRoot": "${workspaceFolder}"}, {"name": "Python: Flask应用", "type": "python", "request": "launch", "module": "flask", "env": {"FLASK_APP": "run.py", "FLASK_ENV": "development"}, "args": ["run", "--no-debugger"], "jinja": true}, {"name": "Python: 直接运行", "type": "python", "request": "launch", "program": "${workspaceFolder}/run.py", "console": "integratedTerminal"}]}