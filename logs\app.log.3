2025-06-17 21:28:02,311 ERROR: 创建lotpriorityconfig记录失败: (1366, "Incorrect integer value: 'high' for column 'priority' at row 1") [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:1117]
2025-06-17 21:28:04,581 ERROR: 创建lotpriorityconfig记录失败: (1366, "Incorrect integer value: 'medium' for column 'priority' at row 1") [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:1117]
2025-06-17 21:28:06,832 ERROR: 创建lotpriorityconfig记录失败: (1366, "Incorrect integer value: 'low' for column 'priority' at row 1") [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:1117]
2025-06-17 21:30:05,093 INFO: 应用启动 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\__init__.py:219]
2025-06-17 21:30:05,094 INFO: 已重新加载 0 个邮箱配置（应用实例未设置） [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\utils\scheduler.py:119]
2025-06-17 21:30:05,103 INFO: 邮件附件定时调度器已启动 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\utils\scheduler.py:74]
2025-06-17 21:30:05,137 INFO: 邮件附件定时任务调度器已启动 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\__init__.py:234]
2025-06-17 21:30:05,147 INFO: 全局定时任务已禁用，邮件调度器跟随禁用 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\utils\scheduler.py:41]
2025-06-17 21:30:05,151 INFO: 检测到全局定时任务已禁用，停止邮件调度器 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\utils\scheduler.py:199]
2025-06-17 21:30:05,301 INFO: APScheduler调度器配置完成: timezone=Asia/Shanghai, max_workers=10 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\scheduler_service.py:98]
2025-06-17 21:30:05,331 INFO: 从数据库加载了 0 个任务 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\scheduler_service.py:178]
2025-06-17 21:30:05,332 INFO: APScheduler调度器启动成功 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\scheduler_service.py:120]
2025-06-17 21:30:05,333 INFO: ✅ APScheduler统一调度器启动成功 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\__init__.py:246]
2025-06-17 21:30:05,336 INFO: 统一模型未启用，使用传统模型 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\compat\model_injector.py:212]
2025-06-17 21:30:05,337 INFO: API兼容性层初始化完成 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\compat\api_compatibility.py:116]
2025-06-17 21:30:05,337 INFO: 兼容层初始化完成 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\__init__.py:264]
2025-06-17 21:30:05,338 WARNING: ⚠️ API v2相关蓝图注册失败: cannot import name 'data_sources' from 'app.routes' (unknown location) [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\__init__.py:275]
2025-06-17 21:30:05,339 INFO: ✅ 性能监控已启用 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\__init__.py:321]
2025-06-17 21:30:05,341 WARNING: ⚠️ 缓存管理器初始化失败: No module named 'redis' [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\__init__.py:334]
2025-06-17 21:30:09,890 INFO: 尝试登录用户: admin [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\auth\routes.py:24]
2025-06-17 21:30:09,892 INFO: 找到用户: admin, 角色: admin, 密码哈希: pbkdf2:sha256:600000$5b3oIzxKyFuBWizM$eaaab87f30981563f8519ad8cb7e8669fa5b6280e439fcd17b1845cdd1c4f56c [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\auth\routes.py:33]
2025-06-17 21:30:10,446 INFO: 密码验证结果: True [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\auth\routes.py:35]
2025-06-17 21:30:10,446 INFO: 用户 admin 登录成功 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\auth\routes.py:42]
2025-06-17 21:30:11,519 ERROR: 检查会话状态失败: 'User' object has no attribute 'id' [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\api_v2\auth\routes.py:63]
2025-06-17 21:30:15,742 ERROR: 检查会话状态失败: 'User' object has no attribute 'id' [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\api_v2\auth\routes.py:63]
2025-06-17 21:30:15,767 INFO: ✅ MySQL数据源可用 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:317]
2025-06-17 21:30:15,802 INFO: 从MySQL获取到 62 条设备状态数据 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:679]
2025-06-17 21:30:15,804 INFO: 🏭 从MySQL获取到 62 条设备状态数据 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:440]
2025-06-17 21:30:23,890 ERROR: 检查会话状态失败: 'User' object has no attribute 'id' [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\api_v2\auth\routes.py:63]
2025-06-17 21:30:23,921 INFO: 📊 从MySQL获取到 171 条待排产批次数据 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:366]
2025-06-17 21:30:29,604 INFO: 成功批量删除et_wait_lot记录，删除数量: 1 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:1501]
2025-06-17 21:30:29,933 INFO: 📊 从MySQL获取到 170 条待排产批次数据 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:366]
2025-06-17 21:30:34,503 INFO: 📊 从MySQL获取到 170 条待排产批次数据 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:366]
2025-06-17 21:30:43,427 INFO: 成功批量删除et_wait_lot记录，删除数量: 1 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:1501]
2025-06-17 21:30:43,688 INFO: 📊 从MySQL获取到 169 条待排产批次数据 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:366]
2025-06-17 21:30:55,314 ERROR: 检查会话状态失败: 'User' object has no attribute 'id' [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\api_v2\auth\routes.py:63]
2025-06-17 21:30:55,318 INFO: 从MySQL获取到 2 条已排产批次数据 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:1697]
2025-06-17 21:30:58,826 ERROR: 检查会话状态失败: 'User' object has no attribute 'id' [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\api_v2\auth\routes.py:63]
2025-06-17 21:30:58,853 INFO: 从MySQL获取到 11 条产品优先级配置数据 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:1574]
2025-06-17 21:31:04,992 INFO: 成功批量删除devicepriorityconfig记录，删除数量: 1 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:1501]
2025-06-17 21:31:05,346 INFO: 从MySQL获取到 10 条产品优先级配置数据 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:1574]
2025-06-17 21:31:22,359 INFO: 用户 admin 开始上传优先级Excel文件 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\api_v2\production\routes.py:571]
2025-06-17 21:31:22,361 INFO: 处理文件: devicepriorityconfig.xlsx [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\api_v2\production\routes.py:589]
2025-06-17 21:31:22,361 INFO: 开始处理文件: devicepriorityconfig.xlsx [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\api_v2\production\routes.py:619]
2025-06-17 21:31:22,362 INFO: 识别为产品优先级配置文件: devicepriorityconfig.xlsx [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\api_v2\production\routes.py:626]
2025-06-17 21:31:22,761 INFO: 成功读取Excel文件，共 6 行数据 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\api_v2\production\routes.py:643]
2025-06-17 21:31:22,770 INFO: 表 devicepriorityconfig 现有 10 条记录 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\api_v2\production\routes.py:662]
2025-06-17 21:31:22,778 WARNING: 第2行数据处理失败: invalid literal for int() with base 10: 'n' [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\api_v2\production\routes.py:703]
