<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}AEC-FT ICP{% endblock %}</title>
    
    <!-- 预加载关键CSS -->
    <link rel="preload" href="{{ url_for('static', filename='vendor/bootstrap/bootstrap.min.css') }}" as="style">
    <link rel="preload" href="{{ url_for('static', filename='vendor/fontawesome/all.min.css') }}" as="style">
    
    <!-- CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='vendor/bootstrap/bootstrap.min.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='vendor/fontawesome/all.min.css') }}"> 
    <link rel="stylesheet" href="{{ url_for('static', filename='css/main.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/compatibility.css') }}">
    
    <style>
        :root {
            --theme-color: #b72424;
            --background-color: #FFFFFF;
            --secondary-background: #F5F5F5;
            --text-color: #333333;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            background-color: var(--background-color);
            color: var(--text-color);
            overflow-x: hidden;
        }
        
        .sidebar {
            background-color: var(--theme-color);
            color: white;
            height: 100vh;
            padding: 1rem;
            position: fixed;
            overflow-y: auto;
            scrollbar-width: thin;
            scrollbar-color: rgba(255,255,255,0.3) transparent;
            will-change: scroll-position; /* 优化滚动性能 */
        }
        
        .sidebar::-webkit-scrollbar {
            width: 6px;
        }
        
        .sidebar::-webkit-scrollbar-track {
            background: transparent;
        }
        
        .sidebar::-webkit-scrollbar-thumb {
            background-color: rgba(255,255,255,0.3);
            border-radius: 3px;
        }
        
        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            padding: 0.5rem 1rem;
            margin: 0.2rem 0;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            transition: background-color 0.15s ease; /* 只优化背景色变化 */
            cursor: pointer;
            will-change: background-color; /* 优化背景色变化性能 */
        }
        
        .sidebar .nav-link:hover {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
        }
        
        .sidebar .nav-link.active {
            color: white;
            background-color: rgba(255, 255, 255, 0.2);
        }

        .sidebar .submenu {
            margin-left: 1.5rem;
            overflow: hidden;
            transition: max-height 0.15s ease-out; /* 更快的展开动画 */
            max-height: 0;
        }

        .sidebar .submenu.show {
            max-height: 300px; /* 减少最大高度 */
        }

        .sidebar .nav-link .arrow {
            transition: transform 0.15s ease; /* 更快的箭头旋转 */
            will-change: transform;
        }

        .sidebar .nav-link.expanded .arrow {
            transform: rotate(90deg);
        }
        
        .main-content {
            padding: 2rem;
            background-color: var(--secondary-background);
            min-height: 100vh;
        }
             
        .btn-primary {
            background-color: var(--theme-color);
            border-color: var(--theme-color);
        }
        
        .btn-primary:hover {
            background-color: var(--theme-color);;
            border-color: var(--theme-color);
        }
        
        .card {
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .custom-navbar {
            background-color: var(--theme-color) !important;
            color: white !important;
            padding: 1rem;
        }

        .custom-navbar .navbar-brand,
        .custom-navbar .nav-link,
        .custom-navbar .btn-link {
            color: white !important;
        }
        
        .version-info {
            padding: 10px 0;
            margin-top: 20px;
            text-align: center;
            opacity: 0.8;
            font-size: 0.8rem;
        }

        .menu-loading {
            text-align: center;
            padding: 20px;
            color: rgba(255,255,255,0.6);
        }
        
        /* 性能优化：减少重绘 */
        * {
            backface-visibility: hidden;
            -webkit-backface-visibility: hidden;
        }
        
        /* 预加载动画优化 */
        .fade-in {
            opacity: 0;
            animation: fadeIn 0.2s ease-in forwards;
        }
        
        @keyframes fadeIn {
            to { opacity: 1; }
        }
    </style>
    {% block extra_css %}{% endblock %}
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 px-0 sidebar">
                <div class="text-center py-4">
                    <h4>AEC-FT ICP</h4>
                    <small class="d-block text-light" style="opacity: 0.8; font-size: 0.75rem; line-height: 1.2;">车规芯片终测智能调度平台</small>
                </div>
                <nav class="nav flex-column" id="sidebar-menu">
                    <div class="menu-loading">
                        <i class="fas fa-spinner fa-spin"></i> 加载菜单中...
                    </div>
                </nav>
                <div class="version-info">
                    <span>版本：{{ app_version }}</span>
                </div>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 ms-sm-auto px-4">
                <!-- Top Navigation -->
                <nav class="navbar navbar-expand-lg navbar-dark custom-navbar">
                    <div class="container-fluid">
                        <a class="navbar-brand" href="{{ url_for('main.index') }}">
                            <i class="fas fa-microchip me-2"></i>AEC-FT Intelligent Commander Platform
                        </a>
                        <div class="d-flex justify-content-between w-100">
                            <h5 class="mb-0">{% block page_title %}{% endblock %}</h5>
                            <div class="dropdown">
                                <button class="btn btn-link text-white dropdown-toggle" type="button" id="userDropdown" data-bs-toggle="dropdown">
                                    <i class="fas fa-user"></i> {{ current_user.username }}
                                </button>
                                <ul class="dropdown-menu dropdown-menu-end">
                                    {% if current_user.role == 'admin' %}
                                    <li><a class="dropdown-item" href="/users">用户管理</a></li>
                                    {% endif %}
                                    {% if current_user.has_permission(26) %}
                                    <li><a class="dropdown-item" href="/system/settings">系统设置</a></li>
                                    {% endif %}
                                    <li><a class="dropdown-item" href="{{ url_for('auth.logout') }}">退出登录</a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </nav>
                
                <!-- Content -->
                <main class="main-content">
                    {% with messages = get_flashed_messages(with_categories=true) %}
                        {% if messages %}
                            {% for category, message in messages %}
                                <div class="alert alert-{{ category }}">{{ message }}</div>
                            {% endfor %}
                        {% endif %}
                    {% endwith %}
                    {% block content %}{% endblock %}
                </main>
            </div>
        </div>
    </div>
    
    <!-- 只加载必要的JavaScript -->
    <script src="{{ url_for('static', filename='vendor/bootstrap/bootstrap.bundle.min.js') }}"></script>
    
    <!-- 菜单优化器 - 预加载 -->
    <link rel="preload" href="{{ url_for('static', filename='js/menu-optimizer.js') }}" as="script">
    
    <script>
        // 性能优化的菜单初始化
        let menuInitialized = false;
        let menuCache = null;
        let topLevelMenus = [];
        
        // 立即执行函数，避免全局变量污染
        (function() {
            'use strict';
            
            // 使用更高效的事件监听
            document.addEventListener('DOMContentLoaded', initializeMenu, { once: true });
            
            function initializeMenu() {
                if (menuInitialized) return;
                
                // 获取当前用户身份标识
                const currentUser = '{{ current_user.username }}' + '_' + '{{ current_user.role }}';
                const cacheKey = 'aps_menu_data_' + currentUser;
                const versionKey = 'aps_menu_version_' + currentUser;
                
                // 检查sessionStorage缓存（带用户身份）
                const cachedMenu = sessionStorage.getItem(cacheKey);
                const cachedVersion = sessionStorage.getItem(versionKey);
                
                // 清除其他用户的缓存
                clearOtherUserCache(currentUser);
                
                if (cachedMenu && cachedVersion) {
                    try {
                        const menuData = JSON.parse(cachedMenu);
                        buildMenuDOM(menuData);
                        setActiveMenu();
                        return;
                    } catch (e) {
                        // 缓存损坏，清除
                        sessionStorage.removeItem(cacheKey);
                        sessionStorage.removeItem(versionKey);
                    }
                }
                
                // 获取菜单数据
                fetchMenuData();
            }
            
            function clearOtherUserCache(currentUser) {
                // 清除其他用户的菜单缓存
                const keysToRemove = [];
                for (let i = 0; i < sessionStorage.length; i++) {
                    const key = sessionStorage.key(i);
                    if (key && (key.startsWith('aps_menu_data_') || key.startsWith('aps_menu_version_'))) {
                        if (!key.includes(currentUser)) {
                            keysToRemove.push(key);
                        }
                    }
                }
                keysToRemove.forEach(key => sessionStorage.removeItem(key));
            }
            
            function fetchMenuData() {
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), 5000); // 5秒超时
                
                fetch('/menu', {
                    signal: controller.signal,
                    cache: 'force-cache' // 强制使用缓存
                })
                .then(response => {
                    clearTimeout(timeoutId);
                    if (!response.ok) throw new Error(`HTTP ${response.status}`);
                    return response.json();
                })
                .then(data => {
                    const menuData = data.menu || data;
                    
                    // 缓存菜单数据（带用户身份）
                    try {
                        const currentUser = '{{ current_user.username }}' + '_' + '{{ current_user.role }}';
                        const cacheKey = 'aps_menu_data_' + currentUser;
                        const versionKey = 'aps_menu_version_' + currentUser;
                        
                        sessionStorage.setItem(cacheKey, JSON.stringify(menuData));
                        if (data.version) {
                            sessionStorage.setItem(versionKey, data.version);
                        }
                    } catch (e) {
                        console.warn('无法缓存菜单数据:', e);
                    }
                    
                    buildMenuDOM(menuData);
                    setActiveMenu();
                })
                .catch(error => {
                    console.error('菜单加载失败:', error);
                    const sidebarMenu = document.getElementById('sidebar-menu');
                    if (sidebarMenu) {
                        sidebarMenu.innerHTML = '<div class="text-center text-danger p-3"><i class="fas fa-exclamation-triangle"></i><br>菜单加载失败<br><small>请刷新页面重试</small></div>';
                    }
                });
            }
            
            function buildMenuDOM(menuData) {
                const sidebarMenu = document.getElementById('sidebar-menu');
                if (!sidebarMenu) return;
                
                const fragment = document.createDocumentFragment();
                topLevelMenus = [];
                
                menuData.forEach(item => {
                    const menuElement = createMenuItem(item, true);
                    if (menuElement) fragment.appendChild(menuElement);
                });
                
                sidebarMenu.innerHTML = '';
                sidebarMenu.appendChild(fragment);
                
                // 使用事件委托
                sidebarMenu.addEventListener('click', handleMenuClick, { passive: false });
                
                menuInitialized = true;
            }
            
            function createMenuItem(item, isTopLevel = false) {
                const menuItem = document.createElement('div');
                menuItem.className = 'nav-item fade-in';
                
                const link = document.createElement('a');
                link.href = item.route || '#';
                link.className = 'nav-link';
                link.dataset.route = item.route || '';
                link.dataset.isTopLevel = isTopLevel;
                
                const hasChildren = item.children && item.children.length > 0;
                const iconHtml = item.icon ? `<i class="${item.icon}" style="margin-right: 8px;"></i>` : '';
                const arrowHtml = hasChildren ? '<i class="fas fa-chevron-right arrow"></i>' : '';
                
                link.innerHTML = `<span class="d-flex align-items-center">${iconHtml}${item.name}</span>${arrowHtml}`;
                menuItem.appendChild(link);
                
                if (hasChildren) {
                    const submenu = document.createElement('div');
                    submenu.className = 'submenu';
                    
                    const childFragment = document.createDocumentFragment();
                    item.children.forEach(child => {
                        const childItem = createMenuItem(child);
                        if (childItem) childFragment.appendChild(childItem);
                    });
                    submenu.appendChild(childFragment);
                    
                    if (isTopLevel) topLevelMenus.push(link);
                    menuItem.appendChild(submenu);
                }
                
                return menuItem;
            }
            
            function handleMenuClick(e) {
                const target = e.target.closest('.nav-link');
                if (!target) return;
                
                const href = target.getAttribute('href');
                const isTopLevel = target.dataset.isTopLevel === 'true';
                const submenu = target.nextElementSibling;
                
                if (href === '#' && submenu?.classList.contains('submenu')) {
                    e.preventDefault();
                    
                    if (isTopLevel) {
                        // 关闭其他顶级菜单
                        topLevelMenus.forEach(menu => {
                            if (menu !== target && menu.classList.contains('expanded')) {
                                menu.classList.remove('expanded');
                                const menuSubmenu = menu.nextElementSibling;
                                if (menuSubmenu?.classList.contains('submenu')) {
                                    menuSubmenu.classList.remove('show');
                                }
                            }
                        });
                    }
                    
                    // 切换当前菜单
                    target.classList.toggle('expanded');
                    submenu.classList.toggle('show');
                }
            }
            
            function setActiveMenu() {
                const currentPath = window.location.pathname;
                const allLinks = document.querySelectorAll('#sidebar-menu .nav-link');
                
                let bestMatch = null;
                let bestMatchLength = 0;
                
                allLinks.forEach(link => {
                    link.classList.remove('active');
                    const href = link.getAttribute('href');
                    if (href && href !== '#' && currentPath.startsWith(href) && href.length > bestMatchLength) {
                        bestMatch = link;
                        bestMatchLength = href.length;
                    }
                });
                
                if (bestMatch) {
                    bestMatch.classList.add('active');
                    
                    // 展开父菜单
                    let parentSubmenu = bestMatch.closest('.submenu');
                    while (parentSubmenu) {
                        parentSubmenu.classList.add('show');
                        const parentLink = parentSubmenu.previousElementSibling;
                        if (parentLink?.classList.contains('nav-link')) {
                            parentLink.classList.add('expanded');
                            if (parentLink.dataset.isTopLevel === 'true') break;
                        }
                        parentSubmenu = parentSubmenu.parentElement?.closest('.submenu');
                    }
                }
            }
            
            // 暴露清除缓存函数
            window.clearMenuCache = function() {
                // 清除所有菜单缓存
                const keysToRemove = [];
                for (let i = 0; i < sessionStorage.length; i++) {
                    const key = sessionStorage.key(i);
                    if (key && (key.startsWith('aps_menu_data_') || key.startsWith('aps_menu_version_'))) {
                        keysToRemove.push(key);
                    }
                }
                keysToRemove.forEach(key => sessionStorage.removeItem(key));
                menuInitialized = false;
                console.log('菜单缓存已清除');
            };
            
            // 暴露刷新菜单函数
            window.refreshMenu = function() {
                window.clearMenuCache();
                initializeMenu();
                console.log('菜单已刷新');
            };
        })();
    </script>
    
    {% block extra_js %}{% endblock %}
    
    <!-- 懒加载聊天机器人 -->
    <script>
        // 延迟加载聊天机器人，避免影响初始页面性能
        setTimeout(() => {
            const savedSettings = localStorage.getItem('chatbotSettings');
            if (savedSettings) {
                try {
                    const settings = JSON.parse(savedSettings);
                    if (settings.enabled) {
                        loadChatbot(settings);
                    }
                } catch (e) {
                    console.warn('聊天机器人配置解析失败:', e);
                }
            }
        }, 2000); // 2秒后加载
        
        function loadChatbot(settings) {
            if (settings.integrationType === 'script' && settings.scriptCode) {
                const script = document.createElement('script');
                script.text = settings.scriptCode;
                document.head.appendChild(script);
            } else if (settings.integrationType === 'iframe' && settings.iframeUrl) {
                const iframe = document.createElement('iframe');
                iframe.src = settings.iframeUrl;
                iframe.style.cssText = `
                    position: fixed; bottom: 20px; right: 20px; width: 350px; height: 500px;
                    border: none; border-radius: 10px; box-shadow: 0 4px 20px rgba(0,0,0,0.15);
                    z-index: 1000; background: white;
                `;
                document.body.appendChild(iframe);
            }
        }
    </script>
</body>
</html> 