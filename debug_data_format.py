#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

print("调试数据格式...")

try:
    from app.services.data_source_manager import DataSourceManager
    
    dm = DataSourceManager()
    
    # 检查EQP_STATUS数据
    print("\n=== EQP_STATUS 数据格式检查 ===")
    eqp_data = dm.get_table_data('EQP_STATUS')
    print(f"数据类型: {type(eqp_data)}")
    print(f"数据长度: {len(eqp_data)}")
    
    if eqp_data:
        print(f"第一条数据类型: {type(eqp_data[0])}")
        print(f"第一条数据内容: {eqp_data[0]}")
        
        if isinstance(eqp_data[0], dict):
            print("✓ 数据格式正确（字典）")
            print(f"字段列表: {list(eqp_data[0].keys())}")
        else:
            print("✗ 数据格式错误（非字典）")
    
    # 检查ET_WAIT_LOT数据
    print("\n=== ET_WAIT_LOT 数据格式检查 ===")
    lot_data = dm.get_table_data('ET_WAIT_LOT')
    print(f"数据类型: {type(lot_data)}")
    print(f"数据长度: {len(lot_data)}")
    
    if lot_data:
        print(f"第一条数据类型: {type(lot_data[0])}")
        print(f"第一条数据内容: {lot_data[0]}")
        
        if isinstance(lot_data[0], dict):
            print("✓ 数据格式正确（字典）")
            print(f"字段列表: {list(lot_data[0].keys())}")
        else:
            print("✗ 数据格式错误（非字典）")
    
    # 检查UPH数据
    print("\n=== ET_UPH_EQP 数据格式检查 ===")
    uph_data = dm.get_table_data('ET_UPH_EQP')
    print(f"数据类型: {type(uph_data)}")
    print(f"数据长度: {len(uph_data)}")
    
    if uph_data:
        print(f"第一条数据类型: {type(uph_data[0])}")
        print(f"第一条数据内容: {uph_data[0]}")
        
        if isinstance(uph_data[0], dict):
            print("✓ 数据格式正确（字典）")
            print(f"字段列表: {list(uph_data[0].keys())}")
        else:
            print("✗ 数据格式错误（非字典）")

except Exception as e:
    print(f"调试过程中发生错误: {e}")
    import traceback
    traceback.print_exc()

print("\n调试完成") 