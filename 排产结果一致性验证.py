#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
排产结果一致性验证脚本
详细对比原版算法和优化版算法的排产结果差异
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import time
import logging
from typing import List, Dict, Tuple
from app.services.real_scheduling_service import RealSchedulingService
from app.services.optimized_scheduling_service import OptimizedSchedulingService

# 配置日志
logging.basicConfig(
    level=logging.WARNING,  # 减少日志干扰
    format='%(asctime)s | %(levelname)s | %(message)s',
    datefmt='%H:%M:%S'
)
logger = logging.getLogger(__name__)

def compare_scheduling_results():
    """详细对比两种算法的排产结果"""
    
    print('🔍 智能排产算法一致性验证')
    print('='*80)
    
    # 1. 执行原版算法
    print('\n📊 **步骤1：执行原版算法**')
    print('-'*50)
    
    original_service = RealSchedulingService()
    start_time = time.time()
    original_results = original_service.execute_real_scheduling('intelligent')
    original_time = time.time() - start_time
    
    print(f"✅ 原版算法完成: {len(original_results)} 个批次, 耗时: {original_time:.2f}s")
    
    # 2. 执行优化版算法
    print('\n🚀 **步骤2：执行优化版算法**')
    print('-'*50)
    
    optimized_service = OptimizedSchedulingService()
    start_time = time.time()
    optimized_results = optimized_service.execute_optimized_scheduling('intelligent')
    optimized_time = time.time() - start_time
    
    print(f"✅ 优化版算法完成: {len(optimized_results)} 个批次, 耗时: {optimized_time:.2f}s")
    
    # 3. 详细对比分析
    print('\n🔍 **步骤3：详细对比分析**')
    print('-'*50)
    
    if not original_results or not optimized_results:
        print("❌ 无法进行对比：其中一个算法没有返回结果")
        return
    
    # 构建对比字典 (LOT_ID -> 结果)
    original_dict = {result.get('LOT_ID'): result for result in original_results}
    optimized_dict = {result.get('LOT_ID'): result for result in optimized_results}
    
    # 关键字段对比
    key_fields = ['LOT_ID', 'HANDLER_ID', 'PRIORITY', 'DEVICE', 'STAGE', 'GOOD_QTY', 'PO_ID']
    additional_fields = ['match_type', 'comprehensive_score', 'processing_time', 'changeover_time']
    
    # 统计信息
    total_original = len(original_results)
    total_optimized = len(optimized_results)
    common_lots = set(original_dict.keys()) & set(optimized_dict.keys())
    only_original = set(original_dict.keys()) - set(optimized_dict.keys())
    only_optimized = set(optimized_dict.keys()) - set(original_dict.keys())
    
    print(f"📈 **基础统计**:")
    print(f"   • 原版排产批次: {total_original}")
    print(f"   • 优化版排产批次: {total_optimized}")
    print(f"   • 共同排产批次: {len(common_lots)}")
    print(f"   • 仅原版排产: {len(only_original)}")
    print(f"   • 仅优化版排产: {len(only_optimized)}")
    
    # 4. 批次差异分析
    print('\n📋 **步骤4：批次差异分析**')
    print('-'*50)
    
    if only_original:
        print(f"⚠️ **仅原版算法排产的批次** ({len(only_original)}个):")
        for i, lot_id in enumerate(sorted(only_original)[:10]):  # 显示前10个
            orig = original_dict[lot_id]
            print(f"   {i+1:2d}. {lot_id} -> {orig.get('HANDLER_ID')} "
                  f"({orig.get('DEVICE')}+{orig.get('STAGE')})")
        if len(only_original) > 10:
            print(f"   ... 还有 {len(only_original)-10} 个批次")
    
    if only_optimized:
        print(f"⚠️ **仅优化版算法排产的批次** ({len(only_optimized)}个):")
        for i, lot_id in enumerate(sorted(only_optimized)[:10]):  # 显示前10个
            opt = optimized_dict[lot_id]
            print(f"   {i+1:2d}. {lot_id} -> {opt.get('HANDLER_ID')} "
                  f"({opt.get('DEVICE')}+{opt.get('STAGE')})")
        if len(only_optimized) > 10:
            print(f"   ... 还有 {len(only_optimized)-10} 个批次")
    
    # 5. 共同批次的字段差异分析
    print('\n🔬 **步骤5：共同批次字段差异分析**')
    print('-'*50)
    
    field_differences = {}
    identical_count = 0
    different_lots = []
    
    for lot_id in sorted(common_lots):
        original = original_dict[lot_id]
        optimized = optimized_dict[lot_id]
        
        lot_differences = {}
        is_identical = True
        
        # 检查关键字段差异
        for field in key_fields:
            orig_val = original.get(field)
            opt_val = optimized.get(field)
            
            if orig_val != opt_val:
                lot_differences[field] = {
                    'original': orig_val,
                    'optimized': opt_val
                }
                is_identical = False
                
                # 统计字段差异
                if field not in field_differences:
                    field_differences[field] = []
                field_differences[field].append(lot_id)
        
        if is_identical:
            identical_count += 1
        else:
            different_lots.append({
                'lot_id': lot_id,
                'differences': lot_differences
            })
    
    print(f"✅ **完全一致的批次**: {identical_count}/{len(common_lots)} ({identical_count/len(common_lots)*100:.1f}%)")
    print(f"⚠️ **存在差异的批次**: {len(different_lots)}/{len(common_lots)} ({len(different_lots)/len(common_lots)*100:.1f}%)")
    
    # 6. 字段差异统计
    print('\n📊 **步骤6：字段差异统计**')
    print('-'*50)
    
    if field_differences:
        for field, affected_lots in field_differences.items():
            print(f"🔸 **{field}字段差异**: {len(affected_lots)} 个批次")
            
            # 显示前5个差异示例
            for i, lot_id in enumerate(affected_lots[:5]):
                lot_diff = next(d for d in different_lots if d['lot_id'] == lot_id)
                diff = lot_diff['differences'][field]
                print(f"     {i+1}. {lot_id}: {diff['original']} → {diff['optimized']}")
            
            if len(affected_lots) > 5:
                print(f"     ... 还有 {len(affected_lots)-5} 个批次存在此字段差异")
            print()
    else:
        print("✅ 所有共同批次的关键字段完全一致！")
    
    # 7. 设备分配差异分析
    print('\n🏭 **步骤7：设备分配差异分析**')
    print('-'*50)
    
    handler_differences = []
    for lot_data in different_lots:
        if 'HANDLER_ID' in lot_data['differences']:
            handler_differences.append(lot_data)
    
    if handler_differences:
        print(f"🎯 **设备分配差异详情** ({len(handler_differences)}个批次):")
        
        for i, lot_data in enumerate(handler_differences[:10]):  # 显示前10个
            lot_id = lot_data['lot_id']
            handler_diff = lot_data['differences']['HANDLER_ID']
            
            # 获取批次详情
            original = original_dict[lot_id]
            optimized = optimized_dict[lot_id]
            
            print(f"\n   {i+1:2d}. **批次**: {lot_id}")
            print(f"      • 产品: {original.get('DEVICE')} + {original.get('STAGE')}")
            print(f"      • 数量: {original.get('GOOD_QTY')} PCS")
            print(f"      • 原版选择: {handler_diff['original']}")
            print(f"      • 优化版选择: {handler_diff['optimized']}")
            
            # 显示评分信息（如果有）
            if original.get('comprehensive_score') and optimized.get('comprehensive_score'):
                print(f"      • 原版评分: {original.get('comprehensive_score'):.2f}")
                print(f"      • 优化版评分: {optimized.get('comprehensive_score'):.2f}")
        
        if len(handler_differences) > 10:
            print(f"\n   ... 还有 {len(handler_differences)-10} 个批次存在设备分配差异")
    else:
        print("✅ 所有共同批次的设备分配完全一致！")
    
    # 8. 优先级差异分析
    print('\n🔢 **步骤8：优先级差异分析**')
    print('-'*50)
    
    priority_differences = []
    for lot_data in different_lots:
        if 'PRIORITY' in lot_data['differences']:
            priority_differences.append(lot_data)
    
    if priority_differences:
        print(f"📈 **优先级差异详情** ({len(priority_differences)}个批次):")
        
        # 按设备分组分析优先级差异
        priority_by_handler = {}
        for lot_data in priority_differences:
            lot_id = lot_data['lot_id']
            original = original_dict[lot_id]
            handler_id = original.get('HANDLER_ID')
            
            if handler_id not in priority_by_handler:
                priority_by_handler[handler_id] = []
            priority_by_handler[handler_id].append(lot_data)
        
        for handler_id, handler_lots in priority_by_handler.items():
            print(f"\n   🏭 **设备 {handler_id}** ({len(handler_lots)}个批次差异):")
            
            for lot_data in handler_lots[:5]:  # 显示前5个
                lot_id = lot_data['lot_id']
                priority_diff = lot_data['differences']['PRIORITY']
                print(f"      • {lot_id}: {priority_diff['original']} → {priority_diff['optimized']}")
    else:
        print("✅ 所有共同批次的优先级完全一致！")
    
    # 9. 总结和建议
    print('\n🎯 **步骤9：总结和建议**')
    print('-'*50)
    
    consistency_rate = identical_count / len(common_lots) * 100 if common_lots else 0
    
    print(f"📊 **一致性评估**:")
    print(f"   • 批次数量一致性: {abs(total_original - total_optimized) <= 2}")
    print(f"   • 共同批次一致性: {consistency_rate:.1f}%")
    print(f"   • 关键字段一致性: {'优秀' if consistency_rate >= 95 else '一般' if consistency_rate >= 80 else '需要优化'}")
    
    if consistency_rate >= 95:
        print("\n✅ **结论**: 优化版算法与原版算法高度一致，可以安全部署")
    elif consistency_rate >= 80:
        print("\n⚠️ **结论**: 优化版算法基本一致，建议进一步调试差异原因")
    else:
        print("\n❌ **结论**: 存在较大差异，需要详细调试优化版算法")
    
    print(f"\n💡 **建议**:")
    if only_original or only_optimized:
        print(f"   • 调查批次数量差异的原因")
    if field_differences:
        print(f"   • 重点关注字段差异: {', '.join(field_differences.keys())}")
    if handler_differences:
        print(f"   • 验证设备匹配算法的一致性")
    if priority_differences:
        print(f"   • 检查优先级排序逻辑")
    
    return {
        'total_original': total_original,
        'total_optimized': total_optimized,
        'consistency_rate': consistency_rate,
        'field_differences': field_differences,
        'different_lots': different_lots
    }

if __name__ == '__main__':
    compare_scheduling_results() 