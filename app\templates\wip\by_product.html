{% extends "base.html" %}

{% block title %}产品WIP跟踪
<style>
:root {
    --primary-color: #b72424;
    --secondary-color: #d73027;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
}

.navbar-brand, .nav-link.active {
    color: var(--primary-color) !important;
}

.card-header {
    background-color: var(--primary-color);
    color: white;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">产品WIP跟踪</h3>
                    <div class="card-tools">
                        <button type="button" id="refreshBtn" class="btn btn-primary">
                            <i class="fas fa-sync-alt"></i> 刷新
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table id="productWipTable" class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>产品ID</th>
                                    <th>设备</th>
                                    <th>芯片ID</th>
                                    <th>封装型号</th>
                                    <th>批次数量</th>
                                    <th>状态</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- 数据将通过JavaScript动态加载 -->
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="card-footer">
                    <div class="float-right">
                        <span id="totalRecords">总记录数: 0</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // 初始化DataTable
        const table = $('#productWipTable').DataTable({
            "responsive": true,
            "lengthChange": true,
            "autoWidth": false,
            "pageLength": 25,
            "language": {
                "emptyTable": "没有可用数据",
                "info": "显示 _START_ 到 _END_ 条，共 _TOTAL_ 条",
                "infoEmpty": "显示 0 到 0 条，共 0 条",
                "infoFiltered": "(从 _MAX_ 条中过滤)",
                "lengthMenu": "显示 _MENU_ 条",
                "search": "搜索:",
                "zeroRecords": "没有找到匹配的记录",
                "paginate": {
                    "first": "首页",
                    "last": "末页",
                    "next": "下一页",
                    "previous": "上一页"
                }
            },
            "columns": [
                { "data": "PROD_ID" },
                { "data": "DEVICE" },
                { "data": "CHIP_ID" },
                { "data": "PKG_PN" },
                { "data": "lot_count" },
                { "data": "status" }
            ]
        });

        // 加载数据函数
        function loadData() {
            // 显示加载指示器
            $('#productWipTable tbody').html('<tr><td colspan="6" class="text-center"><i class="fas fa-spinner fa-spin"></i> 正在加载数据...</td></tr>');
            
            // 这里使用模拟数据，实际项目中应该调用API
            // 实际项目中应该添加相应的API端点
            setTimeout(function() {
                const mockData = [
                    {
                        "PROD_ID": "PROD001",
                        "DEVICE": "Device A",
                        "CHIP_ID": "CHIP001",
                        "PKG_PN": "PKG001",
                        "lot_count": 5,
                        "status": "进行中"
                    },
                    {
                        "PROD_ID": "PROD002",
                        "DEVICE": "Device B",
                        "CHIP_ID": "CHIP002",
                        "PKG_PN": "PKG002",
                        "lot_count": 3,
                        "status": "等待中"
                    }
                ];
                
                // 清空表格并重新加载数据
                table.clear();
                table.rows.add(mockData).draw();
                
                // 更新记录总数
                $('#totalRecords').text('总记录数: ' + mockData.length);
            }, 500);
        }

        // 初始加载数据
        loadData();

        // 刷新按钮点击事件
        $('#refreshBtn').on('click', function() {
            loadData();
        });
    });
</script>
{% endblock %} 