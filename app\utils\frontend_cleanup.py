# -*- coding: utf-8 -*-
"""
前端资源清理工具
用于清理重复的前端资源文件，优化项目结构
"""

import os
import shutil
import hashlib
import logging
from pathlib import Path
from typing import List, Dict, Set, Tuple
from collections import defaultdict

logger = logging.getLogger(__name__)

class FrontendResourceCleaner:
    """前端资源清理器"""
    
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.static_dirs = [
            self.project_root / 'app' / 'static',
            self.project_root / 'static'  # 旧的静态目录
        ]
        
        # 需要保留的文件（即使重复）
        self.preserve_files = {
            'compatibility.css',  # 兼容性样式
            'menu-optimizer.js',  # 菜单优化器
            'echarts-utils.js'    # ECharts工具
        }
        
        # 文件类型映射
        self.file_types = {
            '.css': 'CSS',
            '.js': 'JavaScript',
            '.woff': 'Font',
            '.woff2': 'Font',
            '.ttf': 'Font',
            '.eot': 'Font',
            '.svg': 'SVG/Font',
            '.png': 'Image',
            '.jpg': 'Image',
            '.jpeg': 'Image',
            '.gif': 'Image',
            '.ico': 'Icon'
        }
        
        # 统计信息
        self.stats = {
            'total_files': 0,
            'duplicate_files': 0,
            'cleaned_files': 0,
            'saved_space': 0,
            'errors': []
        }
    
    def calculate_file_hash(self, file_path: Path) -> str:
        """计算文件的MD5哈希值"""
        try:
            hash_md5 = hashlib.md5()
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception as e:
            logger.error(f"计算文件哈希失败 {file_path}: {e}")
            return ""
    
    def find_duplicate_files(self) -> Dict[str, List[Path]]:
        """查找重复文件"""
        file_hashes = defaultdict(list)
        
        for static_dir in self.static_dirs:
            if not static_dir.exists():
                continue
                
            for file_path in static_dir.rglob('*'):
                if file_path.is_file():
                    self.stats['total_files'] += 1
                    
                    # 跳过需要保留的文件
                    if file_path.name in self.preserve_files:
                        continue
                    
                    # 只处理前端资源文件
                    if file_path.suffix.lower() not in self.file_types:
                        continue
                    
                    file_hash = self.calculate_file_hash(file_path)
                    if file_hash:
                        file_hashes[file_hash].append(file_path)
        
        # 只返回有重复的文件
        duplicates = {hash_val: paths for hash_val, paths in file_hashes.items() if len(paths) > 1}
        self.stats['duplicate_files'] = sum(len(paths) - 1 for paths in duplicates.values())
        
        return duplicates
    
    def analyze_duplicates(self, duplicates: Dict[str, List[Path]]) -> Dict[str, Dict]:
        """分析重复文件"""
        analysis = {}
        
        for file_hash, file_paths in duplicates.items():
            if len(file_paths) <= 1:
                continue
            
            # 获取文件信息
            first_file = file_paths[0]
            file_size = first_file.stat().st_size
            file_type = self.file_types.get(first_file.suffix.lower(), 'Unknown')
            
            # 选择保留的文件（优先选择vendor目录下的）
            preferred_file = self.choose_preferred_file(file_paths)
            files_to_remove = [f for f in file_paths if f != preferred_file]
            
            analysis[file_hash] = {
                'file_name': first_file.name,
                'file_type': file_type,
                'file_size': file_size,
                'total_copies': len(file_paths),
                'preferred_file': preferred_file,
                'files_to_remove': files_to_remove,
                'space_saved': file_size * len(files_to_remove)
            }
        
        return analysis
    
    def choose_preferred_file(self, file_paths: List[Path]) -> Path:
        """选择要保留的首选文件"""
        # 优先级：vendor > base > 其他
        priority_dirs = ['vendor', 'base']
        
        for priority_dir in priority_dirs:
            for file_path in file_paths:
                if priority_dir in str(file_path):
                    return file_path
        
        # 如果没有优先目录，选择路径最短的
        return min(file_paths, key=lambda p: len(str(p)))
    
    def generate_cleanup_report(self, analysis: Dict[str, Dict]) -> str:
        """生成清理报告"""
        report_lines = [
            "# 前端资源清理报告",
            f"生成时间: {self.get_current_time()}",
            "",
            "## 统计摘要",
            f"- 总文件数: {self.stats['total_files']}",
            f"- 重复文件数: {self.stats['duplicate_files']}",
            f"- 预计清理文件数: {len([f for a in analysis.values() for f in a['files_to_remove']])}",
            f"- 预计节省空间: {self.format_file_size(sum(a['space_saved'] for a in analysis.values()))}",
            "",
            "## 重复文件详情",
            ""
        ]
        
        # 按文件类型分组
        by_type = defaultdict(list)
        for file_hash, info in analysis.items():
            by_type[info['file_type']].append((file_hash, info))
        
        for file_type, items in by_type.items():
            report_lines.append(f"### {file_type} 文件")
            report_lines.append("")
            
            for file_hash, info in items:
                report_lines.extend([
                    f"**{info['file_name']}** ({info['total_copies']} 个副本)",
                    f"- 文件大小: {self.format_file_size(info['file_size'])}",
                    f"- 保留文件: `{info['preferred_file']}`",
                    f"- 删除文件:"
                ])
                
                for file_to_remove in info['files_to_remove']:
                    report_lines.append(f"  - `{file_to_remove}`")
                
                report_lines.extend([
                    f"- 节省空间: {self.format_file_size(info['space_saved'])}",
                    ""
                ])
        
        return "\n".join(report_lines)
    
    def execute_cleanup(self, analysis: Dict[str, Dict], dry_run: bool = True) -> bool:
        """执行清理操作"""
        if dry_run:
            logger.info("执行模拟清理（不会实际删除文件）")
        else:
            logger.info("执行实际清理操作")
        
        success_count = 0
        error_count = 0
        
        for file_hash, info in analysis.items():
            for file_to_remove in info['files_to_remove']:
                try:
                    if not dry_run:
                        file_to_remove.unlink()
                        logger.info(f"已删除: {file_to_remove}")
                    else:
                        logger.info(f"模拟删除: {file_to_remove}")
                    
                    success_count += 1
                    self.stats['saved_space'] += info['file_size']
                    
                except Exception as e:
                    error_msg = f"删除文件失败 {file_to_remove}: {e}"
                    logger.error(error_msg)
                    self.stats['errors'].append(error_msg)
                    error_count += 1
        
        self.stats['cleaned_files'] = success_count
        
        logger.info(f"清理完成: 成功 {success_count} 个, 失败 {error_count} 个")
        return error_count == 0
    
    def update_template_references(self, analysis: Dict[str, Dict], dry_run: bool = True) -> bool:
        """更新模板中的资源引用"""
        template_dir = self.project_root / 'app' / 'templates'
        if not template_dir.exists():
            return True
        
        # 构建路径映射
        path_mappings = {}
        for info in analysis.values():
            preferred_path = str(info['preferred_file']).replace(str(self.project_root), '')
            preferred_path = preferred_path.replace('\\', '/').lstrip('/')
            
            for file_to_remove in info['files_to_remove']:
                old_path = str(file_to_remove).replace(str(self.project_root), '')
                old_path = old_path.replace('\\', '/').lstrip('/')
                path_mappings[old_path] = preferred_path
        
        if not path_mappings:
            return True
        
        # 更新模板文件
        updated_files = 0
        for template_file in template_dir.rglob('*.html'):
            try:
                with open(template_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                original_content = content
                for old_path, new_path in path_mappings.items():
                    # 更新各种可能的引用格式
                    content = content.replace(f"'{old_path}'", f"'{new_path}'")
                    content = content.replace(f'"{old_path}"', f'"{new_path}"')
                    content = content.replace(f'filename=\'{old_path}\'', f'filename=\'{new_path}\'')
                    content = content.replace(f'filename="{old_path}"', f'filename="{new_path}"')
                
                if content != original_content:
                    if not dry_run:
                        with open(template_file, 'w', encoding='utf-8') as f:
                            f.write(content)
                        logger.info(f"已更新模板: {template_file}")
                    else:
                        logger.info(f"模拟更新模板: {template_file}")
                    
                    updated_files += 1
                    
            except Exception as e:
                error_msg = f"更新模板失败 {template_file}: {e}"
                logger.error(error_msg)
                self.stats['errors'].append(error_msg)
        
        logger.info(f"模板更新完成: {updated_files} 个文件")
        return True
    
    def clean_empty_directories(self, dry_run: bool = True) -> bool:
        """清理空目录"""
        empty_dirs = []
        
        for static_dir in self.static_dirs:
            if not static_dir.exists():
                continue
            
            for dir_path in static_dir.rglob('*'):
                if dir_path.is_dir() and not any(dir_path.iterdir()):
                    empty_dirs.append(dir_path)
        
        for empty_dir in empty_dirs:
            try:
                if not dry_run:
                    empty_dir.rmdir()
                    logger.info(f"已删除空目录: {empty_dir}")
                else:
                    logger.info(f"模拟删除空目录: {empty_dir}")
            except Exception as e:
                error_msg = f"删除空目录失败 {empty_dir}: {e}"
                logger.error(error_msg)
                self.stats['errors'].append(error_msg)
        
        return True
    
    def format_file_size(self, size_bytes: int) -> str:
        """格式化文件大小"""
        if size_bytes == 0:
            return "0 B"
        
        size_names = ["B", "KB", "MB", "GB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1
        
        return f"{size_bytes:.1f} {size_names[i]}"
    
    def get_current_time(self) -> str:
        """获取当前时间字符串"""
        from datetime import datetime
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    def run_cleanup(self, dry_run: bool = True, generate_report: bool = True) -> Tuple[bool, str]:
        """运行完整的清理流程"""
        logger.info("开始前端资源清理...")
        
        # 1. 查找重复文件
        logger.info("正在查找重复文件...")
        duplicates = self.find_duplicate_files()
        
        if not duplicates:
            logger.info("未发现重复文件")
            return True, "未发现重复文件"
        
        # 2. 分析重复文件
        logger.info("正在分析重复文件...")
        analysis = self.analyze_duplicates(duplicates)
        
        # 3. 生成报告
        report = ""
        if generate_report:
            logger.info("正在生成清理报告...")
            report = self.generate_cleanup_report(analysis)
        
        # 4. 执行清理
        logger.info("正在执行清理操作...")
        cleanup_success = self.execute_cleanup(analysis, dry_run)
        
        # 5. 更新模板引用
        logger.info("正在更新模板引用...")
        template_success = self.update_template_references(analysis, dry_run)
        
        # 6. 清理空目录
        logger.info("正在清理空目录...")
        dir_success = self.clean_empty_directories(dry_run)
        
        success = cleanup_success and template_success and dir_success
        
        if success:
            logger.info("前端资源清理完成")
        else:
            logger.error(f"前端资源清理完成，但有 {len(self.stats['errors'])} 个错误")
        
        return success, report


def main():
    """主函数"""
    import sys
    
    # 设置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    # 获取项目根目录
    project_root = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
    
    # 创建清理器
    cleaner = FrontendResourceCleaner(project_root)
    
    # 检查命令行参数
    dry_run = '--execute' not in sys.argv
    
    if dry_run:
        print("🔍 运行模拟模式（不会实际删除文件）")
        print("💡 使用 --execute 参数执行实际清理")
    else:
        print("⚠️  运行实际清理模式（将删除重复文件）")
    
    # 执行清理
    success, report = cleaner.run_cleanup(dry_run=dry_run)
    
    # 保存报告
    if report:
        report_file = Path(project_root) / 'frontend_cleanup_report.md'
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        print(f"📄 清理报告已保存到: {report_file}")
    
    # 输出统计信息
    stats = cleaner.stats
    print("\n📊 清理统计:")
    print(f"   总文件数: {stats['total_files']}")
    print(f"   重复文件数: {stats['duplicate_files']}")
    print(f"   清理文件数: {stats['cleaned_files']}")
    print(f"   节省空间: {cleaner.format_file_size(stats['saved_space'])}")
    
    if stats['errors']:
        print(f"   错误数: {len(stats['errors'])}")
        for error in stats['errors'][:5]:  # 只显示前5个错误
            print(f"     - {error}")
        if len(stats['errors']) > 5:
            print(f"     ... 还有 {len(stats['errors']) - 5} 个错误")
    
    return 0 if success else 1


if __name__ == '__main__':
    exit(main()) 