{% extends "base.html" %}

{% block title %}权限管理
<style>
:root {
    --primary-color: #b72424;
    --secondary-color: #d73027;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
}

.navbar-brand, .nav-link.active {
    color: var(--primary-color) !important;
}

.card-header {
    background-color: var(--primary-color);
    color: white;
}
</style>
{% endblock %}

{% block styles %}
{{ super() }}
<style>
    .permissions-container {
        padding: 20px;
        background-color: var(--secondary-background);
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    .user-list {
        background-color: white;
        border-radius: 6px;
        padding: 15px;
        margin-bottom: 20px;
    }
    
    .permission-grid {
        background-color: white;
        border-radius: 6px;
        padding: 15px;
    }
    
    .menu-item {
        margin: 5px 0;
        padding: 8px;
        border-radius: 4px;
        background-color: #f8f9fa;
    }
    
    .menu-item:hover {
        background-color: #e9ecef;
    }
    
    .menu-item.parent {
        background-color: #e3f2fd;
    }
    
    .menu-item.child {
        margin-left: 20px;
    }
    
    .user-row {
        cursor: pointer;
        padding: 10px;
        border-radius: 4px;
    }
    
    .user-row:hover {
        background-color: #f8f9fa;
    }
    
    .user-row.active {
        background-color: #e3f2fd;
    }

    .permission-modal .modal-body {
        max-height: 600px;
        overflow-y: auto;
    }

    .permission-tree {
        max-height: 500px;
        overflow-y: auto;
        border: 1px solid #dee2e6;
        border-radius: 0.375rem;
        padding: 1rem;
    }

    .menu-group {
        margin-bottom: 1.5rem;
        border: 1px solid #e9ecef;
        border-radius: 0.375rem;
        padding: 1rem;
    }

    .menu-group-header {
        font-weight: 600;
        color: var(--primary-color);
        margin-bottom: 0.75rem;
        padding-bottom: 0.5rem;
        border-bottom: 1px solid #e9ecef;
    }

    .menu-item-checkbox {
        margin-bottom: 0.5rem;
    }

    .submenu-items {
        margin-left: 1.5rem;
        margin-top: 0.5rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="permissions-container">
    <h2 class="mb-4">权限管理</h2>
    
    <div class="row">
        <!-- 用户列表 -->
        <div class="col-md-4">
            <div class="user-list">
                <h4 class="mb-3">用户列表</h4>
                <div class="input-group mb-3">
                    <input type="text" class="form-control" id="userSearch" placeholder="搜索用户...">
                    <button class="btn btn-outline-secondary" type="button">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
                <div id="userList">
                    <!-- 用户列表将通过JavaScript动态填充 -->
                </div>
            </div>
        </div>
        
        <!-- 权限设置 -->
        <div class="col-md-8">
            <div class="permission-grid">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h4 class="mb-0">权限设置</h4>
                    <div>
                        <button class="btn btn-primary" onclick="openPermissionModal()" id="openPermissionBtn" disabled>
                            <i class="fas fa-key"></i> 设置权限
                        </button>
                    </div>
                </div>
                
                <div id="selectedUser" class="mb-3">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        请选择一个用户来设置权限
                    </div>
                </div>
                
                <div id="userPermissionsSummary">
                    <!-- 用户权限摘要将在这里显示 -->
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 权限设置模态框 -->
<div class="modal fade permission-modal" id="permissionModal" tabindex="-1" aria-labelledby="permissionModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="permissionModalLabel">
                    <i class="fas fa-key me-2"></i>设置用户权限
                    <span id="permissionUserBadge" class="badge bg-primary ms-2"></span>
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>权限说明：</strong>选择父菜单将自动选择所有子菜单，取消父菜单将自动取消所有子菜单。管理员用户拥有所有权限且无法修改。
                </div>
                
                <div class="permission-tree" id="permissionTree">
                    <!-- 权限树将通过JavaScript动态生成 -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="savePermissions()">
                    <i class="fas fa-save me-1"></i>保存权限
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script>
    // 全局变量
    let allMenus = [];
    let allUsers = [];
    let currentUser = null;
    let userPermissions = [];
    
    // 页面加载完成后执行
    document.addEventListener('DOMContentLoaded', function() {
        loadUsers();
        loadMenus();
    });
    
    // 加载用户列表
    async function loadUsers() {
        try {
            const response = await fetch('/auth/users');
            if (!response.ok) throw new Error('Failed to load users');
            
            allUsers = await response.json();
            renderUserList(allUsers);
        } catch (error) {
            console.error('Error loading users:', error);
            showAlert('加载用户列表失败: ' + error.message, 'danger');
        }
    }
    
    // 加载菜单配置
    async function loadMenus() {
        try {
            const response = await fetch('/api/v2/auth/menu-settings');
            if (!response.ok) throw new Error('Failed to load menus');
            
            allMenus = await response.json();
            console.log('菜单配置加载成功:', allMenus);
        } catch (error) {
            console.error('Error loading menus:', error);
            showAlert('加载菜单配置失败: ' + error.message, 'danger');
        }
    }
    
    // 渲染用户列表
    function renderUserList(users) {
        const userList = document.getElementById('userList');
        userList.innerHTML = '';
        
        users.forEach(user => {
            const div = document.createElement('div');
            div.className = 'user-row';
            div.onclick = function(event) { selectUser(user, event); };
            div.innerHTML = `
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <strong>${user.username}</strong>
                        <div class="small text-muted">${user.role}</div>
                    </div>
                    <span class="badge ${getRoleBadgeClass(user.role)}">
                        ${user.role}
                    </span>
                </div>
            `;
            userList.appendChild(div);
        });
    }
    
    // 获取角色徽章样式
    function getRoleBadgeClass(role) {
        switch(role) {
            case 'admin': return 'bg-danger';
            case 'tech': return 'bg-primary';
            case 'op': return 'bg-success';
            case 'boss': return 'bg-warning';
            default: return 'bg-secondary';
        }
    }
    
    // 选择用户
    async function selectUser(user, event) {
        currentUser = user;
        
        // 更新UI
        document.querySelectorAll('.user-row').forEach(row => {
            row.classList.remove('active');
        });
        event.currentTarget.classList.add('active');
        
        // 显示选中用户信息
        document.getElementById('selectedUser').innerHTML = `
            <div class="alert alert-primary">
                <i class="fas fa-user me-2"></i>
                当前选中用户: <strong>${user.username}</strong> (${user.role})
                ${user.role === 'admin' ? '<span class="badge bg-danger ms-2">管理员拥有所有权限</span>' : ''}
            </div>
        `;
        
        // 启用权限设置按钮
        document.getElementById('openPermissionBtn').disabled = false;
        
        // 加载用户权限并显示摘要
        await loadUserPermissions(user.username);
        renderPermissionsSummary();
        }
        
        // 加载用户权限
    async function loadUserPermissions(username) {
        try {
            if (currentUser.role === 'admin') {
                // 管理员拥有所有权限
                userPermissions = allMenus.map(menu => menu.id);
            } else {
                const response = await fetch(`/api/v2/auth/users/${username}/permissions`);
            if (!response.ok) throw new Error('Failed to load user permissions');
            
                const data = await response.json();
                userPermissions = data.permissions || [];
            }
        } catch (error) {
            console.error('Error loading user permissions:', error);
            userPermissions = [];
            showAlert('加载用户权限失败: ' + error.message, 'warning');
        }
    }
    
    // 渲染权限摘要
    function renderPermissionsSummary() {
        const summaryDiv = document.getElementById('userPermissionsSummary');
        
        if (userPermissions.length === 0) {
            summaryDiv.innerHTML = `
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    该用户暂无任何权限
                </div>
            `;
            return;
        }
        
        // 获取用户拥有的菜单
        const userMenus = allMenus.filter(menu => userPermissions.includes(menu.id));
        const parentMenus = userMenus.filter(menu => !menu.parent_id);
        
        let summaryHtml = `
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">当前权限 (${userPermissions.length}个)</h6>
                </div>
                <div class="card-body">
                    <div class="row">
        `;
        
        parentMenus.forEach(parentMenu => {
            const children = userMenus.filter(menu => menu.parent_id === parentMenu.id);
            summaryHtml += `
                <div class="col-md-6 mb-3">
                    <div class="border rounded p-2">
                        <div class="fw-bold text-primary mb-1">
                            <i class="${parentMenu.icon || 'fas fa-folder'} me-1"></i>
                            ${parentMenu.name}
                        </div>
                        ${children.length > 0 ? `
                            <div class="small text-muted ms-3">
                                ${children.map(child => `• ${child.name}`).join('<br>')}
                            </div>
                        ` : ''}
                    </div>
                </div>
            `;
        });
        
        summaryHtml += `
                    </div>
                </div>
            </div>
        `;
        
        summaryDiv.innerHTML = summaryHtml;
    }
    
    // 打开权限设置模态框
    function openPermissionModal() {
        if (!currentUser) {
            showAlert('请先选择一个用户', 'warning');
            return;
        }
        
        // 设置模态框标题
        document.getElementById('permissionUserBadge').textContent = currentUser.username;
        
        // 生成权限树
        renderPermissionTree();
        
        // 显示模态框
        const modal = new bootstrap.Modal(document.getElementById('permissionModal'));
        modal.show();
    }
    
    // 渲染权限树
    function renderPermissionTree() {
        const treeDiv = document.getElementById('permissionTree');
        
        if (allMenus.length === 0) {
            treeDiv.innerHTML = `
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    菜单配置加载失败，请刷新页面重试
                </div>
            `;
            return;
        }
        
        // 按父菜单分组
        const parentMenus = allMenus.filter(menu => !menu.parent_id).sort((a, b) => (a.order || 0) - (b.order || 0));
        
        let treeHtml = '';
        
        parentMenus.forEach(parentMenu => {
            const children = allMenus.filter(menu => menu.parent_id === parentMenu.id).sort((a, b) => (a.order || 0) - (b.order || 0));
            const isParentChecked = userPermissions.includes(parentMenu.id);
            const isDisabled = currentUser?.role === 'admin';
            
            treeHtml += `
                <div class="menu-group">
                    <div class="menu-group-header">
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input parent-menu" 
                                   id="menu_${parentMenu.id}" 
                                   value="${parentMenu.id}"
                                   ${isParentChecked ? 'checked' : ''}
                                   ${isDisabled ? 'disabled' : ''}
                                   onchange="handleParentMenuChange(${parentMenu.id})">
                            <label class="form-check-label fw-bold" for="menu_${parentMenu.id}">
                                <i class="${parentMenu.icon || 'fas fa-folder'} me-2"></i>
                                ${parentMenu.name}
                            </label>
                        </div>
                    </div>
            `;
            
            if (children.length > 0) {
                treeHtml += '<div class="submenu-items">';
                children.forEach(childMenu => {
                    const isChildChecked = userPermissions.includes(childMenu.id);
                    treeHtml += `
                        <div class="menu-item-checkbox">
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input child-menu" 
                                       id="menu_${childMenu.id}" 
                                       value="${childMenu.id}"
                                       data-parent="${parentMenu.id}"
                                       ${isChildChecked ? 'checked' : ''}
                                       ${isDisabled ? 'disabled' : ''}
                                       onchange="handleChildMenuChange(${parentMenu.id})">
                                <label class="form-check-label" for="menu_${childMenu.id}">
                                    <i class="${childMenu.icon || 'fas fa-file'} me-2"></i>
                                    ${childMenu.name}
                                </label>
                            </div>
                        </div>
                    `;
                });
                treeHtml += '</div>';
            }
            
            treeHtml += '</div>';
        });
        
        treeDiv.innerHTML = treeHtml;
    }
    
    // 处理父菜单变更
    function handleParentMenuChange(parentId) {
        const parentCheckbox = document.getElementById(`menu_${parentId}`);
        const childCheckboxes = document.querySelectorAll(`input[data-parent="${parentId}"]`);
        
        childCheckboxes.forEach(checkbox => {
            checkbox.checked = parentCheckbox.checked;
        });
    }
    
    // 处理子菜单变更
    function handleChildMenuChange(parentId) {
        const parentCheckbox = document.getElementById(`menu_${parentId}`);
        const childCheckboxes = document.querySelectorAll(`input[data-parent="${parentId}"]`);
        
        const checkedChildren = Array.from(childCheckboxes).filter(cb => cb.checked);
        parentCheckbox.checked = checkedChildren.length > 0;
        parentCheckbox.indeterminate = checkedChildren.length > 0 && checkedChildren.length < childCheckboxes.length;
    }
    
    // 保存权限
    async function savePermissions() {
        if (!currentUser) {
            showAlert('请先选择用户', 'warning');
            return;
        }
        
        if (currentUser.role === 'admin') {
            showAlert('管理员权限无法修改', 'info');
            return;
        }
        
        // 获取所有选中的权限
        const selectedPermissions = Array.from(document.querySelectorAll('#permissionTree input[type="checkbox"]:checked'))
            .map(cb => parseInt(cb.value));
        
        try {
            const response = await fetch(`/api/v2/auth/users/${currentUser.username}/permissions`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    menu_ids: selectedPermissions
                })
            });
            
            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.message || 'Failed to save permissions');
            }
            
            const result = await response.json();
            
            // 更新本地权限数据
            userPermissions = selectedPermissions;
            
            // 刷新权限摘要
            renderPermissionsSummary();
            
            // 关闭模态框
            bootstrap.Modal.getInstance(document.getElementById('permissionModal')).hide();
            
            showAlert('权限保存成功！', 'success');
            
        } catch (error) {
            console.error('Error saving permissions:', error);
            showAlert('保存权限失败: ' + error.message, 'danger');
        }
    }
    
    // 显示提示信息
    function showAlert(message, type = 'info') {
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        // 插入到页面顶部
        const container = document.querySelector('.permissions-container');
        container.insertBefore(alertDiv, container.firstChild);
        
        // 5秒后自动消失
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 5000);
    }
    
    // 搜索用户
    document.getElementById('userSearch').addEventListener('input', function(e) {
        const searchTerm = e.target.value.toLowerCase();
        const filteredUsers = allUsers.filter(user => 
            user.username.toLowerCase().includes(searchTerm) || 
            user.role.toLowerCase().includes(searchTerm)
        );
        renderUserList(filteredUsers);
    });
</script>
{% endblock %} 