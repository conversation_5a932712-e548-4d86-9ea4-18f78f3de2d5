#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

print("检查各表数据情况...")

try:
    from app.services.data_source_manager import DataSourceManager
    
    dm = DataSourceManager()
    
    # 检查关键表
    tables_to_check = [
        'ET_FT_TEST_SPEC',
        'ET_RECIPE_FILE', 
        'et_recipe_file',
        'ET_WAIT_LOT',
        'EQP_STATUS',
        'ET_UPH_EQP'
    ]
    
    print(f"\n=== 数据表检查结果 ===")
    for table_name in tables_to_check:
        try:
            result = dm.get_table_data(table_name)
            if result.get('success'):
                data = result.get('data', [])
                print(f"✅ {table_name}: {len(data)} 条数据")
                
                # 显示字段信息（只对有数据的表）
                if data and len(data) > 0:
                    fields = list(data[0].keys())
                    print(f"   字段数: {len(fields)}")
                    
                    # 对关键表显示重要字段
                    if table_name == 'ET_FT_TEST_SPEC':
                        key_fields = ['DEVICE', 'STAGE', 'PKG_PN', 'HB_PN', 'TB_PN', 'APPROVAL_STATE']
                        available_fields = [f for f in key_fields if f in fields]
                        print(f"   关键字段: {available_fields}")
                    elif table_name in ['ET_RECIPE_FILE', 'et_recipe_file']:
                        # 查找KIT相关字段
                        kit_fields = [f for f in fields if 'KIT' in f.upper() or 'SOCKET' in f.upper()]
                        print(f"   KIT相关字段: {kit_fields}")
                    elif table_name == 'EQP_STATUS':
                        eqp_fields = ['DEVICE', 'HANDLER_ID', 'TESTER_ID', 'HB_PN', 'TB_PN', 'KIT_PN', 'STATUS']
                        available_fields = [f for f in eqp_fields if f in fields]
                        print(f"   设备字段: {available_fields}")
                        
            else:
                print(f"❌ {table_name}: 访问失败")
                
        except Exception as e:
            print(f"❌ {table_name}: 异常 - {str(e)}")
    
    # 检查ET_FT_TEST_SPEC的数据示例
    print(f"\n=== ET_FT_TEST_SPEC 数据示例 ===")
    specs_result = dm.get_table_data('ET_FT_TEST_SPEC')
    if specs_result.get('success'):
        specs_data = specs_result.get('data', [])[:3]  # 取前3条
        for idx, spec in enumerate(specs_data):
            print(f"第{idx+1}条:")
            for field in ['DEVICE', 'STAGE', 'PKG_PN', 'HB_PN', 'TB_PN', 'APPROVAL_STATE']:
                if field in spec:
                    print(f"  {field}: {spec[field]}")
            print()
            
except Exception as e:
    print(f"错误: {e}")
    import traceback
    traceback.print_exc()
