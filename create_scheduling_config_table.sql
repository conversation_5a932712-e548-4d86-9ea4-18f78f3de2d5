-- 创建排产算法权重配置表
-- 该表存储在 aps_system 数据库中

USE aps_system;

-- 删除已存在的表（如果存在）
DROP TABLE IF EXISTS `scheduling_config`;

-- 创建排产算法权重配置表
CREATE TABLE `scheduling_config` (
  `id` int NOT NULL AUTO_INCREMENT,
  `config_name` varchar(100) NOT NULL COMMENT '配置名称',
  `user_id` varchar(50) DEFAULT NULL COMMENT '用户ID，NULL表示默认配置',
  
  -- 五个维度的权重配置
  `tech_match_weight` decimal(5,2) DEFAULT 25.00 COMMENT '技术匹配度权重(%)',
  `load_balance_weight` decimal(5,2) DEFAULT 20.00 COMMENT '负载均衡权重(%)',
  `deadline_weight` decimal(5,2) DEFAULT 25.00 COMMENT '交期紧迫度权重(%)',
  `value_efficiency_weight` decimal(5,2) DEFAULT 20.00 COMMENT '产值效率权重(%)',
  `business_priority_weight` decimal(5,2) DEFAULT 10.00 COMMENT '业务优先级权重(%)',
  
  -- 其他配置参数
  `minor_changeover_time` int DEFAULT 45 COMMENT '小改机时间(分钟)',
  `major_changeover_time` int DEFAULT 120 COMMENT '大改机时间(分钟)',
  `urgent_threshold` int DEFAULT 8 COMMENT '紧急阈值(小时)',
  `normal_threshold` int DEFAULT 24 COMMENT '正常阈值(小时)',
  `critical_threshold` int DEFAULT 72 COMMENT '关键阈值(小时)',
  
  `is_active` tinyint(1) DEFAULT 1 COMMENT '是否激活',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_config` (`user_id`, `config_name`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='排产算法权重配置表';

-- 插入默认配置数据
INSERT INTO `scheduling_config` (
  `config_name`, 
  `user_id`, 
  `tech_match_weight`, 
  `load_balance_weight`, 
  `deadline_weight`, 
  `value_efficiency_weight`, 
  `business_priority_weight`,
  `minor_changeover_time`,
  `major_changeover_time`,
  `urgent_threshold`,
  `normal_threshold`,
  `critical_threshold`,
  `is_active`
) VALUES (
  'default_config',
  NULL,  -- NULL表示默认配置
  25.00,  -- 技术匹配度权重
  20.00,  -- 负载均衡权重
  25.00,  -- 交期紧迫度权重
  20.00,  -- 产值效率权重
  10.00,  -- 业务优先级权重
  45,     -- 小改机时间
  120,    -- 大改机时间
  8,      -- 紧急阈值
  24,     -- 正常阈值
  72,     -- 关键阈值
  1       -- 激活状态
);

-- 插入一个示例用户配置（可选）
INSERT INTO `scheduling_config` (
  `config_name`, 
  `user_id`, 
  `tech_match_weight`, 
  `load_balance_weight`, 
  `deadline_weight`, 
  `value_efficiency_weight`, 
  `business_priority_weight`,
  `minor_changeover_time`,
  `major_changeover_time`,
  `urgent_threshold`,
  `normal_threshold`,
  `critical_threshold`,
  `is_active`
) VALUES (
  'admin_config',
  'admin',
  30.00,  -- 管理员可能更重视技术匹配度
  15.00,  -- 降低负载均衡权重
  30.00,  -- 提高交期紧迫度权重
  20.00,  -- 保持产值效率权重
  5.00,   -- 降低业务优先级权重
  45,     -- 小改机时间
  120,    -- 大改机时间
  8,      -- 紧急阈值
  24,     -- 正常阈值
  72,     -- 关键阈值
  1       -- 激活状态
);

-- 验证数据插入
SELECT * FROM `scheduling_config`;

-- 显示表结构
DESCRIBE `scheduling_config`;
