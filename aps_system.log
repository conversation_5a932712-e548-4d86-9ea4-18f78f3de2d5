 2025-06-15 14:20:42,459 - APS-System - INFO - 应用路径: D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12
2025-06-15 14:20:42,460 - APS-System - INFO - 实例路径: D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\instance
2025-06-15 14:20:42,460 - APS-System - INFO - 检查 MySQL 数据库配置...
2025-06-15 14:20:42,515 - APS-System - INFO - MySQL 数据库 'aps' 已存在
2025-06-15 14:20:42,517 - APS-System - INFO - MySQL 数据库 'aps_system' 已存在
2025-06-15 14:20:42,517 - APS-System - INFO - MySQL 数据库检查成功
2025-06-15 14:20:42,518 - APS-System - INFO - 正在修复数据库权限...
2025-06-15 14:20:42,518 - APS-System - INFO - 发现SQLite文件，但系统已迁移到MySQL，跳过: instance\aps.db
2025-06-15 14:20:42,519 - APS-System - INFO - 数据库文件不存在，跳过: instance\aps_system.db
2025-06-15 14:20:42,519 - APS-System - INFO - 已修复日志目录权限: logs
2025-06-15 14:20:42,520 - APS-System - INFO - 数据库权限修复成功
2025-06-15 14:20:44,805 - app - INFO - 应用启动
2025-06-15 14:20:44,806 - app.utils.scheduler - INFO - 已重新加载 0 个邮箱配置（应用实例未设置）
2025-06-15 14:20:44,814 - app.utils.scheduler - INFO - 邮件附件定时调度器已启动
2025-06-15 14:20:44,830 - app - INFO - 邮件附件定时任务调度器已启动
2025-06-15 14:20:44,868 - app.utils.scheduler - INFO - 全局定时任务已禁用，邮件调度器跟随禁用
2025-06-15 14:20:44,870 - app.utils.scheduler - INFO - 检测到全局定时任务已禁用，停止邮件调度器
2025-06-15 14:20:45,017 - app.services.scheduler_service - INFO - APScheduler调度器配置完成: timezone=Asia/Shanghai, max_workers=10
2025-06-15 14:20:45,044 - apscheduler.scheduler - INFO - Scheduler started
2025-06-15 14:20:45,050 - app.services.scheduler_service - INFO - 从数据库加载了 0 个任务
2025-06-15 14:20:45,051 - app.services.scheduler_service - INFO - APScheduler调度器启动成功
2025-06-15 14:20:45,052 - app - INFO - ✅ APScheduler统一调度器启动成功
2025-06-15 14:20:45,054 - APS-System - INFO - 应用创建成功
2025-06-15 14:20:45,055 - APS-System - INFO - ==================================================
2025-06-15 14:20:45,055 - APS-System - INFO - 🚀 APS车规芯片终测智能调度平台 v2.1
2025-06-15 14:20:45,055 - APS-System - INFO - 📊 100% MySQL数据库架构
2025-06-15 14:20:45,055 - APS-System - INFO - 🌐 访问地址: http://1********:5000
2025-06-15 14:20:45,055 - APS-System - INFO - ==================================================
2025-06-15 14:20:45,055 - APS-System - INFO - 运行模式: 开发环境
2025-06-15 14:20:45,070 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://1********:5000
2025-06-15 14:20:45,070 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-15 14:20:47,202 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:20:47] "[32mGET / HTTP/1.1[0m" 302 -
2025-06-15 14:20:47,217 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:20:47] "GET /auth/login?next=/ HTTP/1.1" 200 -
2025-06-15 14:20:47,787 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:20:47] "GET /static/css/compatibility.css HTTP/1.1" 200 -
2025-06-15 14:20:47,800 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:20:47] "GET /static/js/login/bootstrap.bundle.min.js HTTP/1.1" 200 -
2025-06-15 14:20:47,805 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:20:47] "GET /static/css/all-5.15.4.min.css HTTP/1.1" 200 -
2025-06-15 14:20:47,808 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:20:47] "GET /static/css/login/bootstrap.min.css HTTP/1.1" 200 -
2025-06-15 14:20:47,864 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:20:47] "GET /static/webfonts/fa-solid-900.woff2 HTTP/1.1" 200 -
2025-06-15 14:20:48,902 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:20:48] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-06-15 14:20:49,480 - app.auth.routes - INFO - 尝试登录用户: admin
2025-06-15 14:20:49,489 - app.auth.routes - INFO - 找到用户: admin, 角色: admin, 密码哈希: scrypt:32768:8:1$X8JU5hxR74hpH0tP$36e5b028b335f5d2f9f21e9178a0d529cca80459428b1128d44d8ac646d8f16cdd0823ea12d58ce5bbae1dd243eeb7a50c06f55d6688542dae997f7c75e6ee24
2025-06-15 14:20:49,612 - app.models - INFO - 用户 admin 密码验证结果: True
2025-06-15 14:20:49,613 - app.auth.routes - INFO - 密码验证结果: True
2025-06-15 14:20:49,613 - app.auth.routes - INFO - 用户 admin 登录成功
2025-06-15 14:20:49,622 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:20:49] "[32mPOST /auth/login HTTP/1.1[0m" 302 -
2025-06-15 14:20:49,648 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:20:49] "GET /index HTTP/1.1" 200 -
2025-06-15 14:20:49,747 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:20:49] "GET /static/css/base/bootstrap.min.css HTTP/1.1" 200 -
2025-06-15 14:20:50,001 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:20:50] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 14:20:50,002 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:20:50] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 14:20:50,003 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:20:50] "GET /static/js/base/bootstrap.bundle.min.js HTTP/1.1" 200 -
2025-06-15 14:20:50,004 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:20:50] "GET /static/js/menu-optimizer.js HTTP/1.1" 200 -
2025-06-15 14:20:50,325 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:20:50] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 14:20:50,331 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:20:50] "GET /menu HTTP/1.1" 200 -
2025-06-15 14:20:52,157 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:20:52] "GET /production/semi-auto HTTP/1.1" 200 -
2025-06-15 14:20:52,443 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:20:52] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 14:20:52,523 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:20:52] "GET /static/css/core/main.css HTTP/1.1" 200 -
2025-06-15 14:20:52,525 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:20:52] "GET /static/css/daygrid/main.css HTTP/1.1" 200 -
2025-06-15 14:20:52,530 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:20:52] "GET /static/css/timegrid/main.css HTTP/1.1" 200 -
2025-06-15 14:20:52,531 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:20:52] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 14:20:52,532 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:20:52] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 14:20:52,753 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:20:52] "GET /static/js/xlsx.full.min.js HTTP/1.1" 200 -
2025-06-15 14:20:52,846 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:20:52] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 14:20:52,847 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:20:52] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 14:20:53,235 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:20:53] "GET /api/production/get-import-path HTTP/1.1" 200 -
2025-06-15 14:20:53,234 - app - INFO - 成功连接到MySQL数据库: aps
2025-06-15 14:20:53,272 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:20:53] "GET /api/production/imported-files HTTP/1.1" 200 -
2025-06-15 14:20:53,811 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:20:53] "GET /production/auto HTTP/1.1" 200 -
2025-06-15 14:20:54,102 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:20:54] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 14:20:54,166 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:20:54] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 14:20:54,167 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:20:54] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 14:20:54,173 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:20:54] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 14:20:54,177 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:20:54] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 14:20:54,510 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:20:54] "GET /api/production/schedule-history HTTP/1.1" 200 -
2025-06-15 14:22:58,589 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:22:58] "GET /production/priority-matching HTTP/1.1" 200 -
2025-06-15 14:22:58,667 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:22:58] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 14:22:58,837 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:22:58] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 14:22:58,955 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:22:58] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 14:22:58,956 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:22:58] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 14:22:58,957 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:22:58] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 14:22:58,958 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:22:58] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 14:23:05,063 - app.api.priority_matching_api - INFO - 开始批次匹配: strategy=intelligent, threshold=0.5
2025-06-15 14:23:05,064 - app.services.priority_matching_service - INFO - 开始批次匹配，策略: intelligent, 置信度阈值: 0.5
2025-06-15 14:23:05,084 - app.services.priority_matching_service - INFO - 获取到 171 个待排产批次
2025-06-15 14:23:05,093 - app.services.priority_matching_service - INFO - 获取到 21 个优先级配置
2025-06-15 14:23:05,772 - app.services.priority_matching_service - INFO - 匹配完成: 总批次 171, 成功匹配 171, 匹配率 100.0%
2025-06-15 14:23:06,057 - app.api.priority_matching_api - INFO - 批次匹配完成: 总数=171, 匹配率=100.0%
2025-06-15 14:23:06,063 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:23:06] "POST /api/priority/match-batches HTTP/1.1" 200 -
2025-06-15 14:23:32,990 - app.services.priority_matching_service - INFO - 匹配缓存已清空
2025-06-15 14:23:32,993 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:23:32] "POST /api/priority/clear-cache HTTP/1.1" 200 -
2025-06-15 14:24:11,680 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:24:11] "GET /resources/hardware HTTP/1.1" 200 -
2025-06-15 14:24:11,973 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:24:11] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 14:24:12,039 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:24:12] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 14:24:12,040 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:24:12] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 14:24:12,041 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:24:12] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 14:24:12,042 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:24:12] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 14:24:12,393 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:24:12] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 14:24:58,606 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:24:58] "GET /production/algorithm HTTP/1.1" 200 -
2025-06-15 14:24:58,910 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:24:58] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 14:24:58,959 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:24:58] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 14:24:58,961 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:24:58] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 14:24:58,963 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:24:58] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 14:24:58,964 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:24:58] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 14:24:58,968 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:24:58] "[36mGET /static/js/xlsx.full.min.js HTTP/1.1[0m" 304 -
2025-06-15 14:24:59,297 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:24:59] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 14:24:59,390 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:24:59] "[33mGET /api/production/history-times HTTP/1.1[0m" 404 -
2025-06-15 14:25:06,221 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:25:06] "GET /production/data HTTP/1.1" 200 -
2025-06-15 14:25:06,505 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:25:06] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 14:25:06,590 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:25:06] "GET /static/vendor/echarts/echarts.min.js HTTP/1.1" 200 -
2025-06-15 14:25:06,592 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:25:06] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 14:25:06,595 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:25:06] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 14:25:06,600 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:25:06] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 14:25:06,601 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:25:06] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 14:25:06,924 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:25:06] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 14:25:07,014 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:25:07] "[33mGET /api/production/chart-data?table=Lot_WIP HTTP/1.1[0m" 404 -
2025-06-15 14:25:07,015 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:25:07] "[33mGET /api/production/table-data?table=Lot_WIP&page=1&size=20 HTTP/1.1[0m" 404 -
2025-06-15 14:25:14,797 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:25:14] "[33mGET /api/production/table-data?table=Lot_WIP&page=1&size=20 HTTP/1.1[0m" 404 -
2025-06-15 14:25:14,800 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:25:14] "[33mGET /api/production/chart-data?table=Lot_WIP HTTP/1.1[0m" 404 -
2025-06-15 14:25:30,267 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:25:30] "[33mGET /api/production/table-data?table=HANDLER_WIP&page=1&size=20 HTTP/1.1[0m" 404 -
2025-06-15 14:25:30,581 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:25:30] "[33mGET /api/production/chart-data?table=HANDLER_WIP HTTP/1.1[0m" 404 -
2025-06-15 14:25:34,523 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:25:34] "[33mGET /api/production/table-data?table=EQP_STATUS&page=1&size=20 HTTP/1.1[0m" 404 -
2025-06-15 14:25:34,828 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:25:34] "[33mGET /api/production/chart-data?table=EQP_STATUS HTTP/1.1[0m" 404 -
2025-06-15 14:38:43,791 - APS-System - INFO - 应用路径: D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12
2025-06-15 14:38:43,792 - APS-System - INFO - 实例路径: D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\instance
2025-06-15 14:38:43,792 - APS-System - INFO - 检查 MySQL 数据库配置...
2025-06-15 14:38:43,843 - APS-System - INFO - MySQL 数据库 'aps' 已存在
2025-06-15 14:38:43,844 - APS-System - INFO - MySQL 数据库 'aps_system' 已存在
2025-06-15 14:38:43,844 - APS-System - INFO - MySQL 数据库检查成功
2025-06-15 14:38:43,844 - APS-System - INFO - 正在修复数据库权限...
2025-06-15 14:38:43,845 - APS-System - INFO - 发现SQLite文件，但系统已迁移到MySQL，跳过: instance\aps.db
2025-06-15 14:38:43,846 - APS-System - INFO - 数据库文件不存在，跳过: instance\aps_system.db
2025-06-15 14:38:43,846 - APS-System - INFO - 已修复日志目录权限: logs
2025-06-15 14:38:43,847 - APS-System - INFO - 数据库权限修复成功
2025-06-15 14:38:45,776 - app - INFO - 应用启动
2025-06-15 14:38:45,777 - app.utils.scheduler - INFO - 已重新加载 0 个邮箱配置（应用实例未设置）
2025-06-15 14:38:45,798 - app.utils.scheduler - INFO - 邮件附件定时调度器已启动
2025-06-15 14:38:45,822 - app - INFO - 邮件附件定时任务调度器已启动
2025-06-15 14:38:45,830 - app.utils.scheduler - INFO - 全局定时任务已禁用，邮件调度器跟随禁用
2025-06-15 14:38:45,831 - app.utils.scheduler - INFO - 检测到全局定时任务已禁用，停止邮件调度器
2025-06-15 14:38:45,977 - app.services.scheduler_service - INFO - APScheduler调度器配置完成: timezone=Asia/Shanghai, max_workers=10
2025-06-15 14:38:46,011 - apscheduler.scheduler - INFO - Scheduler started
2025-06-15 14:38:46,018 - app.services.scheduler_service - INFO - 从数据库加载了 0 个任务
2025-06-15 14:38:46,020 - app.services.scheduler_service - INFO - APScheduler调度器启动成功
2025-06-15 14:38:46,021 - app - INFO - ✅ APScheduler统一调度器启动成功
2025-06-15 14:38:46,022 - APS-System - INFO - 应用创建成功
2025-06-15 14:38:46,022 - APS-System - INFO - ==================================================
2025-06-15 14:38:46,023 - APS-System - INFO - 🚀 APS车规芯片终测智能调度平台 v2.1
2025-06-15 14:38:46,023 - APS-System - INFO - 📊 100% MySQL数据库架构
2025-06-15 14:38:46,023 - APS-System - INFO - 🌐 访问地址: http://1********:5000
2025-06-15 14:38:46,023 - APS-System - INFO - ==================================================
2025-06-15 14:38:46,023 - APS-System - INFO - 运行模式: 开发环境
2025-06-15 14:38:46,047 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://1********:5000
2025-06-15 14:38:46,049 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-15 14:38:48,146 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:38:48] "GET / HTTP/1.1" 200 -
2025-06-15 14:38:48,451 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:38:48] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 14:38:48,503 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:38:48] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 14:38:48,504 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:38:48] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 14:38:48,506 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:38:48] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 14:38:48,588 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:38:48] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 14:38:48,986 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:38:48] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 14:38:50,731 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:38:50] "GET /resources/hardware HTTP/1.1" 200 -
2025-06-15 14:38:51,025 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:38:51] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 14:38:51,094 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:38:51] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 14:38:51,095 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:38:51] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 14:38:51,101 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:38:51] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 14:38:51,103 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:38:51] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 14:38:51,444 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:38:51] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 14:38:57,236 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:38:57] "GET /resources/specs HTTP/1.1" 200 -
2025-06-15 14:38:57,520 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:38:57] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 14:38:57,599 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:38:57] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 14:38:57,602 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:38:57] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 14:38:57,605 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:38:57] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 14:38:57,609 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:38:57] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 14:39:00,404 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:39:00] "GET /resources/tester HTTP/1.1" 200 -
2025-06-15 14:39:00,516 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:39:00] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 14:39:00,654 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:39:00] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 14:39:00,762 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:39:00] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 14:39:00,763 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:39:00] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 14:39:00,764 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:39:00] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 14:39:01,714 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:39:01] "GET /resources/uph HTTP/1.1" 200 -
2025-06-15 14:39:02,010 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:39:02] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 14:39:02,074 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:39:02] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 14:39:02,075 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:39:02] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 14:39:02,080 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:39:02] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 14:39:02,080 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:39:02] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 14:39:02,419 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:39:02] "[33mGET /api/user-filter-presets?page_type=ET_UPH_EQP HTTP/1.1[0m" 404 -
2025-06-15 14:39:02,448 - app - INFO - 成功连接到MySQL数据库: aps
2025-06-15 14:39:02,464 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:39:02] "GET /api/resources/data/ET_UPH_EQP?page=1&per_page=50 HTTP/1.1" 200 -
2025-06-15 14:39:04,115 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:39:04] "GET /resources/product-cycle HTTP/1.1" 200 -
2025-06-15 14:39:04,410 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:39:04] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 14:39:04,476 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:39:04] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 14:39:04,478 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:39:04] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 14:39:04,481 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:39:04] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 14:39:04,483 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:39:04] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 14:39:06,471 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:39:06] "GET /resources/uph HTTP/1.1" 200 -
2025-06-15 14:39:06,575 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:39:06] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 14:39:06,731 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:39:06] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 14:39:06,826 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:39:06] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 14:39:06,828 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:39:06] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 14:39:06,830 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:39:06] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 14:39:06,860 - app - INFO - 成功连接到MySQL数据库: aps
2025-06-15 14:39:06,885 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:39:06] "GET /api/resources/data/ET_UPH_EQP?page=1&per_page=50 HTTP/1.1" 200 -
2025-06-15 14:39:07,163 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:39:07] "[33mGET /api/user-filter-presets?page_type=ET_UPH_EQP HTTP/1.1[0m" 404 -
2025-06-15 14:39:19,133 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:39:19] "GET /resources/hardware HTTP/1.1" 200 -
2025-06-15 14:39:19,250 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:39:19] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 14:39:19,402 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:39:19] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 14:39:19,487 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:39:19] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 14:39:19,488 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:39:19] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 14:39:19,490 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:39:19] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 14:39:46,595 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:39:46] "GET /resources/product-cycle HTTP/1.1" 200 -
2025-06-15 14:39:46,892 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:39:46] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 14:39:46,964 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:39:46] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 14:39:46,964 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:39:46] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 14:39:46,965 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:39:46] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 14:39:46,967 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:39:46] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 14:39:48,701 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:39:48] "GET /resources/hardware HTTP/1.1" 200 -
2025-06-15 14:39:48,789 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:39:48] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 14:39:48,961 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:39:48] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 14:39:49,047 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:39:49] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 14:39:49,047 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:39:49] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 14:39:49,048 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:39:49] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 14:39:50,607 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:39:50] "GET /resources/specs HTTP/1.1" 200 -
2025-06-15 14:39:50,920 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:39:50] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 14:39:50,972 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:39:50] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 14:39:50,974 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:39:50] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 14:39:50,976 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:39:50] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 14:39:50,979 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:39:50] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 14:39:52,641 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:39:52] "GET /resources/tester HTTP/1.1" 200 -
2025-06-15 14:39:52,761 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:39:52] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 14:39:52,902 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:39:52] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 14:39:52,999 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:39:52] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 14:39:53,000 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:39:53] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 14:39:53,004 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:39:53] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 14:39:55,808 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:39:55] "GET /resources/uph HTTP/1.1" 200 -
2025-06-15 14:39:56,109 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:39:56] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 14:39:56,176 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:39:56] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 14:39:56,179 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:39:56] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 14:39:56,181 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:39:56] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 14:39:56,183 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:39:56] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 14:39:56,536 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:39:56] "[33mGET /api/user-filter-presets?page_type=ET_UPH_EQP HTTP/1.1[0m" 404 -
2025-06-15 14:39:56,559 - app - INFO - 成功连接到MySQL数据库: aps
2025-06-15 14:39:56,575 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:39:56] "GET /api/resources/data/ET_UPH_EQP?page=1&per_page=50 HTTP/1.1" 200 -
2025-06-15 14:40:01,728 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:40:01] "GET /resources/hardware HTTP/1.1" 200 -
2025-06-15 14:40:02,040 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:40:02] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 14:40:02,076 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:40:02] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 14:40:02,079 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:40:02] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 14:40:02,084 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:40:02] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 14:40:02,092 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:40:02] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 14:40:53,709 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:40:53] "GET /resources/uph HTTP/1.1" 200 -
2025-06-15 14:40:53,806 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:40:53] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 14:40:53,967 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:40:53] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 14:40:54,064 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:40:54] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 14:40:54,064 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:40:54] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 14:40:54,065 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:40:54] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 14:40:54,065 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:40:54] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 14:40:54,284 - app - INFO - 成功连接到MySQL数据库: aps
2025-06-15 14:40:54,295 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:40:54] "GET /api/resources/data/ET_UPH_EQP?page=1&per_page=50 HTTP/1.1" 200 -
2025-06-15 14:40:54,405 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:40:54] "[33mGET /api/user-filter-presets?page_type=ET_UPH_EQP HTTP/1.1[0m" 404 -
2025-06-15 14:41:18,426 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:41:18] "GET /resources/product-cycle HTTP/1.1" 200 -
2025-06-15 14:41:18,739 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:41:18] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 14:41:18,775 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:41:18] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 14:41:18,775 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:41:18] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 14:41:18,775 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:41:18] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 14:41:18,776 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:41:18] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 14:41:19,095 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:41:19] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 14:41:23,066 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:41:23] "GET /resources/uph HTTP/1.1" 200 -
2025-06-15 14:41:23,364 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:41:23] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 14:41:23,433 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:41:23] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 14:41:23,434 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:41:23] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 14:41:23,451 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:41:23] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 14:41:23,452 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:41:23] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 14:41:23,755 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:41:23] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 14:41:23,786 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:41:23] "[33mGET /api/user-filter-presets?page_type=ET_UPH_EQP HTTP/1.1[0m" 404 -
2025-06-15 14:41:23,815 - app - INFO - 成功连接到MySQL数据库: aps
2025-06-15 14:41:23,829 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:41:23] "GET /api/resources/data/ET_UPH_EQP?page=1&per_page=50 HTTP/1.1" 200 -
2025-06-15 14:41:24,728 - app - INFO - 成功连接到MySQL数据库: aps
2025-06-15 14:41:24,745 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:41:24] "GET /api/resources/data/ET_UPH_EQP?page=1&per_page=50 HTTP/1.1" 200 -
2025-06-15 14:41:26,490 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:41:26] "GET /resources/uph HTTP/1.1" 200 -
2025-06-15 14:41:26,788 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:41:26] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 14:41:26,855 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:41:26] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 14:41:26,856 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:41:26] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 14:41:26,857 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:41:26] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 14:41:26,858 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:41:26] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 14:41:27,177 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:41:27] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 14:41:27,182 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:41:27] "[33mGET /api/user-filter-presets?page_type=ET_UPH_EQP HTTP/1.1[0m" 404 -
2025-06-15 14:41:27,191 - app - INFO - 成功连接到MySQL数据库: aps
2025-06-15 14:41:27,205 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:41:27] "GET /api/resources/data/ET_UPH_EQP?page=1&per_page=50 HTTP/1.1" 200 -
2025-06-15 14:41:29,510 - app - INFO - 成功连接到MySQL数据库: aps
2025-06-15 14:41:29,528 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:41:29] "GET /api/resources/data/ET_UPH_EQP?page=1&per_page=50 HTTP/1.1" 200 -
2025-06-15 14:41:31,828 - app - INFO - 成功连接到MySQL数据库: aps
2025-06-15 14:41:31,901 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:41:31] "GET /api/resources/data/ET_UPH_EQP?export=true HTTP/1.1" 200 -
2025-06-15 14:45:16,754 - APS-System - INFO - 应用路径: D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12
2025-06-15 14:45:16,755 - APS-System - INFO - 实例路径: D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\instance
2025-06-15 14:45:16,756 - APS-System - INFO - 检查 MySQL 数据库配置...
2025-06-15 14:45:16,829 - APS-System - INFO - MySQL 数据库 'aps' 已存在
2025-06-15 14:45:16,832 - APS-System - INFO - MySQL 数据库 'aps_system' 已存在
2025-06-15 14:45:16,832 - APS-System - INFO - MySQL 数据库检查成功
2025-06-15 14:45:16,834 - APS-System - INFO - 正在修复数据库权限...
2025-06-15 14:45:16,834 - APS-System - INFO - 发现SQLite文件，但系统已迁移到MySQL，跳过: instance\aps.db
2025-06-15 14:45:16,835 - APS-System - INFO - 数据库文件不存在，跳过: instance\aps_system.db
2025-06-15 14:45:16,836 - APS-System - INFO - 已修复日志目录权限: logs
2025-06-15 14:45:16,836 - APS-System - INFO - 数据库权限修复成功
2025-06-15 14:45:18,843 - app - INFO - 应用启动
2025-06-15 14:45:18,847 - app.utils.scheduler - INFO - 已重新加载 0 个邮箱配置（应用实例未设置）
2025-06-15 14:45:18,863 - app.utils.scheduler - INFO - 邮件附件定时调度器已启动
2025-06-15 14:45:18,863 - app - INFO - 邮件附件定时任务调度器已启动
2025-06-15 14:45:18,934 - app.utils.scheduler - INFO - 全局定时任务已禁用，邮件调度器跟随禁用
2025-06-15 14:45:18,939 - app.utils.scheduler - INFO - 检测到全局定时任务已禁用，停止邮件调度器
2025-06-15 14:45:19,078 - app.services.scheduler_service - INFO - APScheduler调度器配置完成: timezone=Asia/Shanghai, max_workers=10
2025-06-15 14:45:19,101 - apscheduler.scheduler - INFO - Scheduler started
2025-06-15 14:45:19,109 - app.services.scheduler_service - INFO - 从数据库加载了 0 个任务
2025-06-15 14:45:19,111 - app.services.scheduler_service - INFO - APScheduler调度器启动成功
2025-06-15 14:45:19,111 - app - INFO - ✅ APScheduler统一调度器启动成功
2025-06-15 14:45:19,113 - APS-System - INFO - 应用创建成功
2025-06-15 14:45:19,115 - APS-System - INFO - ==================================================
2025-06-15 14:45:19,115 - APS-System - INFO - 🚀 APS车规芯片终测智能调度平台 v2.1
2025-06-15 14:45:19,115 - APS-System - INFO - 📊 100% MySQL数据库架构
2025-06-15 14:45:19,115 - APS-System - INFO - 🌐 访问地址: http://1********:5000
2025-06-15 14:45:19,115 - APS-System - INFO - ==================================================
2025-06-15 14:45:19,116 - APS-System - INFO - 运行模式: 开发环境
2025-06-15 14:45:19,133 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://1********:5000
2025-06-15 14:45:19,133 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-15 14:45:20,753 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:45:20] "GET /index HTTP/1.1" 200 -
2025-06-15 14:45:20,926 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:45:20] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 14:45:20,998 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:45:20] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 14:45:21,120 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:45:21] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 14:45:21,215 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:45:21] "GET / HTTP/1.1" 200 -
2025-06-15 14:45:21,401 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:45:21] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 14:45:21,402 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:45:21] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 14:45:21,508 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:45:21] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 14:45:21,556 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:45:21] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 14:45:21,559 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:45:21] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 14:45:21,560 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:45:21] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 14:45:21,868 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:45:21] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 14:45:21,871 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:45:21] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 14:45:21,928 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:45:21] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 14:45:30,316 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:45:30] "GET /resources/hardware HTTP/1.1" 200 -
2025-06-15 14:45:30,617 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:45:30] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 14:45:30,665 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:45:30] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 14:45:30,667 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:45:30] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 14:45:30,676 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:45:30] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 14:45:30,677 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:45:30] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 14:45:30,990 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:45:30] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 14:45:39,658 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:45:39] "GET /resources/uph HTTP/1.1" 200 -
2025-06-15 14:45:39,954 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:45:39] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 14:45:40,026 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:45:40] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 14:45:40,027 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:45:40] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 14:45:40,028 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:45:40] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 14:45:40,030 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:45:40] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 14:45:40,363 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:45:40] "[33mGET /api/user-filter-presets?page_type=ET_UPH_EQP HTTP/1.1[0m" 404 -
2025-06-15 14:45:40,370 - app - INFO - 成功连接到MySQL数据库: aps
2025-06-15 14:45:40,385 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:45:40] "GET /api/resources/data/ET_UPH_EQP?page=1&per_page=50 HTTP/1.1" 200 -
2025-06-15 14:45:41,782 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:45:41] "GET /resources/specs HTTP/1.1" 200 -
2025-06-15 14:45:42,079 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:45:42] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 14:45:42,148 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:45:42] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 14:45:42,150 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:45:42] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 14:45:42,153 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:45:42] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 14:45:42,156 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:45:42] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 14:45:46,643 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:45:46] "GET /resources/hardware HTTP/1.1" 200 -
2025-06-15 14:45:46,710 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:45:46] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 14:45:46,937 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:45:46] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 14:45:46,997 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:45:46] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 14:45:46,998 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:45:46] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 14:45:47,021 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:45:47] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 14:45:49,924 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:45:49] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 14:45:50,227 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:45:50] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 14:45:50,230 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:45:50] "[33mGET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1[0m" 404 -
2025-06-15 14:45:50,231 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:45:50] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 14:45:51,028 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:45:51] "[33mGET /static/css/base/bootstrap.min.css.map HTTP/1.1[0m" 404 -
2025-06-15 14:51:26,031 - APS-System - INFO - 应用路径: D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12
2025-06-15 14:51:26,032 - APS-System - INFO - 实例路径: D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\instance
2025-06-15 14:51:26,033 - APS-System - INFO - 检查 MySQL 数据库配置...
2025-06-15 14:51:26,091 - APS-System - INFO - MySQL 数据库 'aps' 已存在
2025-06-15 14:51:26,093 - APS-System - INFO - MySQL 数据库 'aps_system' 已存在
2025-06-15 14:51:26,093 - APS-System - INFO - MySQL 数据库检查成功
2025-06-15 14:51:26,093 - APS-System - INFO - 正在修复数据库权限...
2025-06-15 14:51:26,094 - APS-System - INFO - 发现SQLite文件，但系统已迁移到MySQL，跳过: instance\aps.db
2025-06-15 14:51:26,096 - APS-System - INFO - 数据库文件不存在，跳过: instance\aps_system.db
2025-06-15 14:51:26,096 - APS-System - INFO - 已修复日志目录权限: logs
2025-06-15 14:51:26,096 - APS-System - INFO - 数据库权限修复成功
2025-06-15 14:51:28,133 - app - INFO - 应用启动
2025-06-15 14:51:28,135 - app.utils.scheduler - INFO - 已重新加载 0 个邮箱配置（应用实例未设置）
2025-06-15 14:51:28,152 - app.utils.scheduler - INFO - 邮件附件定时调度器已启动
2025-06-15 14:51:28,183 - app - INFO - 邮件附件定时任务调度器已启动
2025-06-15 14:51:28,194 - app.utils.scheduler - INFO - 全局定时任务已禁用，邮件调度器跟随禁用
2025-06-15 14:51:28,195 - app.utils.scheduler - INFO - 检测到全局定时任务已禁用，停止邮件调度器
2025-06-15 14:51:28,354 - app.services.scheduler_service - INFO - APScheduler调度器配置完成: timezone=Asia/Shanghai, max_workers=10
2025-06-15 14:51:28,381 - apscheduler.scheduler - INFO - Scheduler started
2025-06-15 14:51:28,386 - app.services.scheduler_service - INFO - 从数据库加载了 0 个任务
2025-06-15 14:51:28,388 - app.services.scheduler_service - INFO - APScheduler调度器启动成功
2025-06-15 14:51:28,391 - app - INFO - ✅ APScheduler统一调度器启动成功
2025-06-15 14:51:28,392 - APS-System - INFO - 应用创建成功
2025-06-15 14:51:28,393 - APS-System - INFO - ==================================================
2025-06-15 14:51:28,393 - APS-System - INFO - 🚀 APS车规芯片终测智能调度平台 v2.1
2025-06-15 14:51:28,393 - APS-System - INFO - 📊 100% MySQL数据库架构
2025-06-15 14:51:28,394 - APS-System - INFO - 🌐 访问地址: http://1********:5000
2025-06-15 14:51:28,394 - APS-System - INFO - ==================================================
2025-06-15 14:51:28,394 - APS-System - INFO - 运行模式: 开发环境
2025-06-15 14:51:28,409 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://1********:5000
2025-06-15 14:51:28,409 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-15 14:51:29,576 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:51:29] "GET /resources/hardware HTTP/1.1" 200 -
2025-06-15 14:51:29,716 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:51:29] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 14:51:29,789 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:51:29] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 14:51:29,927 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:51:29] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 14:51:30,475 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:51:30] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 14:51:30,483 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:51:30] "GET / HTTP/1.1" 200 -
2025-06-15 14:51:30,564 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:51:30] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 14:51:30,673 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:51:30] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 14:51:30,733 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:51:30] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 14:51:30,829 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:51:30] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 14:51:30,830 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:51:30] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 14:51:30,831 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:51:30] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 14:51:31,105 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:51:31] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 14:51:31,151 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:51:31] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 14:51:35,519 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:51:35] "GET /resources/hardware HTTP/1.1" 200 -
2025-06-15 14:51:35,826 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:51:35] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 14:51:35,878 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:51:35] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 14:51:35,878 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:51:35] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 14:51:35,881 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:51:35] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 14:51:35,889 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:51:35] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 14:51:36,199 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:51:36] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 14:51:57,718 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:51:57] "GET /resources/specs HTTP/1.1" 200 -
2025-06-15 14:51:57,783 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:51:57] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 14:51:57,961 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:51:57] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 14:51:58,094 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:51:58] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 14:51:58,094 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:51:58] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 14:51:58,097 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:51:58] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 14:51:58,413 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:51:58] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 14:51:58,968 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:51:58] "GET /resources/hardware HTTP/1.1" 200 -
2025-06-15 14:51:59,272 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:51:59] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 14:51:59,327 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:51:59] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 14:51:59,329 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:51:59] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 14:51:59,331 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:51:59] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 14:51:59,338 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:51:59] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 14:51:59,656 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:51:59] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 14:52:03,973 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:52:03] "GET /resources/specs HTTP/1.1" 200 -
2025-06-15 14:52:04,279 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:52:04] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 14:52:04,331 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:52:04] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 14:52:04,333 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:52:04] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 14:52:04,336 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:52:04] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 14:52:04,337 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:52:04] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 14:52:06,223 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:52:06] "GET /resources/tester HTTP/1.1" 200 -
2025-06-15 14:52:06,309 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:52:06] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 14:52:06,464 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:52:06] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 14:52:06,578 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:52:06] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 14:52:06,579 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:52:06] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 14:52:06,580 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:52:06] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 14:52:07,586 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:52:07] "GET /resources/uph HTTP/1.1" 200 -
2025-06-15 14:52:07,868 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:52:07] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 14:52:07,929 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:52:07] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 14:52:07,939 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:52:07] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 14:52:07,940 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:52:07] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 14:52:07,944 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:52:07] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 14:52:08,272 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:52:08] "[33mGET /api/user-filter-presets?page_type=ET_UPH_EQP HTTP/1.1[0m" 404 -
2025-06-15 14:52:08,283 - app - INFO - 成功连接到MySQL数据库: aps
2025-06-15 14:52:08,299 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:52:08] "GET /api/resources/data/ET_UPH_EQP?page=1&per_page=50 HTTP/1.1" 200 -
2025-06-15 14:52:09,337 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:52:09] "GET /resources/product-cycle HTTP/1.1" 200 -
2025-06-15 14:52:09,624 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:52:09] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 14:52:09,708 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:52:09] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 14:52:09,709 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:52:09] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 14:52:09,716 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:52:09] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 14:52:09,720 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:52:09] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 14:52:11,635 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:52:11] "GET /resources/hardware HTTP/1.1" 200 -
2025-06-15 14:52:11,742 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:52:11] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 14:52:11,895 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:52:11] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 14:52:11,993 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:52:11] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 14:52:11,995 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:52:11] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 14:52:11,996 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:52:11] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 14:54:26,490 - APS-System - INFO - 应用路径: D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12
2025-06-15 14:54:26,491 - APS-System - INFO - 实例路径: D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\instance
2025-06-15 14:54:26,491 - APS-System - INFO - 检查 MySQL 数据库配置...
2025-06-15 14:54:26,557 - APS-System - INFO - MySQL 数据库 'aps' 已存在
2025-06-15 14:54:26,571 - APS-System - INFO - MySQL 数据库 'aps_system' 已存在
2025-06-15 14:54:26,572 - APS-System - INFO - MySQL 数据库检查成功
2025-06-15 14:54:26,573 - APS-System - INFO - 正在修复数据库权限...
2025-06-15 14:54:26,574 - APS-System - INFO - 发现SQLite文件，但系统已迁移到MySQL，跳过: instance\aps.db
2025-06-15 14:54:26,574 - APS-System - INFO - 数据库文件不存在，跳过: instance\aps_system.db
2025-06-15 14:54:26,576 - APS-System - INFO - 已修复日志目录权限: logs
2025-06-15 14:54:26,579 - APS-System - INFO - 数据库权限修复成功
2025-06-15 14:54:28,646 - app - INFO - 应用启动
2025-06-15 14:54:28,648 - app.utils.scheduler - INFO - 已重新加载 0 个邮箱配置（应用实例未设置）
2025-06-15 14:54:28,664 - app.utils.scheduler - INFO - 邮件附件定时调度器已启动
2025-06-15 14:54:28,706 - app - INFO - 邮件附件定时任务调度器已启动
2025-06-15 14:54:28,717 - app.utils.scheduler - INFO - 全局定时任务已禁用，邮件调度器跟随禁用
2025-06-15 14:54:28,719 - app.utils.scheduler - INFO - 检测到全局定时任务已禁用，停止邮件调度器
2025-06-15 14:54:28,873 - app.services.scheduler_service - INFO - APScheduler调度器配置完成: timezone=Asia/Shanghai, max_workers=10
2025-06-15 14:54:28,883 - apscheduler.scheduler - INFO - Scheduler started
2025-06-15 14:54:28,888 - app.services.scheduler_service - INFO - 从数据库加载了 0 个任务
2025-06-15 14:54:28,890 - app.services.scheduler_service - INFO - APScheduler调度器启动成功
2025-06-15 14:54:28,891 - app - INFO - ✅ APScheduler统一调度器启动成功
2025-06-15 14:54:28,892 - APS-System - INFO - 应用创建成功
2025-06-15 14:54:28,892 - APS-System - INFO - ==================================================
2025-06-15 14:54:28,892 - APS-System - INFO - 🚀 APS车规芯片终测智能调度平台 v2.1
2025-06-15 14:54:28,893 - APS-System - INFO - 📊 100% MySQL数据库架构
2025-06-15 14:54:28,893 - APS-System - INFO - 🌐 访问地址: http://1********:5000
2025-06-15 14:54:28,894 - APS-System - INFO - ==================================================
2025-06-15 14:54:28,896 - APS-System - INFO - 运行模式: 开发环境
2025-06-15 14:54:28,913 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://1********:5000
2025-06-15 14:54:28,915 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-15 14:54:31,012 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:54:31] "GET / HTTP/1.1" 200 -
2025-06-15 14:54:31,268 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:54:31] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 14:54:31,348 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:54:31] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 14:54:31,348 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:54:31] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 14:54:31,350 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:54:31] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 14:54:31,428 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:54:31] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 14:54:31,798 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:54:31] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 14:54:42,141 - APS-System - INFO - 应用路径: D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12
2025-06-15 14:54:42,142 - APS-System - INFO - 实例路径: D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\instance
2025-06-15 14:54:42,142 - APS-System - INFO - 检查 MySQL 数据库配置...
2025-06-15 14:54:42,199 - APS-System - INFO - MySQL 数据库 'aps' 已存在
2025-06-15 14:54:42,202 - APS-System - INFO - MySQL 数据库 'aps_system' 已存在
2025-06-15 14:54:42,203 - APS-System - INFO - MySQL 数据库检查成功
2025-06-15 14:54:42,203 - APS-System - INFO - 正在修复数据库权限...
2025-06-15 14:54:42,204 - APS-System - INFO - 发现SQLite文件，但系统已迁移到MySQL，跳过: instance\aps.db
2025-06-15 14:54:42,204 - APS-System - INFO - 数据库文件不存在，跳过: instance\aps_system.db
2025-06-15 14:54:42,205 - APS-System - INFO - 已修复日志目录权限: logs
2025-06-15 14:54:42,205 - APS-System - INFO - 数据库权限修复成功
2025-06-15 14:54:44,291 - app - INFO - 应用启动
2025-06-15 14:54:44,293 - app.utils.scheduler - INFO - 已重新加载 0 个邮箱配置（应用实例未设置）
2025-06-15 14:54:44,310 - app.utils.scheduler - INFO - 邮件附件定时调度器已启动
2025-06-15 14:54:44,329 - app - INFO - 邮件附件定时任务调度器已启动
2025-06-15 14:54:44,358 - app.utils.scheduler - INFO - 全局定时任务已禁用，邮件调度器跟随禁用
2025-06-15 14:54:44,361 - app.utils.scheduler - INFO - 检测到全局定时任务已禁用，停止邮件调度器
2025-06-15 14:54:44,500 - app.services.scheduler_service - INFO - APScheduler调度器配置完成: timezone=Asia/Shanghai, max_workers=10
2025-06-15 14:54:44,536 - apscheduler.scheduler - INFO - Scheduler started
2025-06-15 14:54:44,542 - app.services.scheduler_service - INFO - 从数据库加载了 0 个任务
2025-06-15 14:54:44,544 - app.services.scheduler_service - INFO - APScheduler调度器启动成功
2025-06-15 14:54:44,545 - app - INFO - ✅ APScheduler统一调度器启动成功
2025-06-15 14:54:44,545 - APS-System - INFO - 应用创建成功
2025-06-15 14:54:44,545 - APS-System - INFO - ==================================================
2025-06-15 14:54:44,546 - APS-System - INFO - 🚀 APS车规芯片终测智能调度平台 v2.1
2025-06-15 14:54:44,546 - APS-System - INFO - 📊 100% MySQL数据库架构
2025-06-15 14:54:44,547 - APS-System - INFO - 🌐 访问地址: http://1********:5000
2025-06-15 14:54:44,548 - APS-System - INFO - ==================================================
2025-06-15 14:54:44,549 - APS-System - INFO - 运行模式: 开发环境
2025-06-15 14:54:44,566 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://1********:5000
2025-06-15 14:54:44,567 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-15 14:54:46,682 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:54:46] "[32mGET / HTTP/1.1[0m" 302 -
2025-06-15 14:54:46,703 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:54:46] "GET /auth/login?next=/ HTTP/1.1" 200 -
2025-06-15 14:54:47,215 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:54:47] "GET /static/css/login/bootstrap.min.css HTTP/1.1" 200 -
2025-06-15 14:54:47,428 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:54:47] "GET /static/css/all-5.15.4.min.css HTTP/1.1" 200 -
2025-06-15 14:54:47,437 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:54:47] "GET /static/js/login/bootstrap.bundle.min.js HTTP/1.1" 200 -
2025-06-15 14:54:47,439 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:54:47] "GET /static/css/compatibility.css HTTP/1.1" 200 -
2025-06-15 14:54:47,810 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:54:47] "GET /static/webfonts/fa-solid-900.woff2 HTTP/1.1" 200 -
2025-06-15 14:54:48,358 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:54:48] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-06-15 14:54:53,289 - app.auth.routes - INFO - 尝试登录用户: admin
2025-06-15 14:54:53,304 - app.auth.routes - INFO - 找到用户: admin, 角色: admin, 密码哈希: scrypt:32768:8:1$X8JU5hxR74hpH0tP$36e5b028b335f5d2f9f21e9178a0d529cca80459428b1128d44d8ac646d8f16cdd0823ea12d58ce5bbae1dd243eeb7a50c06f55d6688542dae997f7c75e6ee24
2025-06-15 14:54:53,652 - app.models - INFO - 用户 admin 密码验证结果: True
2025-06-15 14:54:53,653 - app.auth.routes - INFO - 密码验证结果: True
2025-06-15 14:54:53,655 - app.auth.routes - INFO - 用户 admin 登录成功
2025-06-15 14:54:53,675 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:54:53] "[32mPOST /auth/login HTTP/1.1[0m" 302 -
2025-06-15 14:54:53,721 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:54:53] "GET /index HTTP/1.1" 200 -
2025-06-15 14:54:53,785 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:54:53] "GET /static/css/base/bootstrap.min.css HTTP/1.1" 200 -
2025-06-15 14:54:54,106 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:54:54] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 14:54:54,109 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:54:54] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 14:54:54,111 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:54:54] "GET /static/js/menu-optimizer.js HTTP/1.1" 200 -
2025-06-15 14:54:54,112 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:54:54] "GET /static/js/base/bootstrap.bundle.min.js HTTP/1.1" 200 -
2025-06-15 14:54:54,475 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:54:54] "GET /menu HTTP/1.1" 200 -
2025-06-15 14:55:38,552 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:55:38] "GET /resources/hardware HTTP/1.1" 200 -
2025-06-15 14:55:38,837 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:55:38] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 14:55:38,915 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:55:38] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 14:55:38,918 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:55:38] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 14:55:38,921 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:55:38] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 14:55:38,921 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:55:38] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 14:55:39,273 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:55:39] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 14:55:43,594 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:55:43] "[33mGET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1[0m" 404 -
2025-06-15 14:55:43,907 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:55:43] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 14:55:43,907 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:55:43] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 14:55:43,910 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:55:43] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 14:55:44,651 - werkzeug - INFO - 1******** - - [15/Jun/2025 14:55:44] "[33mGET /static/css/base/bootstrap.min.css.map HTTP/1.1[0m" 404 -
2025-06-15 15:00:59,090 - APS-System - INFO - 应用路径: D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12
2025-06-15 15:00:59,091 - APS-System - INFO - 实例路径: D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\instance
2025-06-15 15:00:59,091 - APS-System - INFO - 检查 MySQL 数据库配置...
2025-06-15 15:00:59,154 - APS-System - INFO - MySQL 数据库 'aps' 已存在
2025-06-15 15:00:59,155 - APS-System - INFO - MySQL 数据库 'aps_system' 已存在
2025-06-15 15:00:59,155 - APS-System - INFO - MySQL 数据库检查成功
2025-06-15 15:00:59,156 - APS-System - INFO - 正在修复数据库权限...
2025-06-15 15:00:59,156 - APS-System - INFO - 发现SQLite文件，但系统已迁移到MySQL，跳过: instance\aps.db
2025-06-15 15:00:59,156 - APS-System - INFO - 数据库文件不存在，跳过: instance\aps_system.db
2025-06-15 15:00:59,156 - APS-System - INFO - 已修复日志目录权限: logs
2025-06-15 15:00:59,156 - APS-System - INFO - 数据库权限修复成功
2025-06-15 15:01:01,388 - app - INFO - 应用启动
2025-06-15 15:01:01,390 - app.utils.scheduler - INFO - 已重新加载 0 个邮箱配置（应用实例未设置）
2025-06-15 15:01:01,399 - app.utils.scheduler - INFO - 邮件附件定时调度器已启动
2025-06-15 15:01:01,430 - app - INFO - 邮件附件定时任务调度器已启动
2025-06-15 15:01:01,444 - app.utils.scheduler - INFO - 全局定时任务已禁用，邮件调度器跟随禁用
2025-06-15 15:01:01,446 - app.utils.scheduler - INFO - 检测到全局定时任务已禁用，停止邮件调度器
2025-06-15 15:01:01,616 - app.services.scheduler_service - INFO - APScheduler调度器配置完成: timezone=Asia/Shanghai, max_workers=10
2025-06-15 15:01:01,638 - apscheduler.scheduler - INFO - Scheduler started
2025-06-15 15:01:01,644 - app.services.scheduler_service - INFO - 从数据库加载了 0 个任务
2025-06-15 15:01:01,645 - app.services.scheduler_service - INFO - APScheduler调度器启动成功
2025-06-15 15:01:01,645 - app - INFO - ✅ APScheduler统一调度器启动成功
2025-06-15 15:01:01,647 - APS-System - INFO - 应用创建成功
2025-06-15 15:01:01,647 - APS-System - INFO - ==================================================
2025-06-15 15:01:01,647 - APS-System - INFO - 🚀 APS车规芯片终测智能调度平台 v2.1
2025-06-15 15:01:01,647 - APS-System - INFO - 📊 100% MySQL数据库架构
2025-06-15 15:01:01,647 - APS-System - INFO - 🌐 访问地址: http://1********:5000
2025-06-15 15:01:01,648 - APS-System - INFO - ==================================================
2025-06-15 15:01:01,648 - APS-System - INFO - 运行模式: 开发环境
2025-06-15 15:01:01,664 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://1********:5000
2025-06-15 15:01:01,665 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-15 15:01:03,850 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:01:03] "[32mGET / HTTP/1.1[0m" 302 -
2025-06-15 15:01:03,864 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:01:03] "GET /auth/login?next=/ HTTP/1.1" 200 -
2025-06-15 15:01:04,210 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:01:04] "GET /static/css/login/bootstrap.min.css HTTP/1.1" 200 -
2025-06-15 15:01:04,407 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:01:04] "GET /static/css/compatibility.css HTTP/1.1" 200 -
2025-06-15 15:01:04,412 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:01:04] "GET /static/css/all-5.15.4.min.css HTTP/1.1" 200 -
2025-06-15 15:01:04,413 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:01:04] "GET /static/js/login/bootstrap.bundle.min.js HTTP/1.1" 200 -
2025-06-15 15:01:04,762 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:01:04] "GET /static/webfonts/fa-solid-900.woff2 HTTP/1.1" 200 -
2025-06-15 15:01:05,406 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:01:05] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-06-15 15:01:06,159 - app.auth.routes - INFO - 尝试登录用户: admin
2025-06-15 15:01:06,197 - app.auth.routes - INFO - 找到用户: admin, 角色: admin, 密码哈希: scrypt:32768:8:1$X8JU5hxR74hpH0tP$36e5b028b335f5d2f9f21e9178a0d529cca80459428b1128d44d8ac646d8f16cdd0823ea12d58ce5bbae1dd243eeb7a50c06f55d6688542dae997f7c75e6ee24
2025-06-15 15:01:06,323 - app.models - INFO - 用户 admin 密码验证结果: True
2025-06-15 15:01:06,325 - app.auth.routes - INFO - 密码验证结果: True
2025-06-15 15:01:06,326 - app.auth.routes - INFO - 用户 admin 登录成功
2025-06-15 15:01:06,343 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:01:06] "[32mPOST /auth/login HTTP/1.1[0m" 302 -
2025-06-15 15:01:06,370 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:01:06] "GET /index HTTP/1.1" 200 -
2025-06-15 15:01:06,422 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:01:06] "GET /static/css/base/bootstrap.min.css HTTP/1.1" 200 -
2025-06-15 15:01:06,740 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:01:06] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 15:01:06,741 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:01:06] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 15:01:06,744 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:01:06] "GET /static/js/base/bootstrap.bundle.min.js HTTP/1.1" 200 -
2025-06-15 15:01:06,745 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:01:06] "GET /static/js/menu-optimizer.js HTTP/1.1" 200 -
2025-06-15 15:01:07,060 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:01:07] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 15:01:07,108 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:01:07] "GET /menu HTTP/1.1" 200 -
2025-06-15 15:01:10,305 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:01:10] "GET /resources/hardware HTTP/1.1" 200 -
2025-06-15 15:01:10,605 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:01:10] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 15:01:10,686 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:01:10] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 15:01:10,690 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:01:10] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 15:01:10,692 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:01:10] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 15:01:10,694 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:01:10] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 15:01:12,064 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:01:12] "[33mGET /api/user-filter-presets?page_type=hardware_resources HTTP/1.1[0m" 404 -
2025-06-15 15:01:12,091 - app - INFO - 成功连接到MySQL数据库: aps
2025-06-15 15:01:12,110 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:01:12] "GET /api/resources/data/eqp_status?page=1&per_page=50 HTTP/1.1" 200 -
2025-06-15 15:01:23,197 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:01:23] "GET /resources/specs HTTP/1.1" 200 -
2025-06-15 15:01:23,491 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:01:23] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 15:01:23,558 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:01:23] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 15:01:23,559 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:01:23] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 15:01:23,563 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:01:23] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 15:01:23,568 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:01:23] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 15:01:23,899 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:01:23] "[33mGET /api/user-filter-presets?page_type=et_ft_test_spec HTTP/1.1[0m" 404 -
2025-06-15 15:01:23,925 - app - INFO - 成功连接到MySQL数据库: aps
2025-06-15 15:01:23,955 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:01:23] "GET /api/resources/data/et_ft_test_spec?page=1&per_page=50 HTTP/1.1" 200 -
2025-06-15 15:01:25,415 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:01:25] "GET /resources/tester HTTP/1.1" 200 -
2025-06-15 15:01:25,700 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:01:25] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 15:01:25,797 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:01:25] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 15:01:25,800 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:01:25] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 15:01:25,806 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:01:25] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 15:01:25,807 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:01:25] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 15:01:26,136 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:01:26] "[33mGET /api/user-filter-presets?page_type=TCC_INV HTTP/1.1[0m" 404 -
2025-06-15 15:01:26,163 - app - INFO - 成功连接到MySQL数据库: aps
2025-06-15 15:01:26,181 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:01:26] "GET /api/resources/data/TCC_INV?page=1&per_page=50 HTTP/1.1" 200 -
2025-06-15 15:01:27,916 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:01:27] "GET /resources/specs HTTP/1.1" 200 -
2025-06-15 15:01:28,215 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:01:28] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 15:01:28,290 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:01:28] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 15:01:28,295 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:01:28] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 15:01:28,298 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:01:28] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 15:01:28,299 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:01:28] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 15:01:28,637 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:01:28] "[33mGET /api/user-filter-presets?page_type=et_ft_test_spec HTTP/1.1[0m" 404 -
2025-06-15 15:01:28,666 - app - INFO - 成功连接到MySQL数据库: aps
2025-06-15 15:01:28,692 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:01:28] "GET /api/resources/data/et_ft_test_spec?page=1&per_page=50 HTTP/1.1" 200 -
2025-06-15 15:01:38,823 - app - INFO - 成功连接到MySQL数据库: aps
2025-06-15 15:01:38,850 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:01:38] "GET /api/resources/data/et_ft_test_spec?page=1&per_page=25 HTTP/1.1" 200 -
2025-06-15 15:01:42,224 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:01:42] "GET /resources/tester HTTP/1.1" 200 -
2025-06-15 15:01:42,281 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:01:42] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 15:01:42,484 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:01:42] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 15:01:42,581 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:01:42] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 15:01:42,582 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:01:42] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 15:01:42,583 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:01:42] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 15:01:42,636 - app - INFO - 成功连接到MySQL数据库: aps
2025-06-15 15:01:42,652 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:01:42] "GET /api/resources/data/TCC_INV?page=1&per_page=50 HTTP/1.1" 200 -
2025-06-15 15:01:42,956 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:01:42] "[33mGET /api/user-filter-presets?page_type=TCC_INV HTTP/1.1[0m" 404 -
2025-06-15 15:01:46,903 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:01:46] "GET /resources/uph HTTP/1.1" 200 -
2025-06-15 15:01:46,999 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:01:46] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 15:01:47,136 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:01:47] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 15:01:47,250 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:01:47] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 15:01:47,255 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:01:47] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 15:01:47,257 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:01:47] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 15:01:47,286 - app - INFO - 成功连接到MySQL数据库: aps
2025-06-15 15:01:47,300 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:01:47] "GET /api/resources/data/ET_UPH_EQP?page=1&per_page=50 HTTP/1.1" 200 -
2025-06-15 15:01:47,623 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:01:47] "[33mGET /api/user-filter-presets?page_type=ET_UPH_EQP HTTP/1.1[0m" 404 -
2025-06-15 15:01:53,711 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:01:53] "GET /resources/product-cycle HTTP/1.1" 200 -
2025-06-15 15:01:53,773 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:01:53] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 15:01:53,958 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:01:53] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 15:01:54,071 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:01:54] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 15:01:54,072 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:01:54] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 15:01:54,073 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:01:54] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 15:01:54,126 - app - INFO - 成功连接到MySQL数据库: aps
2025-06-15 15:01:54,157 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:01:54] "GET /api/resources/data/CT?page=1&per_page=50 HTTP/1.1" 200 -
2025-06-15 15:01:54,411 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:01:54] "[33mGET /api/user-filter-presets?page_type=CT HTTP/1.1[0m" 404 -
2025-06-15 15:02:30,140 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:02:30] "GET /resources/hardware HTTP/1.1" 200 -
2025-06-15 15:02:30,215 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:02:30] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 15:02:30,401 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:02:30] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 15:02:30,495 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:02:30] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 15:02:30,497 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:02:30] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 15:02:30,498 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:02:30] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 15:02:30,499 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:02:30] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 15:02:31,538 - app - INFO - 成功连接到MySQL数据库: aps
2025-06-15 15:02:31,553 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:02:31] "GET /api/resources/data/eqp_status?page=1&per_page=50 HTTP/1.1" 200 -
2025-06-15 15:02:31,837 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:02:31] "[33mGET /api/user-filter-presets?page_type=hardware_resources HTTP/1.1[0m" 404 -
2025-06-15 15:02:47,236 - app - INFO - 成功连接到MySQL数据库: aps
2025-06-15 15:02:47,261 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:02:47] "GET /api/resources/data/eqp_status?page=1&per_page=25 HTTP/1.1" 200 -
2025-06-15 15:02:56,330 - app - INFO - 成功连接到MySQL数据库: aps
2025-06-15 15:02:56,355 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:02:56] "GET /api/resources/data/eqp_status?page=1&per_page=100 HTTP/1.1" 200 -
2025-06-15 15:04:11,918 - app - INFO - 成功连接到MySQL数据库: aps
2025-06-15 15:04:11,931 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:04:11] "GET /api/resources/data/eqp_status?page=1&per_page=100&advanced_filters=[{"field":"DEVICE","operator":"contains","value":"5103"}] HTTP/1.1" 200 -
2025-06-15 15:06:27,620 - app.auth.routes - INFO - 尝试登录用户: admin
2025-06-15 15:06:27,630 - app.auth.routes - INFO - 找到用户: admin, 角色: admin, 密码哈希: scrypt:32768:8:1$X8JU5hxR74hpH0tP$36e5b028b335f5d2f9f21e9178a0d529cca80459428b1128d44d8ac646d8f16cdd0823ea12d58ce5bbae1dd243eeb7a50c06f55d6688542dae997f7c75e6ee24
2025-06-15 15:06:27,874 - app.models - INFO - 用户 admin 密码验证结果: False
2025-06-15 15:06:27,875 - app.auth.routes - INFO - 密码验证结果: False
2025-06-15 15:06:27,877 - app.auth.routes - WARNING - 登录失败: 用户 admin 密码不正确
2025-06-15 15:06:27,880 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:06:27] "[32mPOST /auth/login HTTP/1.1[0m" 302 -
2025-06-15 15:06:29,929 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:06:29] "GET /auth/login HTTP/1.1" 200 -
2025-06-15 15:06:31,954 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:06:31] "[32mGET /api/resources/data/eqp_status?page=1&per_page=10 HTTP/1.1[0m" 302 -
2025-06-15 15:06:34,008 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:06:34] "GET /auth/login?next=/api/resources/data/eqp_status?page%3D1%26per_page%3D10 HTTP/1.1" 200 -
2025-06-15 15:06:36,060 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:06:36] "[32mGET /api/resources/data/et_ft_test_spec?page=1&per_page=10 HTTP/1.1[0m" 302 -
2025-06-15 15:06:38,086 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:06:38] "GET /auth/login?next=/api/resources/data/et_ft_test_spec?page%3D1%26per_page%3D10 HTTP/1.1" 200 -
2025-06-15 15:06:40,121 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:06:40] "[32mGET /api/resources/data/ET_UPH_EQP?page=1&per_page=10 HTTP/1.1[0m" 302 -
2025-06-15 15:06:42,154 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:06:42] "GET /auth/login?next=/api/resources/data/ET_UPH_EQP?page%3D1%26per_page%3D10 HTTP/1.1" 200 -
2025-06-15 15:06:44,207 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:06:44] "[32mGET /api/resources/data/TCC_INV?page=1&per_page=10 HTTP/1.1[0m" 302 -
2025-06-15 15:06:46,263 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:06:46] "GET /auth/login?next=/api/resources/data/TCC_INV?page%3D1%26per_page%3D10 HTTP/1.1" 200 -
2025-06-15 15:06:48,318 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:06:48] "[32mGET /api/resources/data/CT?page=1&per_page=10 HTTP/1.1[0m" 302 -
2025-06-15 15:06:50,370 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:06:50] "GET /auth/login?next=/api/resources/data/CT?page%3D1%26per_page%3D10 HTTP/1.1" 200 -
2025-06-15 15:08:55,525 - APS-System - INFO - 应用路径: D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12
2025-06-15 15:08:55,526 - APS-System - INFO - 实例路径: D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\instance
2025-06-15 15:08:55,526 - APS-System - INFO - 检查 MySQL 数据库配置...
2025-06-15 15:08:55,606 - APS-System - INFO - MySQL 数据库 'aps' 已存在
2025-06-15 15:08:55,607 - APS-System - INFO - MySQL 数据库 'aps_system' 已存在
2025-06-15 15:08:55,608 - APS-System - INFO - MySQL 数据库检查成功
2025-06-15 15:08:55,608 - APS-System - INFO - 正在修复数据库权限...
2025-06-15 15:08:55,608 - APS-System - INFO - 发现SQLite文件，但系统已迁移到MySQL，跳过: instance\aps.db
2025-06-15 15:08:55,609 - APS-System - INFO - 数据库文件不存在，跳过: instance\aps_system.db
2025-06-15 15:08:55,609 - APS-System - INFO - 已修复日志目录权限: logs
2025-06-15 15:08:55,609 - APS-System - INFO - 数据库权限修复成功
2025-06-15 15:08:57,665 - app - INFO - 应用启动
2025-06-15 15:08:57,666 - app.utils.scheduler - INFO - 已重新加载 0 个邮箱配置（应用实例未设置）
2025-06-15 15:08:57,672 - app.utils.scheduler - INFO - 邮件附件定时调度器已启动
2025-06-15 15:08:57,718 - app - INFO - 邮件附件定时任务调度器已启动
2025-06-15 15:08:57,731 - app.utils.scheduler - INFO - 全局定时任务已禁用，邮件调度器跟随禁用
2025-06-15 15:08:57,733 - app.utils.scheduler - INFO - 检测到全局定时任务已禁用，停止邮件调度器
2025-06-15 15:08:57,910 - app.services.scheduler_service - INFO - APScheduler调度器配置完成: timezone=Asia/Shanghai, max_workers=10
2025-06-15 15:08:57,941 - apscheduler.scheduler - INFO - Scheduler started
2025-06-15 15:08:57,945 - app.services.scheduler_service - INFO - 从数据库加载了 0 个任务
2025-06-15 15:08:57,946 - app.services.scheduler_service - INFO - APScheduler调度器启动成功
2025-06-15 15:08:57,947 - app - INFO - ✅ APScheduler统一调度器启动成功
2025-06-15 15:08:57,948 - APS-System - INFO - 应用创建成功
2025-06-15 15:08:57,948 - APS-System - INFO - ==================================================
2025-06-15 15:08:57,949 - APS-System - INFO - 🚀 APS车规芯片终测智能调度平台 v2.1
2025-06-15 15:08:57,949 - APS-System - INFO - 📊 100% MySQL数据库架构
2025-06-15 15:08:57,949 - APS-System - INFO - 🌐 访问地址: http://1********:5000
2025-06-15 15:08:57,949 - APS-System - INFO - ==================================================
2025-06-15 15:08:57,949 - APS-System - INFO - 运行模式: 开发环境
2025-06-15 15:08:57,969 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://1********:5000
2025-06-15 15:08:57,970 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-15 15:09:00,080 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:09:00] "GET / HTTP/1.1" 200 -
2025-06-15 15:09:00,349 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:09:00] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 15:09:00,448 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:09:00] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 15:09:00,450 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:09:00] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 15:09:00,452 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:09:00] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 15:09:00,481 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:09:00] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 15:09:00,877 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:09:00] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 15:09:03,851 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:09:03] "GET /resources/hardware HTTP/1.1" 200 -
2025-06-15 15:09:04,144 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:09:04] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 15:09:04,224 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:09:04] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 15:09:04,226 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:09:04] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 15:09:04,229 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:09:04] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 15:09:04,231 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:09:04] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 15:09:04,563 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:09:04] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 15:09:05,311 - app - INFO - 成功连接到MySQL数据库: aps
2025-06-15 15:09:05,327 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:09:05] "GET /api/resources/data/eqp_status?page=1&per_page=50 HTTP/1.1" 200 -
2025-06-15 15:09:05,590 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:09:05] "[33mGET /api/user-filter-presets?page_type=hardware_resources HTTP/1.1[0m" 404 -
2025-06-15 15:09:14,403 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:09:14] "GET /resources/specs HTTP/1.1" 200 -
2025-06-15 15:09:14,449 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:09:14] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 15:09:14,633 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:09:14] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 15:09:14,762 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:09:14] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 15:09:14,763 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:09:14] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 15:09:14,765 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:09:14] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 15:09:14,779 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:09:14] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 15:09:15,102 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:09:15] "[33mGET /api/user-filter-presets?page_type=et_ft_test_spec HTTP/1.1[0m" 404 -
2025-06-15 15:09:15,110 - app - INFO - 成功连接到MySQL数据库: aps
2025-06-15 15:09:15,140 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:09:15] "GET /api/resources/data/et_ft_test_spec?page=1&per_page=50 HTTP/1.1" 200 -
2025-06-15 15:09:17,441 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:09:17] "GET /resources/tester HTTP/1.1" 200 -
2025-06-15 15:09:17,725 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:09:17] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 15:09:17,805 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:09:17] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 15:09:17,808 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:09:17] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 15:09:17,808 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:09:17] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 15:09:17,810 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:09:17] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 15:09:18,133 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:09:18] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 15:09:18,152 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:09:18] "[33mGET /api/user-filter-presets?page_type=TCC_INV HTTP/1.1[0m" 404 -
2025-06-15 15:09:18,174 - app - INFO - 成功连接到MySQL数据库: aps
2025-06-15 15:09:18,192 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:09:18] "GET /api/resources/data/TCC_INV?page=1&per_page=50 HTTP/1.1" 200 -
2025-06-15 15:09:25,520 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:09:25] "GET /resources/uph HTTP/1.1" 200 -
2025-06-15 15:09:25,808 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:09:25] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 15:09:25,890 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:09:25] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 15:09:25,894 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:09:25] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 15:09:25,896 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:09:25] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 15:09:25,907 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:09:25] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 15:09:26,236 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:09:26] "[33mGET /api/user-filter-presets?page_type=ET_UPH_EQP HTTP/1.1[0m" 404 -
2025-06-15 15:09:26,237 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:09:26] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 15:09:26,240 - app - INFO - 成功连接到MySQL数据库: aps
2025-06-15 15:09:26,260 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:09:26] "GET /api/resources/data/ET_UPH_EQP?page=1&per_page=50 HTTP/1.1" 200 -
2025-06-15 15:09:36,598 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:09:36] "GET /resources/hardware HTTP/1.1" 200 -
2025-06-15 15:09:36,672 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:09:36] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 15:09:36,858 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:09:36] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 15:09:36,951 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:09:36] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 15:09:36,954 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:09:36] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 15:09:36,956 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:09:36] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 15:09:36,959 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:09:36] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 15:09:38,020 - app - INFO - 成功连接到MySQL数据库: aps
2025-06-15 15:09:38,035 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:09:38] "GET /api/resources/data/eqp_status?page=1&per_page=50 HTTP/1.1" 200 -
2025-06-15 15:09:38,303 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:09:38] "[33mGET /api/user-filter-presets?page_type=hardware_resources HTTP/1.1[0m" 404 -
2025-06-15 15:09:52,788 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:09:52] "GET /production/semi-auto HTTP/1.1" 200 -
2025-06-15 15:09:53,083 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:09:53] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 15:09:53,182 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:09:53] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 15:09:53,184 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:09:53] "GET /static/css/core/main.css HTTP/1.1" 200 -
2025-06-15 15:09:53,184 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:09:53] "GET /static/css/daygrid/main.css HTTP/1.1" 200 -
2025-06-15 15:09:53,188 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:09:53] "GET /static/css/timegrid/main.css HTTP/1.1" 200 -
2025-06-15 15:09:53,190 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:09:53] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 15:09:53,390 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:09:53] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 15:09:53,502 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:09:53] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 15:09:53,503 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:09:53] "GET /static/js/xlsx.full.min.js HTTP/1.1" 200 -
2025-06-15 15:09:53,509 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:09:53] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 15:09:53,910 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:09:53] "GET /api/production/get-import-path HTTP/1.1" 200 -
2025-06-15 15:09:53,933 - app - INFO - 成功连接到MySQL数据库: aps
2025-06-15 15:09:53,964 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:09:53] "GET /api/production/imported-files HTTP/1.1" 200 -
2025-06-15 15:09:55,519 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:09:55] "POST /api/production/clear-import-progress HTTP/1.1" 200 -
2025-06-15 15:09:55,840 - app.api.routes - INFO - 使用SQLite数据库: D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\instance\aps.db
2025-06-15 15:09:55,847 - import_excel_to_mysql - INFO - 进度更新: 0% - 正在扫描Excel文件...
2025-06-15 15:09:55,848 - import_excel_to_mysql - INFO - 找到 7 个Excel文件
2025-06-15 15:09:55,850 - import_excel_to_mysql - INFO - 进度更新: 5% - 找到 7 个Excel文件，正在分析文件大小...
2025-06-15 15:09:56,104 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:09:56] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:09:56,635 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:09:56] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:09:57,402 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:09:57] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:09:57,893 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:09:57] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:09:58,394 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:09:58] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:09:58,931 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:09:58] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:09:59,411 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:09:59] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:09:59,928 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:09:59] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:00,398 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:00] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:00,893 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:00] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:01,368 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:01] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:01,916 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:01] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:02,418 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:02] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:02,934 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:02] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:03,396 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:03] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:03,902 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:03] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:04,425 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:04] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:04,907 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:04] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:05,419 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:05] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:05,876 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:05] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:06,453 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:06] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:06,873 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:06] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:07,436 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:07] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:07,895 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:07] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:08,392 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:08] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:09,037 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:09] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:09,097 - import_excel_to_mysql - INFO - 文件 CT.xlsx: 39712 条记录
2025-06-15 15:10:09,157 - import_excel_to_mysql - INFO - 文件 EQP_STATUS.xlsx: 62 条记录
2025-06-15 15:10:09,396 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:09] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:09,877 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:09] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:10,355 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:10] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:10,942 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:10] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:11,414 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:11] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:11,865 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:11] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:12,399 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:12] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:12,937 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:12] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:13,418 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:13] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:13,897 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:13] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:13,916 - import_excel_to_mysql - INFO - 文件 ET_FT_TEST_SPEC.xlsx: 5822 条记录
2025-06-15 15:10:14,194 - import_excel_to_mysql - INFO - 文件 ET_RECIPE_FILE.xlsx: 663 条记录
2025-06-15 15:10:14,399 - import_excel_to_mysql - INFO - 文件 ET_UPH_EQP.xlsx: 913 条记录
2025-06-15 15:10:14,402 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:14] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:14,484 - import_excel_to_mysql - INFO - 文件 ET_WAIT_LOT.xlsx: 171 条记录
2025-06-15 15:10:14,978 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:14] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:15,412 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:15] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:15,871 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:15] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:16,437 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:16] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:16,885 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:16] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:17,383 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:17] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:17,915 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:17] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:18,366 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:18] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:18,901 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:18] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:19,452 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:19] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:19,907 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:19] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:20,404 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:20] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:20,909 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:20] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:21,425 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:21] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:21,896 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:21] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:22,413 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:22] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:22,976 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:22] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:23,432 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:23] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:24,007 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:24] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:24,394 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:24] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:24,941 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:24] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:26,026 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:26] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:27,318 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:27] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:28,760 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:28] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:29,062 - import_excel_to_mysql - INFO - 文件 WIP_LOT.xlsx: 11363 条记录
2025-06-15 15:10:29,064 - import_excel_to_mysql - INFO - 总计需要导入 58706 条记录
2025-06-15 15:10:29,066 - import_excel_to_mysql - INFO - 进度更新: 10% - 分析完成，总计 58706 条记录，正在连接数据库...
2025-06-15 15:10:29,088 - import_excel_to_mysql - INFO - 成功连接到MySQL数据库: 1********:3306/aps
2025-06-15 15:10:29,091 - import_excel_to_mysql - INFO - 进度更新: 15% - 数据库连接成功，开始处理文件...
2025-06-15 15:10:29,091 - import_excel_to_mysql - INFO - 开始处理文件: D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\Input Excel list\2025.06.05\CT.xlsx
2025-06-15 15:10:29,349 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:29] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:30,322 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:30] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:31,314 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:31] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:32,344 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:32] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:32,771 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:32] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:33,111 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:33] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:33,457 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:33] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:33,868 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:33] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:34,382 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:34] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:34,886 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:34] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:35,343 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:35] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:35,439 - import_excel_to_mysql - INFO - 创建表 ct_ct 成功
2025-06-15 15:10:35,643 - import_excel_to_mysql - INFO - 进度更新: 16.447892889994208% - 正在导入表 ct_ct: 1000/39712 条记录
2025-06-15 15:10:35,644 - import_excel_to_mysql - INFO - 表 ct_ct: 已插入 1000/39712 条记录
2025-06-15 15:10:35,832 - import_excel_to_mysql - INFO - 进度更新: 17.895785779988415% - 正在导入表 ct_ct: 2000/39712 条记录
2025-06-15 15:10:35,833 - import_excel_to_mysql - INFO - 表 ct_ct: 已插入 2000/39712 条记录
2025-06-15 15:10:35,953 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:35] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:36,035 - import_excel_to_mysql - INFO - 进度更新: 19.343678669982626% - 正在导入表 ct_ct: 3000/39712 条记录
2025-06-15 15:10:36,036 - import_excel_to_mysql - INFO - 表 ct_ct: 已插入 3000/39712 条记录
2025-06-15 15:10:36,218 - import_excel_to_mysql - INFO - 进度更新: 20.791571559976834% - 正在导入表 ct_ct: 4000/39712 条记录
2025-06-15 15:10:36,218 - import_excel_to_mysql - INFO - 表 ct_ct: 已插入 4000/39712 条记录
2025-06-15 15:10:36,363 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:36] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:36,409 - import_excel_to_mysql - INFO - 进度更新: 22.23946444997104% - 正在导入表 ct_ct: 5000/39712 条记录
2025-06-15 15:10:36,411 - import_excel_to_mysql - INFO - 表 ct_ct: 已插入 5000/39712 条记录
2025-06-15 15:10:36,589 - import_excel_to_mysql - INFO - 进度更新: 23.687357339965253% - 正在导入表 ct_ct: 6000/39712 条记录
2025-06-15 15:10:36,590 - import_excel_to_mysql - INFO - 表 ct_ct: 已插入 6000/39712 条记录
2025-06-15 15:10:36,776 - import_excel_to_mysql - INFO - 进度更新: 25.13525022995946% - 正在导入表 ct_ct: 7000/39712 条记录
2025-06-15 15:10:36,777 - import_excel_to_mysql - INFO - 表 ct_ct: 已插入 7000/39712 条记录
2025-06-15 15:10:36,914 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:36] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:36,966 - import_excel_to_mysql - INFO - 进度更新: 26.583143119953668% - 正在导入表 ct_ct: 8000/39712 条记录
2025-06-15 15:10:36,966 - import_excel_to_mysql - INFO - 表 ct_ct: 已插入 8000/39712 条记录
2025-06-15 15:10:37,156 - import_excel_to_mysql - INFO - 进度更新: 28.031036009947876% - 正在导入表 ct_ct: 9000/39712 条记录
2025-06-15 15:10:37,156 - import_excel_to_mysql - INFO - 表 ct_ct: 已插入 9000/39712 条记录
2025-06-15 15:10:37,344 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:37] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:37,353 - import_excel_to_mysql - INFO - 进度更新: 29.478928899942083% - 正在导入表 ct_ct: 10000/39712 条记录
2025-06-15 15:10:37,354 - import_excel_to_mysql - INFO - 表 ct_ct: 已插入 10000/39712 条记录
2025-06-15 15:10:37,713 - import_excel_to_mysql - INFO - 进度更新: 30.92682178993629% - 正在导入表 ct_ct: 11000/39712 条记录
2025-06-15 15:10:37,713 - import_excel_to_mysql - INFO - 表 ct_ct: 已插入 11000/39712 条记录
2025-06-15 15:10:38,004 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:38] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:38,078 - import_excel_to_mysql - INFO - 进度更新: 32.374714679930506% - 正在导入表 ct_ct: 12000/39712 条记录
2025-06-15 15:10:38,079 - import_excel_to_mysql - INFO - 表 ct_ct: 已插入 12000/39712 条记录
2025-06-15 15:10:38,353 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:38] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:38,392 - import_excel_to_mysql - INFO - 进度更新: 33.82260756992471% - 正在导入表 ct_ct: 13000/39712 条记录
2025-06-15 15:10:38,393 - import_excel_to_mysql - INFO - 表 ct_ct: 已插入 13000/39712 条记录
2025-06-15 15:10:38,716 - import_excel_to_mysql - INFO - 进度更新: 35.27050045991892% - 正在导入表 ct_ct: 14000/39712 条记录
2025-06-15 15:10:38,718 - import_excel_to_mysql - INFO - 表 ct_ct: 已插入 14000/39712 条记录
2025-06-15 15:10:39,003 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:39] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:39,058 - import_excel_to_mysql - INFO - 进度更新: 36.71839334991313% - 正在导入表 ct_ct: 15000/39712 条记录
2025-06-15 15:10:39,059 - import_excel_to_mysql - INFO - 表 ct_ct: 已插入 15000/39712 条记录
2025-06-15 15:10:39,351 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:39] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:39,398 - import_excel_to_mysql - INFO - 进度更新: 38.166286239907336% - 正在导入表 ct_ct: 16000/39712 条记录
2025-06-15 15:10:39,399 - import_excel_to_mysql - INFO - 表 ct_ct: 已插入 16000/39712 条记录
2025-06-15 15:10:39,761 - import_excel_to_mysql - INFO - 进度更新: 39.614179129901544% - 正在导入表 ct_ct: 17000/39712 条记录
2025-06-15 15:10:39,762 - import_excel_to_mysql - INFO - 表 ct_ct: 已插入 17000/39712 条记录
2025-06-15 15:10:40,001 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:40] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:40,108 - import_excel_to_mysql - INFO - 进度更新: 41.06207201989575% - 正在导入表 ct_ct: 18000/39712 条记录
2025-06-15 15:10:40,109 - import_excel_to_mysql - INFO - 表 ct_ct: 已插入 18000/39712 条记录
2025-06-15 15:10:40,401 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:40] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:40,443 - import_excel_to_mysql - INFO - 进度更新: 42.50996490988996% - 正在导入表 ct_ct: 19000/39712 条记录
2025-06-15 15:10:40,444 - import_excel_to_mysql - INFO - 表 ct_ct: 已插入 19000/39712 条记录
2025-06-15 15:10:40,850 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:40] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:40,883 - import_excel_to_mysql - INFO - 进度更新: 43.95785779988417% - 正在导入表 ct_ct: 20000/39712 条记录
2025-06-15 15:10:40,884 - import_excel_to_mysql - INFO - 表 ct_ct: 已插入 20000/39712 条记录
2025-06-15 15:10:41,222 - import_excel_to_mysql - INFO - 进度更新: 45.405750689878374% - 正在导入表 ct_ct: 21000/39712 条记录
2025-06-15 15:10:41,222 - import_excel_to_mysql - INFO - 表 ct_ct: 已插入 21000/39712 条记录
2025-06-15 15:10:41,511 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:41] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:41,557 - import_excel_to_mysql - INFO - 进度更新: 46.85364357987258% - 正在导入表 ct_ct: 22000/39712 条记录
2025-06-15 15:10:41,558 - import_excel_to_mysql - INFO - 表 ct_ct: 已插入 22000/39712 条记录
2025-06-15 15:10:41,841 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:41] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:41,884 - import_excel_to_mysql - INFO - 进度更新: 48.3015364698668% - 正在导入表 ct_ct: 23000/39712 条记录
2025-06-15 15:10:41,885 - import_excel_to_mysql - INFO - 表 ct_ct: 已插入 23000/39712 条记录
2025-06-15 15:10:42,230 - import_excel_to_mysql - INFO - 进度更新: 49.749429359861004% - 正在导入表 ct_ct: 24000/39712 条记录
2025-06-15 15:10:42,231 - import_excel_to_mysql - INFO - 表 ct_ct: 已插入 24000/39712 条记录
2025-06-15 15:10:42,511 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:42] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:42,567 - import_excel_to_mysql - INFO - 进度更新: 51.19732224985521% - 正在导入表 ct_ct: 25000/39712 条记录
2025-06-15 15:10:42,567 - import_excel_to_mysql - INFO - 表 ct_ct: 已插入 25000/39712 条记录
2025-06-15 15:10:42,849 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:42] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:42,891 - import_excel_to_mysql - INFO - 进度更新: 52.64521513984942% - 正在导入表 ct_ct: 26000/39712 条记录
2025-06-15 15:10:42,892 - import_excel_to_mysql - INFO - 表 ct_ct: 已插入 26000/39712 条记录
2025-06-15 15:10:43,333 - import_excel_to_mysql - INFO - 进度更新: 54.09310802984363% - 正在导入表 ct_ct: 27000/39712 条记录
2025-06-15 15:10:43,350 - import_excel_to_mysql - INFO - 表 ct_ct: 已插入 27000/39712 条记录
2025-06-15 15:10:43,736 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:43] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:44,497 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:44] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:44,683 - import_excel_to_mysql - INFO - 进度更新: 55.541000919837835% - 正在导入表 ct_ct: 28000/39712 条记录
2025-06-15 15:10:44,683 - import_excel_to_mysql - INFO - 表 ct_ct: 已插入 28000/39712 条记录
2025-06-15 15:10:45,090 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:45] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:45,147 - import_excel_to_mysql - INFO - 进度更新: 56.98889380983204% - 正在导入表 ct_ct: 29000/39712 条记录
2025-06-15 15:10:45,148 - import_excel_to_mysql - INFO - 表 ct_ct: 已插入 29000/39712 条记录
2025-06-15 15:10:45,522 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:45] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:45,567 - import_excel_to_mysql - INFO - 进度更新: 58.43678669982625% - 正在导入表 ct_ct: 30000/39712 条记录
2025-06-15 15:10:45,568 - import_excel_to_mysql - INFO - 表 ct_ct: 已插入 30000/39712 条记录
2025-06-15 15:10:45,859 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:45] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:45,899 - import_excel_to_mysql - INFO - 进度更新: 59.88467958982046% - 正在导入表 ct_ct: 31000/39712 条记录
2025-06-15 15:10:45,900 - import_excel_to_mysql - INFO - 表 ct_ct: 已插入 31000/39712 条记录
2025-06-15 15:10:46,196 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:46] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:46,242 - import_excel_to_mysql - INFO - 进度更新: 61.33257247981467% - 正在导入表 ct_ct: 32000/39712 条记录
2025-06-15 15:10:46,244 - import_excel_to_mysql - INFO - 表 ct_ct: 已插入 32000/39712 条记录
2025-06-15 15:10:46,538 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:46] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:46,586 - import_excel_to_mysql - INFO - 进度更新: 62.78046536980888% - 正在导入表 ct_ct: 33000/39712 条记录
2025-06-15 15:10:46,587 - import_excel_to_mysql - INFO - 表 ct_ct: 已插入 33000/39712 条记录
2025-06-15 15:10:46,862 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:46] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:46,904 - import_excel_to_mysql - INFO - 进度更新: 64.22835825980309% - 正在导入表 ct_ct: 34000/39712 条记录
2025-06-15 15:10:46,905 - import_excel_to_mysql - INFO - 表 ct_ct: 已插入 34000/39712 条记录
2025-06-15 15:10:47,262 - import_excel_to_mysql - INFO - 进度更新: 65.6762511497973% - 正在导入表 ct_ct: 35000/39712 条记录
2025-06-15 15:10:47,263 - import_excel_to_mysql - INFO - 表 ct_ct: 已插入 35000/39712 条记录
2025-06-15 15:10:47,550 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:47] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:47,603 - import_excel_to_mysql - INFO - 进度更新: 67.1241440397915% - 正在导入表 ct_ct: 36000/39712 条记录
2025-06-15 15:10:47,604 - import_excel_to_mysql - INFO - 表 ct_ct: 已插入 36000/39712 条记录
2025-06-15 15:10:47,884 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:47] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:47,923 - import_excel_to_mysql - INFO - 进度更新: 68.57203692978571% - 正在导入表 ct_ct: 37000/39712 条记录
2025-06-15 15:10:47,924 - import_excel_to_mysql - INFO - 表 ct_ct: 已插入 37000/39712 条记录
2025-06-15 15:10:48,251 - import_excel_to_mysql - INFO - 进度更新: 70.01992981977992% - 正在导入表 ct_ct: 38000/39712 条记录
2025-06-15 15:10:48,252 - import_excel_to_mysql - INFO - 表 ct_ct: 已插入 38000/39712 条记录
2025-06-15 15:10:48,493 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:48] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:48,590 - import_excel_to_mysql - INFO - 进度更新: 71.46782270977413% - 正在导入表 ct_ct: 39000/39712 条记录
2025-06-15 15:10:48,590 - import_excel_to_mysql - INFO - 表 ct_ct: 已插入 39000/39712 条记录
2025-06-15 15:10:48,818 - import_excel_to_mysql - INFO - 进度更新: 72.49872244745% - 正在导入表 ct_ct: 39712/39712 条记录
2025-06-15 15:10:48,819 - import_excel_to_mysql - INFO - 表 ct_ct: 已插入 39712/39712 条记录
2025-06-15 15:10:48,847 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:48] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:48,882 - import_excel_to_mysql - INFO - 文件 D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\Input Excel list\2025.06.05\CT.xlsx 处理完成，共处理 39712 条记录
2025-06-15 15:10:48,886 - import_excel_to_mysql - INFO - 开始处理文件: D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\Input Excel list\2025.06.05\EQP_STATUS.xlsx
2025-06-15 15:10:48,960 - import_excel_to_mysql - INFO - 创建表 eqp_status_eqp_status 成功
2025-06-15 15:10:48,979 - import_excel_to_mysql - INFO - 进度更新: 72.58849180662963% - 正在导入表 eqp_status_eqp_status: 62/62 条记录
2025-06-15 15:10:48,979 - import_excel_to_mysql - INFO - 表 eqp_status_eqp_status: 已插入 62/62 条记录
2025-06-15 15:10:48,983 - import_excel_to_mysql - INFO - 文件 D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\Input Excel list\2025.06.05\EQP_STATUS.xlsx 处理完成，共处理 62 条记录
2025-06-15 15:10:48,983 - import_excel_to_mysql - INFO - 开始处理文件: D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\Input Excel list\2025.06.05\ET_FT_TEST_SPEC.xlsx
2025-06-15 15:10:49,395 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:49] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:49,934 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:49] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:50,411 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:50] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:50,874 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:50] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:51,409 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:51] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:51,911 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:51] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:52,456 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:52] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:52,904 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:52] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:53,390 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:53] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:53,789 - import_excel_to_mysql - INFO - 创建表 et_ft_test_spec_et_ft_test_spec 成功
2025-06-15 15:10:54,008 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:54] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:54,342 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:54] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:54,466 - import_excel_to_mysql - INFO - 进度更新: 74.03638469662386% - 正在导入表 et_ft_test_spec_et_ft_test_spec: 1000/5822 条记录
2025-06-15 15:10:54,466 - import_excel_to_mysql - INFO - 表 et_ft_test_spec_et_ft_test_spec: 已插入 1000/5822 条记录
2025-06-15 15:10:54,954 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:54] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:55,082 - import_excel_to_mysql - INFO - 进度更新: 75.48427758661805% - 正在导入表 et_ft_test_spec_et_ft_test_spec: 2000/5822 条记录
2025-06-15 15:10:55,084 - import_excel_to_mysql - INFO - 表 et_ft_test_spec_et_ft_test_spec: 已插入 2000/5822 条记录
2025-06-15 15:10:55,473 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:55] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:55,732 - import_excel_to_mysql - INFO - 进度更新: 76.93217047661227% - 正在导入表 et_ft_test_spec_et_ft_test_spec: 3000/5822 条记录
2025-06-15 15:10:55,734 - import_excel_to_mysql - INFO - 表 et_ft_test_spec_et_ft_test_spec: 已插入 3000/5822 条记录
2025-06-15 15:10:56,142 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:56] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:56,416 - import_excel_to_mysql - INFO - 进度更新: 78.38006336660648% - 正在导入表 et_ft_test_spec_et_ft_test_spec: 4000/5822 条记录
2025-06-15 15:10:56,416 - import_excel_to_mysql - INFO - 表 et_ft_test_spec_et_ft_test_spec: 已插入 4000/5822 条记录
2025-06-15 15:10:56,762 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:56] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:57,110 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:57] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:57,144 - import_excel_to_mysql - INFO - 进度更新: 79.82795625660069% - 正在导入表 et_ft_test_spec_et_ft_test_spec: 5000/5822 条记录
2025-06-15 15:10:57,145 - import_excel_to_mysql - INFO - 表 et_ft_test_spec_et_ft_test_spec: 已插入 5000/5822 条记录
2025-06-15 15:10:57,569 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:57] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:57,666 - import_excel_to_mysql - INFO - 进度更新: 81.01812421217593% - 正在导入表 et_ft_test_spec_et_ft_test_spec: 5822/5822 条记录
2025-06-15 15:10:57,667 - import_excel_to_mysql - INFO - 表 et_ft_test_spec_et_ft_test_spec: 已插入 5822/5822 条记录
2025-06-15 15:10:57,702 - import_excel_to_mysql - INFO - 文件 D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\Input Excel list\2025.06.05\ET_FT_TEST_SPEC.xlsx 处理完成，共处理 5822 条记录
2025-06-15 15:10:57,704 - import_excel_to_mysql - INFO - 开始处理文件: D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\Input Excel list\2025.06.05\ET_RECIPE_FILE.xlsx
2025-06-15 15:10:57,951 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:57] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:58,120 - import_excel_to_mysql - INFO - 创建表 et_recipe_file_et_recipe_file 成功
2025-06-15 15:10:58,342 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:58] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:58,358 - import_excel_to_mysql - INFO - 进度更新: 81.97807719824209% - 正在导入表 et_recipe_file_et_recipe_file: 663/663 条记录
2025-06-15 15:10:58,358 - import_excel_to_mysql - INFO - 表 et_recipe_file_et_recipe_file: 已插入 663/663 条记录
2025-06-15 15:10:58,364 - import_excel_to_mysql - INFO - 文件 D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\Input Excel list\2025.06.05\ET_RECIPE_FILE.xlsx 处理完成，共处理 663 条记录
2025-06-15 15:10:58,364 - import_excel_to_mysql - INFO - 开始处理文件: D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\Input Excel list\2025.06.05\ET_UPH_EQP.xlsx
2025-06-15 15:10:58,599 - import_excel_to_mysql - INFO - 创建表 et_uph_eqp_et_uph_eqp 成功
2025-06-15 15:10:58,797 - import_excel_to_mysql - INFO - 进度更新: 83.3000034068068% - 正在导入表 et_uph_eqp_et_uph_eqp: 913/913 条记录
2025-06-15 15:10:58,798 - import_excel_to_mysql - INFO - 表 et_uph_eqp_et_uph_eqp: 已插入 913/913 条记录
2025-06-15 15:10:58,804 - import_excel_to_mysql - INFO - 文件 D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\Input Excel list\2025.06.05\ET_UPH_EQP.xlsx 处理完成，共处理 913 条记录
2025-06-15 15:10:58,804 - import_excel_to_mysql - INFO - 开始处理文件: D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\Input Excel list\2025.06.05\ET_WAIT_LOT.xlsx
2025-06-15 15:10:58,881 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:58] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:58,923 - import_excel_to_mysql - INFO - 创建表 et_wait_lot_et_wait_lot 成功
2025-06-15 15:10:58,972 - import_excel_to_mysql - INFO - 进度更新: 83.5475930909958% - 正在导入表 et_wait_lot_et_wait_lot: 171/171 条记录
2025-06-15 15:10:58,976 - import_excel_to_mysql - INFO - 表 et_wait_lot_et_wait_lot: 已插入 171/171 条记录
2025-06-15 15:10:58,980 - import_excel_to_mysql - INFO - 文件 D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\Input Excel list\2025.06.05\ET_WAIT_LOT.xlsx 处理完成，共处理 171 条记录
2025-06-15 15:10:58,981 - import_excel_to_mysql - INFO - 开始处理文件: D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\Input Excel list\2025.06.05\WIP_LOT.xlsx
2025-06-15 15:10:59,399 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:59] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:10:59,901 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:10:59] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:11:00,394 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:11:00] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:11:00,902 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:11:00] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:11:01,416 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:11:01] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:11:01,914 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:11:01] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:11:02,412 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:11:02] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:11:02,908 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:11:02] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:11:03,394 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:11:03] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:11:03,911 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:11:03] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:11:04,403 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:11:04] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:11:04,909 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:11:04] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:11:05,387 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:11:05] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:11:05,885 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:11:05] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:11:06,424 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:11:06] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:11:06,907 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:11:06] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:11:07,398 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:11:07] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:11:07,893 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:11:07] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:11:08,391 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:11:08] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:11:08,898 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:11:08] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:11:09,401 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:11:09] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:11:09,895 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:11:09] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:11:10,507 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:11:10] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:11:10,973 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:11:10] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:11:11,432 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:11:11] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:11:11,596 - import_excel_to_mysql - INFO - 创建表 wip_lot_wip_lot 成功
2025-06-15 15:11:12,062 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:11:12] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:11:12,449 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:11:12] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:11:12,682 - import_excel_to_mysql - INFO - 进度更新: 84.99548598099003% - 正在导入表 wip_lot_wip_lot: 1000/11363 条记录
2025-06-15 15:11:12,683 - import_excel_to_mysql - INFO - 表 wip_lot_wip_lot: 已插入 1000/11363 条记录
2025-06-15 15:11:12,994 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:11:12] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:11:13,556 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:11:13] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:11:13,753 - import_excel_to_mysql - INFO - 进度更新: 86.44337887098423% - 正在导入表 wip_lot_wip_lot: 2000/11363 条记录
2025-06-15 15:11:13,753 - import_excel_to_mysql - INFO - 表 wip_lot_wip_lot: 已插入 2000/11363 条记录
2025-06-15 15:11:14,145 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:11:14] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:11:14,619 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:11:14] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:11:14,829 - import_excel_to_mysql - INFO - 进度更新: 87.89127176097844% - 正在导入表 wip_lot_wip_lot: 3000/11363 条记录
2025-06-15 15:11:14,831 - import_excel_to_mysql - INFO - 表 wip_lot_wip_lot: 已插入 3000/11363 条记录
2025-06-15 15:11:15,126 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:11:15] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:11:15,657 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:11:15] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:11:15,891 - import_excel_to_mysql - INFO - 进度更新: 89.33916465097265% - 正在导入表 wip_lot_wip_lot: 4000/11363 条记录
2025-06-15 15:11:15,893 - import_excel_to_mysql - INFO - 表 wip_lot_wip_lot: 已插入 4000/11363 条记录
2025-06-15 15:11:16,139 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:11:16] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:11:16,649 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:11:16] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:11:16,942 - import_excel_to_mysql - INFO - 进度更新: 90.78705754096686% - 正在导入表 wip_lot_wip_lot: 5000/11363 条记录
2025-06-15 15:11:16,943 - import_excel_to_mysql - INFO - 表 wip_lot_wip_lot: 已插入 5000/11363 条记录
2025-06-15 15:11:17,178 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:11:17] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:11:17,830 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:11:17] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:11:18,036 - import_excel_to_mysql - INFO - 进度更新: 92.23495043096106% - 正在导入表 wip_lot_wip_lot: 6000/11363 条记录
2025-06-15 15:11:18,036 - import_excel_to_mysql - INFO - 表 wip_lot_wip_lot: 已插入 6000/11363 条记录
2025-06-15 15:11:18,330 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:11:18] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:11:18,844 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:11:18] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:11:19,090 - import_excel_to_mysql - INFO - 进度更新: 93.68284332095527% - 正在导入表 wip_lot_wip_lot: 7000/11363 条记录
2025-06-15 15:11:19,091 - import_excel_to_mysql - INFO - 表 wip_lot_wip_lot: 已插入 7000/11363 条记录
2025-06-15 15:11:19,398 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:11:19] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:11:20,014 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:11:20] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:11:20,225 - import_excel_to_mysql - INFO - 进度更新: 95.13073621094948% - 正在导入表 wip_lot_wip_lot: 8000/11363 条记录
2025-06-15 15:11:20,226 - import_excel_to_mysql - INFO - 表 wip_lot_wip_lot: 已插入 8000/11363 条记录
2025-06-15 15:11:20,601 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:11:20] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:11:20,946 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:11:20] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:11:21,082 - import_excel_to_mysql - INFO - 进度更新: 96.57862910094369% - 正在导入表 wip_lot_wip_lot: 9000/11363 条记录
2025-06-15 15:11:21,082 - import_excel_to_mysql - INFO - 表 wip_lot_wip_lot: 已插入 9000/11363 条记录
2025-06-15 15:11:21,476 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:11:21] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:11:22,204 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:11:22] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:11:22,618 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:11:22] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:11:22,816 - import_excel_to_mysql - INFO - 进度更新: 98.0265219909379% - 正在导入表 wip_lot_wip_lot: 10000/11363 条记录
2025-06-15 15:11:22,817 - import_excel_to_mysql - INFO - 表 wip_lot_wip_lot: 已插入 10000/11363 条记录
2025-06-15 15:11:23,090 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:11:23] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:11:23,539 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:11:23] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:11:24,029 - import_excel_to_mysql - INFO - 进度更新: 99% - 正在导入表 wip_lot_wip_lot: 11000/11363 条记录
2025-06-15 15:11:24,030 - import_excel_to_mysql - INFO - 表 wip_lot_wip_lot: 已插入 11000/11363 条记录
2025-06-15 15:11:24,405 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:11:24] "GET /api/production/import-progress HTTP/1.1" 200 -
2025-06-15 15:11:24,536 - import_excel_to_mysql - INFO - 进度更新: 99% - 正在导入表 wip_lot_wip_lot: 11363/11363 条记录
2025-06-15 15:11:24,537 - import_excel_to_mysql - INFO - 表 wip_lot_wip_lot: 已插入 11363/11363 条记录
2025-06-15 15:11:24,573 - import_excel_to_mysql - INFO - 文件 D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\Input Excel list\2025.06.05\WIP_LOT.xlsx 处理完成，共处理 11363 条记录
2025-06-15 15:11:24,579 - import_excel_to_mysql - INFO - 进度更新: 100% - 导入完成！
2025-06-15 15:11:24,589 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:11:24] "POST /api/production/import-from-directory HTTP/1.1" 200 -
2025-06-15 15:11:26,372 - app - INFO - 成功连接到MySQL数据库: aps
2025-06-15 15:11:26,397 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:11:26] "GET /api/production/imported-files HTTP/1.1" 200 -
2025-06-15 15:12:33,570 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:12:33] "GET /resources/hardware HTTP/1.1" 200 -
2025-06-15 15:12:33,875 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:12:33] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 15:12:33,922 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:12:33] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 15:12:33,923 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:12:33] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 15:12:33,924 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:12:33] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 15:12:33,925 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:12:33] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 15:12:34,241 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:12:34] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 15:12:34,966 - app - INFO - 成功连接到MySQL数据库: aps
2025-06-15 15:12:34,971 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:12:34] "GET /api/resources/data/eqp_status?page=1&per_page=50 HTTP/1.1" 200 -
2025-06-15 15:12:35,258 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:12:35] "[33mGET /api/user-filter-presets?page_type=hardware_resources HTTP/1.1[0m" 404 -
2025-06-15 15:12:39,042 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:12:39] "GET /production/semi-auto HTTP/1.1" 200 -
2025-06-15 15:12:39,356 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:12:39] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 15:12:39,387 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:12:39] "[36mGET /static/css/core/main.css HTTP/1.1[0m" 304 -
2025-06-15 15:12:39,388 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:12:39] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 15:12:39,389 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:12:39] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 15:12:39,389 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:12:39] "[36mGET /static/css/daygrid/main.css HTTP/1.1[0m" 304 -
2025-06-15 15:12:39,390 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:12:39] "[36mGET /static/css/timegrid/main.css HTTP/1.1[0m" 304 -
2025-06-15 15:12:39,661 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:12:39] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 15:12:39,707 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:12:39] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 15:12:39,708 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:12:39] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 15:12:39,709 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:12:39] "[36mGET /static/js/xlsx.full.min.js HTTP/1.1[0m" 304 -
2025-06-15 15:12:40,082 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:12:40] "GET /api/production/get-import-path HTTP/1.1" 200 -
2025-06-15 15:12:40,122 - app - INFO - 成功连接到MySQL数据库: aps
2025-06-15 15:12:40,142 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:12:40] "GET /api/production/imported-files HTTP/1.1" 200 -
2025-06-15 15:12:42,791 - app - INFO - 成功连接到MySQL数据库: aps
2025-06-15 15:12:42,800 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:12:42] "GET /api/production/file-data/ct.xlsx?page=1 HTTP/1.1" 200 -
2025-06-15 15:12:49,148 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:12:49] "GET /production/algorithm HTTP/1.1" 200 -
2025-06-15 15:12:49,218 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:12:49] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 15:12:49,405 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:12:49] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 15:12:49,497 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:12:49] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 15:12:49,498 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:12:49] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 15:12:49,498 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:12:49] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 15:12:49,499 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:12:49] "[36mGET /static/js/xlsx.full.min.js HTTP/1.1[0m" 304 -
2025-06-15 15:12:49,819 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:12:49] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 15:12:49,864 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:12:49] "[33mGET /api/production/history-times HTTP/1.1[0m" 404 -
2025-06-15 15:12:51,824 - app.api.production_api - INFO - 开始执行自动排产: {'algorithm': 'priority', 'optimization_target': 'makespan', 'time_limit': 30, 'population_size': 100, 'auto_mode': False}
2025-06-15 15:12:51,829 - app.api.production_api - INFO - 获取到 171 条待排产批次数据
2025-06-15 15:12:51,831 - app.api.production_api - INFO - 加载了 21 条优先级配置
2025-06-15 15:12:51,855 - app.api.production_api - INFO - 获取到 913 台可用设备
2025-06-15 15:12:52,068 - app.api.production_api - INFO - 排产算法 priority 完成，生成 171 条排产记录
2025-06-15 15:12:52,069 - app.api.production_api - INFO - 自动排产完成: 171 条记录，耗时 0.25秒
2025-06-15 15:12:52,071 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:12:52] "POST /api/production/auto-schedule HTTP/1.1" 200 -
2025-06-15 15:13:50,284 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:13:50] "GET /production/auto HTTP/1.1" 200 -
2025-06-15 15:13:50,371 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:13:50] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 15:13:50,542 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:13:50] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 15:13:50,622 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:13:50] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 15:13:50,622 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:13:50] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 15:13:50,623 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:13:50] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 15:13:50,645 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:13:50] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 15:13:50,963 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:13:50] "GET /api/production/schedule-history HTTP/1.1" 200 -
2025-06-15 15:13:59,432 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:13:59] "GET /production/priority-matching HTTP/1.1" 200 -
2025-06-15 15:13:59,735 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:13:59] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 15:13:59,782 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:13:59] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 15:13:59,782 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:13:59] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 15:13:59,782 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:13:59] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 15:13:59,782 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:13:59] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 15:14:00,103 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:14:00] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 15:14:02,936 - app.api.priority_matching_api - INFO - 开始批次匹配: strategy=intelligent, threshold=0.5
2025-06-15 15:14:02,937 - app.services.priority_matching_service - INFO - 开始批次匹配，策略: intelligent, 置信度阈值: 0.5
2025-06-15 15:14:02,944 - app.services.priority_matching_service - INFO - 获取到 171 个待排产批次
2025-06-15 15:14:02,947 - app.services.priority_matching_service - INFO - 获取到 21 个优先级配置
2025-06-15 15:14:03,172 - app.services.priority_matching_service - INFO - 匹配完成: 总批次 171, 成功匹配 171, 匹配率 100.0%
2025-06-15 15:14:03,275 - app.api.priority_matching_api - INFO - 批次匹配完成: 总数=171, 匹配率=100.0%
2025-06-15 15:14:03,278 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:14:03] "POST /api/priority/match-batches HTTP/1.1" 200 -
2025-06-15 15:14:51,004 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:14:51] "GET /production/auto HTTP/1.1" 200 -
2025-06-15 15:14:51,067 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:14:51] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 15:14:51,270 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:14:51] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 15:14:51,347 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:14:51] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 15:14:51,351 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:14:51] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 15:14:51,351 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:14:51] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 15:14:51,356 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:14:51] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 15:14:51,686 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:14:51] "GET /api/production/schedule-history HTTP/1.1" 200 -
2025-06-15 15:14:56,777 - app.api.production_api - INFO - 开始执行自动排产: {'algorithm': 'intelligent', 'optimization_target': 'balanced', 'time_limit': 30, 'population_size': 100, 'auto_mode': False}
2025-06-15 15:14:56,780 - app.api.production_api - INFO - 获取到 171 条待排产批次数据
2025-06-15 15:14:56,782 - app.api.production_api - INFO - 加载了 21 条优先级配置
2025-06-15 15:14:56,807 - app.api.production_api - INFO - 获取到 913 台可用设备
2025-06-15 15:14:57,033 - app.api.production_api - INFO - 排产算法 intelligent 完成，生成 171 条排产记录
2025-06-15 15:14:57,034 - app.api.production_api - INFO - 自动排产完成: 171 条记录，耗时 0.26秒
2025-06-15 15:14:57,037 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:14:57] "POST /api/production/auto-schedule HTTP/1.1" 200 -
2025-06-15 15:15:06,701 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:15:06] "GET /production/algorithm HTTP/1.1" 200 -
2025-06-15 15:15:06,790 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:15:06] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 15:15:06,958 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:15:06] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 15:15:07,037 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:15:07] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 15:15:07,038 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:15:07] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 15:15:07,039 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:15:07] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 15:15:07,040 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:15:07] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 15:15:07,281 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:15:07] "[36mGET /static/js/xlsx.full.min.js HTTP/1.1[0m" 304 -
2025-06-15 15:15:07,651 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:15:07] "[33mGET /api/production/history-times HTTP/1.1[0m" 404 -
2025-06-15 15:15:09,157 - app.api.production_api - INFO - 开始执行自动排产: {'algorithm': 'priority', 'optimization_target': 'makespan', 'time_limit': 30, 'population_size': 100, 'auto_mode': False}
2025-06-15 15:15:09,160 - app.api.production_api - INFO - 获取到 171 条待排产批次数据
2025-06-15 15:15:09,162 - app.api.production_api - INFO - 加载了 21 条优先级配置
2025-06-15 15:15:09,187 - app.api.production_api - INFO - 获取到 913 台可用设备
2025-06-15 15:15:09,413 - app.api.production_api - INFO - 排产算法 priority 完成，生成 171 条排产记录
2025-06-15 15:15:09,415 - app.api.production_api - INFO - 自动排产完成: 171 条记录，耗时 0.26秒
2025-06-15 15:15:09,418 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:15:09] "POST /api/production/auto-schedule HTTP/1.1" 200 -
2025-06-15 15:15:41,041 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:15:41] "GET /production/data HTTP/1.1" 200 -
2025-06-15 15:15:41,114 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:15:41] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 15:15:41,298 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:15:41] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 15:15:41,375 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:15:41] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 15:15:41,376 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:15:41] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 15:15:41,379 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:15:41] "GET /static/vendor/echarts/echarts.min.js HTTP/1.1" 200 -
2025-06-15 15:15:41,380 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:15:41] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 15:15:41,620 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:15:41] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 15:15:41,758 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:15:41] "[33mGET /api/production/chart-data?table=Lot_WIP HTTP/1.1[0m" 404 -
2025-06-15 15:15:41,759 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:15:41] "[33mGET /api/production/table-data?table=Lot_WIP&page=1&size=20 HTTP/1.1[0m" 404 -
2025-06-15 15:19:59,113 - APS-System - INFO - 应用路径: D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12
2025-06-15 15:19:59,113 - APS-System - INFO - 实例路径: D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\instance
2025-06-15 15:19:59,114 - APS-System - INFO - 检查 MySQL 数据库配置...
2025-06-15 15:19:59,169 - APS-System - INFO - MySQL 数据库 'aps' 已存在
2025-06-15 15:19:59,170 - APS-System - INFO - MySQL 数据库 'aps_system' 已存在
2025-06-15 15:19:59,170 - APS-System - INFO - MySQL 数据库检查成功
2025-06-15 15:19:59,171 - APS-System - INFO - 正在修复数据库权限...
2025-06-15 15:19:59,171 - APS-System - INFO - 发现SQLite文件，但系统已迁移到MySQL，跳过: instance\aps.db
2025-06-15 15:19:59,171 - APS-System - INFO - 数据库文件不存在，跳过: instance\aps_system.db
2025-06-15 15:19:59,172 - APS-System - INFO - 已修复日志目录权限: logs
2025-06-15 15:19:59,172 - APS-System - INFO - 数据库权限修复成功
2025-06-15 15:20:00,853 - app - INFO - 应用启动
2025-06-15 15:20:00,855 - app.utils.scheduler - INFO - 已重新加载 0 个邮箱配置（应用实例未设置）
2025-06-15 15:20:00,875 - app.utils.scheduler - INFO - 邮件附件定时调度器已启动
2025-06-15 15:20:00,892 - app - INFO - 邮件附件定时任务调度器已启动
2025-06-15 15:20:00,915 - app.utils.scheduler - INFO - 全局定时任务已禁用，邮件调度器跟随禁用
2025-06-15 15:20:00,917 - app.utils.scheduler - INFO - 检测到全局定时任务已禁用，停止邮件调度器
2025-06-15 15:20:01,029 - app.services.scheduler_service - INFO - APScheduler调度器配置完成: timezone=Asia/Shanghai, max_workers=10
2025-06-15 15:20:01,036 - apscheduler.scheduler - INFO - Scheduler started
2025-06-15 15:20:01,040 - app.services.scheduler_service - INFO - 从数据库加载了 0 个任务
2025-06-15 15:20:01,042 - app.services.scheduler_service - INFO - APScheduler调度器启动成功
2025-06-15 15:20:01,042 - app - INFO - ✅ APScheduler统一调度器启动成功
2025-06-15 15:20:01,043 - APS-System - INFO - 应用创建成功
2025-06-15 15:20:01,044 - APS-System - INFO - ==================================================
2025-06-15 15:20:01,044 - APS-System - INFO - 🚀 APS车规芯片终测智能调度平台 v2.1
2025-06-15 15:20:01,044 - APS-System - INFO - 📊 100% MySQL数据库架构
2025-06-15 15:20:01,044 - APS-System - INFO - 🌐 访问地址: http://1********:5000
2025-06-15 15:20:01,044 - APS-System - INFO - ==================================================
2025-06-15 15:20:01,044 - APS-System - INFO - 运行模式: 开发环境
2025-06-15 15:20:01,058 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://1********:5000
2025-06-15 15:20:01,060 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-15 15:20:03,087 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:20:03] "GET / HTTP/1.1" 200 -
2025-06-15 15:20:03,229 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:20:03] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 15:20:03,305 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:20:03] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 15:20:03,429 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:20:03] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 15:20:03,429 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:20:03] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 15:20:03,431 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:20:03] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 15:20:03,826 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:20:03] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 15:20:08,800 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:20:08] "GET /resources/hardware HTTP/1.1" 200 -
2025-06-15 15:20:09,104 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:20:09] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 15:20:09,147 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:20:09] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 15:20:09,147 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:20:09] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 15:20:09,150 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:20:09] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 15:20:09,151 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:20:09] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 15:20:09,457 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:20:09] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 15:20:13,459 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:20:13] "GET /resources/specs HTTP/1.1" 200 -
2025-06-15 15:20:13,762 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:20:13] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 15:20:13,796 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:20:13] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 15:20:13,797 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:20:13] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 15:20:13,799 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:20:13] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 15:20:13,800 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:20:13] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 15:20:14,122 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:20:14] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 15:20:14,124 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:20:14] "[33mGET /api/user-filter-presets?page_type=et_ft_test_spec HTTP/1.1[0m" 404 -
2025-06-15 15:20:14,134 - app - INFO - 成功连接到MySQL数据库: aps
2025-06-15 15:20:14,159 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:20:14] "GET /api/resources/data/et_ft_test_spec?page=1&per_page=50 HTTP/1.1" 200 -
2025-06-15 15:20:15,590 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:20:15] "GET /resources/hardware HTTP/1.1" 200 -
2025-06-15 15:20:15,902 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:20:15] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 15:20:15,948 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:20:15] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 15:20:15,949 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:20:15] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 15:20:15,952 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:20:15] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 15:20:15,958 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:20:15] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 15:20:16,266 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:20:16] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 15:20:43,127 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:20:43] "GET /resources/hardware HTTP/1.1" 200 -
2025-06-15 15:20:43,156 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:20:43] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 15:20:43,386 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:20:43] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 15:20:43,465 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:20:43] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 15:20:43,465 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:20:43] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 15:20:43,466 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:20:43] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 15:20:43,789 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:20:43] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 15:21:39,163 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:21:39] "GET /resources/hardware HTTP/1.1" 200 -
2025-06-15 15:21:39,464 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:21:39] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 15:21:39,496 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:21:39] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 15:21:39,497 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:21:39] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 15:21:39,497 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:21:39] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 15:21:39,498 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:21:39] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 15:21:39,821 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:21:39] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 15:21:47,060 - APS-System - INFO - 应用路径: D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12
2025-06-15 15:21:47,060 - APS-System - INFO - 实例路径: D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\instance
2025-06-15 15:21:47,061 - APS-System - INFO - 检查 MySQL 数据库配置...
2025-06-15 15:21:47,111 - APS-System - INFO - MySQL 数据库 'aps' 已存在
2025-06-15 15:21:47,112 - APS-System - INFO - MySQL 数据库 'aps_system' 已存在
2025-06-15 15:21:47,112 - APS-System - INFO - MySQL 数据库检查成功
2025-06-15 15:21:47,112 - APS-System - INFO - 正在修复数据库权限...
2025-06-15 15:21:47,113 - APS-System - INFO - 发现SQLite文件，但系统已迁移到MySQL，跳过: instance\aps.db
2025-06-15 15:21:47,113 - APS-System - INFO - 数据库文件不存在，跳过: instance\aps_system.db
2025-06-15 15:21:47,113 - APS-System - INFO - 已修复日志目录权限: logs
2025-06-15 15:21:47,114 - APS-System - INFO - 数据库权限修复成功
2025-06-15 15:21:48,972 - app - INFO - 应用启动
2025-06-15 15:21:48,973 - app.utils.scheduler - INFO - 已重新加载 0 个邮箱配置（应用实例未设置）
2025-06-15 15:21:48,982 - app.utils.scheduler - INFO - 邮件附件定时调度器已启动
2025-06-15 15:21:49,008 - app - INFO - 邮件附件定时任务调度器已启动
2025-06-15 15:21:49,015 - app.utils.scheduler - INFO - 全局定时任务已禁用，邮件调度器跟随禁用
2025-06-15 15:21:49,017 - app.utils.scheduler - INFO - 检测到全局定时任务已禁用，停止邮件调度器
2025-06-15 15:21:49,152 - app.services.scheduler_service - INFO - APScheduler调度器配置完成: timezone=Asia/Shanghai, max_workers=10
2025-06-15 15:21:49,159 - apscheduler.scheduler - INFO - Scheduler started
2025-06-15 15:21:49,164 - app.services.scheduler_service - INFO - 从数据库加载了 0 个任务
2025-06-15 15:21:49,165 - app.services.scheduler_service - INFO - APScheduler调度器启动成功
2025-06-15 15:21:49,165 - app - INFO - ✅ APScheduler统一调度器启动成功
2025-06-15 15:21:49,166 - APS-System - INFO - 应用创建成功
2025-06-15 15:21:49,167 - APS-System - INFO - ==================================================
2025-06-15 15:21:49,167 - APS-System - INFO - 🚀 APS车规芯片终测智能调度平台 v2.1
2025-06-15 15:21:49,167 - APS-System - INFO - 📊 100% MySQL数据库架构
2025-06-15 15:21:49,167 - APS-System - INFO - 🌐 访问地址: http://1********:5000
2025-06-15 15:21:49,167 - APS-System - INFO - ==================================================
2025-06-15 15:21:49,167 - APS-System - INFO - 运行模式: 开发环境
2025-06-15 15:21:49,179 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://1********:5000
2025-06-15 15:21:49,179 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-15 15:21:51,215 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:21:51] "GET / HTTP/1.1" 200 -
2025-06-15 15:21:51,336 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:21:51] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 15:21:51,441 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:21:51] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 15:21:51,552 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:21:51] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 15:21:51,554 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:21:51] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 15:21:51,555 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:21:51] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 15:21:51,923 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:21:51] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 15:21:53,418 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:21:53] "GET /resources/hardware HTTP/1.1" 200 -
2025-06-15 15:21:53,721 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:21:53] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 15:21:53,769 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:21:53] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 15:21:53,773 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:21:53] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 15:21:53,777 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:21:53] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 15:21:53,780 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:21:53] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 15:21:54,092 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:21:54] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 15:21:55,112 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:21:55] "[33mGET /api/user-filter-presets?page_type=hardware_resources HTTP/1.1[0m" 404 -
2025-06-15 15:21:55,138 - app - INFO - 成功连接到MySQL数据库: aps
2025-06-15 15:21:55,145 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:21:55] "GET /api/resources/data/eqp_status?page=1&per_page=50 HTTP/1.1" 200 -
2025-06-15 15:22:36,231 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:22:36] "GET /production/semi-auto HTTP/1.1" 200 -
2025-06-15 15:22:36,540 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:22:36] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 15:22:36,576 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:22:36] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 15:22:36,578 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:22:36] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 15:22:36,579 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:22:36] "[36mGET /static/css/core/main.css HTTP/1.1[0m" 304 -
2025-06-15 15:22:36,580 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:22:36] "[36mGET /static/css/daygrid/main.css HTTP/1.1[0m" 304 -
2025-06-15 15:22:36,580 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:22:36] "[36mGET /static/css/timegrid/main.css HTTP/1.1[0m" 304 -
2025-06-15 15:22:36,851 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:22:36] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 15:22:36,896 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:22:36] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 15:22:36,897 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:22:36] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 15:22:36,903 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:22:36] "[36mGET /static/js/xlsx.full.min.js HTTP/1.1[0m" 304 -
2025-06-15 15:22:37,284 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:22:37] "GET /api/production/get-import-path HTTP/1.1" 200 -
2025-06-15 15:22:37,292 - app - INFO - 成功连接到MySQL数据库: aps
2025-06-15 15:22:37,311 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:22:37] "GET /api/production/imported-files HTTP/1.1" 200 -
2025-06-15 15:22:43,659 - app - INFO - 成功连接到MySQL数据库: aps
2025-06-15 15:22:43,666 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:22:43] "GET /api/production/file-data/et_wait_lot.xlsx?page=1 HTTP/1.1" 200 -
2025-06-15 15:22:48,207 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:22:48] "GET /production/algorithm HTTP/1.1" 200 -
2025-06-15 15:22:48,269 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:22:48] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 15:22:48,452 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:22:48] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 15:22:48,559 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:22:48] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 15:22:48,567 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:22:48] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 15:22:48,574 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:22:48] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 15:22:48,579 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:22:48] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 15:22:48,775 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:22:48] "[36mGET /static/js/xlsx.full.min.js HTTP/1.1[0m" 304 -
2025-06-15 15:22:49,128 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:22:49] "[33mGET /api/production/history-times HTTP/1.1[0m" 404 -
2025-06-15 15:22:51,444 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:22:51] "GET /production/semi-auto HTTP/1.1" 200 -
2025-06-15 15:22:51,743 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:22:51] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 15:22:51,853 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:22:51] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 15:22:51,854 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:22:51] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 15:22:51,858 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:22:51] "[36mGET /static/css/core/main.css HTTP/1.1[0m" 304 -
2025-06-15 15:22:51,862 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:22:51] "[36mGET /static/css/daygrid/main.css HTTP/1.1[0m" 304 -
2025-06-15 15:22:51,863 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:22:51] "[36mGET /static/css/timegrid/main.css HTTP/1.1[0m" 304 -
2025-06-15 15:22:52,052 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:22:52] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 15:22:52,173 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:22:52] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 15:22:52,174 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:22:52] "[36mGET /static/js/xlsx.full.min.js HTTP/1.1[0m" 304 -
2025-06-15 15:22:52,539 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:22:52] "GET /api/production/get-import-path HTTP/1.1" 200 -
2025-06-15 15:22:52,545 - app - INFO - 成功连接到MySQL数据库: aps
2025-06-15 15:22:52,569 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:22:52] "GET /api/production/imported-files HTTP/1.1" 200 -
2025-06-15 15:23:08,329 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:23:08] "GET /production/auto HTTP/1.1" 200 -
2025-06-15 15:23:08,628 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:23:08] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 15:23:08,677 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:23:08] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 15:23:08,677 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:23:08] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 15:23:08,679 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:23:08] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 15:23:08,679 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:23:08] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 15:23:09,022 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:23:09] "GET /api/production/schedule-history HTTP/1.1" 200 -
2025-06-15 15:23:20,051 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:23:20] "GET /production/semi-auto HTTP/1.1" 200 -
2025-06-15 15:23:20,351 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:23:20] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 15:23:20,397 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:23:20] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 15:23:20,400 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:23:20] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 15:23:20,408 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:23:20] "[36mGET /static/css/core/main.css HTTP/1.1[0m" 304 -
2025-06-15 15:23:20,409 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:23:20] "[36mGET /static/css/daygrid/main.css HTTP/1.1[0m" 304 -
2025-06-15 15:23:20,412 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:23:20] "[36mGET /static/css/timegrid/main.css HTTP/1.1[0m" 304 -
2025-06-15 15:23:20,660 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:23:20] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 15:23:20,705 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:23:20] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 15:23:20,711 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:23:20] "[36mGET /static/js/xlsx.full.min.js HTTP/1.1[0m" 304 -
2025-06-15 15:23:21,074 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:23:21] "GET /api/production/get-import-path HTTP/1.1" 200 -
2025-06-15 15:23:21,077 - app - INFO - 成功连接到MySQL数据库: aps
2025-06-15 15:23:21,095 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:23:21] "GET /api/production/imported-files HTTP/1.1" 200 -
2025-06-15 15:23:22,350 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:23:22] "GET /production/algorithm HTTP/1.1" 200 -
2025-06-15 15:23:22,660 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:23:22] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 15:23:22,707 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:23:22] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 15:23:22,709 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:23:22] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 15:23:22,710 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:23:22] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 15:23:22,711 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:23:22] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 15:23:22,712 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:23:22] "[36mGET /static/js/xlsx.full.min.js HTTP/1.1[0m" 304 -
2025-06-15 15:23:23,072 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:23:23] "[33mGET /api/production/history-times HTTP/1.1[0m" 404 -
2025-06-15 15:23:28,999 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:23:28] "GET /production/priority-matching HTTP/1.1" 200 -
2025-06-15 15:23:29,297 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:23:29] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 15:23:29,344 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:23:29] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 15:23:29,358 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:23:29] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 15:23:29,361 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:23:29] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 15:23:29,365 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:23:29] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 15:23:31,018 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:23:31] "GET /production/priority-matching HTTP/1.1" 200 -
2025-06-15 15:23:31,097 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:23:31] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 15:23:31,281 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:23:31] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 15:23:31,358 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:23:31] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 15:23:31,361 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:23:31] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 15:23:31,362 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:23:31] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 15:24:20,782 - app.api.priority_matching_api - INFO - 开始批次匹配: strategy=intelligent, threshold=0.5
2025-06-15 15:24:20,783 - app.services.priority_matching_service - INFO - 开始批次匹配，策略: intelligent, 置信度阈值: 0.5
2025-06-15 15:24:20,795 - app.services.priority_matching_service - INFO - 获取到 171 个待排产批次
2025-06-15 15:24:20,801 - app.services.priority_matching_service - INFO - 获取到 21 个优先级配置
2025-06-15 15:24:21,396 - app.services.priority_matching_service - INFO - 匹配完成: 总批次 171, 成功匹配 171, 匹配率 100.0%
2025-06-15 15:24:21,550 - app.api.priority_matching_api - INFO - 批次匹配完成: 总数=171, 匹配率=100.0%
2025-06-15 15:24:21,556 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:24:21] "POST /api/priority/match-batches HTTP/1.1" 200 -
2025-06-15 15:25:01,497 - APS-System - INFO - 应用路径: D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12
2025-06-15 15:25:01,498 - APS-System - INFO - 实例路径: D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\instance
2025-06-15 15:25:01,498 - APS-System - INFO - 检查 MySQL 数据库配置...
2025-06-15 15:25:01,547 - APS-System - INFO - MySQL 数据库 'aps' 已存在
2025-06-15 15:25:01,548 - APS-System - INFO - MySQL 数据库 'aps_system' 已存在
2025-06-15 15:25:01,549 - APS-System - INFO - MySQL 数据库检查成功
2025-06-15 15:25:01,549 - APS-System - INFO - 正在修复数据库权限...
2025-06-15 15:25:01,549 - APS-System - INFO - 发现SQLite文件，但系统已迁移到MySQL，跳过: instance\aps.db
2025-06-15 15:25:01,549 - APS-System - INFO - 数据库文件不存在，跳过: instance\aps_system.db
2025-06-15 15:25:01,550 - APS-System - INFO - 已修复日志目录权限: logs
2025-06-15 15:25:01,551 - APS-System - INFO - 数据库权限修复成功
2025-06-15 15:25:03,253 - app - INFO - 应用启动
2025-06-15 15:25:03,255 - app.utils.scheduler - INFO - 已重新加载 0 个邮箱配置（应用实例未设置）
2025-06-15 15:25:03,273 - app.utils.scheduler - INFO - 邮件附件定时调度器已启动
2025-06-15 15:25:03,295 - app - INFO - 邮件附件定时任务调度器已启动
2025-06-15 15:25:03,330 - app.utils.scheduler - INFO - 全局定时任务已禁用，邮件调度器跟随禁用
2025-06-15 15:25:03,332 - app.utils.scheduler - INFO - 检测到全局定时任务已禁用，停止邮件调度器
2025-06-15 15:25:03,461 - app.services.scheduler_service - INFO - APScheduler调度器配置完成: timezone=Asia/Shanghai, max_workers=10
2025-06-15 15:25:03,480 - apscheduler.scheduler - INFO - Scheduler started
2025-06-15 15:25:03,485 - app.services.scheduler_service - INFO - 从数据库加载了 0 个任务
2025-06-15 15:25:03,487 - app.services.scheduler_service - INFO - APScheduler调度器启动成功
2025-06-15 15:25:03,488 - app - INFO - ✅ APScheduler统一调度器启动成功
2025-06-15 15:25:03,489 - APS-System - INFO - 应用创建成功
2025-06-15 15:25:03,489 - APS-System - INFO - ==================================================
2025-06-15 15:25:03,489 - APS-System - INFO - 🚀 APS车规芯片终测智能调度平台 v2.1
2025-06-15 15:25:03,490 - APS-System - INFO - 📊 100% MySQL数据库架构
2025-06-15 15:25:03,490 - APS-System - INFO - 🌐 访问地址: http://1********:5000
2025-06-15 15:25:03,490 - APS-System - INFO - ==================================================
2025-06-15 15:25:03,490 - APS-System - INFO - 运行模式: 开发环境
2025-06-15 15:25:03,506 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://1********:5000
2025-06-15 15:25:03,506 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-15 15:25:05,612 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:25:05] "GET / HTTP/1.1" 200 -
2025-06-15 15:25:06,091 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:25:06] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 15:25:06,414 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:25:06] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 15:25:06,728 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:25:06] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 15:25:06,738 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:25:06] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 15:25:06,747 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:25:06] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 15:25:07,160 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:25:07] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 15:25:08,811 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:25:08] "GET /resources/hardware HTTP/1.1" 200 -
2025-06-15 15:25:09,105 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:25:09] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 15:25:09,155 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:25:09] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 15:25:09,158 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:25:09] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 15:25:09,160 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:25:09] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 15:25:09,161 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:25:09] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 15:25:09,494 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:25:09] "[33mGET /api/user-filter-presets?page_type=hardware_resources HTTP/1.1[0m" 404 -
2025-06-15 15:25:09,495 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:25:09] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 15:25:09,502 - app - INFO - 成功连接到MySQL数据库: aps
2025-06-15 15:25:09,522 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:25:09] "GET /api/resources/data/eqp_status?page=1&per_page=50 HTTP/1.1" 200 -
2025-06-15 15:25:11,016 - app - INFO - 成功连接到MySQL数据库: aps
2025-06-15 15:25:11,021 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:25:11] "GET /api/resources/data/eqp_status?page=1&per_page=50 HTTP/1.1" 200 -
2025-06-15 15:25:21,466 - app - INFO - 成功连接到MySQL数据库: aps
2025-06-15 15:25:21,472 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:25:21] "GET /api/resources/data/eqp_status?page=1&per_page=50&advanced_filters=[{"field":"HANDLER_TYPE","operator":"contains","value":"平移"}] HTTP/1.1" 200 -
2025-06-15 15:25:23,788 - app - INFO - 成功连接到MySQL数据库: aps
2025-06-15 15:25:23,797 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:25:23] "GET /api/resources/data/eqp_status?advanced_filters=[{"field":"HANDLER_TYPE","operator":"contains","value":"平移"}]&export=true HTTP/1.1" 200 -
2025-06-15 15:25:42,320 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:25:42] "GET /production/semi-auto HTTP/1.1" 200 -
2025-06-15 15:25:42,618 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:25:42] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 15:25:42,682 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:25:42] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 15:25:42,688 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:25:42] "[36mGET /static/css/core/main.css HTTP/1.1[0m" 304 -
2025-06-15 15:25:42,686 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:25:42] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 15:25:42,696 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:25:42] "[36mGET /static/css/daygrid/main.css HTTP/1.1[0m" 304 -
2025-06-15 15:25:42,714 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:25:42] "[36mGET /static/css/timegrid/main.css HTTP/1.1[0m" 304 -
2025-06-15 15:25:42,927 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:25:42] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 15:25:43,006 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:25:43] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 15:25:43,012 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:25:43] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 15:25:43,037 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:25:43] "[36mGET /static/js/xlsx.full.min.js HTTP/1.1[0m" 304 -
2025-06-15 15:25:43,431 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:25:43] "GET /api/production/get-import-path HTTP/1.1" 200 -
2025-06-15 15:25:43,440 - app - INFO - 成功连接到MySQL数据库: aps
2025-06-15 15:25:43,465 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:25:43] "GET /api/production/imported-files HTTP/1.1" 200 -
2025-06-15 15:25:52,430 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:25:52] "GET /production/algorithm HTTP/1.1" 200 -
2025-06-15 15:25:52,723 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:25:52] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 15:25:52,771 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:25:52] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 15:25:52,774 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:25:52] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 15:25:52,779 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:25:52] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 15:25:52,779 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:25:52] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 15:25:52,783 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:25:52] "[36mGET /static/js/xlsx.full.min.js HTTP/1.1[0m" 304 -
2025-06-15 15:25:53,109 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:25:53] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 15:25:53,170 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:25:53] "[33mGET /api/production/history-times HTTP/1.1[0m" 404 -
2025-06-15 15:26:01,362 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:26:01] "GET /production/semi-auto HTTP/1.1" 200 -
2025-06-15 15:26:01,667 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:26:01] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 15:26:01,701 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:26:01] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 15:26:01,703 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:26:01] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 15:26:01,706 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:26:01] "[36mGET /static/css/timegrid/main.css HTTP/1.1[0m" 304 -
2025-06-15 15:26:01,707 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:26:01] "[36mGET /static/css/daygrid/main.css HTTP/1.1[0m" 304 -
2025-06-15 15:26:01,709 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:26:01] "[36mGET /static/css/core/main.css HTTP/1.1[0m" 304 -
2025-06-15 15:26:01,974 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:26:01] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 15:26:02,025 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:26:02] "[36mGET /static/js/xlsx.full.min.js HTTP/1.1[0m" 304 -
2025-06-15 15:26:02,026 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:26:02] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 15:26:02,027 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:26:02] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 15:26:02,391 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:26:02] "GET /api/production/get-import-path HTTP/1.1" 200 -
2025-06-15 15:26:02,398 - app - INFO - 成功连接到MySQL数据库: aps
2025-06-15 15:26:02,420 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:26:02] "GET /api/production/imported-files HTTP/1.1" 200 -
2025-06-15 15:26:06,148 - app - INFO - 成功连接到MySQL数据库: aps
2025-06-15 15:26:06,156 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:26:06] "GET /api/production/file-data/et_wait_lot.xlsx?page=1 HTTP/1.1" 200 -
2025-06-15 15:26:14,919 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:26:14] "GET /production/algorithm HTTP/1.1" 200 -
2025-06-15 15:26:14,996 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:26:14] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 15:26:15,184 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:26:15] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 15:26:15,263 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:26:15] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 15:26:15,275 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:26:15] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 15:26:15,280 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:26:15] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 15:26:15,288 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:26:15] "[36mGET /static/js/xlsx.full.min.js HTTP/1.1[0m" 304 -
2025-06-15 15:26:15,597 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:26:15] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 15:26:15,659 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:26:15] "[33mGET /api/production/history-times HTTP/1.1[0m" 404 -
2025-06-15 15:26:18,830 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:26:18] "GET /production/semi-auto HTTP/1.1" 200 -
2025-06-15 15:26:18,888 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:26:18] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 15:26:19,074 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:26:19] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 15:26:19,167 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:26:19] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 15:26:19,168 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:26:19] "[36mGET /static/css/daygrid/main.css HTTP/1.1[0m" 304 -
2025-06-15 15:26:19,169 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:26:19] "[36mGET /static/css/core/main.css HTTP/1.1[0m" 304 -
2025-06-15 15:26:19,170 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:26:19] "[36mGET /static/css/timegrid/main.css HTTP/1.1[0m" 304 -
2025-06-15 15:26:19,196 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:26:19] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 15:26:19,379 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:26:19] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 15:26:19,477 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:26:19] "[36mGET /static/js/xlsx.full.min.js HTTP/1.1[0m" 304 -
2025-06-15 15:26:19,477 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:26:19] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 15:26:19,837 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:26:19] "GET /api/production/get-import-path HTTP/1.1" 200 -
2025-06-15 15:26:19,838 - app - INFO - 成功连接到MySQL数据库: aps
2025-06-15 15:26:19,869 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:26:19] "GET /api/production/imported-files HTTP/1.1" 200 -
2025-06-15 15:26:22,560 - app - INFO - 成功连接到MySQL数据库: aps
2025-06-15 15:26:22,571 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:26:22] "GET /api/production/file-data/ct.xlsx?page=1 HTTP/1.1" 200 -
2025-06-15 15:26:29,187 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:26:29] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 15:26:29,187 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:26:29] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 15:26:29,188 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:26:29] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 15:26:29,188 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:26:29] "[36mGET /static/css/core/main.css HTTP/1.1[0m" 304 -
2025-06-15 15:26:29,189 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:26:29] "[36mGET /static/css/daygrid/main.css HTTP/1.1[0m" 304 -
2025-06-15 15:26:29,194 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:26:29] "[36mGET /static/css/timegrid/main.css HTTP/1.1[0m" 304 -
2025-06-15 15:26:29,491 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:26:29] "[33mGET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1[0m" 404 -
2025-06-15 15:26:29,859 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:26:29] "[33mGET /static/css/base/bootstrap.min.css.map HTTP/1.1[0m" 404 -
2025-06-15 15:26:57,072 - app - INFO - 成功连接到MySQL数据库: aps
2025-06-15 15:26:57,089 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:26:57] "GET /api/production/imported-files HTTP/1.1" 200 -
2025-06-15 15:26:57,375 - app - INFO - 成功连接到MySQL数据库: aps
2025-06-15 15:26:57,384 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:26:57] "GET /api/production/file-data/ct.xlsx?page=1 HTTP/1.1" 200 -
2025-06-15 15:26:58,687 - app - INFO - 成功连接到MySQL数据库: aps
2025-06-15 15:26:58,700 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:26:58] "GET /api/production/imported-files HTTP/1.1" 200 -
2025-06-15 15:27:03,217 - app - INFO - 成功连接到MySQL数据库: aps
2025-06-15 15:27:03,222 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:27:03] "GET /api/production/file-data/et_wait_lot.xlsx?page=1 HTTP/1.1" 200 -
2025-06-15 15:29:01,218 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:29:01] "GET /production/semi-auto HTTP/1.1" 200 -
2025-06-15 15:29:01,261 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:29:01] "GET /static/css/base/bootstrap.min.css HTTP/1.1" 200 -
2025-06-15 15:29:01,461 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:29:01] "GET /static/css/all-5.15.4.min.css HTTP/1.1" 200 -
2025-06-15 15:29:01,576 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:29:01] "GET /static/css/core/main.css HTTP/1.1" 200 -
2025-06-15 15:29:01,577 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:29:01] "GET /static/css/daygrid/main.css HTTP/1.1" 200 -
2025-06-15 15:29:01,578 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:29:01] "GET /static/css/compatibility.css HTTP/1.1" 200 -
2025-06-15 15:29:01,579 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:29:01] "GET /static/css/timegrid/main.css HTTP/1.1" 200 -
2025-06-15 15:29:01,583 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:29:01] "GET /static/webfonts/fa-solid-900.woff2 HTTP/1.1" 200 -
2025-06-15 15:29:01,783 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:29:01] "GET /static/js/base/bootstrap.bundle.min.js HTTP/1.1" 200 -
2025-06-15 15:29:01,895 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:29:01] "[33mGET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1[0m" 404 -
2025-06-15 15:29:01,895 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:29:01] "GET /static/js/menu-optimizer.js HTTP/1.1" 200 -
2025-06-15 15:29:01,897 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:29:01] "GET /static/js/xlsx.full.min.js HTTP/1.1" 200 -
2025-06-15 15:29:01,905 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:29:01] "[33mGET /static/css/base/bootstrap.min.css.map HTTP/1.1[0m" 404 -
2025-06-15 15:29:02,365 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:29:02] "GET /api/production/get-import-path HTTP/1.1" 200 -
2025-06-15 15:29:02,366 - app - INFO - 成功连接到MySQL数据库: aps
2025-06-15 15:29:02,393 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:29:02] "GET /api/production/imported-files HTTP/1.1" 200 -
2025-06-15 15:29:05,084 - app - INFO - 成功连接到MySQL数据库: aps
2025-06-15 15:29:05,098 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:29:05] "GET /api/production/file-data/ct.xlsx?page=1 HTTP/1.1" 200 -
2025-06-15 15:29:10,678 - app - INFO - 成功连接到MySQL数据库: aps
2025-06-15 15:29:10,698 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:29:10] "GET /api/production/file-data/wip_lot.xlsx?page=1 HTTP/1.1" 200 -
2025-06-15 15:29:12,762 - app - INFO - 成功连接到MySQL数据库: aps
2025-06-15 15:29:12,769 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:29:12] "GET /api/production/file-data/et_wait_lot.xlsx?page=1 HTTP/1.1" 200 -
2025-06-15 15:36:53,181 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:36:53] "GET /production/semi-auto HTTP/1.1" 200 -
2025-06-15 15:36:53,212 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:36:53] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 15:36:53,439 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:36:53] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 15:36:53,516 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:36:53] "[36mGET /static/css/core/main.css HTTP/1.1[0m" 304 -
2025-06-15 15:36:53,518 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:36:53] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 15:36:53,519 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:36:53] "[36mGET /static/css/daygrid/main.css HTTP/1.1[0m" 304 -
2025-06-15 15:36:53,520 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:36:53] "[36mGET /static/css/timegrid/main.css HTTP/1.1[0m" 304 -
2025-06-15 15:36:53,521 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:36:53] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 15:36:53,750 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:36:53] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 15:36:53,827 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:36:53] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 15:36:53,843 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:36:53] "[36mGET /static/js/xlsx.full.min.js HTTP/1.1[0m" 304 -
2025-06-15 15:36:54,215 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:36:54] "GET /api/production/get-import-path HTTP/1.1" 200 -
2025-06-15 15:36:54,216 - app - INFO - 成功连接到MySQL数据库: aps
2025-06-15 15:36:54,232 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:36:54] "GET /api/production/imported-files HTTP/1.1" 200 -
2025-06-15 15:36:55,953 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:36:55] "GET /production/algorithm HTTP/1.1" 200 -
2025-06-15 15:36:56,259 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:36:56] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 15:36:56,290 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:36:56] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 15:36:56,291 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:36:56] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 15:36:56,291 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:36:56] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 15:36:56,292 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:36:56] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 15:36:56,295 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:36:56] "[36mGET /static/js/xlsx.full.min.js HTTP/1.1[0m" 304 -
2025-06-15 15:36:56,612 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:36:56] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 15:36:56,658 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:36:56] "[33mGET /api/production/history-times HTTP/1.1[0m" 404 -
2025-06-15 15:36:59,601 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:36:59] "GET /production/semi-auto HTTP/1.1" 200 -
2025-06-15 15:36:59,902 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:36:59] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 15:36:59,949 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:36:59] "[36mGET /static/css/timegrid/main.css HTTP/1.1[0m" 304 -
2025-06-15 15:36:59,949 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:36:59] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 15:36:59,950 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:36:59] "[36mGET /static/css/daygrid/main.css HTTP/1.1[0m" 304 -
2025-06-15 15:36:59,950 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:36:59] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 15:36:59,950 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:36:59] "[36mGET /static/css/core/main.css HTTP/1.1[0m" 304 -
2025-06-15 15:37:00,207 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:37:00] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 15:37:00,255 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:37:00] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 15:37:00,255 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:37:00] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 15:37:00,261 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:37:00] "[36mGET /static/js/xlsx.full.min.js HTTP/1.1[0m" 304 -
2025-06-15 15:37:00,604 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:37:00] "GET /api/production/get-import-path HTTP/1.1" 200 -
2025-06-15 15:37:00,605 - app - INFO - 成功连接到MySQL数据库: aps
2025-06-15 15:37:00,623 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:37:00] "GET /api/production/imported-files HTTP/1.1" 200 -
2025-06-15 15:37:12,653 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:37:12] "GET /production/priority-matching HTTP/1.1" 200 -
2025-06-15 15:37:12,962 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:37:12] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 15:37:12,995 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:37:12] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 15:37:12,996 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:37:12] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 15:37:12,998 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:37:12] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 15:37:13,001 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:37:13] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 15:37:13,321 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:37:13] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 15:37:17,650 - app.api.priority_matching_api - INFO - 开始批次匹配: strategy=intelligent, threshold=0.5
2025-06-15 15:37:17,652 - app.services.priority_matching_service - INFO - 开始批次匹配，策略: intelligent, 置信度阈值: 0.5
2025-06-15 15:37:17,659 - app.services.priority_matching_service - INFO - 获取到 171 个待排产批次
2025-06-15 15:37:17,663 - app.services.priority_matching_service - INFO - 获取到 21 个优先级配置
2025-06-15 15:37:17,906 - app.services.priority_matching_service - INFO - 匹配完成: 总批次 171, 成功匹配 171, 匹配率 100.0%
2025-06-15 15:37:18,015 - app.api.priority_matching_api - INFO - 批次匹配完成: 总数=171, 匹配率=100.0%
2025-06-15 15:37:18,018 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:37:18] "POST /api/priority/match-batches HTTP/1.1" 200 -
2025-06-15 15:38:17,521 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:38:17] "GET /production/semi-auto HTTP/1.1" 200 -
2025-06-15 15:38:17,597 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:38:17] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 15:38:17,766 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:38:17] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 15:38:17,876 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:38:17] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 15:38:17,877 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:38:17] "[36mGET /static/css/daygrid/main.css HTTP/1.1[0m" 304 -
2025-06-15 15:38:17,878 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:38:17] "[36mGET /static/css/core/main.css HTTP/1.1[0m" 304 -
2025-06-15 15:38:17,878 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:38:17] "[36mGET /static/css/timegrid/main.css HTTP/1.1[0m" 304 -
2025-06-15 15:38:17,905 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:38:17] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 15:38:18,074 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:38:18] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 15:38:18,198 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:38:18] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 15:38:18,200 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:38:18] "[36mGET /static/js/xlsx.full.min.js HTTP/1.1[0m" 304 -
2025-06-15 15:38:18,559 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:38:18] "GET /api/production/get-import-path HTTP/1.1" 200 -
2025-06-15 15:38:18,562 - app - INFO - 成功连接到MySQL数据库: aps
2025-06-15 15:38:18,589 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:38:18] "GET /api/production/imported-files HTTP/1.1" 200 -
2025-06-15 15:48:34,502 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:48:34] "GET /production/semi-auto HTTP/1.1" 200 -
2025-06-15 15:48:34,576 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:48:34] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 15:48:34,745 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:48:34] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 15:48:34,838 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:48:34] "[36mGET /static/css/core/main.css HTTP/1.1[0m" 304 -
2025-06-15 15:48:34,839 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:48:34] "[36mGET /static/css/daygrid/main.css HTTP/1.1[0m" 304 -
2025-06-15 15:48:34,839 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:48:34] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 15:48:34,840 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:48:34] "[36mGET /static/css/timegrid/main.css HTTP/1.1[0m" 304 -
2025-06-15 15:48:34,882 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:48:34] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 15:48:35,052 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:48:35] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 15:48:35,146 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:48:35] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 15:48:35,161 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:48:35] "[36mGET /static/js/xlsx.full.min.js HTTP/1.1[0m" 304 -
2025-06-15 15:48:35,538 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:48:35] "GET /api/production/get-import-path HTTP/1.1" 200 -
2025-06-15 15:48:35,541 - app - INFO - 成功连接到MySQL数据库: aps
2025-06-15 15:48:35,556 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:48:35] "GET /api/production/imported-files HTTP/1.1" 200 -
2025-06-15 15:48:37,601 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:48:37] "GET /production/auto HTTP/1.1" 200 -
2025-06-15 15:48:37,908 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:48:37] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 15:48:37,945 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:48:37] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 15:48:37,947 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:48:37] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 15:48:37,948 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:48:37] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 15:48:37,950 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:48:37] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 15:48:38,259 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:48:38] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 15:48:38,280 - werkzeug - INFO - 1******** - - [15/Jun/2025 15:48:38] "GET /api/production/schedule-history HTTP/1.1" 200 -
2025-06-15 16:04:21,667 - APS-System - INFO - 应用路径: D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12
2025-06-15 16:04:21,669 - APS-System - INFO - 实例路径: D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\instance
2025-06-15 16:04:21,670 - APS-System - INFO - 检查 MySQL 数据库配置...
2025-06-15 16:04:21,738 - APS-System - INFO - MySQL 数据库 'aps' 已存在
2025-06-15 16:04:21,739 - APS-System - INFO - MySQL 数据库 'aps_system' 已存在
2025-06-15 16:04:21,739 - APS-System - INFO - MySQL 数据库检查成功
2025-06-15 16:04:21,740 - APS-System - INFO - 正在修复数据库权限...
2025-06-15 16:04:21,740 - APS-System - INFO - 发现SQLite文件，但系统已迁移到MySQL，跳过: instance\aps.db
2025-06-15 16:04:21,741 - APS-System - INFO - 数据库文件不存在，跳过: instance\aps_system.db
2025-06-15 16:04:21,741 - APS-System - INFO - 已修复日志目录权限: logs
2025-06-15 16:04:21,741 - APS-System - INFO - 数据库权限修复成功
2025-06-15 16:04:24,200 - app - INFO - 应用启动
2025-06-15 16:04:24,202 - app.utils.scheduler - INFO - 已重新加载 0 个邮箱配置（应用实例未设置）
2025-06-15 16:04:24,208 - app.utils.scheduler - INFO - 邮件附件定时调度器已启动
2025-06-15 16:04:24,252 - app - INFO - 邮件附件定时任务调度器已启动
2025-06-15 16:04:24,263 - app.utils.scheduler - INFO - 全局定时任务已禁用，邮件调度器跟随禁用
2025-06-15 16:04:24,265 - app.utils.scheduler - INFO - 检测到全局定时任务已禁用，停止邮件调度器
2025-06-15 16:04:24,484 - app.services.scheduler_service - INFO - APScheduler调度器配置完成: timezone=Asia/Shanghai, max_workers=10
2025-06-15 16:04:24,505 - apscheduler.scheduler - INFO - Scheduler started
2025-06-15 16:04:24,512 - app.services.scheduler_service - INFO - 从数据库加载了 0 个任务
2025-06-15 16:04:24,513 - app.services.scheduler_service - INFO - APScheduler调度器启动成功
2025-06-15 16:04:24,514 - app - INFO - ✅ APScheduler统一调度器启动成功
2025-06-15 16:04:24,515 - APS-System - INFO - 应用创建成功
2025-06-15 16:04:24,515 - APS-System - INFO - ==================================================
2025-06-15 16:04:24,515 - APS-System - INFO - 🚀 APS车规芯片终测智能调度平台 v2.1
2025-06-15 16:04:24,516 - APS-System - INFO - 📊 100% MySQL数据库架构
2025-06-15 16:04:24,516 - APS-System - INFO - 🌐 访问地址: http://1********:5000
2025-06-15 16:04:24,516 - APS-System - INFO - ==================================================
2025-06-15 16:04:24,516 - APS-System - INFO - 运行模式: 开发环境
2025-06-15 16:04:24,544 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://1********:5000
2025-06-15 16:04:24,545 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-15 16:04:26,674 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:04:26] "GET / HTTP/1.1" 200 -
2025-06-15 16:04:26,860 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:04:26] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 16:04:26,892 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:04:26] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 16:04:27,047 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:04:27] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 16:04:27,048 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:04:27] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 16:04:27,050 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:04:27] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 16:04:27,446 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:04:27] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 16:04:28,961 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:04:28] "GET /production/semi-auto HTTP/1.1" 200 -
2025-06-15 16:04:29,254 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:04:29] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 16:04:29,301 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:04:29] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 16:04:29,302 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:04:29] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 16:04:29,304 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:04:29] "[36mGET /static/css/daygrid/main.css HTTP/1.1[0m" 304 -
2025-06-15 16:04:29,304 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:04:29] "[36mGET /static/css/core/main.css HTTP/1.1[0m" 304 -
2025-06-15 16:04:29,308 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:04:29] "[36mGET /static/css/timegrid/main.css HTTP/1.1[0m" 304 -
2025-06-15 16:04:29,562 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:04:29] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 16:04:29,609 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:04:29] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 16:04:29,612 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:04:29] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 16:04:29,616 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:04:29] "[36mGET /static/js/xlsx.full.min.js HTTP/1.1[0m" 304 -
2025-06-15 16:04:30,018 - app - INFO - 成功连接到MySQL数据库: aps
2025-06-15 16:04:30,026 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:04:30] "GET /api/production/get-import-path HTTP/1.1" 200 -
2025-06-15 16:04:30,045 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:04:30] "GET /api/production/imported-files HTTP/1.1" 200 -
2025-06-15 16:04:30,939 - app.api.production_api - INFO - 开始执行自动排产: {'algorithm': 'intelligent', 'optimization_target': 'balanced', 'time_limit': 30, 'population_size': 100, 'auto_mode': False}
2025-06-15 16:04:30,944 - app.api.production_api - INFO - 获取到 171 条待排产批次数据
2025-06-15 16:04:30,949 - app.api.production_api - INFO - 加载了 21 条优先级配置
2025-06-15 16:04:30,981 - app.api.production_api - INFO - 获取到 913 台可用设备
2025-06-15 16:04:31,239 - app.api.production_api - INFO - 排产算法 intelligent 完成，生成 171 条排产记录
2025-06-15 16:04:31,240 - app.api.production_api - INFO - 自动排产完成: 171 条记录，耗时 0.30秒
2025-06-15 16:04:31,243 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:04:31] "POST /api/production/auto-schedule HTTP/1.1" 200 -
2025-06-15 16:05:06,158 - app.api.production_api - INFO - 开始执行自动排产: {'algorithm': 'deadline', 'optimization_target': 'balanced', 'time_limit': 30, 'population_size': 100, 'auto_mode': False}
2025-06-15 16:05:06,161 - app.api.production_api - INFO - 获取到 171 条待排产批次数据
2025-06-15 16:05:06,163 - app.api.production_api - INFO - 加载了 21 条优先级配置
2025-06-15 16:05:06,188 - app.api.production_api - INFO - 获取到 913 台可用设备
2025-06-15 16:05:06,961 - app.api.production_api - INFO - 排产算法 deadline 完成，生成 171 条排产记录
2025-06-15 16:05:06,963 - app.api.production_api - INFO - 自动排产完成: 171 条记录，耗时 0.81秒
2025-06-15 16:05:06,975 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:05:06] "POST /api/production/auto-schedule HTTP/1.1" 200 -
2025-06-15 16:05:51,609 - app - INFO - 成功连接到MySQL数据库: aps
2025-06-15 16:05:51,623 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:05:51] "GET /api/production/file-data/ct.xlsx?page=1 HTTP/1.1" 200 -
2025-06-15 16:09:51,656 - APS-System - INFO - 应用路径: D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12
2025-06-15 16:09:51,657 - APS-System - INFO - 实例路径: D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\instance
2025-06-15 16:09:51,657 - APS-System - INFO - 检查 MySQL 数据库配置...
2025-06-15 16:09:51,715 - APS-System - INFO - MySQL 数据库 'aps' 已存在
2025-06-15 16:09:51,717 - APS-System - INFO - MySQL 数据库 'aps_system' 已存在
2025-06-15 16:09:51,718 - APS-System - INFO - MySQL 数据库检查成功
2025-06-15 16:09:51,718 - APS-System - INFO - 正在修复数据库权限...
2025-06-15 16:09:51,718 - APS-System - INFO - 发现SQLite文件，但系统已迁移到MySQL，跳过: instance\aps.db
2025-06-15 16:09:51,718 - APS-System - INFO - 数据库文件不存在，跳过: instance\aps_system.db
2025-06-15 16:09:51,720 - APS-System - INFO - 已修复日志目录权限: logs
2025-06-15 16:09:51,720 - APS-System - INFO - 数据库权限修复成功
2025-06-15 16:09:53,973 - app - INFO - 应用启动
2025-06-15 16:09:53,975 - app.utils.scheduler - INFO - 已重新加载 0 个邮箱配置（应用实例未设置）
2025-06-15 16:09:53,992 - app.utils.scheduler - INFO - 邮件附件定时调度器已启动
2025-06-15 16:09:54,019 - app - INFO - 邮件附件定时任务调度器已启动
2025-06-15 16:09:54,027 - app.utils.scheduler - INFO - 全局定时任务已禁用，邮件调度器跟随禁用
2025-06-15 16:09:54,030 - app.utils.scheduler - INFO - 检测到全局定时任务已禁用，停止邮件调度器
2025-06-15 16:09:54,186 - app.services.scheduler_service - INFO - APScheduler调度器配置完成: timezone=Asia/Shanghai, max_workers=10
2025-06-15 16:09:54,195 - apscheduler.scheduler - INFO - Scheduler started
2025-06-15 16:09:54,201 - app.services.scheduler_service - INFO - 从数据库加载了 0 个任务
2025-06-15 16:09:54,205 - app.services.scheduler_service - INFO - APScheduler调度器启动成功
2025-06-15 16:09:54,205 - app - INFO - ✅ APScheduler统一调度器启动成功
2025-06-15 16:09:54,207 - APS-System - INFO - 应用创建成功
2025-06-15 16:09:54,208 - APS-System - INFO - ==================================================
2025-06-15 16:09:54,208 - APS-System - INFO - 🚀 APS车规芯片终测智能调度平台 v2.1
2025-06-15 16:09:54,208 - APS-System - INFO - 📊 100% MySQL数据库架构
2025-06-15 16:09:54,208 - APS-System - INFO - 🌐 访问地址: http://1********:5000
2025-06-15 16:09:54,209 - APS-System - INFO - ==================================================
2025-06-15 16:09:54,209 - APS-System - INFO - 运行模式: 开发环境
2025-06-15 16:09:54,232 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://1********:5000
2025-06-15 16:09:54,233 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-15 16:09:56,334 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:09:56] "GET / HTTP/1.1" 200 -
2025-06-15 16:09:56,508 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:09:56] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 16:09:56,560 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:09:56] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 16:09:56,687 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:09:56] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 16:09:56,687 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:09:56] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 16:09:56,688 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:09:56] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 16:09:57,082 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:09:57] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 16:09:58,828 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:09:58] "GET /production/semi-auto HTTP/1.1" 200 -
2025-06-15 16:09:59,131 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:09:59] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 16:09:59,194 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:09:59] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 16:09:59,195 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:09:59] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 16:09:59,196 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:09:59] "[36mGET /static/css/core/main.css HTTP/1.1[0m" 304 -
2025-06-15 16:09:59,206 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:09:59] "[36mGET /static/css/daygrid/main.css HTTP/1.1[0m" 304 -
2025-06-15 16:09:59,206 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:09:59] "[36mGET /static/css/timegrid/main.css HTTP/1.1[0m" 304 -
2025-06-15 16:09:59,451 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:09:59] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 16:09:59,515 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:09:59] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 16:09:59,516 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:09:59] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 16:09:59,516 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:09:59] "[36mGET /static/js/xlsx.full.min.js HTTP/1.1[0m" 304 -
2025-06-15 16:09:59,904 - app - INFO - 成功连接到MySQL数据库: aps
2025-06-15 16:09:59,932 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:09:59] "GET /api/production/imported-files HTTP/1.1" 200 -
2025-06-15 16:09:59,946 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:09:59] "GET /api/production/get-import-path HTTP/1.1" 200 -
2025-06-15 16:10:02,942 - app.api.production_api - INFO - 开始执行自动排产: {'algorithm': 'deadline', 'optimization_target': 'balanced', 'time_limit': 30, 'population_size': 100, 'auto_mode': False}
2025-06-15 16:10:02,949 - app.api.production_api - INFO - 获取到 171 条待排产批次数据
2025-06-15 16:10:02,956 - app.api.production_api - INFO - 加载了 21 条优先级配置
2025-06-15 16:10:02,996 - app.api.production_api - INFO - 获取到 913 台可用设备
2025-06-15 16:10:03,733 - app.api.production_api - INFO - 排产算法 deadline 完成，生成 171 条排产记录
2025-06-15 16:10:03,735 - app.api.production_api - INFO - 自动排产完成: 171 条记录，耗时 0.79秒
2025-06-15 16:10:03,740 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:10:03] "POST /api/production/auto-schedule HTTP/1.1" 200 -
2025-06-15 16:13:54,833 - APS-System - INFO - 应用路径: D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12
2025-06-15 16:13:54,833 - APS-System - INFO - 实例路径: D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\instance
2025-06-15 16:13:54,834 - APS-System - INFO - 检查 MySQL 数据库配置...
2025-06-15 16:13:54,880 - APS-System - INFO - MySQL 数据库 'aps' 已存在
2025-06-15 16:13:54,881 - APS-System - INFO - MySQL 数据库 'aps_system' 已存在
2025-06-15 16:13:54,881 - APS-System - INFO - MySQL 数据库检查成功
2025-06-15 16:13:54,881 - APS-System - INFO - 正在修复数据库权限...
2025-06-15 16:13:54,882 - APS-System - INFO - 发现SQLite文件，但系统已迁移到MySQL，跳过: instance\aps.db
2025-06-15 16:13:54,882 - APS-System - INFO - 数据库文件不存在，跳过: instance\aps_system.db
2025-06-15 16:13:54,883 - APS-System - INFO - 已修复日志目录权限: logs
2025-06-15 16:13:54,883 - APS-System - INFO - 数据库权限修复成功
2025-06-15 16:13:56,876 - app - INFO - 应用启动
2025-06-15 16:13:56,876 - app.utils.scheduler - INFO - 已重新加载 0 个邮箱配置（应用实例未设置）
2025-06-15 16:13:56,886 - app.utils.scheduler - INFO - 邮件附件定时调度器已启动
2025-06-15 16:13:56,913 - app - INFO - 邮件附件定时任务调度器已启动
2025-06-15 16:13:56,938 - app.utils.scheduler - INFO - 全局定时任务已禁用，邮件调度器跟随禁用
2025-06-15 16:13:56,940 - app.utils.scheduler - INFO - 检测到全局定时任务已禁用，停止邮件调度器
2025-06-15 16:13:57,075 - app.services.scheduler_service - INFO - APScheduler调度器配置完成: timezone=Asia/Shanghai, max_workers=10
2025-06-15 16:13:57,108 - apscheduler.scheduler - INFO - Scheduler started
2025-06-15 16:13:57,112 - app.services.scheduler_service - INFO - 从数据库加载了 0 个任务
2025-06-15 16:13:57,113 - app.services.scheduler_service - INFO - APScheduler调度器启动成功
2025-06-15 16:13:57,113 - app - INFO - ✅ APScheduler统一调度器启动成功
2025-06-15 16:13:57,115 - APS-System - INFO - 应用创建成功
2025-06-15 16:13:57,115 - APS-System - INFO - ==================================================
2025-06-15 16:13:57,115 - APS-System - INFO - 🚀 APS车规芯片终测智能调度平台 v2.1
2025-06-15 16:13:57,115 - APS-System - INFO - 📊 100% MySQL数据库架构
2025-06-15 16:13:57,115 - APS-System - INFO - 🌐 访问地址: http://1********:5000
2025-06-15 16:13:57,115 - APS-System - INFO - ==================================================
2025-06-15 16:13:57,115 - APS-System - INFO - 运行模式: 开发环境
2025-06-15 16:13:57,135 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://1********:5000
2025-06-15 16:13:57,135 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-15 16:13:59,189 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:13:59] "GET / HTTP/1.1" 200 -
2025-06-15 16:13:59,505 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:13:59] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 16:13:59,541 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:13:59] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 16:13:59,544 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:13:59] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 16:13:59,546 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:13:59] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 16:13:59,664 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:13:59] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 16:14:00,044 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:14:00] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 16:14:01,686 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:14:01] "GET /production/semi-auto HTTP/1.1" 200 -
2025-06-15 16:14:01,986 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:14:01] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 16:14:02,043 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:14:02] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 16:14:02,044 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:14:02] "[36mGET /static/css/core/main.css HTTP/1.1[0m" 304 -
2025-06-15 16:14:02,047 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:14:02] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 16:14:02,051 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:14:02] "[36mGET /static/css/daygrid/main.css HTTP/1.1[0m" 304 -
2025-06-15 16:14:02,062 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:14:02] "[36mGET /static/css/timegrid/main.css HTTP/1.1[0m" 304 -
2025-06-15 16:14:02,311 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:14:02] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 16:14:02,361 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:14:02] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 16:14:02,366 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:14:02] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 16:14:02,369 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:14:02] "[36mGET /static/js/xlsx.full.min.js HTTP/1.1[0m" 304 -
2025-06-15 16:14:02,756 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:14:02] "GET /api/production/get-import-path HTTP/1.1" 200 -
2025-06-15 16:14:02,770 - app - INFO - 成功连接到MySQL数据库: aps
2025-06-15 16:14:02,798 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:14:02] "GET /api/production/imported-files HTTP/1.1" 200 -
2025-06-15 16:14:03,439 - app.api.production_api - INFO - 开始执行自动排产: {'algorithm': 'intelligent', 'optimization_target': 'balanced', 'time_limit': 30, 'population_size': 100, 'auto_mode': False}
2025-06-15 16:14:03,446 - app.api.production_api - INFO - 获取到 171 条待排产批次数据
2025-06-15 16:14:03,453 - app.api.production_api - INFO - 加载了 21 条优先级配置
2025-06-15 16:14:03,496 - app.api.production_api - INFO - 获取到 913 台可用设备
2025-06-15 16:14:04,176 - app.api.production_api - INFO - 排产算法 intelligent 完成，生成 171 条排产记录
2025-06-15 16:14:04,179 - app.api.production_api - INFO - 自动排产完成: 171 条记录，耗时 0.74秒
2025-06-15 16:14:04,186 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:14:04] "POST /api/production/auto-schedule HTTP/1.1" 200 -
2025-06-15 16:14:15,826 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:14:15] "GET /production/auto HTTP/1.1" 200 -
2025-06-15 16:14:15,889 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:14:15] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 16:14:16,062 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:14:16] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 16:14:16,171 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:14:16] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 16:14:16,177 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:14:16] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 16:14:16,175 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:14:16] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 16:14:16,205 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:14:16] "GET /api/production/schedule-history HTTP/1.1" 200 -
2025-06-15 16:14:37,841 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:14:37] "GET /production/semi-auto HTTP/1.1" 200 -
2025-06-15 16:14:37,930 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:14:37] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 16:14:38,096 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:14:38] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 16:14:38,188 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:14:38] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 16:14:38,189 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:14:38] "[36mGET /static/css/core/main.css HTTP/1.1[0m" 304 -
2025-06-15 16:14:38,191 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:14:38] "[36mGET /static/css/daygrid/main.css HTTP/1.1[0m" 304 -
2025-06-15 16:14:38,197 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:14:38] "[36mGET /static/css/timegrid/main.css HTTP/1.1[0m" 304 -
2025-06-15 16:14:38,247 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:14:38] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 16:14:38,415 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:14:38] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 16:14:38,507 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:14:38] "[36mGET /static/js/xlsx.full.min.js HTTP/1.1[0m" 304 -
2025-06-15 16:14:38,864 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:14:38] "GET /api/production/get-import-path HTTP/1.1" 200 -
2025-06-15 16:14:38,870 - app - INFO - 成功连接到MySQL数据库: aps
2025-06-15 16:14:38,884 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:14:38] "GET /api/production/imported-files HTTP/1.1" 200 -
2025-06-15 16:14:39,657 - app.api.production_api - INFO - 开始执行自动排产: {'algorithm': 'intelligent', 'optimization_target': 'balanced', 'time_limit': 30, 'population_size': 100, 'auto_mode': False}
2025-06-15 16:14:39,664 - app.api.production_api - INFO - 获取到 171 条待排产批次数据
2025-06-15 16:14:39,669 - app.api.production_api - INFO - 加载了 21 条优先级配置
2025-06-15 16:14:39,705 - app.api.production_api - INFO - 获取到 913 台可用设备
2025-06-15 16:14:40,324 - app.api.production_api - INFO - 排产算法 intelligent 完成，生成 171 条排产记录
2025-06-15 16:14:40,326 - app.api.production_api - INFO - 自动排产完成: 171 条记录，耗时 0.67秒
2025-06-15 16:14:40,334 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:14:40] "POST /api/production/auto-schedule HTTP/1.1" 200 -
2025-06-15 16:14:44,740 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:14:44] "GET /production/auto HTTP/1.1" 200 -
2025-06-15 16:14:44,825 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:14:44] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 16:14:45,010 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:14:45] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 16:14:45,074 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:14:45] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 16:14:45,076 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:14:45] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 16:14:45,083 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:14:45] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 16:14:45,110 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:14:45] "GET /api/production/schedule-history HTTP/1.1" 200 -
2025-06-15 16:14:48,445 - app.api.production_api - INFO - 开始执行自动排产: {'algorithm': 'intelligent', 'optimization_target': 'balanced', 'time_limit': 30, 'population_size': 100, 'auto_mode': False}
2025-06-15 16:14:48,448 - app.api.production_api - INFO - 获取到 171 条待排产批次数据
2025-06-15 16:14:48,450 - app.api.production_api - INFO - 加载了 21 条优先级配置
2025-06-15 16:14:48,478 - app.api.production_api - INFO - 获取到 913 台可用设备
2025-06-15 16:14:49,120 - app.api.production_api - INFO - 排产算法 intelligent 完成，生成 171 条排产记录
2025-06-15 16:14:49,121 - app.api.production_api - INFO - 自动排产完成: 171 条记录，耗时 0.68秒
2025-06-15 16:14:49,127 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:14:49] "POST /api/production/auto-schedule HTTP/1.1" 200 -
2025-06-15 16:14:58,442 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:14:58] "GET /production/semi-auto HTTP/1.1" 200 -
2025-06-15 16:14:58,754 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:14:58] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 16:14:58,798 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:14:58] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 16:14:58,799 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:14:58] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 16:14:58,801 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:14:58] "[36mGET /static/css/core/main.css HTTP/1.1[0m" 304 -
2025-06-15 16:14:58,810 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:14:58] "[36mGET /static/css/timegrid/main.css HTTP/1.1[0m" 304 -
2025-06-15 16:14:58,811 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:14:58] "[36mGET /static/css/daygrid/main.css HTTP/1.1[0m" 304 -
2025-06-15 16:14:59,072 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:14:59] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 16:14:59,131 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:14:59] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 16:14:59,133 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:14:59] "[36mGET /static/js/xlsx.full.min.js HTTP/1.1[0m" 304 -
2025-06-15 16:14:59,486 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:14:59] "GET /api/production/get-import-path HTTP/1.1" 200 -
2025-06-15 16:14:59,502 - app - INFO - 成功连接到MySQL数据库: aps
2025-06-15 16:14:59,527 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:14:59] "GET /api/production/imported-files HTTP/1.1" 200 -
2025-06-15 16:15:59,647 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:15:59] "GET /production/auto HTTP/1.1" 200 -
2025-06-15 16:15:59,959 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:15:59] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 16:15:59,991 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:15:59] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 16:15:59,999 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:15:59] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 16:16:00,000 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:16:00] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 16:16:00,004 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:16:00] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 16:16:00,331 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:16:00] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 16:16:00,336 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:16:00] "GET /api/production/schedule-history HTTP/1.1" 200 -
2025-06-15 16:16:04,975 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:16:04] "GET /production/semi-auto HTTP/1.1" 200 -
2025-06-15 16:16:05,282 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:16:05] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 16:16:05,318 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:16:05] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 16:16:05,321 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:16:05] "[36mGET /static/css/core/main.css HTTP/1.1[0m" 304 -
2025-06-15 16:16:05,321 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:16:05] "[36mGET /static/css/daygrid/main.css HTTP/1.1[0m" 304 -
2025-06-15 16:16:05,322 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:16:05] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 16:16:05,323 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:16:05] "[36mGET /static/css/timegrid/main.css HTTP/1.1[0m" 304 -
2025-06-15 16:16:05,590 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:16:05] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 16:16:05,652 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:16:05] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 16:16:05,653 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:16:05] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 16:16:05,658 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:16:05] "[36mGET /static/js/xlsx.full.min.js HTTP/1.1[0m" 304 -
2025-06-15 16:16:06,027 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:16:06] "GET /api/production/get-import-path HTTP/1.1" 200 -
2025-06-15 16:16:06,028 - app - INFO - 成功连接到MySQL数据库: aps
2025-06-15 16:16:06,050 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:16:06] "GET /api/production/imported-files HTTP/1.1" 200 -
2025-06-15 16:16:06,770 - app.api.production_api - INFO - 开始执行自动排产: {'algorithm': 'intelligent', 'optimization_target': 'balanced', 'time_limit': 30, 'population_size': 100, 'auto_mode': False}
2025-06-15 16:16:06,778 - app.api.production_api - INFO - 获取到 171 条待排产批次数据
2025-06-15 16:16:06,782 - app.api.production_api - INFO - 加载了 21 条优先级配置
2025-06-15 16:16:06,834 - app.api.production_api - INFO - 获取到 913 台可用设备
2025-06-15 16:16:07,597 - app.api.production_api - INFO - 排产算法 intelligent 完成，生成 171 条排产记录
2025-06-15 16:16:07,599 - app.api.production_api - INFO - 自动排产完成: 171 条记录，耗时 0.83秒
2025-06-15 16:16:07,603 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:16:07] "POST /api/production/auto-schedule HTTP/1.1" 200 -
2025-06-15 16:16:13,399 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:16:13] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 16:16:13,401 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:16:13] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 16:16:13,410 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:16:13] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 16:16:13,412 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:16:13] "[36mGET /static/css/core/main.css HTTP/1.1[0m" 304 -
2025-06-15 16:16:13,417 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:16:13] "[36mGET /static/css/daygrid/main.css HTTP/1.1[0m" 304 -
2025-06-15 16:16:13,423 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:16:13] "[36mGET /static/css/timegrid/main.css HTTP/1.1[0m" 304 -
2025-06-15 16:16:13,719 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:16:13] "[33mGET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1[0m" 404 -
2025-06-15 16:16:14,275 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:16:14] "[33mGET /static/css/base/bootstrap.min.css.map HTTP/1.1[0m" 404 -
2025-06-15 16:16:19,575 - app - INFO - 成功连接到MySQL数据库: aps
2025-06-15 16:16:19,580 - app.api.routes - ERROR - Error inserting record: {'ORDER': 1, 'HANDLER_ID': 'D1', 'LOT_ID': 'YX2500002027', 'LOT_TYPE': '', 'GOOD_QTY': 18462, 'PROD_ID': '', 'DEVICE': 'JWQ7843-50SOTH-D1_TR1', 'CHIP_ID': 'JPQ21D34E_P01R', 'PKG_PN': 'SOT223', 'PO_ID': '', 'STAGE': 'ROOM-TTR-FT', 'WIP_STATE': '', 'PROC_STATE': '', 'HOLD_STATE': '', 'FLOW_ID': '', 'FLOW_VER': '', 'RELEASE_TIME': '', 'FAC_ID': '', 'CREATE_TIME': ''}, Error: not all arguments converted during string formatting
2025-06-15 16:16:19,589 - app.api.routes - ERROR - Error in save_priority_done: not all arguments converted during string formatting
2025-06-15 16:16:19,597 - app.api.routes - ERROR - 详细错误信息: Traceback (most recent call last):
  File "D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\api\routes.py", line 885, in save_priority_done
    raise e
  File "D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\api\routes.py", line 853, in save_priority_done
    cursor.execute("""
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\pymysql\cursors.py", line 151, in execute
    query = self.mogrify(query, args)
            ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\pymysql\cursors.py", line 129, in mogrify
    query = query % self._escape_args(args, conn)
            ~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
TypeError: not all arguments converted during string formatting

2025-06-15 16:16:19,602 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:16:19] "[35m[1mPOST /api/production/save-priority-done HTTP/1.1[0m" 500 -
2025-06-15 16:16:22,070 - app - INFO - 成功连接到MySQL数据库: aps
2025-06-15 16:16:22,071 - app.api.routes - ERROR - Error inserting record: {'ORDER': 1, 'HANDLER_ID': 'D1', 'LOT_ID': 'YX2500002027', 'LOT_TYPE': '', 'GOOD_QTY': 18462, 'PROD_ID': '', 'DEVICE': 'JWQ7843-50SOTH-D1_TR1', 'CHIP_ID': 'JPQ21D34E_P01R', 'PKG_PN': 'SOT223', 'PO_ID': '', 'STAGE': 'ROOM-TTR-FT', 'WIP_STATE': '', 'PROC_STATE': '', 'HOLD_STATE': '', 'FLOW_ID': '', 'FLOW_VER': '', 'RELEASE_TIME': '', 'FAC_ID': '', 'CREATE_TIME': ''}, Error: not all arguments converted during string formatting
2025-06-15 16:16:22,072 - app.api.routes - ERROR - Error in save_priority_done: not all arguments converted during string formatting
2025-06-15 16:16:22,073 - app.api.routes - ERROR - 详细错误信息: Traceback (most recent call last):
  File "D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\api\routes.py", line 885, in save_priority_done
    raise e
  File "D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\api\routes.py", line 853, in save_priority_done
    cursor.execute("""
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\pymysql\cursors.py", line 151, in execute
    query = self.mogrify(query, args)
            ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\pymysql\cursors.py", line 129, in mogrify
    query = query % self._escape_args(args, conn)
            ~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
TypeError: not all arguments converted during string formatting

2025-06-15 16:16:22,076 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:16:22] "[35m[1mPOST /api/production/save-priority-done HTTP/1.1[0m" 500 -
2025-06-15 16:16:22,682 - app - INFO - 成功连接到MySQL数据库: aps
2025-06-15 16:16:22,684 - app.api.routes - ERROR - Error inserting record: {'ORDER': 1, 'HANDLER_ID': 'D1', 'LOT_ID': 'YX2500002027', 'LOT_TYPE': '', 'GOOD_QTY': 18462, 'PROD_ID': '', 'DEVICE': 'JWQ7843-50SOTH-D1_TR1', 'CHIP_ID': 'JPQ21D34E_P01R', 'PKG_PN': 'SOT223', 'PO_ID': '', 'STAGE': 'ROOM-TTR-FT', 'WIP_STATE': '', 'PROC_STATE': '', 'HOLD_STATE': '', 'FLOW_ID': '', 'FLOW_VER': '', 'RELEASE_TIME': '', 'FAC_ID': '', 'CREATE_TIME': ''}, Error: not all arguments converted during string formatting
2025-06-15 16:16:22,685 - app.api.routes - ERROR - Error in save_priority_done: not all arguments converted during string formatting
2025-06-15 16:16:22,687 - app.api.routes - ERROR - 详细错误信息: Traceback (most recent call last):
  File "D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\api\routes.py", line 885, in save_priority_done
    raise e
  File "D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\api\routes.py", line 853, in save_priority_done
    cursor.execute("""
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\pymysql\cursors.py", line 151, in execute
    query = self.mogrify(query, args)
            ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\pymysql\cursors.py", line 129, in mogrify
    query = query % self._escape_args(args, conn)
            ~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
TypeError: not all arguments converted during string formatting

2025-06-15 16:16:22,689 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:16:22] "[35m[1mPOST /api/production/save-priority-done HTTP/1.1[0m" 500 -
2025-06-15 16:16:23,533 - app - INFO - 成功连接到MySQL数据库: aps
2025-06-15 16:16:23,534 - app.api.routes - ERROR - Error inserting record: {'ORDER': 1, 'HANDLER_ID': 'D1', 'LOT_ID': 'YX2500002027', 'LOT_TYPE': '', 'GOOD_QTY': 18462, 'PROD_ID': '', 'DEVICE': 'JWQ7843-50SOTH-D1_TR1', 'CHIP_ID': 'JPQ21D34E_P01R', 'PKG_PN': 'SOT223', 'PO_ID': '', 'STAGE': 'ROOM-TTR-FT', 'WIP_STATE': '', 'PROC_STATE': '', 'HOLD_STATE': '', 'FLOW_ID': '', 'FLOW_VER': '', 'RELEASE_TIME': '', 'FAC_ID': '', 'CREATE_TIME': ''}, Error: not all arguments converted during string formatting
2025-06-15 16:16:23,535 - app.api.routes - ERROR - Error in save_priority_done: not all arguments converted during string formatting
2025-06-15 16:16:23,536 - app.api.routes - ERROR - 详细错误信息: Traceback (most recent call last):
  File "D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\api\routes.py", line 885, in save_priority_done
    raise e
  File "D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\api\routes.py", line 853, in save_priority_done
    cursor.execute("""
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\pymysql\cursors.py", line 151, in execute
    query = self.mogrify(query, args)
            ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\pymysql\cursors.py", line 129, in mogrify
    query = query % self._escape_args(args, conn)
            ~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
TypeError: not all arguments converted during string formatting

2025-06-15 16:16:23,538 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:16:23] "[35m[1mPOST /api/production/save-priority-done HTTP/1.1[0m" 500 -
2025-06-15 16:16:34,492 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:16:34] "[33mGET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1[0m" 404 -
2025-06-15 16:16:34,858 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:16:34] "[33mGET /static/css/base/bootstrap.min.css.map HTTP/1.1[0m" 404 -
2025-06-15 16:19:05,115 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:19:05] "GET /production/auto HTTP/1.1" 200 -
2025-06-15 16:19:05,198 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:19:05] "GET /static/css/base/bootstrap.min.css HTTP/1.1" 200 -
2025-06-15 16:19:05,379 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:19:05] "GET /static/css/all-5.15.4.min.css HTTP/1.1" 200 -
2025-06-15 16:19:05,492 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:19:05] "GET /static/js/base/bootstrap.bundle.min.js HTTP/1.1" 200 -
2025-06-15 16:19:05,494 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:19:05] "GET /static/webfonts/fa-solid-900.woff2 HTTP/1.1" 200 -
2025-06-15 16:19:05,494 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:19:05] "GET /static/css/compatibility.css HTTP/1.1" 200 -
2025-06-15 16:19:05,495 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:19:05] "GET /static/js/menu-optimizer.js HTTP/1.1" 200 -
2025-06-15 16:19:05,740 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:19:05] "GET /api/production/schedule-history HTTP/1.1" 200 -
2025-06-15 16:19:05,745 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:19:05] "[33mGET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1[0m" 404 -
2025-06-15 16:19:05,917 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:19:05] "[33mGET /static/css/base/bootstrap.min.css.map HTTP/1.1[0m" 404 -
2025-06-15 16:26:36,615 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:26:36] "GET /production/priority-matching HTTP/1.1" 200 -
2025-06-15 16:26:36,702 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:26:36] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 16:26:36,871 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:26:36] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 16:26:36,966 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:26:36] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 16:26:36,966 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:26:36] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 16:26:36,966 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:26:36] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 16:26:36,967 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:26:36] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 16:43:00,902 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:43:00] "GET /production/semi-auto HTTP/1.1" 200 -
2025-06-15 16:43:00,962 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:43:00] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 16:43:01,147 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:43:01] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 16:43:01,243 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:43:01] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 16:43:01,243 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:43:01] "[36mGET /static/css/daygrid/main.css HTTP/1.1[0m" 304 -
2025-06-15 16:43:01,244 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:43:01] "[36mGET /static/css/core/main.css HTTP/1.1[0m" 304 -
2025-06-15 16:43:01,245 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:43:01] "[36mGET /static/css/timegrid/main.css HTTP/1.1[0m" 304 -
2025-06-15 16:43:01,271 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:43:01] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 16:43:01,456 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:43:01] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 16:43:01,558 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:43:01] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 16:43:01,569 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:43:01] "[36mGET /static/js/xlsx.full.min.js HTTP/1.1[0m" 304 -
2025-06-15 16:43:01,956 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:43:01] "GET /api/production/get-import-path HTTP/1.1" 200 -
2025-06-15 16:43:01,958 - app - INFO - 成功连接到MySQL数据库: aps
2025-06-15 16:43:01,977 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:43:01] "GET /api/production/imported-files HTTP/1.1" 200 -
2025-06-15 16:43:02,215 - app.api.production_api - INFO - 开始执行自动排产: {'algorithm': 'intelligent', 'optimization_target': 'balanced', 'time_limit': 30, 'population_size': 100, 'auto_mode': False}
2025-06-15 16:43:02,219 - app.api.production_api - INFO - 获取到 171 条待排产批次数据
2025-06-15 16:43:02,224 - app.api.production_api - INFO - 加载了 21 条优先级配置
2025-06-15 16:43:02,266 - app.api.production_api - INFO - 获取到 913 台可用设备
2025-06-15 16:43:02,647 - app.api.production_api - INFO - 排产算法 intelligent 完成，生成 171 条排产记录
2025-06-15 16:43:02,648 - app.api.production_api - INFO - 自动排产完成: 171 条记录，耗时 0.43秒
2025-06-15 16:43:02,651 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:43:02] "POST /api/production/auto-schedule HTTP/1.1" 200 -
2025-06-15 16:48:00,145 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:48:00] "GET /production/priority-matching HTTP/1.1" 200 -
2025-06-15 16:48:00,253 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:48:00] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 16:48:00,411 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:48:00] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 16:48:00,489 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:48:00] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 16:48:00,489 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:48:00] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 16:48:00,489 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:48:00] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 16:48:00,502 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:48:00] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 16:48:03,736 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:48:03] "GET /production/semi-auto HTTP/1.1" 200 -
2025-06-15 16:48:03,797 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:48:03] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 16:48:03,999 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:48:03] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 16:48:04,079 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:48:04] "[36mGET /static/css/core/main.css HTTP/1.1[0m" 304 -
2025-06-15 16:48:04,079 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:48:04] "[36mGET /static/css/daygrid/main.css HTTP/1.1[0m" 304 -
2025-06-15 16:48:04,080 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:48:04] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 16:48:04,080 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:48:04] "[36mGET /static/css/timegrid/main.css HTTP/1.1[0m" 304 -
2025-06-15 16:48:04,107 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:48:04] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 16:48:04,308 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:48:04] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 16:48:04,387 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:48:04] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 16:48:04,403 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:48:04] "[36mGET /static/js/xlsx.full.min.js HTTP/1.1[0m" 304 -
2025-06-15 16:48:04,777 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:48:04] "GET /api/production/get-import-path HTTP/1.1" 200 -
2025-06-15 16:48:04,779 - app - INFO - 成功连接到MySQL数据库: aps
2025-06-15 16:48:04,796 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:48:04] "GET /api/production/imported-files HTTP/1.1" 200 -
2025-06-15 16:48:15,897 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:48:15] "GET /production/priority-matching HTTP/1.1" 200 -
2025-06-15 16:48:16,198 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:48:16] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 16:48:16,245 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:48:16] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 16:48:16,245 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:48:16] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 16:48:16,245 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:48:16] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 16:48:16,246 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:48:16] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 16:48:16,566 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:48:16] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 16:48:19,050 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:48:19] "GET /production/semi-auto HTTP/1.1" 200 -
2025-06-15 16:48:19,360 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:48:19] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 16:48:19,394 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:48:19] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 16:48:19,394 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:48:19] "[36mGET /static/css/core/main.css HTTP/1.1[0m" 304 -
2025-06-15 16:48:19,395 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:48:19] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 16:48:19,395 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:48:19] "[36mGET /static/css/daygrid/main.css HTTP/1.1[0m" 304 -
2025-06-15 16:48:19,396 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:48:19] "[36mGET /static/css/timegrid/main.css HTTP/1.1[0m" 304 -
2025-06-15 16:48:19,671 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:48:19] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 16:48:19,702 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:48:19] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 16:48:19,702 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:48:19] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 16:48:19,716 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:48:19] "[36mGET /static/js/xlsx.full.min.js HTTP/1.1[0m" 304 -
2025-06-15 16:48:20,079 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:48:20] "GET /api/production/get-import-path HTTP/1.1" 200 -
2025-06-15 16:48:20,081 - app - INFO - 成功连接到MySQL数据库: aps
2025-06-15 16:48:20,112 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:48:20] "GET /api/production/imported-files HTTP/1.1" 200 -
2025-06-15 16:50:28,850 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:50:28] "GET /production/priority-matching HTTP/1.1" 200 -
2025-06-15 16:50:28,925 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:50:28] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 16:50:29,110 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:50:29] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 16:50:29,189 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:50:29] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 16:50:29,190 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:50:29] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 16:50:29,192 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:50:29] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 16:50:29,193 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:50:29] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 16:58:43,123 - APS-System - INFO - 应用路径: D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12
2025-06-15 16:58:43,124 - APS-System - INFO - 实例路径: D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\instance
2025-06-15 16:58:43,125 - APS-System - INFO - 检查 MySQL 数据库配置...
2025-06-15 16:58:43,193 - APS-System - INFO - MySQL 数据库 'aps' 已存在
2025-06-15 16:58:43,195 - APS-System - INFO - MySQL 数据库 'aps_system' 已存在
2025-06-15 16:58:43,195 - APS-System - INFO - MySQL 数据库检查成功
2025-06-15 16:58:43,196 - APS-System - INFO - 正在修复数据库权限...
2025-06-15 16:58:43,197 - APS-System - INFO - 发现SQLite文件，但系统已迁移到MySQL，跳过: instance\aps.db
2025-06-15 16:58:43,197 - APS-System - INFO - 数据库文件不存在，跳过: instance\aps_system.db
2025-06-15 16:58:43,198 - APS-System - INFO - 已修复日志目录权限: logs
2025-06-15 16:58:43,198 - APS-System - INFO - 数据库权限修复成功
2025-06-15 16:58:45,833 - app - INFO - 应用启动
2025-06-15 16:58:45,834 - app.utils.scheduler - INFO - 已重新加载 0 个邮箱配置（应用实例未设置）
2025-06-15 16:58:45,853 - app.utils.scheduler - INFO - 邮件附件定时调度器已启动
2025-06-15 16:58:45,889 - app - INFO - 邮件附件定时任务调度器已启动
2025-06-15 16:58:45,899 - app.utils.scheduler - INFO - 全局定时任务已禁用，邮件调度器跟随禁用
2025-06-15 16:58:45,902 - app.utils.scheduler - INFO - 检测到全局定时任务已禁用，停止邮件调度器
2025-06-15 16:58:46,086 - app.services.scheduler_service - INFO - APScheduler调度器配置完成: timezone=Asia/Shanghai, max_workers=10
2025-06-15 16:58:46,100 - apscheduler.scheduler - INFO - Scheduler started
2025-06-15 16:58:46,107 - app.services.scheduler_service - INFO - 从数据库加载了 0 个任务
2025-06-15 16:58:46,109 - app.services.scheduler_service - INFO - APScheduler调度器启动成功
2025-06-15 16:58:46,110 - app - INFO - ✅ APScheduler统一调度器启动成功
2025-06-15 16:58:46,110 - APS-System - INFO - 应用创建成功
2025-06-15 16:58:46,112 - APS-System - INFO - ==================================================
2025-06-15 16:58:46,112 - APS-System - INFO - 🚀 APS车规芯片终测智能调度平台 v2.1
2025-06-15 16:58:46,112 - APS-System - INFO - 📊 100% MySQL数据库架构
2025-06-15 16:58:46,112 - APS-System - INFO - 🌐 访问地址: http://1********:5000
2025-06-15 16:58:46,113 - APS-System - INFO - ==================================================
2025-06-15 16:58:46,113 - APS-System - INFO - 运行模式: 开发环境
2025-06-15 16:58:46,130 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://1********:5000
2025-06-15 16:58:46,130 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-15 16:58:48,480 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:58:48] "[32mGET / HTTP/1.1[0m" 302 -
2025-06-15 16:58:48,501 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:58:48] "GET /auth/login?next=/ HTTP/1.1" 200 -
2025-06-15 16:58:48,900 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:58:48] "GET /static/css/login/bootstrap.min.css HTTP/1.1" 200 -
2025-06-15 16:58:49,143 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:58:49] "GET /static/css/all-5.15.4.min.css HTTP/1.1" 200 -
2025-06-15 16:58:49,151 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:58:49] "GET /static/css/compatibility.css HTTP/1.1" 200 -
2025-06-15 16:58:49,159 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:58:49] "GET /static/js/login/bootstrap.bundle.min.js HTTP/1.1" 200 -
2025-06-15 16:58:49,528 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:58:49] "GET /static/webfonts/fa-solid-900.woff2 HTTP/1.1" 200 -
2025-06-15 16:58:50,365 - app.auth.routes - INFO - 尝试登录用户: admin
2025-06-15 16:58:50,400 - app.auth.routes - INFO - 找到用户: admin, 角色: admin, 密码哈希: scrypt:32768:8:1$X8JU5hxR74hpH0tP$36e5b028b335f5d2f9f21e9178a0d529cca80459428b1128d44d8ac646d8f16cdd0823ea12d58ce5bbae1dd243eeb7a50c06f55d6688542dae997f7c75e6ee24
2025-06-15 16:58:50,574 - app.models - INFO - 用户 admin 密码验证结果: True
2025-06-15 16:58:50,576 - app.auth.routes - INFO - 密码验证结果: True
2025-06-15 16:58:50,577 - app.auth.routes - INFO - 用户 admin 登录成功
2025-06-15 16:58:50,592 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:58:50] "[32mPOST /auth/login HTTP/1.1[0m" 302 -
2025-06-15 16:58:50,688 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:58:50] "GET /index HTTP/1.1" 200 -
2025-06-15 16:58:51,039 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:58:51] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 16:58:51,039 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:58:51] "GET /static/css/base/bootstrap.min.css HTTP/1.1" 200 -
2025-06-15 16:58:51,041 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:58:51] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 16:58:51,045 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:58:51] "GET /static/js/base/bootstrap.bundle.min.js HTTP/1.1" 200 -
2025-06-15 16:58:51,047 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:58:51] "GET /static/js/menu-optimizer.js HTTP/1.1" 200 -
2025-06-15 16:58:51,300 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:58:51] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 16:58:51,379 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:58:51] "GET /menu HTTP/1.1" 200 -
2025-06-15 16:58:51,739 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:58:51] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-06-15 16:58:54,224 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:58:54] "GET /production/priority-matching HTTP/1.1" 200 -
2025-06-15 16:58:54,512 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:58:54] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 16:58:54,579 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:58:54] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 16:58:54,583 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:58:54] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 16:58:54,605 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:58:54] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 16:58:54,609 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:58:54] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 16:59:15,310 - app.api.production_api - INFO - 读取Excel文件成功，共 5 行数据
2025-06-15 16:59:15,324 - app.api.production_api - ERROR - 映射批次优先级字段失败: name 'pd' is not defined
2025-06-15 16:59:15,328 - app.api.production_api - ERROR - 映射批次优先级字段失败: name 'pd' is not defined
2025-06-15 16:59:15,332 - app.api.production_api - ERROR - 映射批次优先级字段失败: name 'pd' is not defined
2025-06-15 16:59:15,333 - app.api.production_api - ERROR - 映射批次优先级字段失败: name 'pd' is not defined
2025-06-15 16:59:15,335 - app.api.production_api - ERROR - 映射批次优先级字段失败: name 'pd' is not defined
2025-06-15 16:59:15,340 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:59:15] "POST /api/production/import-priority-excel HTTP/1.1" 200 -
2025-06-15 16:59:16,373 - app.api.production_api - INFO - 读取Excel文件成功，共 5 行数据
2025-06-15 16:59:16,376 - app.api.production_api - ERROR - 映射批次优先级字段失败: name 'pd' is not defined
2025-06-15 16:59:16,377 - app.api.production_api - ERROR - 映射批次优先级字段失败: name 'pd' is not defined
2025-06-15 16:59:16,377 - app.api.production_api - ERROR - 映射批次优先级字段失败: name 'pd' is not defined
2025-06-15 16:59:16,378 - app.api.production_api - ERROR - 映射批次优先级字段失败: name 'pd' is not defined
2025-06-15 16:59:16,379 - app.api.production_api - ERROR - 映射批次优先级字段失败: name 'pd' is not defined
2025-06-15 16:59:16,382 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:59:16] "POST /api/production/import-priority-excel HTTP/1.1" 200 -
2025-06-15 16:59:16,883 - app.api.production_api - INFO - 读取Excel文件成功，共 5 行数据
2025-06-15 16:59:16,886 - app.api.production_api - ERROR - 映射批次优先级字段失败: name 'pd' is not defined
2025-06-15 16:59:16,889 - app.api.production_api - ERROR - 映射批次优先级字段失败: name 'pd' is not defined
2025-06-15 16:59:16,890 - app.api.production_api - ERROR - 映射批次优先级字段失败: name 'pd' is not defined
2025-06-15 16:59:16,892 - app.api.production_api - ERROR - 映射批次优先级字段失败: name 'pd' is not defined
2025-06-15 16:59:16,893 - app.api.production_api - ERROR - 映射批次优先级字段失败: name 'pd' is not defined
2025-06-15 16:59:16,895 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:59:16] "POST /api/production/import-priority-excel HTTP/1.1" 200 -
2025-06-15 16:59:17,051 - app.api.production_api - INFO - 读取Excel文件成功，共 5 行数据
2025-06-15 16:59:17,055 - app.api.production_api - ERROR - 映射批次优先级字段失败: name 'pd' is not defined
2025-06-15 16:59:17,056 - app.api.production_api - ERROR - 映射批次优先级字段失败: name 'pd' is not defined
2025-06-15 16:59:17,057 - app.api.production_api - ERROR - 映射批次优先级字段失败: name 'pd' is not defined
2025-06-15 16:59:17,058 - app.api.production_api - ERROR - 映射批次优先级字段失败: name 'pd' is not defined
2025-06-15 16:59:17,059 - app.api.production_api - ERROR - 映射批次优先级字段失败: name 'pd' is not defined
2025-06-15 16:59:17,068 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:59:17] "POST /api/production/import-priority-excel HTTP/1.1" 200 -
2025-06-15 16:59:17,133 - app.api.production_api - INFO - 读取Excel文件成功，共 5 行数据
2025-06-15 16:59:17,136 - app.api.production_api - ERROR - 映射批次优先级字段失败: name 'pd' is not defined
2025-06-15 16:59:17,137 - app.api.production_api - ERROR - 映射批次优先级字段失败: name 'pd' is not defined
2025-06-15 16:59:17,139 - app.api.production_api - ERROR - 映射批次优先级字段失败: name 'pd' is not defined
2025-06-15 16:59:17,140 - app.api.production_api - ERROR - 映射批次优先级字段失败: name 'pd' is not defined
2025-06-15 16:59:17,141 - app.api.production_api - ERROR - 映射批次优先级字段失败: name 'pd' is not defined
2025-06-15 16:59:17,143 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:59:17] "POST /api/production/import-priority-excel HTTP/1.1" 200 -
2025-06-15 16:59:30,469 - app.api.production_api - INFO - 读取Excel文件成功，共 5 行数据
2025-06-15 16:59:30,470 - app.api.production_api - ERROR - 映射批次优先级字段失败: name 'pd' is not defined
2025-06-15 16:59:30,473 - app.api.production_api - ERROR - 映射批次优先级字段失败: name 'pd' is not defined
2025-06-15 16:59:30,475 - app.api.production_api - ERROR - 映射批次优先级字段失败: name 'pd' is not defined
2025-06-15 16:59:30,494 - app.api.production_api - ERROR - 映射批次优先级字段失败: name 'pd' is not defined
2025-06-15 16:59:30,495 - app.api.production_api - ERROR - 映射批次优先级字段失败: name 'pd' is not defined
2025-06-15 16:59:30,498 - werkzeug - INFO - 1******** - - [15/Jun/2025 16:59:30] "POST /api/production/import-priority-excel HTTP/1.1" 200 -
2025-06-15 17:01:40,145 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:01:40] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 17:01:40,145 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:01:40] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 17:01:40,147 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:01:40] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 17:01:40,153 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:01:40] "[33mGET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1[0m" 404 -
2025-06-15 17:01:40,408 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:01:40] "[33mGET /static/css/base/bootstrap.min.css.map HTTP/1.1[0m" 404 -
2025-06-15 17:06:01,177 - app.services.priority_matching_service - INFO - 匹配缓存已清空
2025-06-15 17:06:01,181 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:06:01] "POST /api/priority/clear-cache HTTP/1.1" 200 -
2025-06-15 17:11:38,260 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:11:38] "GET /production/priority-matching HTTP/1.1" 200 -
2025-06-15 17:11:38,299 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:11:38] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 17:11:38,518 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:11:38] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 17:11:38,627 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:11:38] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 17:11:38,628 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:11:38] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 17:11:38,629 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:11:38] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 17:11:38,951 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:11:38] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 17:11:42,172 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:11:42] "GET /production/priority-matching HTTP/1.1" 200 -
2025-06-15 17:11:42,471 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:11:42] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 17:11:42,506 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:11:42] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 17:11:42,509 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:11:42] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 17:11:42,509 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:11:42] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 17:11:42,512 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:11:42] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 17:11:42,826 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:11:42] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 17:11:58,010 - APS-System - INFO - 应用路径: D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12
2025-06-15 17:11:58,013 - APS-System - INFO - 实例路径: D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\instance
2025-06-15 17:11:58,016 - APS-System - INFO - 检查 MySQL 数据库配置...
2025-06-15 17:11:58,078 - APS-System - INFO - MySQL 数据库 'aps' 已存在
2025-06-15 17:11:58,080 - APS-System - INFO - MySQL 数据库 'aps_system' 已存在
2025-06-15 17:11:58,080 - APS-System - INFO - MySQL 数据库检查成功
2025-06-15 17:11:58,080 - APS-System - INFO - 正在修复数据库权限...
2025-06-15 17:11:58,081 - APS-System - INFO - 发现SQLite文件，但系统已迁移到MySQL，跳过: instance\aps.db
2025-06-15 17:11:58,082 - APS-System - INFO - 数据库文件不存在，跳过: instance\aps_system.db
2025-06-15 17:11:58,083 - APS-System - INFO - 已修复日志目录权限: logs
2025-06-15 17:11:58,085 - APS-System - INFO - 数据库权限修复成功
2025-06-15 17:12:00,325 - app - INFO - 应用启动
2025-06-15 17:12:00,327 - app.utils.scheduler - INFO - 已重新加载 0 个邮箱配置（应用实例未设置）
2025-06-15 17:12:00,344 - app.utils.scheduler - INFO - 邮件附件定时调度器已启动
2025-06-15 17:12:00,371 - app - INFO - 邮件附件定时任务调度器已启动
2025-06-15 17:12:00,399 - app.utils.scheduler - INFO - 全局定时任务已禁用，邮件调度器跟随禁用
2025-06-15 17:12:00,402 - app.utils.scheduler - INFO - 检测到全局定时任务已禁用，停止邮件调度器
2025-06-15 17:12:00,558 - app.services.scheduler_service - INFO - APScheduler调度器配置完成: timezone=Asia/Shanghai, max_workers=10
2025-06-15 17:12:00,580 - apscheduler.scheduler - INFO - Scheduler started
2025-06-15 17:12:00,585 - app.services.scheduler_service - INFO - 从数据库加载了 0 个任务
2025-06-15 17:12:00,587 - app.services.scheduler_service - INFO - APScheduler调度器启动成功
2025-06-15 17:12:00,588 - app - INFO - ✅ APScheduler统一调度器启动成功
2025-06-15 17:12:00,589 - APS-System - INFO - 应用创建成功
2025-06-15 17:12:00,589 - APS-System - INFO - ==================================================
2025-06-15 17:12:00,589 - APS-System - INFO - 🚀 APS车规芯片终测智能调度平台 v2.1
2025-06-15 17:12:00,589 - APS-System - INFO - 📊 100% MySQL数据库架构
2025-06-15 17:12:00,590 - APS-System - INFO - 🌐 访问地址: http://1********:5000
2025-06-15 17:12:00,590 - APS-System - INFO - ==================================================
2025-06-15 17:12:00,590 - APS-System - INFO - 运行模式: 开发环境
2025-06-15 17:12:00,613 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://1********:5000
2025-06-15 17:12:00,614 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-15 17:12:01,760 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:12:01] "GET /production/priority-matching HTTP/1.1" 200 -
2025-06-15 17:12:01,883 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:12:01] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 17:12:01,997 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:12:01] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 17:12:02,120 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:12:02] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 17:12:02,627 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:12:02] "GET / HTTP/1.1" 200 -
2025-06-15 17:12:02,631 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:12:02] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 17:12:02,722 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:12:02] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 17:12:02,874 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:12:02] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 17:12:02,999 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:12:02] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 17:12:03,001 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:12:03] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 17:12:03,010 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:12:03] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 17:12:03,011 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:12:03] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 17:12:03,427 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:12:03] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 17:12:03,686 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:12:03] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 17:12:05,869 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:12:05] "GET /production/priority-matching HTTP/1.1" 200 -
2025-06-15 17:12:06,176 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:12:06] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 17:12:06,207 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:12:06] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 17:12:06,209 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:12:06] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 17:12:06,212 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:12:06] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 17:12:06,215 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:12:06] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 17:12:06,532 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:12:06] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 17:17:28,941 - APS-System - INFO - 应用路径: D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12
2025-06-15 17:17:28,941 - APS-System - INFO - 实例路径: D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\instance
2025-06-15 17:17:28,942 - APS-System - INFO - 检查 MySQL 数据库配置...
2025-06-15 17:17:29,009 - APS-System - INFO - MySQL 数据库 'aps' 已存在
2025-06-15 17:17:29,010 - APS-System - INFO - MySQL 数据库 'aps_system' 已存在
2025-06-15 17:17:29,011 - APS-System - INFO - MySQL 数据库检查成功
2025-06-15 17:17:29,011 - APS-System - INFO - 正在修复数据库权限...
2025-06-15 17:17:29,012 - APS-System - INFO - 发现SQLite文件，但系统已迁移到MySQL，跳过: instance\aps.db
2025-06-15 17:17:29,012 - APS-System - INFO - 数据库文件不存在，跳过: instance\aps_system.db
2025-06-15 17:17:29,013 - APS-System - INFO - 已修复日志目录权限: logs
2025-06-15 17:17:29,013 - APS-System - INFO - 数据库权限修复成功
2025-06-15 17:17:31,538 - app - INFO - 应用启动
2025-06-15 17:17:31,539 - app.utils.scheduler - INFO - 已重新加载 0 个邮箱配置（应用实例未设置）
2025-06-15 17:17:31,552 - app.utils.scheduler - INFO - 邮件附件定时调度器已启动
2025-06-15 17:17:31,587 - app - INFO - 邮件附件定时任务调度器已启动
2025-06-15 17:17:31,594 - app.utils.scheduler - INFO - 全局定时任务已禁用，邮件调度器跟随禁用
2025-06-15 17:17:31,596 - app.utils.scheduler - INFO - 检测到全局定时任务已禁用，停止邮件调度器
2025-06-15 17:17:31,790 - app.services.scheduler_service - INFO - APScheduler调度器配置完成: timezone=Asia/Shanghai, max_workers=10
2025-06-15 17:17:31,805 - apscheduler.scheduler - INFO - Scheduler started
2025-06-15 17:17:31,811 - app.services.scheduler_service - INFO - 从数据库加载了 0 个任务
2025-06-15 17:17:31,812 - app.services.scheduler_service - INFO - APScheduler调度器启动成功
2025-06-15 17:17:31,813 - app - INFO - ✅ APScheduler统一调度器启动成功
2025-06-15 17:17:31,815 - APS-System - INFO - 应用创建成功
2025-06-15 17:17:31,815 - APS-System - INFO - ==================================================
2025-06-15 17:17:31,816 - APS-System - INFO - 🚀 APS车规芯片终测智能调度平台 v2.1
2025-06-15 17:17:31,816 - APS-System - INFO - 📊 100% MySQL数据库架构
2025-06-15 17:17:31,816 - APS-System - INFO - 🌐 访问地址: http://1********:5000
2025-06-15 17:17:31,816 - APS-System - INFO - ==================================================
2025-06-15 17:17:31,817 - APS-System - INFO - 运行模式: 开发环境
2025-06-15 17:17:31,839 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://1********:5000
2025-06-15 17:17:31,840 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-15 17:17:33,963 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:17:33] "GET / HTTP/1.1" 200 -
2025-06-15 17:17:34,231 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:17:34] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 17:17:34,339 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:17:34] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 17:17:34,339 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:17:34] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 17:17:34,340 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:17:34] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 17:17:34,358 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:17:34] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 17:17:34,781 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:17:34] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 17:17:37,835 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:17:37] "GET /production/priority-matching HTTP/1.1" 200 -
2025-06-15 17:17:38,139 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:17:38] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 17:17:38,187 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:17:38] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 17:17:38,188 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:17:38] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 17:17:38,189 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:17:38] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 17:17:38,193 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:17:38] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 17:17:38,509 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:17:38] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 17:24:43,692 - APS-System - INFO - 应用路径: D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12
2025-06-15 17:24:43,692 - APS-System - INFO - 实例路径: D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\instance
2025-06-15 17:24:43,693 - APS-System - INFO - 检查 MySQL 数据库配置...
2025-06-15 17:24:43,742 - APS-System - INFO - MySQL 数据库 'aps' 已存在
2025-06-15 17:24:43,743 - APS-System - INFO - MySQL 数据库 'aps_system' 已存在
2025-06-15 17:24:43,744 - APS-System - INFO - MySQL 数据库检查成功
2025-06-15 17:24:43,744 - APS-System - INFO - 正在修复数据库权限...
2025-06-15 17:24:43,745 - APS-System - INFO - 发现SQLite文件，但系统已迁移到MySQL，跳过: instance\aps.db
2025-06-15 17:24:43,745 - APS-System - INFO - 数据库文件不存在，跳过: instance\aps_system.db
2025-06-15 17:24:43,746 - APS-System - INFO - 已修复日志目录权限: logs
2025-06-15 17:24:43,746 - APS-System - INFO - 数据库权限修复成功
2025-06-15 17:24:45,513 - app - INFO - 应用启动
2025-06-15 17:24:45,514 - app.utils.scheduler - INFO - 已重新加载 0 个邮箱配置（应用实例未设置）
2025-06-15 17:24:45,535 - app.utils.scheduler - INFO - 邮件附件定时调度器已启动
2025-06-15 17:24:45,553 - app - INFO - 邮件附件定时任务调度器已启动
2025-06-15 17:24:45,575 - app.utils.scheduler - INFO - 全局定时任务已禁用，邮件调度器跟随禁用
2025-06-15 17:24:45,577 - app.utils.scheduler - INFO - 检测到全局定时任务已禁用，停止邮件调度器
2025-06-15 17:24:45,703 - app.services.scheduler_service - INFO - APScheduler调度器配置完成: timezone=Asia/Shanghai, max_workers=10
2025-06-15 17:24:45,710 - apscheduler.scheduler - INFO - Scheduler started
2025-06-15 17:24:45,716 - app.services.scheduler_service - INFO - 从数据库加载了 0 个任务
2025-06-15 17:24:45,717 - app.services.scheduler_service - INFO - APScheduler调度器启动成功
2025-06-15 17:24:45,718 - app - INFO - ✅ APScheduler统一调度器启动成功
2025-06-15 17:24:45,718 - APS-System - INFO - 应用创建成功
2025-06-15 17:24:45,718 - APS-System - INFO - ==================================================
2025-06-15 17:24:45,719 - APS-System - INFO - 🚀 APS车规芯片终测智能调度平台 v2.1
2025-06-15 17:24:45,719 - APS-System - INFO - 📊 100% MySQL数据库架构
2025-06-15 17:24:45,719 - APS-System - INFO - 🌐 访问地址: http://1********:5000
2025-06-15 17:24:45,719 - APS-System - INFO - ==================================================
2025-06-15 17:24:45,720 - APS-System - INFO - 运行模式: 开发环境
2025-06-15 17:24:45,736 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://1********:5000
2025-06-15 17:24:45,736 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-15 17:24:47,825 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:24:47] "GET / HTTP/1.1" 200 -
2025-06-15 17:24:48,219 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:24:48] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 17:24:48,220 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:24:48] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 17:24:48,631 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:24:48] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 17:24:48,828 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:24:48] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 17:24:48,849 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:24:48] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 17:24:49,207 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:24:49] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 17:24:54,676 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:24:54] "GET /production/priority-matching HTTP/1.1" 200 -
2025-06-15 17:24:54,966 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:24:54] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 17:24:55,026 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:24:55] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 17:24:55,026 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:24:55] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 17:24:55,028 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:24:55] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 17:24:55,029 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:24:55] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 17:24:55,348 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:24:55] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 17:25:04,419 - app.api.production_api - INFO - 接收到文件上传请求: 文件名=devicepriorityconfig.xlsx, 类型=devicepriorityconfig, 策略=skip
2025-06-15 17:25:04,923 - app.api.production_api - INFO - 读取Excel文件成功，共 5 行数据
2025-06-15 17:25:04,925 - app.api.production_api - ERROR - 导入Excel文件失败: 'Config' object has no attribute 'MYSQL_DATABASE'
2025-06-15 17:25:04,929 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:25:04] "POST /api/production/import-priority-excel HTTP/1.1" 200 -
2025-06-15 17:25:05,248 - app.api.production_api - INFO - 接收到文件上传请求: 文件名=lotpriorityconfig.xlsx, 类型=lotpriorityconfig, 策略=skip
2025-06-15 17:25:05,309 - app.api.production_api - INFO - 读取Excel文件成功，共 5 行数据
2025-06-15 17:25:05,314 - app.api.production_api - ERROR - 导入Excel文件失败: 'Config' object has no attribute 'MYSQL_DATABASE'
2025-06-15 17:25:05,317 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:25:05] "POST /api/production/import-priority-excel HTTP/1.1" 200 -
2025-06-15 17:28:55,344 - APS-System - INFO - 应用路径: D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12
2025-06-15 17:28:55,344 - APS-System - INFO - 实例路径: D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\instance
2025-06-15 17:28:55,344 - APS-System - INFO - 检查 MySQL 数据库配置...
2025-06-15 17:28:55,406 - APS-System - INFO - MySQL 数据库 'aps' 已存在
2025-06-15 17:28:55,408 - APS-System - INFO - MySQL 数据库 'aps_system' 已存在
2025-06-15 17:28:55,409 - APS-System - INFO - MySQL 数据库检查成功
2025-06-15 17:28:55,410 - APS-System - INFO - 正在修复数据库权限...
2025-06-15 17:28:55,410 - APS-System - INFO - 发现SQLite文件，但系统已迁移到MySQL，跳过: instance\aps.db
2025-06-15 17:28:55,410 - APS-System - INFO - 数据库文件不存在，跳过: instance\aps_system.db
2025-06-15 17:28:55,411 - APS-System - INFO - 已修复日志目录权限: logs
2025-06-15 17:28:55,411 - APS-System - INFO - 数据库权限修复成功
2025-06-15 17:28:57,917 - app - INFO - 应用启动
2025-06-15 17:28:57,919 - app.utils.scheduler - INFO - 已重新加载 0 个邮箱配置（应用实例未设置）
2025-06-15 17:28:57,938 - app.utils.scheduler - INFO - 邮件附件定时调度器已启动
2025-06-15 17:28:57,966 - app - INFO - 邮件附件定时任务调度器已启动
2025-06-15 17:28:57,977 - app.utils.scheduler - INFO - 全局定时任务已禁用，邮件调度器跟随禁用
2025-06-15 17:28:57,978 - app.utils.scheduler - INFO - 检测到全局定时任务已禁用，停止邮件调度器
2025-06-15 17:28:58,145 - app.services.scheduler_service - INFO - APScheduler调度器配置完成: timezone=Asia/Shanghai, max_workers=10
2025-06-15 17:28:58,178 - apscheduler.scheduler - INFO - Scheduler started
2025-06-15 17:28:58,182 - app.services.scheduler_service - INFO - 从数据库加载了 0 个任务
2025-06-15 17:28:58,183 - app.services.scheduler_service - INFO - APScheduler调度器启动成功
2025-06-15 17:28:58,184 - app - INFO - ✅ APScheduler统一调度器启动成功
2025-06-15 17:28:58,185 - APS-System - INFO - 应用创建成功
2025-06-15 17:28:58,185 - APS-System - INFO - ==================================================
2025-06-15 17:28:58,185 - APS-System - INFO - 🚀 APS车规芯片终测智能调度平台 v2.1
2025-06-15 17:28:58,185 - APS-System - INFO - 📊 100% MySQL数据库架构
2025-06-15 17:28:58,185 - APS-System - INFO - 🌐 访问地址: http://1********:5000
2025-06-15 17:28:58,185 - APS-System - INFO - ==================================================
2025-06-15 17:28:58,186 - APS-System - INFO - 运行模式: 开发环境
2025-06-15 17:28:58,209 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://1********:5000
2025-06-15 17:28:58,210 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-15 17:29:00,312 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:29:00] "GET / HTTP/1.1" 200 -
2025-06-15 17:29:00,364 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:29:00] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 17:29:00,572 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:29:00] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 17:29:00,681 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:29:00] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 17:29:00,682 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:29:00] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 17:29:00,683 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:29:00] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 17:29:01,129 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:29:01] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 17:30:11,726 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:30:11] "GET / HTTP/1.1" 200 -
2025-06-15 17:30:11,859 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:30:11] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 17:30:11,957 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:30:11] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 17:30:12,072 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:30:12] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 17:30:12,072 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:30:12] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 17:30:12,075 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:30:12] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 17:30:12,390 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:30:12] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 17:30:16,404 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:30:16] "GET /production/priority-matching HTTP/1.1" 200 -
2025-06-15 17:30:16,692 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:30:16] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 17:30:16,757 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:30:16] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 17:30:16,761 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:30:16] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 17:30:16,769 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:30:16] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 17:30:16,770 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:30:16] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 17:30:17,089 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:30:17] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 17:30:34,452 - app.api.production_api - INFO - 接收到文件上传请求: 文件名=devicepriorityconfig.xlsx, 类型=devicepriorityconfig, 策略=skip
2025-06-15 17:30:35,115 - app.api.production_api - INFO - 读取Excel文件成功，共 5 行数据
2025-06-15 17:30:35,146 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:30:35] "POST /api/production/import-priority-excel HTTP/1.1" 200 -
2025-06-15 17:30:35,158 - app.api.production_api - INFO - 接收到文件上传请求: 文件名=lotpriorityconfig.xlsx, 类型=lotpriorityconfig, 策略=skip
2025-06-15 17:30:35,207 - app.api.production_api - INFO - 读取Excel文件成功，共 5 行数据
2025-06-15 17:30:35,224 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:30:35] "POST /api/production/import-priority-excel HTTP/1.1" 200 -
2025-06-15 17:31:02,509 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:31:02] "GET /production/priority-matching HTTP/1.1" 200 -
2025-06-15 17:31:02,546 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:31:02] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 17:31:02,771 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:31:02] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 17:31:02,865 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:31:02] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 17:31:02,866 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:31:02] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 17:31:02,867 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:31:02] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 17:31:03,203 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:31:03] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 17:34:04,985 - APS-System - INFO - 应用路径: D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12
2025-06-15 17:34:04,986 - APS-System - INFO - 实例路径: D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\instance
2025-06-15 17:34:04,986 - APS-System - INFO - 检查 MySQL 数据库配置...
2025-06-15 17:34:05,042 - APS-System - INFO - MySQL 数据库 'aps' 已存在
2025-06-15 17:34:05,045 - APS-System - INFO - MySQL 数据库 'aps_system' 已存在
2025-06-15 17:34:05,045 - APS-System - INFO - MySQL 数据库检查成功
2025-06-15 17:34:05,046 - APS-System - INFO - 正在修复数据库权限...
2025-06-15 17:34:05,046 - APS-System - INFO - 发现SQLite文件，但系统已迁移到MySQL，跳过: instance\aps.db
2025-06-15 17:34:05,047 - APS-System - INFO - 数据库文件不存在，跳过: instance\aps_system.db
2025-06-15 17:34:05,048 - APS-System - INFO - 已修复日志目录权限: logs
2025-06-15 17:34:05,048 - APS-System - INFO - 数据库权限修复成功
2025-06-15 17:34:07,439 - app - INFO - 应用启动
2025-06-15 17:34:07,442 - app.utils.scheduler - INFO - 已重新加载 0 个邮箱配置（应用实例未设置）
2025-06-15 17:34:07,455 - app.utils.scheduler - INFO - 邮件附件定时调度器已启动
2025-06-15 17:34:07,488 - app - INFO - 邮件附件定时任务调度器已启动
2025-06-15 17:34:07,512 - app.utils.scheduler - INFO - 全局定时任务已禁用，邮件调度器跟随禁用
2025-06-15 17:34:07,514 - app.utils.scheduler - INFO - 检测到全局定时任务已禁用，停止邮件调度器
2025-06-15 17:34:07,670 - app.services.scheduler_service - INFO - APScheduler调度器配置完成: timezone=Asia/Shanghai, max_workers=10
2025-06-15 17:34:07,694 - apscheduler.scheduler - INFO - Scheduler started
2025-06-15 17:34:07,700 - app.services.scheduler_service - INFO - 从数据库加载了 0 个任务
2025-06-15 17:34:07,701 - app.services.scheduler_service - INFO - APScheduler调度器启动成功
2025-06-15 17:34:07,702 - app - INFO - ✅ APScheduler统一调度器启动成功
2025-06-15 17:34:07,703 - APS-System - INFO - 应用创建成功
2025-06-15 17:34:07,703 - APS-System - INFO - ==================================================
2025-06-15 17:34:07,703 - APS-System - INFO - 🚀 APS车规芯片终测智能调度平台 v2.1
2025-06-15 17:34:07,703 - APS-System - INFO - 📊 100% MySQL数据库架构
2025-06-15 17:34:07,703 - APS-System - INFO - 🌐 访问地址: http://1********:5000
2025-06-15 17:34:07,704 - APS-System - INFO - ==================================================
2025-06-15 17:34:07,704 - APS-System - INFO - 运行模式: 开发环境
2025-06-15 17:34:07,721 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://1********:5000
2025-06-15 17:34:07,721 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-15 17:34:09,831 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:34:09] "GET / HTTP/1.1" 200 -
2025-06-15 17:34:10,044 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:34:10] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 17:34:10,051 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:34:10] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 17:34:10,196 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:34:10] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 17:34:10,198 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:34:10] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 17:34:10,201 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:34:10] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 17:34:10,622 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:34:10] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 17:34:12,418 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:34:12] "GET /production/priority-matching HTTP/1.1" 200 -
2025-06-15 17:34:12,713 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:34:12] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 17:34:12,762 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:34:12] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 17:34:12,763 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:34:12] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 17:34:12,764 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:34:12] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 17:34:12,765 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:34:12] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 17:34:13,082 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:34:13] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 17:34:25,162 - app.api.production_api - INFO - 接收到文件上传请求: 文件名=devicepriorityconfig.xlsx, 类型=devicepriorityconfig, 策略=replace
2025-06-15 17:34:25,690 - app.api.production_api - INFO - 读取Excel文件成功，共 5 行数据
2025-06-15 17:34:25,713 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:34:25] "POST /api/production/import-priority-excel HTTP/1.1" 200 -
2025-06-15 17:34:26,041 - app.api.production_api - INFO - 接收到文件上传请求: 文件名=lotpriorityconfig.xlsx, 类型=lotpriorityconfig, 策略=replace
2025-06-15 17:34:26,089 - app.api.production_api - INFO - 读取Excel文件成功，共 5 行数据
2025-06-15 17:34:26,111 - werkzeug - INFO - 1******** - - [15/Jun/2025 17:34:26] "POST /api/production/import-priority-excel HTTP/1.1" 200 -
2025-06-15 20:06:32,186 - APS-System - INFO - 应用路径: D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12
2025-06-15 20:06:32,186 - APS-System - INFO - 实例路径: D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\instance
2025-06-15 20:06:32,187 - APS-System - INFO - 检查 MySQL 数据库配置...
2025-06-15 20:06:32,250 - APS-System - INFO - MySQL 数据库 'aps' 已存在
2025-06-15 20:06:32,251 - APS-System - INFO - MySQL 数据库 'aps_system' 已存在
2025-06-15 20:06:32,252 - APS-System - INFO - MySQL 数据库检查成功
2025-06-15 20:06:32,252 - APS-System - INFO - 正在修复数据库权限...
2025-06-15 20:06:32,252 - APS-System - INFO - 数据库文件不存在，跳过: instance\aps.db
2025-06-15 20:06:32,253 - APS-System - INFO - 数据库文件不存在，跳过: instance\aps_system.db
2025-06-15 20:06:32,254 - APS-System - INFO - 已修复日志目录权限: logs
2025-06-15 20:06:32,254 - APS-System - INFO - 数据库权限修复成功
2025-06-15 20:06:34,541 - app - INFO - 应用启动
2025-06-15 20:06:34,544 - app.utils.scheduler - INFO - 已重新加载 0 个邮箱配置（应用实例未设置）
2025-06-15 20:06:34,561 - app.utils.scheduler - INFO - 邮件附件定时调度器已启动
2025-06-15 20:06:34,596 - app - INFO - 邮件附件定时任务调度器已启动
2025-06-15 20:06:34,608 - app.utils.scheduler - INFO - 全局定时任务已禁用，邮件调度器跟随禁用
2025-06-15 20:06:34,612 - app.utils.scheduler - INFO - 检测到全局定时任务已禁用，停止邮件调度器
2025-06-15 20:06:34,828 - app.services.scheduler_service - INFO - APScheduler调度器配置完成: timezone=Asia/Shanghai, max_workers=10
2025-06-15 20:06:34,869 - apscheduler.scheduler - INFO - Scheduler started
2025-06-15 20:06:34,877 - app.services.scheduler_service - INFO - 从数据库加载了 0 个任务
2025-06-15 20:06:34,878 - app.services.scheduler_service - INFO - APScheduler调度器启动成功
2025-06-15 20:06:34,879 - app - INFO - ✅ APScheduler统一调度器启动成功
2025-06-15 20:06:34,880 - APS-System - INFO - 应用创建成功
2025-06-15 20:06:34,880 - APS-System - INFO - ==================================================
2025-06-15 20:06:34,881 - APS-System - INFO - 🚀 APS车规芯片终测智能调度平台 v2.1
2025-06-15 20:06:34,881 - APS-System - INFO - 📊 100% MySQL数据库架构
2025-06-15 20:06:34,881 - APS-System - INFO - 🌐 访问地址: http://1********:5000
2025-06-15 20:06:34,882 - APS-System - INFO - ==================================================
2025-06-15 20:06:34,882 - APS-System - INFO - 运行模式: 开发环境
2025-06-15 20:06:34,919 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://1********:5000
2025-06-15 20:06:34,920 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-15 20:06:37,018 - werkzeug - INFO - 1******** - - [15/Jun/2025 20:06:37] "[32mGET / HTTP/1.1[0m" 302 -
2025-06-15 20:06:37,042 - werkzeug - INFO - 1******** - - [15/Jun/2025 20:06:37] "GET /auth/login?next=/ HTTP/1.1" 200 -
2025-06-15 20:06:37,380 - werkzeug - INFO - 1******** - - [15/Jun/2025 20:06:37] "GET /static/css/login/bootstrap.min.css HTTP/1.1" 200 -
2025-06-15 20:06:37,387 - werkzeug - INFO - 1******** - - [15/Jun/2025 20:06:37] "GET /static/css/all-5.15.4.min.css HTTP/1.1" 200 -
2025-06-15 20:06:37,387 - werkzeug - INFO - 1******** - - [15/Jun/2025 20:06:37] "GET /static/css/compatibility.css HTTP/1.1" 200 -
2025-06-15 20:06:37,388 - werkzeug - INFO - 1******** - - [15/Jun/2025 20:06:37] "GET /static/js/login/bootstrap.bundle.min.js HTTP/1.1" 200 -
2025-06-15 20:06:37,776 - werkzeug - INFO - 1******** - - [15/Jun/2025 20:06:37] "GET /static/webfonts/fa-solid-900.woff2 HTTP/1.1" 200 -
2025-06-15 20:06:38,465 - werkzeug - INFO - 1******** - - [15/Jun/2025 20:06:38] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-06-15 20:06:39,550 - app.auth.routes - INFO - 尝试登录用户: admin
2025-06-15 20:06:39,572 - app.auth.routes - INFO - 找到用户: admin, 角色: admin, 密码哈希: scrypt:32768:8:1$X8JU5hxR74hpH0tP$36e5b028b335f5d2f9f21e9178a0d529cca80459428b1128d44d8ac646d8f16cdd0823ea12d58ce5bbae1dd243eeb7a50c06f55d6688542dae997f7c75e6ee24
2025-06-15 20:06:39,699 - app.models - INFO - 用户 admin 密码验证结果: True
2025-06-15 20:06:39,701 - app.auth.routes - INFO - 密码验证结果: True
2025-06-15 20:06:39,701 - app.auth.routes - INFO - 用户 admin 登录成功
2025-06-15 20:06:39,718 - werkzeug - INFO - 1******** - - [15/Jun/2025 20:06:39] "[32mPOST /auth/login HTTP/1.1[0m" 302 -
2025-06-15 20:06:39,745 - werkzeug - INFO - 1******** - - [15/Jun/2025 20:06:39] "GET /index HTTP/1.1" 200 -
2025-06-15 20:06:39,815 - werkzeug - INFO - 1******** - - [15/Jun/2025 20:06:39] "GET /static/css/base/bootstrap.min.css HTTP/1.1" 200 -
2025-06-15 20:06:40,108 - werkzeug - INFO - 1******** - - [15/Jun/2025 20:06:40] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 20:06:40,111 - werkzeug - INFO - 1******** - - [15/Jun/2025 20:06:40] "GET /static/js/base/bootstrap.bundle.min.js HTTP/1.1" 200 -
2025-06-15 20:06:40,112 - werkzeug - INFO - 1******** - - [15/Jun/2025 20:06:40] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 20:06:40,114 - werkzeug - INFO - 1******** - - [15/Jun/2025 20:06:40] "GET /static/js/menu-optimizer.js HTTP/1.1" 200 -
2025-06-15 20:06:40,479 - werkzeug - INFO - 1******** - - [15/Jun/2025 20:06:40] "GET /menu HTTP/1.1" 200 -
2025-06-15 20:06:45,232 - werkzeug - INFO - 1******** - - [15/Jun/2025 20:06:45] "GET /production/priority-matching HTTP/1.1" 200 -
2025-06-15 20:06:45,517 - werkzeug - INFO - 1******** - - [15/Jun/2025 20:06:45] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 20:06:45,582 - werkzeug - INFO - 1******** - - [15/Jun/2025 20:06:45] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 20:06:45,583 - werkzeug - INFO - 1******** - - [15/Jun/2025 20:06:45] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 20:06:45,589 - werkzeug - INFO - 1******** - - [15/Jun/2025 20:06:45] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 20:06:45,595 - werkzeug - INFO - 1******** - - [15/Jun/2025 20:06:45] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 21:14:09,505 - APS-System - INFO - 应用路径: d:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12
2025-06-15 21:14:09,506 - APS-System - INFO - 实例路径: d:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\instance
2025-06-15 21:14:09,506 - APS-System - INFO - 检查 MySQL 数据库配置...
2025-06-15 21:14:09,562 - APS-System - INFO - MySQL 数据库 'aps' 已存在
2025-06-15 21:14:09,564 - APS-System - INFO - MySQL 数据库 'aps_system' 已存在
2025-06-15 21:14:09,565 - APS-System - INFO - MySQL 数据库检查成功
2025-06-15 21:14:09,565 - APS-System - INFO - 正在修复数据库权限...
2025-06-15 21:14:09,566 - APS-System - INFO - 数据库文件不存在，跳过: instance\aps.db
2025-06-15 21:14:09,566 - APS-System - INFO - 数据库文件不存在，跳过: instance\aps_system.db
2025-06-15 21:14:09,567 - APS-System - INFO - 已修复日志目录权限: logs
2025-06-15 21:14:09,567 - APS-System - INFO - 数据库权限修复成功
2025-06-15 21:14:11,800 - app - INFO - 应用启动
2025-06-15 21:14:11,802 - app.utils.scheduler - INFO - 已重新加载 0 个邮箱配置（应用实例未设置）
2025-06-15 21:14:11,819 - app.utils.scheduler - INFO - 邮件附件定时调度器已启动
2025-06-15 21:14:11,833 - app - INFO - 邮件附件定时任务调度器已启动
2025-06-15 21:14:11,874 - app.utils.scheduler - INFO - 全局定时任务已禁用，邮件调度器跟随禁用
2025-06-15 21:14:11,876 - app.utils.scheduler - INFO - 检测到全局定时任务已禁用，停止邮件调度器
2025-06-15 21:14:12,018 - app.services.scheduler_service - INFO - APScheduler调度器配置完成: timezone=Asia/Shanghai, max_workers=10
2025-06-15 21:14:12,041 - apscheduler.scheduler - INFO - Scheduler started
2025-06-15 21:14:12,047 - app.services.scheduler_service - INFO - 从数据库加载了 0 个任务
2025-06-15 21:14:12,049 - app.services.scheduler_service - INFO - APScheduler调度器启动成功
2025-06-15 21:14:12,050 - app - INFO - ✅ APScheduler统一调度器启动成功
2025-06-15 21:14:12,052 - APS-System - INFO - 应用创建成功
2025-06-15 21:14:12,052 - APS-System - INFO - ==================================================
2025-06-15 21:14:12,053 - APS-System - INFO - 🚀 APS车规芯片终测智能调度平台 v2.1
2025-06-15 21:14:12,053 - APS-System - INFO - 📊 100% MySQL数据库架构
2025-06-15 21:14:12,053 - APS-System - INFO - 🌐 访问地址: http://1********:5000
2025-06-15 21:14:12,053 - APS-System - INFO - ==================================================
2025-06-15 21:14:12,053 - APS-System - INFO - 运行模式: 开发环境
2025-06-15 21:14:12,068 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://1********:5000
2025-06-15 21:14:12,069 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-15 21:14:14,124 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:14:14] "GET / HTTP/1.1" 200 -
2025-06-15 21:14:14,157 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:14:14] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 21:14:14,386 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:14:14] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 21:14:14,466 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:14:14] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 21:14:14,468 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:14:14] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 21:14:14,468 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:14:14] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 21:14:14,878 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:14:14] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 21:14:18,481 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:14:18] "GET /production/semi-auto HTTP/1.1" 200 -
2025-06-15 21:14:18,773 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:14:18] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 21:14:18,840 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:14:18] "GET /static/css/core/main.css HTTP/1.1" 200 -
2025-06-15 21:14:18,843 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:14:18] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 21:14:18,846 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:14:18] "GET /static/css/daygrid/main.css HTTP/1.1" 200 -
2025-06-15 21:14:18,854 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:14:18] "GET /static/css/timegrid/main.css HTTP/1.1" 200 -
2025-06-15 21:14:18,858 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:14:18] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 21:14:19,089 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:14:19] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 21:14:19,153 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:14:19] "GET /static/js/xlsx.full.min.js HTTP/1.1" 200 -
2025-06-15 21:14:19,158 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:14:19] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 21:14:19,182 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:14:19] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 21:14:19,560 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:14:19] "GET /api/production/get-import-path HTTP/1.1" 200 -
2025-06-15 21:14:19,576 - app - INFO - 成功连接到MySQL数据库: aps
2025-06-15 21:14:19,726 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:14:19] "GET /api/production/imported-files HTTP/1.1" 200 -
2025-06-15 21:14:20,123 - app.api.production_api - INFO - 开始执行自动排产: {'algorithm': 'intelligent', 'optimization_target': 'balanced', 'time_limit': 30, 'population_size': 100, 'auto_mode': False}
2025-06-15 21:14:20,142 - app.api.production_api - INFO - 获取到 171 条待排产批次数据
2025-06-15 21:14:20,152 - app.api.production_api - INFO - 加载了 21 条优先级配置
2025-06-15 21:14:20,226 - app.api.production_api - INFO - 获取到 913 台可用设备
2025-06-15 21:14:21,055 - app.api.production_api - INFO - 排产算法 intelligent 完成，生成 171 条排产记录
2025-06-15 21:14:21,057 - app.api.production_api - INFO - 自动排产完成: 171 条记录，耗时 0.93秒
2025-06-15 21:14:21,106 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:14:21] "POST /api/production/auto-schedule HTTP/1.1" 200 -
2025-06-15 21:14:25,314 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:14:25] "GET /production/algorithm HTTP/1.1" 200 -
2025-06-15 21:14:25,392 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:14:25] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 21:14:25,548 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:14:25] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 21:14:25,679 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:14:25] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 21:14:25,680 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:14:25] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 21:14:25,684 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:14:25] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 21:14:25,691 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:14:25] "[36mGET /static/js/xlsx.full.min.js HTTP/1.1[0m" 304 -
2025-06-15 21:14:26,006 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:14:26] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 21:14:26,077 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:14:26] "[33mGET /api/production/history-times HTTP/1.1[0m" 404 -
2025-06-15 21:14:30,078 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:14:30] "GET /production/priority-matching HTTP/1.1" 200 -
2025-06-15 21:14:30,162 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:14:30] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 21:14:30,319 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:14:30] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 21:14:30,447 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:14:30] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 21:14:30,447 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:14:30] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 21:14:30,452 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:14:30] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 21:14:30,461 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:14:30] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 21:15:33,636 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:15:33] "GET /production/priority-matching HTTP/1.1" 200 -
2025-06-15 21:15:33,668 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:15:33] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 21:15:33,897 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:15:33] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 21:15:33,976 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:15:33] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 21:15:33,977 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:15:33] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 21:15:33,977 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:15:33] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 21:15:34,302 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:15:34] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 21:15:36,725 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:15:36] "GET /production/priority-matching HTTP/1.1" 200 -
2025-06-15 21:15:37,027 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:15:37] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 21:15:37,077 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:15:37] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 21:15:37,078 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:15:37] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 21:15:37,078 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:15:37] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 21:15:37,083 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:15:37] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 21:15:37,436 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:15:37] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 21:17:20,515 - APS-System - INFO - 应用路径: D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12
2025-06-15 21:17:20,516 - APS-System - INFO - 实例路径: D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\instance
2025-06-15 21:17:20,516 - APS-System - INFO - 检查 MySQL 数据库配置...
2025-06-15 21:17:20,574 - APS-System - INFO - MySQL 数据库 'aps' 已存在
2025-06-15 21:17:20,575 - APS-System - INFO - MySQL 数据库 'aps_system' 已存在
2025-06-15 21:17:20,575 - APS-System - INFO - MySQL 数据库检查成功
2025-06-15 21:17:20,576 - APS-System - INFO - 正在修复数据库权限...
2025-06-15 21:17:20,576 - APS-System - INFO - 数据库文件不存在，跳过: instance\aps.db
2025-06-15 21:17:20,577 - APS-System - INFO - 数据库文件不存在，跳过: instance\aps_system.db
2025-06-15 21:17:20,577 - APS-System - INFO - 已修复日志目录权限: logs
2025-06-15 21:17:20,577 - APS-System - INFO - 数据库权限修复成功
2025-06-15 21:17:22,735 - app - INFO - 应用启动
2025-06-15 21:17:22,736 - app.utils.scheduler - INFO - 已重新加载 0 个邮箱配置（应用实例未设置）
2025-06-15 21:17:22,753 - app.utils.scheduler - INFO - 邮件附件定时调度器已启动
2025-06-15 21:17:22,769 - app - INFO - 邮件附件定时任务调度器已启动
2025-06-15 21:17:22,798 - app.utils.scheduler - INFO - 全局定时任务已禁用，邮件调度器跟随禁用
2025-06-15 21:17:22,806 - app.utils.scheduler - INFO - 检测到全局定时任务已禁用，停止邮件调度器
2025-06-15 21:17:23,015 - app.services.scheduler_service - INFO - APScheduler调度器配置完成: timezone=Asia/Shanghai, max_workers=10
2025-06-15 21:17:23,029 - apscheduler.scheduler - INFO - Scheduler started
2025-06-15 21:17:23,035 - app.services.scheduler_service - INFO - 从数据库加载了 0 个任务
2025-06-15 21:17:23,036 - app.services.scheduler_service - INFO - APScheduler调度器启动成功
2025-06-15 21:17:23,036 - app - INFO - ✅ APScheduler统一调度器启动成功
2025-06-15 21:17:23,038 - APS-System - INFO - 应用创建成功
2025-06-15 21:17:23,038 - APS-System - INFO - ==================================================
2025-06-15 21:17:23,038 - APS-System - INFO - 🚀 APS车规芯片终测智能调度平台 v2.1
2025-06-15 21:17:23,039 - APS-System - INFO - 📊 100% MySQL数据库架构
2025-06-15 21:17:23,039 - APS-System - INFO - 🌐 访问地址: http://1********:5000
2025-06-15 21:17:23,039 - APS-System - INFO - ==================================================
2025-06-15 21:17:23,039 - APS-System - INFO - 运行模式: 开发环境
2025-06-15 21:17:23,055 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://1********:5000
2025-06-15 21:17:23,056 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-15 21:17:25,095 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:17:25] "GET / HTTP/1.1" 200 -
2025-06-15 21:17:25,119 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:17:25] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 21:17:25,340 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:17:25] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 21:17:25,434 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:17:25] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 21:17:25,436 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:17:25] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 21:17:25,437 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:17:25] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 21:17:25,833 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:17:25] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 21:17:28,712 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:17:28] "GET /production/priority-matching HTTP/1.1" 200 -
2025-06-15 21:17:29,016 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:17:29] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 21:17:29,080 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:17:29] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 21:17:29,081 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:17:29] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 21:17:29,083 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:17:29] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 21:17:29,085 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:17:29] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 21:17:29,405 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:17:29] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 21:17:48,174 - app.api.priority_matching_api - INFO - 开始批次匹配: strategy=intelligent, threshold=0.5
2025-06-15 21:17:48,176 - app.services.priority_matching_service - INFO - 开始批次匹配，策略: intelligent, 置信度阈值: 0.5
2025-06-15 21:17:48,196 - app.services.priority_matching_service - INFO - 获取到 171 个待排产批次
2025-06-15 21:17:48,205 - app.services.priority_matching_service - INFO - 获取到 21 个优先级配置
2025-06-15 21:17:48,896 - app.services.priority_matching_service - INFO - 匹配完成: 总批次 171, 成功匹配 171, 匹配率 100.0%
2025-06-15 21:17:49,183 - app.api.priority_matching_api - INFO - 批次匹配完成: 总数=171, 匹配率=100.0%
2025-06-15 21:17:49,190 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:17:49] "POST /api/priority/match-batches HTTP/1.1" 200 -
2025-06-15 21:18:09,255 - app.api.production_api - INFO - 接收到文件上传请求: 文件名=devicepriorityconfig.xlsx, 类型=devicepriorityconfig, 策略=skip
2025-06-15 21:18:10,001 - app.api.production_api - INFO - 读取Excel文件成功，共 5 行数据
2025-06-15 21:18:10,066 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:18:10] "POST /api/production/import-priority-excel HTTP/1.1" 200 -
2025-06-15 21:18:10,082 - app.api.production_api - INFO - 接收到文件上传请求: 文件名=lotpriorityconfig.xlsx, 类型=lotpriorityconfig, 策略=skip
2025-06-15 21:18:10,118 - app.api.production_api - INFO - 读取Excel文件成功，共 5 行数据
2025-06-15 21:18:10,135 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:18:10] "POST /api/production/import-priority-excel HTTP/1.1" 200 -
2025-06-15 21:18:18,090 - app.api.production_api - INFO - 接收到文件上传请求: 文件名=devicepriorityconfig.xlsx, 类型=devicepriorityconfig, 策略=replace
2025-06-15 21:18:18,126 - app.api.production_api - INFO - 读取Excel文件成功，共 5 行数据
2025-06-15 21:18:18,173 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:18:18] "POST /api/production/import-priority-excel HTTP/1.1" 200 -
2025-06-15 21:18:18,355 - app.api.production_api - INFO - 接收到文件上传请求: 文件名=lotpriorityconfig.xlsx, 类型=lotpriorityconfig, 策略=replace
2025-06-15 21:18:18,394 - app.api.production_api - INFO - 读取Excel文件成功，共 5 行数据
2025-06-15 21:18:18,425 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:18:18] "POST /api/production/import-priority-excel HTTP/1.1" 200 -
2025-06-15 21:18:21,131 - app.api.priority_matching_api - INFO - 开始批次匹配: strategy=intelligent, threshold=0.5
2025-06-15 21:18:21,132 - app.services.priority_matching_service - INFO - 开始批次匹配，策略: intelligent, 置信度阈值: 0.5
2025-06-15 21:18:21,146 - app.services.priority_matching_service - INFO - 获取到 171 个待排产批次
2025-06-15 21:18:21,152 - app.services.priority_matching_service - INFO - 获取到 21 个优先级配置
2025-06-15 21:18:21,154 - app.services.priority_matching_service - INFO - 匹配完成: 总批次 171, 成功匹配 171, 匹配率 100.0%
2025-06-15 21:18:21,521 - app.api.priority_matching_api - INFO - 批次匹配完成: 总数=171, 匹配率=100.0%
2025-06-15 21:18:21,527 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:18:21] "POST /api/priority/match-batches HTTP/1.1" 200 -
2025-06-15 21:23:10,640 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:23:10] "GET /production/priority-matching HTTP/1.1" 200 -
2025-06-15 21:23:10,680 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:23:10] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 21:23:10,893 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:23:10] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 21:23:10,987 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:23:10] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 21:23:10,988 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:23:10] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 21:23:10,989 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:23:10] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 21:23:11,313 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:23:11] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 21:26:15,113 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:26:15] "[32mGET /index HTTP/1.1[0m" 302 -
2025-06-15 21:26:15,121 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:26:15] "GET /auth/login?next=/index HTTP/1.1" 200 -
2025-06-15 21:26:15,372 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:26:15] "GET /static/css/login/bootstrap.min.css HTTP/1.1" 200 -
2025-06-15 21:26:15,495 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:26:15] "GET /static/css/all-5.15.4.min.css HTTP/1.1" 200 -
2025-06-15 21:26:15,496 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:26:15] "GET /static/css/compatibility.css HTTP/1.1" 200 -
2025-06-15 21:26:15,496 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:26:15] "GET /static/js/login/bootstrap.bundle.min.js HTTP/1.1" 200 -
2025-06-15 21:26:15,868 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:26:15] "GET /static/webfonts/fa-solid-900.woff2 HTTP/1.1" 200 -
2025-06-15 21:26:16,495 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:26:16] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-06-15 21:26:17,296 - app.auth.routes - INFO - 尝试登录用户: admin
2025-06-15 21:26:17,302 - app.auth.routes - INFO - 找到用户: admin, 角色: admin, 密码哈希: scrypt:32768:8:1$X8JU5hxR74hpH0tP$36e5b028b335f5d2f9f21e9178a0d529cca80459428b1128d44d8ac646d8f16cdd0823ea12d58ce5bbae1dd243eeb7a50c06f55d6688542dae997f7c75e6ee24
2025-06-15 21:26:17,437 - app.models - INFO - 用户 admin 密码验证结果: True
2025-06-15 21:26:17,438 - app.auth.routes - INFO - 密码验证结果: True
2025-06-15 21:26:17,438 - app.auth.routes - INFO - 用户 admin 登录成功
2025-06-15 21:26:17,453 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:26:17] "[32mPOST /auth/login HTTP/1.1[0m" 302 -
2025-06-15 21:26:17,468 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:26:17] "GET /index HTTP/1.1" 200 -
2025-06-15 21:26:17,553 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:26:17] "GET /static/css/base/bootstrap.min.css HTTP/1.1" 200 -
2025-06-15 21:26:17,819 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:26:17] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 21:26:17,820 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:26:17] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 21:26:17,823 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:26:17] "GET /static/js/base/bootstrap.bundle.min.js HTTP/1.1" 200 -
2025-06-15 21:26:17,825 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:26:17] "GET /static/js/menu-optimizer.js HTTP/1.1" 200 -
2025-06-15 21:26:18,151 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:26:18] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 21:26:18,155 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:26:18] "GET /menu HTTP/1.1" 200 -
2025-06-15 21:26:20,021 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:26:20] "GET /production/priority-matching HTTP/1.1" 200 -
2025-06-15 21:26:20,335 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:26:20] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 21:26:20,379 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:26:20] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 21:26:20,380 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:26:20] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 21:26:20,380 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:26:20] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 21:26:20,381 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:26:20] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 21:35:13,895 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:35:13] "[32mGET /index HTTP/1.1[0m" 302 -
2025-06-15 21:35:13,911 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:35:13] "GET /auth/login?next=/index HTTP/1.1" 200 -
2025-06-15 21:35:14,147 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:35:14] "GET /static/css/login/bootstrap.min.css HTTP/1.1" 200 -
2025-06-15 21:35:14,271 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:35:14] "GET /static/css/all-5.15.4.min.css HTTP/1.1" 200 -
2025-06-15 21:35:14,275 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:35:14] "GET /static/css/compatibility.css HTTP/1.1" 200 -
2025-06-15 21:35:14,275 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:35:14] "GET /static/js/login/bootstrap.bundle.min.js HTTP/1.1" 200 -
2025-06-15 21:35:14,635 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:35:14] "GET /static/webfonts/fa-solid-900.woff2 HTTP/1.1" 200 -
2025-06-15 21:35:15,299 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:35:15] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-06-15 21:35:16,069 - app.auth.routes - INFO - 尝试登录用户: admin
2025-06-15 21:35:16,074 - app.auth.routes - INFO - 找到用户: admin, 角色: admin, 密码哈希: scrypt:32768:8:1$X8JU5hxR74hpH0tP$36e5b028b335f5d2f9f21e9178a0d529cca80459428b1128d44d8ac646d8f16cdd0823ea12d58ce5bbae1dd243eeb7a50c06f55d6688542dae997f7c75e6ee24
2025-06-15 21:35:16,208 - app.models - INFO - 用户 admin 密码验证结果: True
2025-06-15 21:35:16,209 - app.auth.routes - INFO - 密码验证结果: True
2025-06-15 21:35:16,210 - app.auth.routes - INFO - 用户 admin 登录成功
2025-06-15 21:35:16,223 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:35:16] "[32mPOST /auth/login HTTP/1.1[0m" 302 -
2025-06-15 21:35:16,233 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:35:16] "GET /index HTTP/1.1" 200 -
2025-06-15 21:35:16,329 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:35:16] "GET /static/css/base/bootstrap.min.css HTTP/1.1" 200 -
2025-06-15 21:35:16,577 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:35:16] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 21:35:16,579 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:35:16] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 21:35:16,586 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:35:16] "GET /static/js/base/bootstrap.bundle.min.js HTTP/1.1" 200 -
2025-06-15 21:35:16,586 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:35:16] "GET /static/js/menu-optimizer.js HTTP/1.1" 200 -
2025-06-15 21:35:16,902 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:35:16] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 21:35:16,932 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:35:16] "GET /menu HTTP/1.1" 200 -
2025-06-15 21:35:19,415 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:35:19] "GET /production/priority-matching HTTP/1.1" 200 -
2025-06-15 21:35:19,727 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:35:19] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 21:35:19,779 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:35:19] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 21:35:19,780 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:35:19] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 21:35:19,782 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:35:19] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 21:35:19,782 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:35:19] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 21:45:17,112 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:45:17] "GET /production/semi-auto HTTP/1.1" 200 -
2025-06-15 21:45:17,203 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:45:17] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 21:45:17,373 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:45:17] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 21:45:17,466 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:45:17] "GET /static/css/core/main.css HTTP/1.1" 200 -
2025-06-15 21:45:17,471 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:45:17] "GET /static/css/daygrid/main.css HTTP/1.1" 200 -
2025-06-15 21:45:17,473 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:45:17] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 21:45:17,473 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:45:17] "GET /static/css/timegrid/main.css HTTP/1.1" 200 -
2025-06-15 21:45:17,511 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:45:17] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 21:45:17,698 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:45:17] "GET /static/js/xlsx.full.min.js HTTP/1.1" 200 -
2025-06-15 21:45:17,778 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:45:17] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 21:45:17,782 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:45:17] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 21:45:18,145 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:45:18] "GET /api/production/get-import-path HTTP/1.1" 200 -
2025-06-15 21:45:18,150 - app - INFO - 成功连接到MySQL数据库: aps
2025-06-15 21:45:18,164 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:45:18] "GET /api/production/imported-files HTTP/1.1" 200 -
2025-06-15 21:46:29,034 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:46:29] "GET /production/priority-matching HTTP/1.1" 200 -
2025-06-15 21:46:29,103 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:46:29] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 21:46:29,292 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:46:29] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 21:46:29,396 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:46:29] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 21:46:29,397 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:46:29] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 21:46:29,398 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:46:29] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 21:46:29,420 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:46:29] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 21:46:36,356 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:46:36] "GET /production/semi-auto HTTP/1.1" 200 -
2025-06-15 21:46:36,459 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:46:36] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 21:46:36,616 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:46:36] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 21:46:36,709 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:46:36] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 21:46:36,713 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:46:36] "[36mGET /static/css/core/main.css HTTP/1.1[0m" 304 -
2025-06-15 21:46:36,713 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:46:36] "[36mGET /static/css/daygrid/main.css HTTP/1.1[0m" 304 -
2025-06-15 21:46:36,714 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:46:36] "[36mGET /static/css/timegrid/main.css HTTP/1.1[0m" 304 -
2025-06-15 21:46:36,769 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:46:36] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 21:46:36,929 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:46:36] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 21:46:37,017 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:46:37] "[36mGET /static/js/xlsx.full.min.js HTTP/1.1[0m" 304 -
2025-06-15 21:46:37,390 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:46:37] "GET /api/production/get-import-path HTTP/1.1" 200 -
2025-06-15 21:46:37,414 - app - INFO - 成功连接到MySQL数据库: aps
2025-06-15 21:46:37,431 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:46:37] "GET /api/production/imported-files HTTP/1.1" 200 -
2025-06-15 21:46:37,994 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:46:37] "GET /production/semi-auto HTTP/1.1" 200 -
2025-06-15 21:46:38,294 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:46:38] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 21:46:38,324 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:46:38] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 21:46:38,329 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:46:38] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 21:46:38,330 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:46:38] "[36mGET /static/css/core/main.css HTTP/1.1[0m" 304 -
2025-06-15 21:46:38,333 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:46:38] "[36mGET /static/css/daygrid/main.css HTTP/1.1[0m" 304 -
2025-06-15 21:46:38,346 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:46:38] "[36mGET /static/css/timegrid/main.css HTTP/1.1[0m" 304 -
2025-06-15 21:46:38,604 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:46:38] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 21:46:38,636 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:46:38] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 21:46:38,641 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:46:38] "[36mGET /static/js/xlsx.full.min.js HTTP/1.1[0m" 304 -
2025-06-15 21:46:38,998 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:46:38] "GET /api/production/get-import-path HTTP/1.1" 200 -
2025-06-15 21:46:39,021 - app - INFO - 成功连接到MySQL数据库: aps
2025-06-15 21:46:39,039 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:46:39] "GET /api/production/imported-files HTTP/1.1" 200 -
2025-06-15 21:46:50,057 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:46:50] "GET /production/priority-matching HTTP/1.1" 200 -
2025-06-15 21:46:50,357 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:46:50] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 21:46:50,404 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:46:50] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 21:46:50,406 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:46:50] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 21:46:50,406 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:46:50] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 21:46:50,408 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:46:50] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 21:47:00,170 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:47:00] "GET /production/semi-auto HTTP/1.1" 200 -
2025-06-15 21:47:00,245 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:47:00] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 21:47:00,417 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:47:00] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 21:47:00,511 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:47:00] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 21:47:00,511 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:47:00] "[36mGET /static/css/daygrid/main.css HTTP/1.1[0m" 304 -
2025-06-15 21:47:00,512 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:47:00] "[36mGET /static/css/core/main.css HTTP/1.1[0m" 304 -
2025-06-15 21:47:00,514 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:47:00] "[36mGET /static/css/timegrid/main.css HTTP/1.1[0m" 304 -
2025-06-15 21:47:00,553 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:47:00] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 21:47:00,736 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:47:00] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 21:47:00,832 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:47:00] "[36mGET /static/js/xlsx.full.min.js HTTP/1.1[0m" 304 -
2025-06-15 21:47:01,190 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:47:01] "GET /api/production/get-import-path HTTP/1.1" 200 -
2025-06-15 21:47:01,191 - app - INFO - 成功连接到MySQL数据库: aps
2025-06-15 21:47:01,214 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:47:01] "GET /api/production/imported-files HTTP/1.1" 200 -
2025-06-15 21:49:04,336 - APS-System - INFO - 应用路径: D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12
2025-06-15 21:49:04,337 - APS-System - INFO - 实例路径: D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\instance
2025-06-15 21:49:04,337 - APS-System - INFO - 检查 MySQL 数据库配置...
2025-06-15 21:49:04,372 - APS-System - INFO - MySQL 数据库 'aps' 已存在
2025-06-15 21:49:04,373 - APS-System - INFO - MySQL 数据库 'aps_system' 已存在
2025-06-15 21:49:04,373 - APS-System - INFO - MySQL 数据库检查成功
2025-06-15 21:49:04,373 - APS-System - INFO - 正在修复数据库权限...
2025-06-15 21:49:04,374 - APS-System - INFO - 数据库文件不存在，跳过: instance\aps.db
2025-06-15 21:49:04,374 - APS-System - INFO - 数据库文件不存在，跳过: instance\aps_system.db
2025-06-15 21:49:04,374 - APS-System - INFO - 已修复日志目录权限: logs
2025-06-15 21:49:04,375 - APS-System - INFO - 数据库权限修复成功
2025-06-15 21:49:05,890 - app - INFO - 应用启动
2025-06-15 21:49:05,891 - app.utils.scheduler - INFO - 已重新加载 0 个邮箱配置（应用实例未设置）
2025-06-15 21:49:05,904 - app.utils.scheduler - INFO - 邮件附件定时调度器已启动
2025-06-15 21:49:05,921 - app - INFO - 邮件附件定时任务调度器已启动
2025-06-15 21:49:05,927 - app.utils.scheduler - INFO - 全局定时任务已禁用，邮件调度器跟随禁用
2025-06-15 21:49:05,929 - app.utils.scheduler - INFO - 检测到全局定时任务已禁用，停止邮件调度器
2025-06-15 21:49:06,081 - app.services.scheduler_service - INFO - APScheduler调度器配置完成: timezone=Asia/Shanghai, max_workers=10
2025-06-15 21:49:06,110 - apscheduler.scheduler - INFO - Scheduler started
2025-06-15 21:49:06,113 - app.services.scheduler_service - INFO - 从数据库加载了 0 个任务
2025-06-15 21:49:06,118 - app.services.scheduler_service - INFO - APScheduler调度器启动成功
2025-06-15 21:49:06,124 - app - INFO - ✅ APScheduler统一调度器启动成功
2025-06-15 21:49:06,131 - APS-System - INFO - 应用创建成功
2025-06-15 21:49:06,131 - APS-System - INFO - ==================================================
2025-06-15 21:49:06,131 - APS-System - INFO - 🚀 APS车规芯片终测智能调度平台 v2.1
2025-06-15 21:49:06,131 - APS-System - INFO - 📊 100% MySQL数据库架构
2025-06-15 21:49:06,131 - APS-System - INFO - 🌐 访问地址: http://1********:5000
2025-06-15 21:49:06,131 - APS-System - INFO - ==================================================
2025-06-15 21:49:06,131 - APS-System - INFO - 运行模式: 开发环境
2025-06-15 21:49:06,151 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://1********:5000
2025-06-15 21:49:06,151 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-15 21:49:08,179 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:49:08] "GET / HTTP/1.1" 200 -
2025-06-15 21:49:08,205 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:49:08] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 21:49:08,438 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:49:08] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 21:49:08,516 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:49:08] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 21:49:08,517 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:49:08] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 21:49:08,517 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:49:08] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 21:49:08,885 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:49:08] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 21:49:11,392 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:49:11] "GET /production/priority-matching HTTP/1.1" 200 -
2025-06-15 21:49:11,699 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:49:11] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 21:49:11,732 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:49:11] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 21:49:11,732 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:49:11] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 21:49:11,733 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:49:11] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 21:49:11,734 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:49:11] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 21:49:12,052 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:49:12] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 21:49:31,714 - APS-System - INFO - 应用路径: D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12
2025-06-15 21:49:31,715 - APS-System - INFO - 实例路径: D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\instance
2025-06-15 21:49:31,716 - APS-System - INFO - 检查 MySQL 数据库配置...
2025-06-15 21:49:31,751 - APS-System - INFO - MySQL 数据库 'aps' 已存在
2025-06-15 21:49:31,751 - APS-System - INFO - MySQL 数据库 'aps_system' 已存在
2025-06-15 21:49:31,753 - APS-System - INFO - MySQL 数据库检查成功
2025-06-15 21:49:31,753 - APS-System - INFO - 正在修复数据库权限...
2025-06-15 21:49:31,753 - APS-System - INFO - 数据库文件不存在，跳过: instance\aps.db
2025-06-15 21:49:31,754 - APS-System - INFO - 数据库文件不存在，跳过: instance\aps_system.db
2025-06-15 21:49:31,754 - APS-System - INFO - 已修复日志目录权限: logs
2025-06-15 21:49:31,754 - APS-System - INFO - 数据库权限修复成功
2025-06-15 21:49:33,344 - app - INFO - 应用启动
2025-06-15 21:49:33,350 - app.utils.scheduler - INFO - 已重新加载 0 个邮箱配置（应用实例未设置）
2025-06-15 21:49:33,375 - app.utils.scheduler - INFO - 邮件附件定时调度器已启动
2025-06-15 21:49:33,396 - app.utils.scheduler - INFO - 全局定时任务已禁用，邮件调度器跟随禁用
2025-06-15 21:49:33,398 - app - INFO - 邮件附件定时任务调度器已启动
2025-06-15 21:49:33,403 - app.utils.scheduler - INFO - 检测到全局定时任务已禁用，停止邮件调度器
2025-06-15 21:49:33,522 - app.services.scheduler_service - INFO - APScheduler调度器配置完成: timezone=Asia/Shanghai, max_workers=10
2025-06-15 21:49:33,533 - apscheduler.scheduler - INFO - Scheduler started
2025-06-15 21:49:33,536 - app.services.scheduler_service - INFO - 从数据库加载了 0 个任务
2025-06-15 21:49:33,541 - app.services.scheduler_service - INFO - APScheduler调度器启动成功
2025-06-15 21:49:33,543 - app - INFO - ✅ APScheduler统一调度器启动成功
2025-06-15 21:49:33,548 - APS-System - INFO - 应用创建成功
2025-06-15 21:49:33,548 - APS-System - INFO - ==================================================
2025-06-15 21:49:33,549 - APS-System - INFO - 🚀 APS车规芯片终测智能调度平台 v2.1
2025-06-15 21:49:33,549 - APS-System - INFO - 📊 100% MySQL数据库架构
2025-06-15 21:49:33,549 - APS-System - INFO - 🌐 访问地址: http://1********:5000
2025-06-15 21:49:33,549 - APS-System - INFO - ==================================================
2025-06-15 21:49:33,550 - APS-System - INFO - 运行模式: 开发环境
2025-06-15 21:49:33,564 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://1********:5000
2025-06-15 21:49:33,564 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-15 21:49:35,632 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:49:35] "[32mGET / HTTP/1.1[0m" 302 -
2025-06-15 21:49:35,639 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:49:35] "GET /auth/login?next=/ HTTP/1.1" 200 -
2025-06-15 21:49:35,901 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:49:35] "GET /static/css/login/bootstrap.min.css HTTP/1.1" 200 -
2025-06-15 21:49:35,978 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:49:35] "GET /static/css/all-5.15.4.min.css HTTP/1.1" 200 -
2025-06-15 21:49:35,979 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:49:35] "GET /static/css/compatibility.css HTTP/1.1" 200 -
2025-06-15 21:49:35,980 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:49:35] "GET /static/js/login/bootstrap.bundle.min.js HTTP/1.1" 200 -
2025-06-15 21:49:36,315 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:49:36] "GET /static/webfonts/fa-solid-900.woff2 HTTP/1.1" 200 -
2025-06-15 21:49:36,742 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:49:36] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-06-15 21:49:37,564 - app.auth.routes - INFO - 尝试登录用户: admin
2025-06-15 21:49:37,568 - app.auth.routes - INFO - 找到用户: admin, 角色: admin, 密码哈希: scrypt:32768:8:1$X8JU5hxR74hpH0tP$36e5b028b335f5d2f9f21e9178a0d529cca80459428b1128d44d8ac646d8f16cdd0823ea12d58ce5bbae1dd243eeb7a50c06f55d6688542dae997f7c75e6ee24
2025-06-15 21:49:37,653 - app.models - INFO - 用户 admin 密码验证结果: True
2025-06-15 21:49:37,654 - app.auth.routes - INFO - 密码验证结果: True
2025-06-15 21:49:37,655 - app.auth.routes - INFO - 用户 admin 登录成功
2025-06-15 21:49:37,659 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:49:37] "[32mPOST /auth/login HTTP/1.1[0m" 302 -
2025-06-15 21:49:37,667 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:49:37] "GET /index HTTP/1.1" 200 -
2025-06-15 21:49:37,808 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:49:37] "GET /static/css/base/bootstrap.min.css HTTP/1.1" 200 -
2025-06-15 21:49:38,007 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:49:38] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 21:49:38,008 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:49:38] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 21:49:38,009 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:49:38] "GET /static/js/base/bootstrap.bundle.min.js HTTP/1.1" 200 -
2025-06-15 21:49:38,011 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:49:38] "GET /static/js/menu-optimizer.js HTTP/1.1" 200 -
2025-06-15 21:49:38,330 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:49:38] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 21:49:38,332 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:49:38] "GET /menu HTTP/1.1" 200 -
2025-06-15 21:49:40,751 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:49:40] "GET /production/priority-matching HTTP/1.1" 200 -
2025-06-15 21:49:41,065 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:49:41] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 21:49:41,095 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:49:41] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 21:49:41,097 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:49:41] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 21:49:41,098 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:49:41] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 21:49:41,098 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:49:41] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 21:49:51,763 - app.api.production_api - INFO - 接收到文件上传请求: 文件名=devicepriorityconfig.xlsx, 类型=devicepriorityconfig, 策略=skip
2025-06-15 21:49:51,799 - app.api.production_api - INFO - 读取Excel文件成功，共 5 行数据
2025-06-15 21:49:51,829 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:49:51] "POST /api/production/import-priority-excel HTTP/1.1" 200 -
2025-06-15 21:49:52,026 - app.api.production_api - INFO - 接收到文件上传请求: 文件名=lotpriorityconfig.xlsx, 类型=lotpriorityconfig, 策略=skip
2025-06-15 21:49:52,070 - app.api.production_api - INFO - 读取Excel文件成功，共 5 行数据
2025-06-15 21:49:52,078 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:49:52] "POST /api/production/import-priority-excel HTTP/1.1" 200 -
2025-06-15 21:51:14,798 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:51:14] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 21:51:14,799 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:51:14] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 21:51:14,799 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:51:14] "[33mGET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1[0m" 404 -
2025-06-15 21:51:14,799 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:51:14] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 21:51:15,134 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:51:15] "[33mGET /static/css/base/bootstrap.min.css.map HTTP/1.1[0m" 404 -
2025-06-15 21:56:59,892 - APS-System - INFO - 应用路径: D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12
2025-06-15 21:56:59,892 - APS-System - INFO - 实例路径: D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\instance
2025-06-15 21:56:59,893 - APS-System - INFO - 检查 MySQL 数据库配置...
2025-06-15 21:56:59,928 - APS-System - INFO - MySQL 数据库 'aps' 已存在
2025-06-15 21:56:59,930 - APS-System - INFO - MySQL 数据库 'aps_system' 已存在
2025-06-15 21:56:59,930 - APS-System - INFO - MySQL 数据库检查成功
2025-06-15 21:56:59,930 - APS-System - INFO - 正在修复数据库权限...
2025-06-15 21:56:59,930 - APS-System - INFO - 数据库文件不存在，跳过: instance\aps.db
2025-06-15 21:56:59,931 - APS-System - INFO - 数据库文件不存在，跳过: instance\aps_system.db
2025-06-15 21:56:59,931 - APS-System - INFO - 已修复日志目录权限: logs
2025-06-15 21:56:59,931 - APS-System - INFO - 数据库权限修复成功
2025-06-15 21:57:01,734 - app - INFO - 应用启动
2025-06-15 21:57:01,735 - app.utils.scheduler - INFO - 已重新加载 0 个邮箱配置（应用实例未设置）
2025-06-15 21:57:01,747 - app.utils.scheduler - INFO - 邮件附件定时调度器已启动
2025-06-15 21:57:01,769 - app - INFO - 邮件附件定时任务调度器已启动
2025-06-15 21:57:01,803 - app.utils.scheduler - INFO - 全局定时任务已禁用，邮件调度器跟随禁用
2025-06-15 21:57:01,804 - app.utils.scheduler - INFO - 检测到全局定时任务已禁用，停止邮件调度器
2025-06-15 21:57:01,902 - app.services.scheduler_service - INFO - APScheduler调度器配置完成: timezone=Asia/Shanghai, max_workers=10
2025-06-15 21:57:01,924 - apscheduler.scheduler - INFO - Scheduler started
2025-06-15 21:57:01,928 - app.services.scheduler_service - INFO - 从数据库加载了 0 个任务
2025-06-15 21:57:01,930 - app.services.scheduler_service - INFO - APScheduler调度器启动成功
2025-06-15 21:57:01,931 - app - INFO - ✅ APScheduler统一调度器启动成功
2025-06-15 21:57:01,931 - APS-System - INFO - 应用创建成功
2025-06-15 21:57:01,931 - APS-System - INFO - ==================================================
2025-06-15 21:57:01,931 - APS-System - INFO - 🚀 APS车规芯片终测智能调度平台 v2.1
2025-06-15 21:57:01,931 - APS-System - INFO - 📊 100% MySQL数据库架构
2025-06-15 21:57:01,932 - APS-System - INFO - 🌐 访问地址: http://1********:5000
2025-06-15 21:57:01,932 - APS-System - INFO - ==================================================
2025-06-15 21:57:01,932 - APS-System - INFO - 运行模式: 开发环境
2025-06-15 21:57:01,946 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://1********:5000
2025-06-15 21:57:01,946 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-15 21:57:03,988 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:57:03] "GET / HTTP/1.1" 200 -
2025-06-15 21:57:04,019 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:57:04] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 21:57:04,232 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:57:04] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 21:57:04,328 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:57:04] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 21:57:04,328 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:57:04] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 21:57:04,329 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:57:04] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 21:57:04,709 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:57:04] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 21:57:51,516 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:57:51] "GET /production/priority-matching HTTP/1.1" 200 -
2025-06-15 21:57:51,823 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:57:51] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 21:57:51,857 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:57:51] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 21:57:51,858 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:57:51] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 21:57:51,863 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:57:51] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 21:57:51,865 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:57:51] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 21:57:52,191 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:57:52] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 21:57:59,445 - app.api.production_api - INFO - 接收到文件上传请求: 文件名=devicepriorityconfig.xlsx, 类型=devicepriorityconfig, 策略=skip
2025-06-15 21:57:59,477 - app.api.production_api - INFO - 读取Excel文件成功，共 5 行数据
2025-06-15 21:57:59,492 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:57:59] "POST /api/production/import-priority-excel HTTP/1.1" 200 -
2025-06-15 21:57:59,815 - app.api.production_api - INFO - 接收到文件上传请求: 文件名=lotpriorityconfig.xlsx, 类型=lotpriorityconfig, 策略=skip
2025-06-15 21:57:59,846 - app.api.production_api - INFO - 读取Excel文件成功，共 5 行数据
2025-06-15 21:57:59,880 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:57:59] "POST /api/production/import-priority-excel HTTP/1.1" 200 -
2025-06-15 21:58:04,368 - app.api.production_api - INFO - 接收到文件上传请求: 文件名=devicepriorityconfig.xlsx, 类型=devicepriorityconfig, 策略=replace
2025-06-15 21:58:04,405 - app.api.production_api - INFO - 读取Excel文件成功，共 5 行数据
2025-06-15 21:58:04,424 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:58:04] "POST /api/production/import-priority-excel HTTP/1.1" 200 -
2025-06-15 21:58:04,746 - app.api.production_api - INFO - 接收到文件上传请求: 文件名=lotpriorityconfig.xlsx, 类型=lotpriorityconfig, 策略=replace
2025-06-15 21:58:04,764 - app.api.production_api - INFO - 读取Excel文件成功，共 5 行数据
2025-06-15 21:58:04,791 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:58:04] "POST /api/production/import-priority-excel HTTP/1.1" 200 -
2025-06-15 21:58:52,873 - APS-System - INFO - 应用路径: D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12
2025-06-15 21:58:52,874 - APS-System - INFO - 实例路径: D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\instance
2025-06-15 21:58:52,875 - APS-System - INFO - 检查 MySQL 数据库配置...
2025-06-15 21:58:52,912 - APS-System - INFO - MySQL 数据库 'aps' 已存在
2025-06-15 21:58:52,913 - APS-System - INFO - MySQL 数据库 'aps_system' 已存在
2025-06-15 21:58:52,913 - APS-System - INFO - MySQL 数据库检查成功
2025-06-15 21:58:52,914 - APS-System - INFO - 正在修复数据库权限...
2025-06-15 21:58:52,914 - APS-System - INFO - 数据库文件不存在，跳过: instance\aps.db
2025-06-15 21:58:52,914 - APS-System - INFO - 数据库文件不存在，跳过: instance\aps_system.db
2025-06-15 21:58:52,915 - APS-System - INFO - 已修复日志目录权限: logs
2025-06-15 21:58:52,915 - APS-System - INFO - 数据库权限修复成功
2025-06-15 21:58:54,505 - app - INFO - 应用启动
2025-06-15 21:58:54,506 - app.utils.scheduler - INFO - 已重新加载 0 个邮箱配置（应用实例未设置）
2025-06-15 21:58:54,515 - app.utils.scheduler - INFO - 邮件附件定时调度器已启动
2025-06-15 21:58:54,550 - app.utils.scheduler - INFO - 全局定时任务已禁用，邮件调度器跟随禁用
2025-06-15 21:58:54,552 - app - INFO - 邮件附件定时任务调度器已启动
2025-06-15 21:58:54,558 - app.utils.scheduler - INFO - 检测到全局定时任务已禁用，停止邮件调度器
2025-06-15 21:58:54,682 - app.services.scheduler_service - INFO - APScheduler调度器配置完成: timezone=Asia/Shanghai, max_workers=10
2025-06-15 21:58:54,719 - apscheduler.scheduler - INFO - Scheduler started
2025-06-15 21:58:54,724 - app.services.scheduler_service - INFO - 从数据库加载了 0 个任务
2025-06-15 21:58:54,729 - app.services.scheduler_service - INFO - APScheduler调度器启动成功
2025-06-15 21:58:54,732 - app - INFO - ✅ APScheduler统一调度器启动成功
2025-06-15 21:58:54,736 - APS-System - INFO - 应用创建成功
2025-06-15 21:58:54,736 - APS-System - INFO - ==================================================
2025-06-15 21:58:54,736 - APS-System - INFO - 🚀 APS车规芯片终测智能调度平台 v2.1
2025-06-15 21:58:54,736 - APS-System - INFO - 📊 100% MySQL数据库架构
2025-06-15 21:58:54,736 - APS-System - INFO - 🌐 访问地址: http://1********:5000
2025-06-15 21:58:54,737 - APS-System - INFO - ==================================================
2025-06-15 21:58:54,737 - APS-System - INFO - 运行模式: 开发环境
2025-06-15 21:58:54,756 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://1********:5000
2025-06-15 21:58:54,756 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-15 21:58:56,428 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:58:56] "GET / HTTP/1.1" 200 -
2025-06-15 21:58:56,728 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:58:56] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 21:58:56,791 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:58:56] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 21:58:56,792 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:58:56] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 21:58:56,792 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:58:56] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 21:58:56,793 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:58:56] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 21:58:57,149 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:58:57] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 21:58:58,813 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:58:58] "GET /production/priority-matching HTTP/1.1" 200 -
2025-06-15 21:58:59,127 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:58:59] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 21:58:59,144 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:58:59] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 21:58:59,144 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:58:59] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 21:58:59,145 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:58:59] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 21:58:59,145 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:58:59] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 21:58:59,481 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:58:59] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 21:59:23,923 - app.api.production_api - INFO - 接收到文件上传请求: 文件名=devicepriorityconfig.xlsx, 类型=devicepriorityconfig, 策略=replace
2025-06-15 21:59:23,955 - app.api.production_api - INFO - 读取Excel文件成功，共 5 行数据
2025-06-15 21:59:23,969 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:59:23] "POST /api/production/import-priority-excel HTTP/1.1" 200 -
2025-06-15 21:59:24,283 - app.api.production_api - INFO - 接收到文件上传请求: 文件名=lotpriorityconfig.xlsx, 类型=lotpriorityconfig, 策略=replace
2025-06-15 21:59:24,302 - app.api.production_api - INFO - 读取Excel文件成功，共 5 行数据
2025-06-15 21:59:24,336 - werkzeug - INFO - 1******** - - [15/Jun/2025 21:59:24] "POST /api/production/import-priority-excel HTTP/1.1" 200 -
2025-06-15 22:00:34,135 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:00:34] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 22:00:34,136 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:00:34] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 22:00:34,137 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:00:34] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 22:00:34,138 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:00:34] "[33mGET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1[0m" 404 -
2025-06-15 22:00:34,448 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:00:34] "[33mGET /static/css/base/bootstrap.min.css.map HTTP/1.1[0m" 404 -
2025-06-15 22:02:12,245 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:02:12] "[33mGET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1[0m" 404 -
2025-06-15 22:02:12,292 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:02:12] "[33mGET /static/css/base/bootstrap.min.css.map HTTP/1.1[0m" 404 -
2025-06-15 22:02:17,525 - app.api.production_api - INFO - 接收到文件上传请求: 文件名=devicepriorityconfig.xlsx, 类型=devicepriorityconfig, 策略=update
2025-06-15 22:02:17,555 - app.api.production_api - INFO - 读取Excel文件成功，共 5 行数据
2025-06-15 22:02:17,567 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:02:17] "POST /api/production/import-priority-excel HTTP/1.1" 200 -
2025-06-15 22:02:17,886 - app.api.production_api - INFO - 接收到文件上传请求: 文件名=lotpriorityconfig.xlsx, 类型=lotpriorityconfig, 策略=update
2025-06-15 22:02:17,924 - app.api.production_api - INFO - 读取Excel文件成功，共 5 行数据
2025-06-15 22:02:17,934 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:02:17] "POST /api/production/import-priority-excel HTTP/1.1" 200 -
2025-06-15 22:02:41,958 - app.api.production_api - INFO - 接收到文件上传请求: 文件名=devicepriorityconfig.xlsx, 类型=devicepriorityconfig, 策略=update
2025-06-15 22:02:41,986 - app.api.production_api - INFO - 读取Excel文件成功，共 5 行数据
2025-06-15 22:02:42,020 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:02:42] "POST /api/production/import-priority-excel HTTP/1.1" 200 -
2025-06-15 22:02:42,336 - app.api.production_api - INFO - 接收到文件上传请求: 文件名=lotpriorityconfig.xlsx, 类型=lotpriorityconfig, 策略=update
2025-06-15 22:02:42,373 - app.api.production_api - INFO - 读取Excel文件成功，共 5 行数据
2025-06-15 22:02:42,381 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:02:42] "POST /api/production/import-priority-excel HTTP/1.1" 200 -
2025-06-15 22:03:47,297 - app.api.production_api - INFO - 接收到文件上传请求: 文件名=devicepriorityconfig.xlsx, 类型=devicepriorityconfig, 策略=update
2025-06-15 22:03:47,331 - app.api.production_api - INFO - 读取Excel文件成功，共 6 行数据
2025-06-15 22:03:47,348 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:03:47] "POST /api/production/import-priority-excel HTTP/1.1" 200 -
2025-06-15 22:03:47,546 - app.api.production_api - INFO - 接收到文件上传请求: 文件名=lotpriorityconfig.xlsx, 类型=lotpriorityconfig, 策略=update
2025-06-15 22:03:47,577 - app.api.production_api - INFO - 读取Excel文件成功，共 5 行数据
2025-06-15 22:03:47,586 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:03:47] "POST /api/production/import-priority-excel HTTP/1.1" 200 -
2025-06-15 22:04:12,912 - app.api.production_api - INFO - 接收到文件上传请求: 文件名=devicepriorityconfig.xlsx, 类型=devicepriorityconfig, 策略=replace
2025-06-15 22:04:12,943 - app.api.production_api - INFO - 读取Excel文件成功，共 6 行数据
2025-06-15 22:04:12,962 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:04:12] "POST /api/production/import-priority-excel HTTP/1.1" 200 -
2025-06-15 22:04:13,284 - app.api.production_api - INFO - 接收到文件上传请求: 文件名=lotpriorityconfig.xlsx, 类型=lotpriorityconfig, 策略=replace
2025-06-15 22:04:13,314 - app.api.production_api - INFO - 读取Excel文件成功，共 5 行数据
2025-06-15 22:04:13,329 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:04:13] "POST /api/production/import-priority-excel HTTP/1.1" 200 -
2025-06-15 22:04:24,518 - app.api.production_api - INFO - 接收到文件上传请求: 文件名=devicepriorityconfig.xlsx, 类型=devicepriorityconfig, 策略=replace
2025-06-15 22:04:24,546 - app.api.production_api - INFO - 读取Excel文件成功，共 6 行数据
2025-06-15 22:04:24,577 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:04:24] "POST /api/production/import-priority-excel HTTP/1.1" 200 -
2025-06-15 22:04:24,892 - app.api.production_api - INFO - 接收到文件上传请求: 文件名=lotpriorityconfig.xlsx, 类型=lotpriorityconfig, 策略=replace
2025-06-15 22:04:24,926 - app.api.production_api - INFO - 读取Excel文件成功，共 5 行数据
2025-06-15 22:04:24,945 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:04:24] "POST /api/production/import-priority-excel HTTP/1.1" 200 -
2025-06-15 22:12:05,719 - APS-System - INFO - 应用路径: D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12
2025-06-15 22:12:05,719 - APS-System - INFO - 实例路径: D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\instance
2025-06-15 22:12:05,720 - APS-System - INFO - 检查 MySQL 数据库配置...
2025-06-15 22:12:05,778 - APS-System - INFO - MySQL 数据库 'aps' 已存在
2025-06-15 22:12:05,779 - APS-System - INFO - MySQL 数据库 'aps_system' 已存在
2025-06-15 22:12:05,780 - APS-System - INFO - MySQL 数据库检查成功
2025-06-15 22:12:05,780 - APS-System - INFO - 正在修复数据库权限...
2025-06-15 22:12:05,781 - APS-System - INFO - 数据库文件不存在，跳过: instance\aps.db
2025-06-15 22:12:05,781 - APS-System - INFO - 数据库文件不存在，跳过: instance\aps_system.db
2025-06-15 22:12:05,781 - APS-System - INFO - 已修复日志目录权限: logs
2025-06-15 22:12:05,781 - APS-System - INFO - 数据库权限修复成功
2025-06-15 22:12:07,508 - app - INFO - 应用启动
2025-06-15 22:12:07,509 - app.utils.scheduler - INFO - 已重新加载 0 个邮箱配置（应用实例未设置）
2025-06-15 22:12:07,516 - app.utils.scheduler - INFO - 邮件附件定时调度器已启动
2025-06-15 22:12:07,543 - app - INFO - 邮件附件定时任务调度器已启动
2025-06-15 22:12:07,552 - app.utils.scheduler - INFO - 全局定时任务已禁用，邮件调度器跟随禁用
2025-06-15 22:12:07,554 - app.utils.scheduler - INFO - 检测到全局定时任务已禁用，停止邮件调度器
2025-06-15 22:12:07,678 - app.services.scheduler_service - INFO - APScheduler调度器配置完成: timezone=Asia/Shanghai, max_workers=10
2025-06-15 22:12:07,686 - apscheduler.scheduler - INFO - Scheduler started
2025-06-15 22:12:07,689 - app.services.scheduler_service - INFO - 从数据库加载了 0 个任务
2025-06-15 22:12:07,690 - app.services.scheduler_service - INFO - APScheduler调度器启动成功
2025-06-15 22:12:07,690 - app - INFO - ✅ APScheduler统一调度器启动成功
2025-06-15 22:12:07,692 - APS-System - INFO - 应用创建成功
2025-06-15 22:12:07,692 - APS-System - INFO - ==================================================
2025-06-15 22:12:07,692 - APS-System - INFO - 🚀 APS车规芯片终测智能调度平台 v2.1
2025-06-15 22:12:07,692 - APS-System - INFO - 📊 100% MySQL数据库架构
2025-06-15 22:12:07,692 - APS-System - INFO - 🌐 访问地址: http://1********:5000
2025-06-15 22:12:07,692 - APS-System - INFO - ==================================================
2025-06-15 22:12:07,692 - APS-System - INFO - 运行模式: 开发环境
2025-06-15 22:12:07,702 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://1********:5000
2025-06-15 22:12:07,702 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-15 22:12:09,734 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:12:09] "GET / HTTP/1.1" 200 -
2025-06-15 22:12:09,764 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:12:09] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 22:12:09,985 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:12:09] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 22:12:10,083 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:12:10] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 22:12:10,084 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:12:10] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 22:12:10,084 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:12:10] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 22:12:10,450 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:12:10] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 22:12:12,168 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:12:12] "[33mGET /api/production/device-priority-config?per_page=3 HTTP/1.1[0m" 404 -
2025-06-15 22:19:18,029 - APS-System - INFO - 应用路径: D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12
2025-06-15 22:19:18,029 - APS-System - INFO - 实例路径: D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\instance
2025-06-15 22:19:18,029 - APS-System - INFO - 检查 MySQL 数据库配置...
2025-06-15 22:19:18,066 - APS-System - INFO - MySQL 数据库 'aps' 已存在
2025-06-15 22:19:18,067 - APS-System - INFO - MySQL 数据库 'aps_system' 已存在
2025-06-15 22:19:18,067 - APS-System - INFO - MySQL 数据库检查成功
2025-06-15 22:19:18,067 - APS-System - INFO - 正在修复数据库权限...
2025-06-15 22:19:18,067 - APS-System - INFO - 数据库文件不存在，跳过: instance\aps.db
2025-06-15 22:19:18,067 - APS-System - INFO - 数据库文件不存在，跳过: instance\aps_system.db
2025-06-15 22:19:18,068 - APS-System - INFO - 已修复日志目录权限: logs
2025-06-15 22:19:18,068 - APS-System - INFO - 数据库权限修复成功
2025-06-15 22:19:19,762 - app - INFO - 应用启动
2025-06-15 22:19:19,765 - app.utils.scheduler - INFO - 已重新加载 0 个邮箱配置（应用实例未设置）
2025-06-15 22:19:19,784 - app.utils.scheduler - INFO - 邮件附件定时调度器已启动
2025-06-15 22:19:19,811 - app.utils.scheduler - INFO - 全局定时任务已禁用，邮件调度器跟随禁用
2025-06-15 22:19:19,813 - app - INFO - 邮件附件定时任务调度器已启动
2025-06-15 22:19:19,817 - app.utils.scheduler - INFO - 检测到全局定时任务已禁用，停止邮件调度器
2025-06-15 22:19:19,935 - app.services.scheduler_service - INFO - APScheduler调度器配置完成: timezone=Asia/Shanghai, max_workers=10
2025-06-15 22:19:19,947 - apscheduler.scheduler - INFO - Scheduler started
2025-06-15 22:19:19,951 - app.services.scheduler_service - INFO - 从数据库加载了 0 个任务
2025-06-15 22:19:19,956 - app.services.scheduler_service - INFO - APScheduler调度器启动成功
2025-06-15 22:19:19,959 - app - INFO - ✅ APScheduler统一调度器启动成功
2025-06-15 22:19:19,962 - APS-System - INFO - 应用创建成功
2025-06-15 22:19:19,963 - APS-System - INFO - ==================================================
2025-06-15 22:19:19,963 - APS-System - INFO - 🚀 APS车规芯片终测智能调度平台 v2.1
2025-06-15 22:19:19,963 - APS-System - INFO - 📊 100% MySQL数据库架构
2025-06-15 22:19:19,963 - APS-System - INFO - 🌐 访问地址: http://1********:5000
2025-06-15 22:19:19,963 - APS-System - INFO - ==================================================
2025-06-15 22:19:19,963 - APS-System - INFO - 运行模式: 开发环境
2025-06-15 22:19:19,976 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://1********:5000
2025-06-15 22:19:19,976 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-15 22:19:22,136 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:19:22] "[32mGET / HTTP/1.1[0m" 302 -
2025-06-15 22:19:22,169 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:19:22] "GET /auth/login?next=/ HTTP/1.1" 200 -
2025-06-15 22:19:22,401 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:19:22] "GET /static/css/login/bootstrap.min.css HTTP/1.1" 200 -
2025-06-15 22:19:22,511 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:19:22] "GET /static/css/all-5.15.4.min.css HTTP/1.1" 200 -
2025-06-15 22:19:22,511 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:19:22] "GET /static/js/login/bootstrap.bundle.min.js HTTP/1.1" 200 -
2025-06-15 22:19:22,512 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:19:22] "GET /static/css/compatibility.css HTTP/1.1" 200 -
2025-06-15 22:19:22,564 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:19:22] "GET /static/webfonts/fa-solid-900.woff2 HTTP/1.1" 200 -
2025-06-15 22:19:24,043 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:19:24] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-06-15 22:19:25,329 - app.auth.routes - INFO - 尝试登录用户: admin
2025-06-15 22:19:25,353 - app.auth.routes - INFO - 找到用户: admin, 角色: admin, 密码哈希: scrypt:32768:8:1$X8JU5hxR74hpH0tP$36e5b028b335f5d2f9f21e9178a0d529cca80459428b1128d44d8ac646d8f16cdd0823ea12d58ce5bbae1dd243eeb7a50c06f55d6688542dae997f7c75e6ee24
2025-06-15 22:19:25,524 - app.models - INFO - 用户 admin 密码验证结果: True
2025-06-15 22:19:25,536 - app.auth.routes - INFO - 密码验证结果: True
2025-06-15 22:19:25,553 - app.auth.routes - INFO - 用户 admin 登录成功
2025-06-15 22:19:25,569 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:19:25] "[32mPOST /auth/login HTTP/1.1[0m" 302 -
2025-06-15 22:19:25,655 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:19:25] "GET /index HTTP/1.1" 200 -
2025-06-15 22:19:26,014 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:19:26] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 22:19:26,014 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:19:26] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 22:19:26,015 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:19:26] "GET /static/js/base/bootstrap.bundle.min.js HTTP/1.1" 200 -
2025-06-15 22:19:26,016 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:19:26] "GET /static/css/base/bootstrap.min.css HTTP/1.1" 200 -
2025-06-15 22:19:26,016 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:19:26] "GET /static/js/menu-optimizer.js HTTP/1.1" 200 -
2025-06-15 22:19:26,248 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:19:26] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 22:19:26,338 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:19:26] "GET /menu HTTP/1.1" 200 -
2025-06-15 22:19:31,690 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:19:31] "GET /production/priority-matching HTTP/1.1" 200 -
2025-06-15 22:19:31,993 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:19:31] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 22:19:32,041 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:19:32] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 22:19:32,048 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:19:32] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 22:19:32,048 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:19:32] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 22:19:32,051 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:19:32] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 22:19:32,362 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:19:32] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 22:19:46,764 - APS-System - INFO - 应用路径: D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12
2025-06-15 22:19:46,765 - APS-System - INFO - 实例路径: D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\instance
2025-06-15 22:19:46,766 - APS-System - INFO - 检查 MySQL 数据库配置...
2025-06-15 22:19:46,812 - APS-System - INFO - MySQL 数据库 'aps' 已存在
2025-06-15 22:19:46,813 - APS-System - INFO - MySQL 数据库 'aps_system' 已存在
2025-06-15 22:19:46,814 - APS-System - INFO - MySQL 数据库检查成功
2025-06-15 22:19:46,814 - APS-System - INFO - 正在修复数据库权限...
2025-06-15 22:19:46,815 - APS-System - INFO - 数据库文件不存在，跳过: instance\aps.db
2025-06-15 22:19:46,815 - APS-System - INFO - 数据库文件不存在，跳过: instance\aps_system.db
2025-06-15 22:19:46,815 - APS-System - INFO - 已修复日志目录权限: logs
2025-06-15 22:19:46,816 - APS-System - INFO - 数据库权限修复成功
2025-06-15 22:19:48,685 - app - INFO - 应用启动
2025-06-15 22:19:48,688 - app.utils.scheduler - INFO - 已重新加载 0 个邮箱配置（应用实例未设置）
2025-06-15 22:19:48,701 - app.utils.scheduler - INFO - 邮件附件定时调度器已启动
2025-06-15 22:19:48,724 - app - INFO - 邮件附件定时任务调度器已启动
2025-06-15 22:19:48,731 - app.utils.scheduler - INFO - 全局定时任务已禁用，邮件调度器跟随禁用
2025-06-15 22:19:48,732 - app.utils.scheduler - INFO - 检测到全局定时任务已禁用，停止邮件调度器
2025-06-15 22:19:48,868 - app.services.scheduler_service - INFO - APScheduler调度器配置完成: timezone=Asia/Shanghai, max_workers=10
2025-06-15 22:19:48,877 - apscheduler.scheduler - INFO - Scheduler started
2025-06-15 22:19:48,881 - app.services.scheduler_service - INFO - 从数据库加载了 0 个任务
2025-06-15 22:19:48,882 - app.services.scheduler_service - INFO - APScheduler调度器启动成功
2025-06-15 22:19:48,883 - app - INFO - ✅ APScheduler统一调度器启动成功
2025-06-15 22:19:48,883 - APS-System - INFO - 应用创建成功
2025-06-15 22:19:48,884 - APS-System - INFO - ==================================================
2025-06-15 22:19:48,884 - APS-System - INFO - 🚀 APS车规芯片终测智能调度平台 v2.1
2025-06-15 22:19:48,884 - APS-System - INFO - 📊 100% MySQL数据库架构
2025-06-15 22:19:48,885 - APS-System - INFO - 🌐 访问地址: http://1********:5000
2025-06-15 22:19:48,885 - APS-System - INFO - ==================================================
2025-06-15 22:19:48,885 - APS-System - INFO - 运行模式: 开发环境
2025-06-15 22:19:48,898 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://1********:5000
2025-06-15 22:19:48,898 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-15 22:19:50,913 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:19:50] "GET / HTTP/1.1" 200 -
2025-06-15 22:19:50,943 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:19:50] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 22:19:51,174 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:19:51] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 22:19:51,252 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:19:51] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 22:19:51,252 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:19:51] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 22:19:51,253 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:19:51] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 22:19:51,633 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:19:51] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 22:19:54,010 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:19:54] "GET /production/priority-matching HTTP/1.1" 200 -
2025-06-15 22:19:54,312 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:19:54] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 22:19:54,359 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:19:54] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 22:19:54,359 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:19:54] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 22:19:54,360 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:19:54] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 22:19:54,360 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:19:54] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 22:19:54,679 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:19:54] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 22:20:09,998 - app.api.production_api - INFO - 接收到文件上传请求: 文件名=devicepriorityconfig.xlsx, 类型=devicepriorityconfig, 策略=replace
2025-06-15 22:20:10,039 - app.api.production_api - INFO - 读取Excel文件成功，共 6 行数据
2025-06-15 22:20:10,049 - app.api.production_api - ERROR - 处理第 1 行数据失败: (1054, "Unknown column 'STAGE' in 'where clause'")
2025-06-15 22:20:10,050 - app.api.production_api - ERROR - 处理第 2 行数据失败: (1054, "Unknown column 'STAGE' in 'where clause'")
2025-06-15 22:20:10,054 - app.api.production_api - ERROR - 处理第 3 行数据失败: (1054, "Unknown column 'STAGE' in 'where clause'")
2025-06-15 22:20:10,055 - app.api.production_api - ERROR - 处理第 4 行数据失败: (1054, "Unknown column 'STAGE' in 'where clause'")
2025-06-15 22:20:10,057 - app.api.production_api - ERROR - 处理第 5 行数据失败: (1054, "Unknown column 'STAGE' in 'where clause'")
2025-06-15 22:20:10,058 - app.api.production_api - ERROR - 处理第 6 行数据失败: (1054, "Unknown column 'STAGE' in 'where clause'")
2025-06-15 22:20:10,061 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:20:10] "POST /api/production/import-priority-excel HTTP/1.1" 200 -
2025-06-15 22:20:10,377 - app.api.production_api - INFO - 接收到文件上传请求: 文件名=lotpriorityconfig.xlsx, 类型=lotpriorityconfig, 策略=replace
2025-06-15 22:20:10,409 - app.api.production_api - INFO - 读取Excel文件成功，共 5 行数据
2025-06-15 22:20:10,414 - app.api.production_api - ERROR - 处理第 1 行数据失败: (1054, "Unknown column 'DEVICE' in 'where clause'")
2025-06-15 22:20:10,416 - app.api.production_api - ERROR - 处理第 2 行数据失败: (1054, "Unknown column 'DEVICE' in 'where clause'")
2025-06-15 22:20:10,416 - app.api.production_api - ERROR - 处理第 3 行数据失败: (1054, "Unknown column 'DEVICE' in 'where clause'")
2025-06-15 22:20:10,418 - app.api.production_api - ERROR - 处理第 4 行数据失败: (1054, "Unknown column 'DEVICE' in 'where clause'")
2025-06-15 22:20:10,420 - app.api.production_api - ERROR - 处理第 5 行数据失败: (1054, "Unknown column 'DEVICE' in 'where clause'")
2025-06-15 22:20:10,424 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:20:10] "POST /api/production/import-priority-excel HTTP/1.1" 200 -
2025-06-15 22:30:00,853 - APS-System - INFO - 应用路径: D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12
2025-06-15 22:30:00,853 - APS-System - INFO - 实例路径: D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\instance
2025-06-15 22:30:00,853 - APS-System - INFO - 检查 MySQL 数据库配置...
2025-06-15 22:30:00,905 - APS-System - INFO - MySQL 数据库 'aps' 已存在
2025-06-15 22:30:00,906 - APS-System - INFO - MySQL 数据库 'aps_system' 已存在
2025-06-15 22:30:00,906 - APS-System - INFO - MySQL 数据库检查成功
2025-06-15 22:30:00,906 - APS-System - INFO - 正在修复数据库权限...
2025-06-15 22:30:00,907 - APS-System - INFO - 数据库文件不存在，跳过: instance\aps.db
2025-06-15 22:30:00,907 - APS-System - INFO - 数据库文件不存在，跳过: instance\aps_system.db
2025-06-15 22:30:00,907 - APS-System - INFO - 已修复日志目录权限: logs
2025-06-15 22:30:00,907 - APS-System - INFO - 数据库权限修复成功
2025-06-15 22:30:02,572 - app - INFO - 应用启动
2025-06-15 22:30:02,574 - app.utils.scheduler - INFO - 已重新加载 0 个邮箱配置（应用实例未设置）
2025-06-15 22:30:02,579 - app.utils.scheduler - INFO - 邮件附件定时调度器已启动
2025-06-15 22:30:02,607 - app - INFO - 邮件附件定时任务调度器已启动
2025-06-15 22:30:02,632 - app.utils.scheduler - INFO - 全局定时任务已禁用，邮件调度器跟随禁用
2025-06-15 22:30:02,642 - app.utils.scheduler - INFO - 检测到全局定时任务已禁用，停止邮件调度器
2025-06-15 22:30:02,750 - app.services.scheduler_service - INFO - APScheduler调度器配置完成: timezone=Asia/Shanghai, max_workers=10
2025-06-15 22:30:02,762 - apscheduler.scheduler - INFO - Scheduler started
2025-06-15 22:30:02,767 - app.services.scheduler_service - INFO - 从数据库加载了 0 个任务
2025-06-15 22:30:02,771 - app.services.scheduler_service - INFO - APScheduler调度器启动成功
2025-06-15 22:30:02,775 - app - INFO - ✅ APScheduler统一调度器启动成功
2025-06-15 22:30:02,780 - APS-System - INFO - 应用创建成功
2025-06-15 22:30:02,780 - APS-System - INFO - ==================================================
2025-06-15 22:30:02,780 - APS-System - INFO - 🚀 APS车规芯片终测智能调度平台 v2.1
2025-06-15 22:30:02,781 - APS-System - INFO - 📊 100% MySQL数据库架构
2025-06-15 22:30:02,781 - APS-System - INFO - 🌐 访问地址: http://1********:5000
2025-06-15 22:30:02,781 - APS-System - INFO - ==================================================
2025-06-15 22:30:02,781 - APS-System - INFO - 运行模式: 开发环境
2025-06-15 22:30:02,796 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://1********:5000
2025-06-15 22:30:02,797 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-15 22:30:04,883 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:30:04] "GET / HTTP/1.1" 200 -
2025-06-15 22:30:04,929 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:30:04] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 22:30:05,131 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:30:05] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 22:30:05,240 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:30:05] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 22:30:05,242 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:30:05] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 22:30:05,244 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:30:05] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 22:30:05,776 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:30:05] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 22:35:18,077 - APS-System - INFO - 应用路径: D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12
2025-06-15 22:35:18,077 - APS-System - INFO - 实例路径: D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\instance
2025-06-15 22:35:18,078 - APS-System - INFO - 检查 MySQL 数据库配置...
2025-06-15 22:35:18,117 - APS-System - INFO - MySQL 数据库 'aps' 已存在
2025-06-15 22:35:18,117 - APS-System - INFO - MySQL 数据库 'aps_system' 已存在
2025-06-15 22:35:18,118 - APS-System - INFO - MySQL 数据库检查成功
2025-06-15 22:35:18,118 - APS-System - INFO - 正在修复数据库权限...
2025-06-15 22:35:18,118 - APS-System - INFO - 数据库文件不存在，跳过: instance\aps.db
2025-06-15 22:35:18,119 - APS-System - INFO - 数据库文件不存在，跳过: instance\aps_system.db
2025-06-15 22:35:18,119 - APS-System - INFO - 已修复日志目录权限: logs
2025-06-15 22:35:18,119 - APS-System - INFO - 数据库权限修复成功
2025-06-15 22:35:19,623 - app - INFO - 应用启动
2025-06-15 22:35:19,627 - app.utils.scheduler - INFO - 已重新加载 0 个邮箱配置（应用实例未设置）
2025-06-15 22:35:19,643 - app.utils.scheduler - INFO - 邮件附件定时调度器已启动
2025-06-15 22:35:19,676 - app.utils.scheduler - INFO - 全局定时任务已禁用，邮件调度器跟随禁用
2025-06-15 22:35:19,678 - app - INFO - 邮件附件定时任务调度器已启动
2025-06-15 22:35:19,682 - app.utils.scheduler - INFO - 检测到全局定时任务已禁用，停止邮件调度器
2025-06-15 22:35:19,805 - app.services.scheduler_service - INFO - APScheduler调度器配置完成: timezone=Asia/Shanghai, max_workers=10
2025-06-15 22:35:19,814 - apscheduler.scheduler - INFO - Scheduler started
2025-06-15 22:35:19,818 - app.services.scheduler_service - INFO - 从数据库加载了 0 个任务
2025-06-15 22:35:19,821 - app.services.scheduler_service - INFO - APScheduler调度器启动成功
2025-06-15 22:35:19,823 - app - INFO - ✅ APScheduler统一调度器启动成功
2025-06-15 22:35:19,825 - APS-System - INFO - 应用创建成功
2025-06-15 22:35:19,825 - APS-System - INFO - ==================================================
2025-06-15 22:35:19,825 - APS-System - INFO - 🚀 APS车规芯片终测智能调度平台 v2.1
2025-06-15 22:35:19,825 - APS-System - INFO - 📊 100% MySQL数据库架构
2025-06-15 22:35:19,825 - APS-System - INFO - 🌐 访问地址: http://1********:5000
2025-06-15 22:35:19,826 - APS-System - INFO - ==================================================
2025-06-15 22:35:19,826 - APS-System - INFO - 运行模式: 开发环境
2025-06-15 22:35:19,838 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://1********:5000
2025-06-15 22:35:19,839 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-15 22:35:21,813 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:35:21] "GET / HTTP/1.1" 200 -
2025-06-15 22:35:21,844 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:35:21] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 22:35:22,076 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:35:22] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 22:35:22,153 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:35:22] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 22:35:22,155 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:35:22] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 22:35:22,156 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:35:22] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 22:35:22,521 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:35:22] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 22:35:25,698 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:35:25] "GET /production/priority-matching HTTP/1.1" 200 -
2025-06-15 22:35:26,007 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:35:26] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 22:35:26,039 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:35:26] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 22:35:26,040 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:35:26] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 22:35:26,041 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:35:26] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 22:35:26,042 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:35:26] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 22:35:26,363 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:35:26] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 22:38:44,913 - app.api.production_api - INFO - 接收到文件上传请求: 文件名=devicepriorityconfig.xlsx, 类型=devicepriorityconfig, 策略=replace
2025-06-15 22:38:44,954 - app.api.production_api - INFO - 读取Excel文件成功，共 6 行数据
2025-06-15 22:38:44,968 - app.api.production_api - ERROR - 处理第 1 行数据失败: (1054, "Unknown column 'STAGE' in 'where clause'")
2025-06-15 22:38:44,970 - app.api.production_api - ERROR - 处理第 2 行数据失败: (1054, "Unknown column 'STAGE' in 'where clause'")
2025-06-15 22:38:44,971 - app.api.production_api - ERROR - 处理第 3 行数据失败: (1054, "Unknown column 'STAGE' in 'where clause'")
2025-06-15 22:38:44,972 - app.api.production_api - ERROR - 处理第 4 行数据失败: (1054, "Unknown column 'STAGE' in 'where clause'")
2025-06-15 22:38:44,973 - app.api.production_api - ERROR - 处理第 5 行数据失败: (1054, "Unknown column 'STAGE' in 'where clause'")
2025-06-15 22:38:44,974 - app.api.production_api - ERROR - 处理第 6 行数据失败: (1054, "Unknown column 'STAGE' in 'where clause'")
2025-06-15 22:38:44,976 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:38:44] "POST /api/production/import-priority-excel HTTP/1.1" 200 -
2025-06-15 22:38:45,174 - app.api.production_api - INFO - 接收到文件上传请求: 文件名=lotpriorityconfig.xlsx, 类型=lotpriorityconfig, 策略=replace
2025-06-15 22:38:45,209 - app.api.production_api - INFO - 读取Excel文件成功，共 5 行数据
2025-06-15 22:38:45,218 - app.api.production_api - ERROR - 处理第 1 行数据失败: (1054, "Unknown column 'DEVICE' in 'where clause'")
2025-06-15 22:38:45,219 - app.api.production_api - ERROR - 处理第 2 行数据失败: (1054, "Unknown column 'DEVICE' in 'where clause'")
2025-06-15 22:38:45,219 - app.api.production_api - ERROR - 处理第 3 行数据失败: (1054, "Unknown column 'DEVICE' in 'where clause'")
2025-06-15 22:38:45,223 - app.api.production_api - ERROR - 处理第 4 行数据失败: (1054, "Unknown column 'DEVICE' in 'where clause'")
2025-06-15 22:38:45,227 - app.api.production_api - ERROR - 处理第 5 行数据失败: (1054, "Unknown column 'DEVICE' in 'where clause'")
2025-06-15 22:38:45,231 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:38:45] "POST /api/production/import-priority-excel HTTP/1.1" 200 -
2025-06-15 22:38:54,905 - app.api.production_api - INFO - 接收到文件上传请求: 文件名=devicepriorityconfig.xlsx, 类型=devicepriorityconfig, 策略=update
2025-06-15 22:38:54,932 - app.api.production_api - INFO - 读取Excel文件成功，共 6 行数据
2025-06-15 22:38:54,937 - app.api.production_api - ERROR - 处理第 1 行数据失败: (1054, "Unknown column 'STAGE' in 'where clause'")
2025-06-15 22:38:54,939 - app.api.production_api - ERROR - 处理第 2 行数据失败: (1054, "Unknown column 'STAGE' in 'where clause'")
2025-06-15 22:38:54,940 - app.api.production_api - ERROR - 处理第 3 行数据失败: (1054, "Unknown column 'STAGE' in 'where clause'")
2025-06-15 22:38:54,941 - app.api.production_api - ERROR - 处理第 4 行数据失败: (1054, "Unknown column 'STAGE' in 'where clause'")
2025-06-15 22:38:54,942 - app.api.production_api - ERROR - 处理第 5 行数据失败: (1054, "Unknown column 'STAGE' in 'where clause'")
2025-06-15 22:38:54,943 - app.api.production_api - ERROR - 处理第 6 行数据失败: (1054, "Unknown column 'STAGE' in 'where clause'")
2025-06-15 22:38:54,945 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:38:54] "POST /api/production/import-priority-excel HTTP/1.1" 200 -
2025-06-15 22:38:55,267 - app.api.production_api - INFO - 接收到文件上传请求: 文件名=lotpriorityconfig.xlsx, 类型=lotpriorityconfig, 策略=update
2025-06-15 22:38:55,295 - app.api.production_api - INFO - 读取Excel文件成功，共 5 行数据
2025-06-15 22:38:55,325 - app.api.production_api - ERROR - 处理第 1 行数据失败: (1054, "Unknown column 'DEVICE' in 'where clause'")
2025-06-15 22:38:55,328 - app.api.production_api - ERROR - 处理第 2 行数据失败: (1054, "Unknown column 'DEVICE' in 'where clause'")
2025-06-15 22:38:55,330 - app.api.production_api - ERROR - 处理第 3 行数据失败: (1054, "Unknown column 'DEVICE' in 'where clause'")
2025-06-15 22:38:55,331 - app.api.production_api - ERROR - 处理第 4 行数据失败: (1054, "Unknown column 'DEVICE' in 'where clause'")
2025-06-15 22:38:55,332 - app.api.production_api - ERROR - 处理第 5 行数据失败: (1054, "Unknown column 'DEVICE' in 'where clause'")
2025-06-15 22:38:55,336 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:38:55] "POST /api/production/import-priority-excel HTTP/1.1" 200 -
2025-06-15 22:41:59,128 - APS-System - INFO - 应用路径: D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12
2025-06-15 22:41:59,129 - APS-System - INFO - 实例路径: D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\instance
2025-06-15 22:41:59,129 - APS-System - INFO - 检查 MySQL 数据库配置...
2025-06-15 22:41:59,200 - APS-System - INFO - MySQL 数据库 'aps' 已存在
2025-06-15 22:41:59,202 - APS-System - INFO - MySQL 数据库 'aps_system' 已存在
2025-06-15 22:41:59,202 - APS-System - INFO - MySQL 数据库检查成功
2025-06-15 22:41:59,203 - APS-System - INFO - 正在修复数据库权限...
2025-06-15 22:41:59,203 - APS-System - INFO - 数据库文件不存在，跳过: instance\aps.db
2025-06-15 22:41:59,204 - APS-System - INFO - 数据库文件不存在，跳过: instance\aps_system.db
2025-06-15 22:41:59,205 - APS-System - INFO - 已修复日志目录权限: logs
2025-06-15 22:41:59,205 - APS-System - INFO - 数据库权限修复成功
2025-06-15 22:42:02,000 - app - INFO - 应用启动
2025-06-15 22:42:02,002 - app.utils.scheduler - INFO - 已重新加载 0 个邮箱配置（应用实例未设置）
2025-06-15 22:42:02,012 - app.utils.scheduler - INFO - 邮件附件定时调度器已启动
2025-06-15 22:42:02,059 - app - INFO - 邮件附件定时任务调度器已启动
2025-06-15 22:42:02,087 - app.utils.scheduler - INFO - 全局定时任务已禁用，邮件调度器跟随禁用
2025-06-15 22:42:02,090 - app.utils.scheduler - INFO - 检测到全局定时任务已禁用，停止邮件调度器
2025-06-15 22:42:02,288 - app.services.scheduler_service - INFO - APScheduler调度器配置完成: timezone=Asia/Shanghai, max_workers=10
2025-06-15 22:42:02,299 - apscheduler.scheduler - INFO - Scheduler started
2025-06-15 22:42:02,306 - app.services.scheduler_service - INFO - 从数据库加载了 0 个任务
2025-06-15 22:42:02,307 - app.services.scheduler_service - INFO - APScheduler调度器启动成功
2025-06-15 22:42:02,309 - app - INFO - ✅ APScheduler统一调度器启动成功
2025-06-15 22:42:02,309 - APS-System - INFO - 应用创建成功
2025-06-15 22:42:02,310 - APS-System - INFO - ==================================================
2025-06-15 22:42:02,310 - APS-System - INFO - 🚀 APS车规芯片终测智能调度平台 v2.1
2025-06-15 22:42:02,310 - APS-System - INFO - 📊 100% MySQL数据库架构
2025-06-15 22:42:02,310 - APS-System - INFO - 🌐 访问地址: http://1********:5000
2025-06-15 22:42:02,310 - APS-System - INFO - ==================================================
2025-06-15 22:42:02,310 - APS-System - INFO - 运行模式: 开发环境
2025-06-15 22:42:02,328 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://1********:5000
2025-06-15 22:42:02,328 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-15 22:42:04,409 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:42:04] "GET / HTTP/1.1" 200 -
2025-06-15 22:42:04,448 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:42:04] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 22:42:04,654 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:42:04] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 22:42:04,764 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:42:04] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 22:42:04,768 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:42:04] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 22:42:04,769 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:42:04] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 22:42:05,217 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:42:05] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 22:42:10,090 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:42:10] "GET /production/priority-matching HTTP/1.1" 200 -
2025-06-15 22:42:10,392 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:42:10] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 22:42:10,437 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:42:10] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 22:42:10,442 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:42:10] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 22:42:10,446 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:42:10] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 22:42:10,451 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:42:10] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 22:42:10,774 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:42:10] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 22:42:24,819 - app.api.production_api - INFO - 接收到文件上传请求: 文件名=devicepriorityconfig.xlsx, 类型=devicepriorityconfig, 策略=replace
2025-06-15 22:42:24,852 - app.api.production_api - INFO - 读取Excel文件成功，共 6 行数据
2025-06-15 22:42:24,859 - app.api.production_api - ERROR - 处理第 1 行数据失败: (1054, "Unknown column 'STAGE' in 'where clause'")
2025-06-15 22:42:24,860 - app.api.production_api - ERROR - 处理第 2 行数据失败: (1054, "Unknown column 'STAGE' in 'where clause'")
2025-06-15 22:42:24,862 - app.api.production_api - ERROR - 处理第 3 行数据失败: (1054, "Unknown column 'STAGE' in 'where clause'")
2025-06-15 22:42:24,863 - app.api.production_api - ERROR - 处理第 4 行数据失败: (1054, "Unknown column 'STAGE' in 'where clause'")
2025-06-15 22:42:24,894 - app.api.production_api - ERROR - 处理第 5 行数据失败: (1054, "Unknown column 'STAGE' in 'where clause'")
2025-06-15 22:42:24,908 - app.api.production_api - ERROR - 处理第 6 行数据失败: (1054, "Unknown column 'STAGE' in 'where clause'")
2025-06-15 22:42:24,924 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:42:24] "POST /api/production/import-priority-excel HTTP/1.1" 200 -
2025-06-15 22:42:25,235 - app.api.production_api - INFO - 接收到文件上传请求: 文件名=lotpriorityconfig.xlsx, 类型=lotpriorityconfig, 策略=replace
2025-06-15 22:42:25,297 - app.api.production_api - INFO - 读取Excel文件成功，共 5 行数据
2025-06-15 22:42:25,322 - app.api.production_api - ERROR - 处理第 1 行数据失败: (1054, "Unknown column 'DEVICE' in 'where clause'")
2025-06-15 22:42:25,347 - app.api.production_api - ERROR - 处理第 2 行数据失败: (1054, "Unknown column 'DEVICE' in 'where clause'")
2025-06-15 22:42:25,365 - app.api.production_api - ERROR - 处理第 3 行数据失败: (1054, "Unknown column 'DEVICE' in 'where clause'")
2025-06-15 22:42:25,380 - app.api.production_api - ERROR - 处理第 4 行数据失败: (1054, "Unknown column 'DEVICE' in 'where clause'")
2025-06-15 22:42:25,397 - app.api.production_api - ERROR - 处理第 5 行数据失败: (1054, "Unknown column 'DEVICE' in 'where clause'")
2025-06-15 22:42:25,417 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:42:25] "POST /api/production/import-priority-excel HTTP/1.1" 200 -
2025-06-15 22:42:36,932 - app.api.production_api - INFO - 接收到文件上传请求: 文件名=devicepriorityconfig.xlsx, 类型=devicepriorityconfig, 策略=update
2025-06-15 22:42:36,984 - app.api.production_api - INFO - 读取Excel文件成功，共 6 行数据
2025-06-15 22:42:37,040 - app.api.production_api - ERROR - 处理第 1 行数据失败: (1054, "Unknown column 'STAGE' in 'where clause'")
2025-06-15 22:42:37,071 - app.api.production_api - ERROR - 处理第 2 行数据失败: (1054, "Unknown column 'STAGE' in 'where clause'")
2025-06-15 22:42:37,090 - app.api.production_api - ERROR - 处理第 3 行数据失败: (1054, "Unknown column 'STAGE' in 'where clause'")
2025-06-15 22:42:37,107 - app.api.production_api - ERROR - 处理第 4 行数据失败: (1054, "Unknown column 'STAGE' in 'where clause'")
2025-06-15 22:42:37,123 - app.api.production_api - ERROR - 处理第 5 行数据失败: (1054, "Unknown column 'STAGE' in 'where clause'")
2025-06-15 22:42:37,140 - app.api.production_api - ERROR - 处理第 6 行数据失败: (1054, "Unknown column 'STAGE' in 'where clause'")
2025-06-15 22:42:37,158 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:42:37] "POST /api/production/import-priority-excel HTTP/1.1" 200 -
2025-06-15 22:42:37,477 - app.api.production_api - INFO - 接收到文件上传请求: 文件名=lotpriorityconfig.xlsx, 类型=lotpriorityconfig, 策略=update
2025-06-15 22:42:37,556 - app.api.production_api - INFO - 读取Excel文件成功，共 5 行数据
2025-06-15 22:42:37,579 - app.api.production_api - ERROR - 处理第 1 行数据失败: (1054, "Unknown column 'DEVICE' in 'where clause'")
2025-06-15 22:42:37,599 - app.api.production_api - ERROR - 处理第 2 行数据失败: (1054, "Unknown column 'DEVICE' in 'where clause'")
2025-06-15 22:42:37,617 - app.api.production_api - ERROR - 处理第 3 行数据失败: (1054, "Unknown column 'DEVICE' in 'where clause'")
2025-06-15 22:42:37,632 - app.api.production_api - ERROR - 处理第 4 行数据失败: (1054, "Unknown column 'DEVICE' in 'where clause'")
2025-06-15 22:42:37,650 - app.api.production_api - ERROR - 处理第 5 行数据失败: (1054, "Unknown column 'DEVICE' in 'where clause'")
2025-06-15 22:42:37,670 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:42:37] "POST /api/production/import-priority-excel HTTP/1.1" 200 -
2025-06-15 22:42:40,147 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:42:40] "[32mPOST /api/production/import-priority-excel HTTP/1.1[0m" 302 -
2025-06-15 22:42:41,854 - app.api.production_api - INFO - 接收到文件上传请求: 文件名=devicepriorityconfig.xlsx, 类型=devicepriorityconfig, 策略=replace
2025-06-15 22:42:41,905 - app.api.production_api - INFO - 读取Excel文件成功，共 6 行数据
2025-06-15 22:42:41,921 - app.api.production_api - ERROR - 处理第 1 行数据失败: (1054, "Unknown column 'STAGE' in 'where clause'")
2025-06-15 22:42:41,937 - app.api.production_api - ERROR - 处理第 2 行数据失败: (1054, "Unknown column 'STAGE' in 'where clause'")
2025-06-15 22:42:41,953 - app.api.production_api - ERROR - 处理第 3 行数据失败: (1054, "Unknown column 'STAGE' in 'where clause'")
2025-06-15 22:42:41,971 - app.api.production_api - ERROR - 处理第 4 行数据失败: (1054, "Unknown column 'STAGE' in 'where clause'")
2025-06-15 22:42:41,983 - app.api.production_api - ERROR - 处理第 5 行数据失败: (1054, "Unknown column 'STAGE' in 'where clause'")
2025-06-15 22:42:42,000 - app.api.production_api - ERROR - 处理第 6 行数据失败: (1054, "Unknown column 'STAGE' in 'where clause'")
2025-06-15 22:42:42,024 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:42:42] "POST /api/production/import-priority-excel HTTP/1.1" 200 -
2025-06-15 22:42:42,178 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:42:42] "GET /auth/login?next=/api/production/import-priority-excel HTTP/1.1" 200 -
2025-06-15 22:42:42,349 - app.api.production_api - INFO - 接收到文件上传请求: 文件名=lotpriorityconfig.xlsx, 类型=lotpriorityconfig, 策略=replace
2025-06-15 22:42:42,392 - app.api.production_api - INFO - 读取Excel文件成功，共 5 行数据
2025-06-15 22:42:42,412 - app.api.production_api - ERROR - 处理第 1 行数据失败: (1054, "Unknown column 'DEVICE' in 'where clause'")
2025-06-15 22:42:42,438 - app.api.production_api - ERROR - 处理第 2 行数据失败: (1054, "Unknown column 'DEVICE' in 'where clause'")
2025-06-15 22:42:42,459 - app.api.production_api - ERROR - 处理第 3 行数据失败: (1054, "Unknown column 'DEVICE' in 'where clause'")
2025-06-15 22:42:42,477 - app.api.production_api - ERROR - 处理第 4 行数据失败: (1054, "Unknown column 'DEVICE' in 'where clause'")
2025-06-15 22:42:42,493 - app.api.production_api - ERROR - 处理第 5 行数据失败: (1054, "Unknown column 'DEVICE' in 'where clause'")
2025-06-15 22:42:42,511 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:42:42] "POST /api/production/import-priority-excel HTTP/1.1" 200 -
2025-06-15 22:42:44,224 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:42:44] "[32mPOST /api/production/import-priority-excel HTTP/1.1[0m" 302 -
2025-06-15 22:42:46,271 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:42:46] "GET /auth/login?next=/api/production/import-priority-excel HTTP/1.1" 200 -
2025-06-15 22:42:48,308 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:42:48] "[33mGET /api/production/lot-priority-config?per_page=100 HTTP/1.1[0m" 404 -
2025-06-15 22:42:48,717 - app.api.production_api - INFO - 接收到文件上传请求: 文件名=devicepriorityconfig.xlsx, 类型=devicepriorityconfig, 策略=replace
2025-06-15 22:42:48,761 - app.api.production_api - INFO - 读取Excel文件成功，共 6 行数据
2025-06-15 22:42:48,804 - app.api.production_api - ERROR - 处理第 1 行数据失败: (1054, "Unknown column 'STAGE' in 'where clause'")
2025-06-15 22:42:48,824 - app.api.production_api - ERROR - 处理第 2 行数据失败: (1054, "Unknown column 'STAGE' in 'where clause'")
2025-06-15 22:42:48,854 - app.api.production_api - ERROR - 处理第 3 行数据失败: (1054, "Unknown column 'STAGE' in 'where clause'")
2025-06-15 22:42:48,880 - app.api.production_api - ERROR - 处理第 4 行数据失败: (1054, "Unknown column 'STAGE' in 'where clause'")
2025-06-15 22:42:48,903 - app.api.production_api - ERROR - 处理第 5 行数据失败: (1054, "Unknown column 'STAGE' in 'where clause'")
2025-06-15 22:42:48,921 - app.api.production_api - ERROR - 处理第 6 行数据失败: (1054, "Unknown column 'STAGE' in 'where clause'")
2025-06-15 22:42:48,946 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:42:48] "POST /api/production/import-priority-excel HTTP/1.1" 200 -
2025-06-15 22:42:49,266 - app.api.production_api - INFO - 接收到文件上传请求: 文件名=lotpriorityconfig.xlsx, 类型=lotpriorityconfig, 策略=replace
2025-06-15 22:42:49,343 - app.api.production_api - INFO - 读取Excel文件成功，共 5 行数据
2025-06-15 22:42:49,353 - app.api.production_api - ERROR - 处理第 1 行数据失败: (1054, "Unknown column 'DEVICE' in 'where clause'")
2025-06-15 22:42:49,365 - app.api.production_api - ERROR - 处理第 2 行数据失败: (1054, "Unknown column 'DEVICE' in 'where clause'")
2025-06-15 22:42:49,377 - app.api.production_api - ERROR - 处理第 3 行数据失败: (1054, "Unknown column 'DEVICE' in 'where clause'")
2025-06-15 22:42:49,388 - app.api.production_api - ERROR - 处理第 4 行数据失败: (1054, "Unknown column 'DEVICE' in 'where clause'")
2025-06-15 22:42:49,406 - app.api.production_api - ERROR - 处理第 5 行数据失败: (1054, "Unknown column 'DEVICE' in 'where clause'")
2025-06-15 22:42:49,427 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:42:49] "POST /api/production/import-priority-excel HTTP/1.1" 200 -
2025-06-15 22:42:50,342 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:42:50] "[33mGET /api/production/device-priority-config?per_page=100 HTTP/1.1[0m" 404 -
2025-06-15 22:43:06,053 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:43:06] "[33mGET /api/production/lot-priority-config HTTP/1.1[0m" 404 -
2025-06-15 22:43:17,546 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:43:17] "[32mGET / HTTP/1.1[0m" 302 -
2025-06-15 22:43:17,555 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:43:17] "GET /auth/login?next=/ HTTP/1.1" 200 -
2025-06-15 22:43:49,488 - APS-System - INFO - 应用路径: D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12
2025-06-15 22:43:49,488 - APS-System - INFO - 实例路径: D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\instance
2025-06-15 22:43:49,489 - APS-System - INFO - 检查 MySQL 数据库配置...
2025-06-15 22:43:49,524 - APS-System - INFO - MySQL 数据库 'aps' 已存在
2025-06-15 22:43:49,525 - APS-System - INFO - MySQL 数据库 'aps_system' 已存在
2025-06-15 22:43:49,525 - APS-System - INFO - MySQL 数据库检查成功
2025-06-15 22:43:49,525 - APS-System - INFO - 正在修复数据库权限...
2025-06-15 22:43:49,526 - APS-System - INFO - 数据库文件不存在，跳过: instance\aps.db
2025-06-15 22:43:49,526 - APS-System - INFO - 数据库文件不存在，跳过: instance\aps_system.db
2025-06-15 22:43:49,527 - APS-System - INFO - 已修复日志目录权限: logs
2025-06-15 22:43:49,527 - APS-System - INFO - 数据库权限修复成功
2025-06-15 22:43:51,117 - app - INFO - 应用启动
2025-06-15 22:43:51,120 - app.utils.scheduler - INFO - 已重新加载 0 个邮箱配置（应用实例未设置）
2025-06-15 22:43:51,130 - app.utils.scheduler - INFO - 邮件附件定时调度器已启动
2025-06-15 22:43:51,158 - app - INFO - 邮件附件定时任务调度器已启动
2025-06-15 22:43:51,166 - app.utils.scheduler - INFO - 全局定时任务已禁用，邮件调度器跟随禁用
2025-06-15 22:43:51,167 - app.utils.scheduler - INFO - 检测到全局定时任务已禁用，停止邮件调度器
2025-06-15 22:43:51,307 - app.services.scheduler_service - INFO - APScheduler调度器配置完成: timezone=Asia/Shanghai, max_workers=10
2025-06-15 22:43:51,335 - apscheduler.scheduler - INFO - Scheduler started
2025-06-15 22:43:51,339 - app.services.scheduler_service - INFO - 从数据库加载了 0 个任务
2025-06-15 22:43:51,340 - app.services.scheduler_service - INFO - APScheduler调度器启动成功
2025-06-15 22:43:51,341 - app - INFO - ✅ APScheduler统一调度器启动成功
2025-06-15 22:43:51,342 - APS-System - INFO - 应用创建成功
2025-06-15 22:43:51,342 - APS-System - INFO - ==================================================
2025-06-15 22:43:51,343 - APS-System - INFO - 🚀 APS车规芯片终测智能调度平台 v2.1
2025-06-15 22:43:51,343 - APS-System - INFO - 📊 100% MySQL数据库架构
2025-06-15 22:43:51,343 - APS-System - INFO - 🌐 访问地址: http://1********:5000
2025-06-15 22:43:51,343 - APS-System - INFO - ==================================================
2025-06-15 22:43:51,343 - APS-System - INFO - 运行模式: 开发环境
2025-06-15 22:43:51,356 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://1********:5000
2025-06-15 22:43:51,356 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-15 22:43:53,354 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:43:53] "GET / HTTP/1.1" 200 -
2025-06-15 22:43:53,380 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:43:53] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 22:43:53,615 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:43:53] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 22:43:53,693 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:43:53] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 22:43:53,694 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:43:53] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 22:43:53,694 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:43:53] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 22:43:54,122 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:43:54] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 22:44:34,289 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:44:34] "GET /production/priority-matching HTTP/1.1" 200 -
2025-06-15 22:44:34,597 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:44:34] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 22:44:34,634 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:44:34] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 22:44:34,634 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:44:34] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 22:44:34,635 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:44:34] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 22:44:34,637 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:44:34] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 22:44:34,980 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:44:34] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 22:44:41,569 - app.api.production_api - INFO - 接收到文件上传请求: 文件名=devicepriorityconfig.xlsx, 类型=devicepriorityconfig, 策略=skip
2025-06-15 22:44:41,597 - app.api.production_api - INFO - 读取Excel文件成功，共 6 行数据
2025-06-15 22:44:41,602 - app.api.production_api - ERROR - 处理第 1 行数据失败: (1054, "Unknown column 'STAGE' in 'where clause'")
2025-06-15 22:44:41,603 - app.api.production_api - ERROR - 处理第 2 行数据失败: (1054, "Unknown column 'STAGE' in 'where clause'")
2025-06-15 22:44:41,605 - app.api.production_api - ERROR - 处理第 3 行数据失败: (1054, "Unknown column 'STAGE' in 'where clause'")
2025-06-15 22:44:41,605 - app.api.production_api - ERROR - 处理第 4 行数据失败: (1054, "Unknown column 'STAGE' in 'where clause'")
2025-06-15 22:44:41,606 - app.api.production_api - ERROR - 处理第 5 行数据失败: (1054, "Unknown column 'STAGE' in 'where clause'")
2025-06-15 22:44:41,607 - app.api.production_api - ERROR - 处理第 6 行数据失败: (1054, "Unknown column 'STAGE' in 'where clause'")
2025-06-15 22:44:41,611 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:44:41] "POST /api/production/import-priority-excel HTTP/1.1" 200 -
2025-06-15 22:44:41,924 - app.api.production_api - INFO - 接收到文件上传请求: 文件名=lotpriorityconfig.xlsx, 类型=lotpriorityconfig, 策略=skip
2025-06-15 22:44:41,957 - app.api.production_api - INFO - 读取Excel文件成功，共 5 行数据
2025-06-15 22:44:41,963 - app.api.production_api - ERROR - 处理第 1 行数据失败: (1054, "Unknown column 'DEVICE' in 'where clause'")
2025-06-15 22:44:41,964 - app.api.production_api - ERROR - 处理第 2 行数据失败: (1054, "Unknown column 'DEVICE' in 'where clause'")
2025-06-15 22:44:41,965 - app.api.production_api - ERROR - 处理第 3 行数据失败: (1054, "Unknown column 'DEVICE' in 'where clause'")
2025-06-15 22:44:41,965 - app.api.production_api - ERROR - 处理第 4 行数据失败: (1054, "Unknown column 'DEVICE' in 'where clause'")
2025-06-15 22:44:41,966 - app.api.production_api - ERROR - 处理第 5 行数据失败: (1054, "Unknown column 'DEVICE' in 'where clause'")
2025-06-15 22:44:41,967 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:44:41] "POST /api/production/import-priority-excel HTTP/1.1" 200 -
2025-06-15 22:44:46,255 - app.api.production_api - INFO - 接收到文件上传请求: 文件名=devicepriorityconfig.xlsx, 类型=devicepriorityconfig, 策略=replace
2025-06-15 22:44:46,281 - app.api.production_api - INFO - 读取Excel文件成功，共 6 行数据
2025-06-15 22:44:46,287 - app.api.production_api - ERROR - 处理第 1 行数据失败: (1054, "Unknown column 'STAGE' in 'where clause'")
2025-06-15 22:44:46,290 - app.api.production_api - ERROR - 处理第 2 行数据失败: (1054, "Unknown column 'STAGE' in 'where clause'")
2025-06-15 22:44:46,291 - app.api.production_api - ERROR - 处理第 3 行数据失败: (1054, "Unknown column 'STAGE' in 'where clause'")
2025-06-15 22:44:46,292 - app.api.production_api - ERROR - 处理第 4 行数据失败: (1054, "Unknown column 'STAGE' in 'where clause'")
2025-06-15 22:44:46,293 - app.api.production_api - ERROR - 处理第 5 行数据失败: (1054, "Unknown column 'STAGE' in 'where clause'")
2025-06-15 22:44:46,295 - app.api.production_api - ERROR - 处理第 6 行数据失败: (1054, "Unknown column 'STAGE' in 'where clause'")
2025-06-15 22:44:46,299 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:44:46] "POST /api/production/import-priority-excel HTTP/1.1" 200 -
2025-06-15 22:44:46,616 - app.api.production_api - INFO - 接收到文件上传请求: 文件名=lotpriorityconfig.xlsx, 类型=lotpriorityconfig, 策略=replace
2025-06-15 22:44:46,640 - app.api.production_api - INFO - 读取Excel文件成功，共 5 行数据
2025-06-15 22:44:46,648 - app.api.production_api - ERROR - 处理第 1 行数据失败: (1054, "Unknown column 'DEVICE' in 'where clause'")
2025-06-15 22:44:46,650 - app.api.production_api - ERROR - 处理第 2 行数据失败: (1054, "Unknown column 'DEVICE' in 'where clause'")
2025-06-15 22:44:46,651 - app.api.production_api - ERROR - 处理第 3 行数据失败: (1054, "Unknown column 'DEVICE' in 'where clause'")
2025-06-15 22:44:46,652 - app.api.production_api - ERROR - 处理第 4 行数据失败: (1054, "Unknown column 'DEVICE' in 'where clause'")
2025-06-15 22:44:46,652 - app.api.production_api - ERROR - 处理第 5 行数据失败: (1054, "Unknown column 'DEVICE' in 'where clause'")
2025-06-15 22:44:46,654 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:44:46] "POST /api/production/import-priority-excel HTTP/1.1" 200 -
2025-06-15 22:48:54,236 - APS-System - INFO - 应用路径: D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12
2025-06-15 22:48:54,236 - APS-System - INFO - 实例路径: D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\instance
2025-06-15 22:48:54,238 - APS-System - INFO - 检查 MySQL 数据库配置...
2025-06-15 22:48:54,279 - APS-System - INFO - MySQL 数据库 'aps' 已存在
2025-06-15 22:48:54,279 - APS-System - INFO - MySQL 数据库 'aps_system' 已存在
2025-06-15 22:48:54,279 - APS-System - INFO - MySQL 数据库检查成功
2025-06-15 22:48:54,280 - APS-System - INFO - 正在修复数据库权限...
2025-06-15 22:48:54,280 - APS-System - INFO - 数据库文件不存在，跳过: instance\aps.db
2025-06-15 22:48:54,280 - APS-System - INFO - 数据库文件不存在，跳过: instance\aps_system.db
2025-06-15 22:48:54,280 - APS-System - INFO - 已修复日志目录权限: logs
2025-06-15 22:48:54,280 - APS-System - INFO - 数据库权限修复成功
2025-06-15 22:48:56,329 - app - INFO - 应用启动
2025-06-15 22:48:56,331 - app.utils.scheduler - INFO - 已重新加载 0 个邮箱配置（应用实例未设置）
2025-06-15 22:48:56,341 - app.utils.scheduler - INFO - 邮件附件定时调度器已启动
2025-06-15 22:48:56,378 - app - INFO - 邮件附件定时任务调度器已启动
2025-06-15 22:48:56,388 - app.utils.scheduler - INFO - 全局定时任务已禁用，邮件调度器跟随禁用
2025-06-15 22:48:56,390 - app.utils.scheduler - INFO - 检测到全局定时任务已禁用，停止邮件调度器
2025-06-15 22:48:56,550 - app.services.scheduler_service - INFO - APScheduler调度器配置完成: timezone=Asia/Shanghai, max_workers=10
2025-06-15 22:48:56,567 - apscheduler.scheduler - INFO - Scheduler started
2025-06-15 22:48:56,572 - app.services.scheduler_service - INFO - 从数据库加载了 0 个任务
2025-06-15 22:48:56,585 - app.services.scheduler_service - INFO - APScheduler调度器启动成功
2025-06-15 22:48:56,590 - app - INFO - ✅ APScheduler统一调度器启动成功
2025-06-15 22:48:56,598 - APS-System - INFO - 应用创建成功
2025-06-15 22:48:56,600 - APS-System - INFO - ==================================================
2025-06-15 22:48:56,601 - APS-System - INFO - 🚀 APS车规芯片终测智能调度平台 v2.1
2025-06-15 22:48:56,604 - APS-System - INFO - 📊 100% MySQL数据库架构
2025-06-15 22:48:56,605 - APS-System - INFO - 🌐 访问地址: http://1********:5000
2025-06-15 22:48:56,605 - APS-System - INFO - ==================================================
2025-06-15 22:48:56,605 - APS-System - INFO - 运行模式: 开发环境
2025-06-15 22:48:56,624 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://1********:5000
2025-06-15 22:48:56,624 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-15 22:48:58,684 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:48:58] "GET / HTTP/1.1" 200 -
2025-06-15 22:48:58,727 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:48:58] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 22:48:58,946 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:48:58] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 22:48:59,053 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:48:59] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 22:48:59,054 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:48:59] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 22:48:59,057 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:48:59] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 22:48:59,654 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:48:59] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 22:49:35,849 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:49:35] "[32mPOST /api/production/import-priority-excel HTTP/1.1[0m" 302 -
2025-06-15 22:49:37,893 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:49:37] "GET /auth/login?next=/api/production/import-priority-excel HTTP/1.1" 200 -
2025-06-15 22:49:39,940 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:49:39] "[32mPOST /api/production/import-priority-excel HTTP/1.1[0m" 302 -
2025-06-15 22:49:41,998 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:49:41] "GET /auth/login?next=/api/production/import-priority-excel HTTP/1.1" 200 -
2025-06-15 22:49:44,045 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:49:44] "[33mGET /api/production/lot-priority-config?per_page=100 HTTP/1.1[0m" 404 -
2025-06-15 22:49:46,092 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:49:46] "[33mGET /api/production/device-priority-config?per_page=100 HTTP/1.1[0m" 404 -
2025-06-15 22:53:16,407 - APS-System - INFO - 应用路径: D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12
2025-06-15 22:53:16,408 - APS-System - INFO - 实例路径: D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\instance
2025-06-15 22:53:16,408 - APS-System - INFO - 检查 MySQL 数据库配置...
2025-06-15 22:53:16,451 - APS-System - INFO - MySQL 数据库 'aps' 已存在
2025-06-15 22:53:16,452 - APS-System - INFO - MySQL 数据库 'aps_system' 已存在
2025-06-15 22:53:16,452 - APS-System - INFO - MySQL 数据库检查成功
2025-06-15 22:53:16,452 - APS-System - INFO - 正在修复数据库权限...
2025-06-15 22:53:16,453 - APS-System - INFO - 数据库文件不存在，跳过: instance\aps.db
2025-06-15 22:53:16,453 - APS-System - INFO - 数据库文件不存在，跳过: instance\aps_system.db
2025-06-15 22:53:16,454 - APS-System - INFO - 已修复日志目录权限: logs
2025-06-15 22:53:16,454 - APS-System - INFO - 数据库权限修复成功
2025-06-15 22:53:18,119 - app - INFO - 应用启动
2025-06-15 22:53:18,124 - app.utils.scheduler - INFO - 已重新加载 0 个邮箱配置（应用实例未设置）
2025-06-15 22:53:18,140 - app.utils.scheduler - INFO - 邮件附件定时调度器已启动
2025-06-15 22:53:18,173 - app - INFO - 邮件附件定时任务调度器已启动
2025-06-15 22:53:18,192 - app.utils.scheduler - INFO - 全局定时任务已禁用，邮件调度器跟随禁用
2025-06-15 22:53:18,201 - app.utils.scheduler - INFO - 检测到全局定时任务已禁用，停止邮件调度器
2025-06-15 22:53:18,314 - app.services.scheduler_service - INFO - APScheduler调度器配置完成: timezone=Asia/Shanghai, max_workers=10
2025-06-15 22:53:18,343 - apscheduler.scheduler - INFO - Scheduler started
2025-06-15 22:53:18,347 - app.services.scheduler_service - INFO - 从数据库加载了 0 个任务
2025-06-15 22:53:18,350 - app.services.scheduler_service - INFO - APScheduler调度器启动成功
2025-06-15 22:53:18,352 - app - INFO - ✅ APScheduler统一调度器启动成功
2025-06-15 22:53:18,354 - APS-System - INFO - 应用创建成功
2025-06-15 22:53:18,354 - APS-System - INFO - ==================================================
2025-06-15 22:53:18,354 - APS-System - INFO - 🚀 APS车规芯片终测智能调度平台 v2.1
2025-06-15 22:53:18,355 - APS-System - INFO - 📊 100% MySQL数据库架构
2025-06-15 22:53:18,355 - APS-System - INFO - 🌐 访问地址: http://1********:5000
2025-06-15 22:53:18,356 - APS-System - INFO - ==================================================
2025-06-15 22:53:18,356 - APS-System - INFO - 运行模式: 开发环境
2025-06-15 22:53:18,367 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://1********:5000
2025-06-15 22:53:18,367 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-15 22:53:23,532 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:53:23] "[32mGET /index HTTP/1.1[0m" 302 -
2025-06-15 22:53:23,543 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:53:23] "GET /auth/login?next=/index HTTP/1.1" 200 -
2025-06-15 22:53:23,797 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:53:23] "GET /static/css/login/bootstrap.min.css HTTP/1.1" 200 -
2025-06-15 22:53:23,892 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:53:23] "GET /static/css/all-5.15.4.min.css HTTP/1.1" 200 -
2025-06-15 22:53:23,892 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:53:23] "GET /static/css/compatibility.css HTTP/1.1" 200 -
2025-06-15 22:53:23,893 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:53:23] "GET /static/js/login/bootstrap.bundle.min.js HTTP/1.1" 200 -
2025-06-15 22:53:24,242 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:53:24] "GET /static/webfonts/fa-solid-900.woff2 HTTP/1.1" 200 -
2025-06-15 22:53:24,726 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:53:24] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-06-15 22:53:25,529 - app.auth.routes - INFO - 尝试登录用户: admin
2025-06-15 22:53:25,538 - app.auth.routes - INFO - 找到用户: admin, 角色: admin, 密码哈希: scrypt:32768:8:1$X8JU5hxR74hpH0tP$36e5b028b335f5d2f9f21e9178a0d529cca80459428b1128d44d8ac646d8f16cdd0823ea12d58ce5bbae1dd243eeb7a50c06f55d6688542dae997f7c75e6ee24
2025-06-15 22:53:25,694 - app.models - INFO - 用户 admin 密码验证结果: True
2025-06-15 22:53:25,695 - app.auth.routes - INFO - 密码验证结果: True
2025-06-15 22:53:25,697 - app.auth.routes - INFO - 用户 admin 登录成功
2025-06-15 22:53:25,703 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:53:25] "[32mPOST /auth/login HTTP/1.1[0m" 302 -
2025-06-15 22:53:25,713 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:53:25] "GET /index HTTP/1.1" 200 -
2025-06-15 22:53:25,777 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:53:25] "GET /static/css/base/bootstrap.min.css HTTP/1.1" 200 -
2025-06-15 22:53:26,054 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:53:26] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 22:53:26,055 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:53:26] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 22:53:26,060 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:53:26] "GET /static/js/base/bootstrap.bundle.min.js HTTP/1.1" 200 -
2025-06-15 22:53:26,064 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:53:26] "GET /static/js/menu-optimizer.js HTTP/1.1" 200 -
2025-06-15 22:53:26,379 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:53:26] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 22:53:26,381 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:53:26] "GET /menu HTTP/1.1" 200 -
2025-06-15 22:53:28,144 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:53:28] "GET /production/priority-matching HTTP/1.1" 200 -
2025-06-15 22:53:28,451 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:53:28] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 22:53:28,484 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:53:28] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 22:53:28,485 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:53:28] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 22:53:28,486 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:53:28] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 22:53:28,488 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:53:28] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 22:53:37,391 - app.api.production_api - INFO - 接收到文件上传请求: 文件名=devicepriorityconfig.xlsx, 类型=devicepriorityconfig, 策略=replace
2025-06-15 22:53:37,418 - app.api.production_api - INFO - 读取Excel文件成功，共 6 行数据
2025-06-15 22:53:37,423 - app.api.production_api - ERROR - 处理第 1 行数据失败: (1054, "Unknown column 'STAGE' in 'where clause'")
2025-06-15 22:53:37,424 - app.api.production_api - ERROR - 处理第 2 行数据失败: (1054, "Unknown column 'STAGE' in 'where clause'")
2025-06-15 22:53:37,425 - app.api.production_api - ERROR - 处理第 3 行数据失败: (1054, "Unknown column 'STAGE' in 'where clause'")
2025-06-15 22:53:37,425 - app.api.production_api - ERROR - 处理第 4 行数据失败: (1054, "Unknown column 'STAGE' in 'where clause'")
2025-06-15 22:53:37,426 - app.api.production_api - ERROR - 处理第 5 行数据失败: (1054, "Unknown column 'STAGE' in 'where clause'")
2025-06-15 22:53:37,427 - app.api.production_api - ERROR - 处理第 6 行数据失败: (1054, "Unknown column 'STAGE' in 'where clause'")
2025-06-15 22:53:37,430 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:53:37] "POST /api/production/import-priority-excel HTTP/1.1" 200 -
2025-06-15 22:53:37,655 - app.api.production_api - INFO - 接收到文件上传请求: 文件名=lotpriorityconfig.xlsx, 类型=lotpriorityconfig, 策略=replace
2025-06-15 22:53:37,696 - app.api.production_api - INFO - 读取Excel文件成功，共 5 行数据
2025-06-15 22:53:37,702 - app.api.production_api - ERROR - 处理第 1 行数据失败: (1054, "Unknown column 'DEVICE' in 'where clause'")
2025-06-15 22:53:37,704 - app.api.production_api - ERROR - 处理第 2 行数据失败: (1054, "Unknown column 'DEVICE' in 'where clause'")
2025-06-15 22:53:37,705 - app.api.production_api - ERROR - 处理第 3 行数据失败: (1054, "Unknown column 'DEVICE' in 'where clause'")
2025-06-15 22:53:37,706 - app.api.production_api - ERROR - 处理第 4 行数据失败: (1054, "Unknown column 'DEVICE' in 'where clause'")
2025-06-15 22:53:37,708 - app.api.production_api - ERROR - 处理第 5 行数据失败: (1054, "Unknown column 'DEVICE' in 'where clause'")
2025-06-15 22:53:37,711 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:53:37] "POST /api/production/import-priority-excel HTTP/1.1" 200 -
2025-06-15 22:53:44,001 - app.api.production_api - INFO - 接收到文件上传请求: 文件名=devicepriorityconfig.xlsx, 类型=devicepriorityconfig, 策略=update
2025-06-15 22:53:44,029 - app.api.production_api - INFO - 读取Excel文件成功，共 6 行数据
2025-06-15 22:53:44,034 - app.api.production_api - ERROR - 处理第 1 行数据失败: (1054, "Unknown column 'STAGE' in 'where clause'")
2025-06-15 22:53:44,036 - app.api.production_api - ERROR - 处理第 2 行数据失败: (1054, "Unknown column 'STAGE' in 'where clause'")
2025-06-15 22:53:44,037 - app.api.production_api - ERROR - 处理第 3 行数据失败: (1054, "Unknown column 'STAGE' in 'where clause'")
2025-06-15 22:53:44,038 - app.api.production_api - ERROR - 处理第 4 行数据失败: (1054, "Unknown column 'STAGE' in 'where clause'")
2025-06-15 22:53:44,039 - app.api.production_api - ERROR - 处理第 5 行数据失败: (1054, "Unknown column 'STAGE' in 'where clause'")
2025-06-15 22:53:44,040 - app.api.production_api - ERROR - 处理第 6 行数据失败: (1054, "Unknown column 'STAGE' in 'where clause'")
2025-06-15 22:53:44,044 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:53:44] "POST /api/production/import-priority-excel HTTP/1.1" 200 -
2025-06-15 22:53:44,368 - app.api.production_api - INFO - 接收到文件上传请求: 文件名=lotpriorityconfig.xlsx, 类型=lotpriorityconfig, 策略=update
2025-06-15 22:53:44,398 - app.api.production_api - INFO - 读取Excel文件成功，共 5 行数据
2025-06-15 22:53:44,404 - app.api.production_api - ERROR - 处理第 1 行数据失败: (1054, "Unknown column 'DEVICE' in 'where clause'")
2025-06-15 22:53:44,405 - app.api.production_api - ERROR - 处理第 2 行数据失败: (1054, "Unknown column 'DEVICE' in 'where clause'")
2025-06-15 22:53:44,405 - app.api.production_api - ERROR - 处理第 3 行数据失败: (1054, "Unknown column 'DEVICE' in 'where clause'")
2025-06-15 22:53:44,406 - app.api.production_api - ERROR - 处理第 4 行数据失败: (1054, "Unknown column 'DEVICE' in 'where clause'")
2025-06-15 22:53:44,406 - app.api.production_api - ERROR - 处理第 5 行数据失败: (1054, "Unknown column 'DEVICE' in 'where clause'")
2025-06-15 22:53:44,408 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:53:44] "POST /api/production/import-priority-excel HTTP/1.1" 200 -
2025-06-15 22:53:48,618 - app.api.production_api - INFO - 接收到文件上传请求: 文件名=devicepriorityconfig.xlsx, 类型=devicepriorityconfig, 策略=skip
2025-06-15 22:53:48,650 - app.api.production_api - INFO - 读取Excel文件成功，共 6 行数据
2025-06-15 22:53:48,655 - app.api.production_api - ERROR - 处理第 1 行数据失败: (1054, "Unknown column 'STAGE' in 'where clause'")
2025-06-15 22:53:48,656 - app.api.production_api - ERROR - 处理第 2 行数据失败: (1054, "Unknown column 'STAGE' in 'where clause'")
2025-06-15 22:53:48,657 - app.api.production_api - ERROR - 处理第 3 行数据失败: (1054, "Unknown column 'STAGE' in 'where clause'")
2025-06-15 22:53:48,658 - app.api.production_api - ERROR - 处理第 4 行数据失败: (1054, "Unknown column 'STAGE' in 'where clause'")
2025-06-15 22:53:48,660 - app.api.production_api - ERROR - 处理第 5 行数据失败: (1054, "Unknown column 'STAGE' in 'where clause'")
2025-06-15 22:53:48,660 - app.api.production_api - ERROR - 处理第 6 行数据失败: (1054, "Unknown column 'STAGE' in 'where clause'")
2025-06-15 22:53:48,663 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:53:48] "POST /api/production/import-priority-excel HTTP/1.1" 200 -
2025-06-15 22:53:48,986 - app.api.production_api - INFO - 接收到文件上传请求: 文件名=lotpriorityconfig.xlsx, 类型=lotpriorityconfig, 策略=skip
2025-06-15 22:53:49,004 - app.api.production_api - INFO - 读取Excel文件成功，共 5 行数据
2025-06-15 22:53:49,014 - app.api.production_api - ERROR - 处理第 1 行数据失败: (1054, "Unknown column 'DEVICE' in 'where clause'")
2025-06-15 22:53:49,016 - app.api.production_api - ERROR - 处理第 2 行数据失败: (1054, "Unknown column 'DEVICE' in 'where clause'")
2025-06-15 22:53:49,016 - app.api.production_api - ERROR - 处理第 3 行数据失败: (1054, "Unknown column 'DEVICE' in 'where clause'")
2025-06-15 22:53:49,017 - app.api.production_api - ERROR - 处理第 4 行数据失败: (1054, "Unknown column 'DEVICE' in 'where clause'")
2025-06-15 22:53:49,020 - app.api.production_api - ERROR - 处理第 5 行数据失败: (1054, "Unknown column 'DEVICE' in 'where clause'")
2025-06-15 22:53:49,021 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:53:49] "POST /api/production/import-priority-excel HTTP/1.1" 200 -
2025-06-15 22:56:55,617 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:56:55] "GET /production/semi-auto HTTP/1.1" 200 -
2025-06-15 22:56:55,690 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:56:55] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 22:56:55,874 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:56:55] "GET /static/css/core/main.css HTTP/1.1" 200 -
2025-06-15 22:56:55,952 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:56:55] "GET /static/css/daygrid/main.css HTTP/1.1" 200 -
2025-06-15 22:56:55,954 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:56:55] "GET /static/css/timegrid/main.css HTTP/1.1" 200 -
2025-06-15 22:56:55,958 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:56:55] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 22:56:55,959 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:56:55] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 22:56:56,002 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:56:56] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 22:56:56,183 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:56:56] "GET /static/js/xlsx.full.min.js HTTP/1.1" 200 -
2025-06-15 22:56:56,262 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:56:56] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 22:56:56,290 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:56:56] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 22:56:56,621 - app - INFO - 成功连接到MySQL数据库: aps
2025-06-15 22:56:56,626 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:56:56] "GET /api/production/get-import-path HTTP/1.1" 200 -
2025-06-15 22:56:56,635 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:56:56] "GET /api/production/imported-files HTTP/1.1" 200 -
2025-06-15 22:57:29,948 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:57:29] "GET /resources/hardware HTTP/1.1" 200 -
2025-06-15 22:57:30,230 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:57:30] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 22:57:30,283 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:57:30] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 22:57:30,286 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:57:30] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 22:57:30,289 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:57:30] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 22:57:30,291 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:57:30] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 22:57:30,605 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:57:30] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 22:57:30,635 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:57:30] "[33mGET /api/user-filter-presets?page_type=hardware_resources HTTP/1.1[0m" 404 -
2025-06-15 22:57:30,640 - app - INFO - 成功连接到MySQL数据库: aps
2025-06-15 22:57:30,650 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:57:30] "GET /api/resources/data/eqp_status?page=1&per_page=50 HTTP/1.1" 200 -
2025-06-15 22:57:36,885 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:57:36] "GET /resources/uph HTTP/1.1" 200 -
2025-06-15 22:57:37,184 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:57:37] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 22:57:37,222 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:57:37] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 22:57:37,222 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:57:37] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 22:57:37,222 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:57:37] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 22:57:37,227 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:57:37] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 22:57:37,541 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:57:37] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 22:57:37,558 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:57:37] "[33mGET /api/user-filter-presets?page_type=ET_UPH_EQP HTTP/1.1[0m" 404 -
2025-06-15 22:57:37,584 - app - INFO - 成功连接到MySQL数据库: aps
2025-06-15 22:57:37,597 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:57:37] "GET /api/resources/data/ET_UPH_EQP?page=1&per_page=50 HTTP/1.1" 200 -
2025-06-15 22:57:39,680 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:57:39] "GET /resources/hardware HTTP/1.1" 200 -
2025-06-15 22:57:39,981 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:57:39] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 22:57:40,020 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:57:40] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 22:57:40,022 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:57:40] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 22:57:40,026 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:57:40] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 22:57:40,026 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:57:40] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 22:57:40,352 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:57:40] "[33mGET /api/user-filter-presets?page_type=hardware_resources HTTP/1.1[0m" 404 -
2025-06-15 22:57:40,353 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:57:40] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 22:57:40,359 - app - INFO - 成功连接到MySQL数据库: aps
2025-06-15 22:57:40,367 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:57:40] "GET /api/resources/data/eqp_status?page=1&per_page=50 HTTP/1.1" 200 -
2025-06-15 22:57:59,008 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:57:59] "GET /production/semi-auto HTTP/1.1" 200 -
2025-06-15 22:57:59,313 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:57:59] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 22:57:59,344 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:57:59] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 22:57:59,346 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:57:59] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 22:57:59,346 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:57:59] "[36mGET /static/css/core/main.css HTTP/1.1[0m" 304 -
2025-06-15 22:57:59,347 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:57:59] "[36mGET /static/css/daygrid/main.css HTTP/1.1[0m" 304 -
2025-06-15 22:57:59,350 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:57:59] "[36mGET /static/css/timegrid/main.css HTTP/1.1[0m" 304 -
2025-06-15 22:57:59,621 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:57:59] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 22:57:59,653 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:57:59] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 22:57:59,656 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:57:59] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 22:57:59,660 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:57:59] "[36mGET /static/js/xlsx.full.min.js HTTP/1.1[0m" 304 -
2025-06-15 22:58:00,028 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:58:00] "GET /api/production/get-import-path HTTP/1.1" 200 -
2025-06-15 22:58:00,030 - app - INFO - 成功连接到MySQL数据库: aps
2025-06-15 22:58:00,049 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:58:00] "GET /api/production/imported-files HTTP/1.1" 200 -
2025-06-15 22:58:21,608 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:58:21] "GET /production/priority-matching HTTP/1.1" 200 -
2025-06-15 22:58:21,911 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:58:21] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 22:58:21,949 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:58:21] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 22:58:21,953 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:58:21] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 22:58:21,955 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:58:21] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 22:58:21,959 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:58:21] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 22:58:22,263 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:58:22] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 22:58:26,380 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:58:26] "GET /production/semi-auto HTTP/1.1" 200 -
2025-06-15 22:58:26,467 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:58:26] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 22:58:26,636 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:58:26] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 22:58:26,715 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:58:26] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 22:58:26,719 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:58:26] "[36mGET /static/css/daygrid/main.css HTTP/1.1[0m" 304 -
2025-06-15 22:58:26,721 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:58:26] "[36mGET /static/css/core/main.css HTTP/1.1[0m" 304 -
2025-06-15 22:58:26,724 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:58:26] "[36mGET /static/css/timegrid/main.css HTTP/1.1[0m" 304 -
2025-06-15 22:58:26,774 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:58:26] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 22:58:26,945 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:58:26] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 22:58:27,037 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:58:27] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 22:58:27,042 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:58:27] "[36mGET /static/js/xlsx.full.min.js HTTP/1.1[0m" 304 -
2025-06-15 22:58:27,410 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:58:27] "GET /api/production/get-import-path HTTP/1.1" 200 -
2025-06-15 22:58:27,411 - app - INFO - 成功连接到MySQL数据库: aps
2025-06-15 22:58:27,430 - werkzeug - INFO - 1******** - - [15/Jun/2025 22:58:27] "GET /api/production/imported-files HTTP/1.1" 200 -
2025-06-15 23:00:25,511 - werkzeug - INFO - 1******** - - [15/Jun/2025 23:00:25] "GET /production/auto HTTP/1.1" 200 -
2025-06-15 23:00:25,557 - werkzeug - INFO - 1******** - - [15/Jun/2025 23:00:25] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 23:00:25,759 - werkzeug - INFO - 1******** - - [15/Jun/2025 23:00:25] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 23:00:25,857 - werkzeug - INFO - 1******** - - [15/Jun/2025 23:00:25] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 23:00:25,858 - werkzeug - INFO - 1******** - - [15/Jun/2025 23:00:25] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 23:00:25,862 - werkzeug - INFO - 1******** - - [15/Jun/2025 23:00:25] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 23:00:25,885 - werkzeug - INFO - 1******** - - [15/Jun/2025 23:00:25] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 23:00:26,198 - werkzeug - INFO - 1******** - - [15/Jun/2025 23:00:26] "GET /api/production/schedule-history HTTP/1.1" 200 -
2025-06-15 23:00:30,940 - werkzeug - INFO - 1******** - - [15/Jun/2025 23:00:30] "GET /production/semi-auto HTTP/1.1" 200 -
2025-06-15 23:00:31,241 - werkzeug - INFO - 1******** - - [15/Jun/2025 23:00:31] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 23:00:31,275 - werkzeug - INFO - 1******** - - [15/Jun/2025 23:00:31] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 23:00:31,280 - werkzeug - INFO - 1******** - - [15/Jun/2025 23:00:31] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 23:00:31,287 - werkzeug - INFO - 1******** - - [15/Jun/2025 23:00:31] "[36mGET /static/css/core/main.css HTTP/1.1[0m" 304 -
2025-06-15 23:00:31,293 - werkzeug - INFO - 1******** - - [15/Jun/2025 23:00:31] "[36mGET /static/css/daygrid/main.css HTTP/1.1[0m" 304 -
2025-06-15 23:00:31,291 - werkzeug - INFO - 1******** - - [15/Jun/2025 23:00:31] "[36mGET /static/css/timegrid/main.css HTTP/1.1[0m" 304 -
2025-06-15 23:00:31,547 - werkzeug - INFO - 1******** - - [15/Jun/2025 23:00:31] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 23:00:31,593 - werkzeug - INFO - 1******** - - [15/Jun/2025 23:00:31] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 23:00:31,594 - werkzeug - INFO - 1******** - - [15/Jun/2025 23:00:31] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 23:00:31,610 - werkzeug - INFO - 1******** - - [15/Jun/2025 23:00:31] "[36mGET /static/js/xlsx.full.min.js HTTP/1.1[0m" 304 -
2025-06-15 23:00:31,964 - werkzeug - INFO - 1******** - - [15/Jun/2025 23:00:31] "GET /api/production/get-import-path HTTP/1.1" 200 -
2025-06-15 23:00:31,989 - app - INFO - 成功连接到MySQL数据库: aps
2025-06-15 23:00:32,006 - werkzeug - INFO - 1******** - - [15/Jun/2025 23:00:32] "GET /api/production/imported-files HTTP/1.1" 200 -
2025-06-15 23:00:34,222 - werkzeug - INFO - 1******** - - [15/Jun/2025 23:00:34] "GET /production/auto HTTP/1.1" 200 -
2025-06-15 23:00:34,531 - werkzeug - INFO - 1******** - - [15/Jun/2025 23:00:34] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 23:00:34,563 - werkzeug - INFO - 1******** - - [15/Jun/2025 23:00:34] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 23:00:34,563 - werkzeug - INFO - 1******** - - [15/Jun/2025 23:00:34] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 23:00:34,564 - werkzeug - INFO - 1******** - - [15/Jun/2025 23:00:34] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 23:00:34,566 - werkzeug - INFO - 1******** - - [15/Jun/2025 23:00:34] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 23:00:34,884 - werkzeug - INFO - 1******** - - [15/Jun/2025 23:00:34] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 23:00:34,888 - werkzeug - INFO - 1******** - - [15/Jun/2025 23:00:34] "GET /api/production/schedule-history HTTP/1.1" 200 -
2025-06-15 23:00:42,922 - werkzeug - INFO - 1******** - - [15/Jun/2025 23:00:42] "GET /production/algorithm HTTP/1.1" 200 -
2025-06-15 23:00:43,232 - werkzeug - INFO - 1******** - - [15/Jun/2025 23:00:43] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 23:00:43,264 - werkzeug - INFO - 1******** - - [15/Jun/2025 23:00:43] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 23:00:43,268 - werkzeug - INFO - 1******** - - [15/Jun/2025 23:00:43] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 23:00:43,271 - werkzeug - INFO - 1******** - - [15/Jun/2025 23:00:43] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 23:00:43,285 - werkzeug - INFO - 1******** - - [15/Jun/2025 23:00:43] "[36mGET /static/js/xlsx.full.min.js HTTP/1.1[0m" 304 -
2025-06-15 23:00:43,286 - werkzeug - INFO - 1******** - - [15/Jun/2025 23:00:43] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 23:00:43,598 - werkzeug - INFO - 1******** - - [15/Jun/2025 23:00:43] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 23:00:43,659 - werkzeug - INFO - 1******** - - [15/Jun/2025 23:00:43] "[33mGET /api/production/history-times HTTP/1.1[0m" 404 -
2025-06-15 23:00:51,414 - werkzeug - INFO - 1******** - - [15/Jun/2025 23:00:51] "GET /production/auto HTTP/1.1" 200 -
2025-06-15 23:00:51,518 - werkzeug - INFO - 1******** - - [15/Jun/2025 23:00:51] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 23:00:51,673 - werkzeug - INFO - 1******** - - [15/Jun/2025 23:00:51] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 23:00:51,758 - werkzeug - INFO - 1******** - - [15/Jun/2025 23:00:51] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 23:00:51,761 - werkzeug - INFO - 1******** - - [15/Jun/2025 23:00:51] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 23:00:51,760 - werkzeug - INFO - 1******** - - [15/Jun/2025 23:00:51] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 23:00:51,759 - werkzeug - INFO - 1******** - - [15/Jun/2025 23:00:51] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 23:00:52,003 - werkzeug - INFO - 1******** - - [15/Jun/2025 23:00:52] "GET /api/production/schedule-history HTTP/1.1" 200 -
2025-06-15 23:00:52,531 - werkzeug - INFO - 1******** - - [15/Jun/2025 23:00:52] "GET /production/semi-auto HTTP/1.1" 200 -
2025-06-15 23:00:52,835 - werkzeug - INFO - 1******** - - [15/Jun/2025 23:00:52] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 23:00:52,867 - werkzeug - INFO - 1******** - - [15/Jun/2025 23:00:52] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 23:00:52,871 - werkzeug - INFO - 1******** - - [15/Jun/2025 23:00:52] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 23:00:52,872 - werkzeug - INFO - 1******** - - [15/Jun/2025 23:00:52] "[36mGET /static/css/core/main.css HTTP/1.1[0m" 304 -
2025-06-15 23:00:52,890 - werkzeug - INFO - 1******** - - [15/Jun/2025 23:00:52] "[36mGET /static/css/daygrid/main.css HTTP/1.1[0m" 304 -
2025-06-15 23:00:52,901 - werkzeug - INFO - 1******** - - [15/Jun/2025 23:00:52] "[36mGET /static/css/timegrid/main.css HTTP/1.1[0m" 304 -
2025-06-15 23:00:53,144 - werkzeug - INFO - 1******** - - [15/Jun/2025 23:00:53] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 23:00:53,177 - werkzeug - INFO - 1******** - - [15/Jun/2025 23:00:53] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 23:00:53,192 - werkzeug - INFO - 1******** - - [15/Jun/2025 23:00:53] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 23:00:53,221 - werkzeug - INFO - 1******** - - [15/Jun/2025 23:00:53] "[36mGET /static/js/xlsx.full.min.js HTTP/1.1[0m" 304 -
2025-06-15 23:00:53,587 - werkzeug - INFO - 1******** - - [15/Jun/2025 23:00:53] "GET /api/production/get-import-path HTTP/1.1" 200 -
2025-06-15 23:00:53,589 - app - INFO - 成功连接到MySQL数据库: aps
2025-06-15 23:00:53,605 - werkzeug - INFO - 1******** - - [15/Jun/2025 23:00:53] "GET /api/production/imported-files HTTP/1.1" 200 -
2025-06-15 23:01:42,851 - werkzeug - INFO - 1******** - - [15/Jun/2025 23:01:42] "GET /orders/semi-auto HTTP/1.1" 200 -
2025-06-15 23:01:43,123 - werkzeug - INFO - 1******** - - [15/Jun/2025 23:01:43] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-15 23:01:43,185 - werkzeug - INFO - 1******** - - [15/Jun/2025 23:01:43] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-15 23:01:43,186 - werkzeug - INFO - 1******** - - [15/Jun/2025 23:01:43] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-15 23:01:43,187 - werkzeug - INFO - 1******** - - [15/Jun/2025 23:01:43] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-15 23:01:43,189 - werkzeug - INFO - 1******** - - [15/Jun/2025 23:01:43] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-15 23:01:43,522 - werkzeug - INFO - 1******** - - [15/Jun/2025 23:01:43] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-15 23:01:43,556 - werkzeug - INFO - 1******** - - [15/Jun/2025 23:01:43] "GET /api/excel_mappings HTTP/1.1" 200 -
2025-06-15 23:01:43,556 - werkzeug - INFO - 1******** - - [15/Jun/2025 23:01:43] "GET /api/email_configs?_=1749999703225 HTTP/1.1" 200 -
2025-06-16 10:42:26,623 - APS-System - INFO - 应用路径: D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12
2025-06-16 10:42:26,624 - APS-System - INFO - 实例路径: D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\instance
2025-06-16 10:42:26,624 - APS-System - INFO - 检查 MySQL 数据库配置...
2025-06-16 10:42:26,748 - APS-System - INFO - MySQL 数据库 'aps' 已存在
2025-06-16 10:42:26,750 - APS-System - INFO - MySQL 数据库 'aps_system' 已存在
2025-06-16 10:42:26,750 - APS-System - INFO - MySQL 数据库检查成功
2025-06-16 10:42:26,750 - APS-System - INFO - 正在修复数据库权限...
2025-06-16 10:42:26,750 - APS-System - INFO - 数据库文件不存在，跳过: instance\aps.db
2025-06-16 10:42:26,750 - APS-System - INFO - 数据库文件不存在，跳过: instance\aps_system.db
2025-06-16 10:42:26,752 - APS-System - INFO - 已修复日志目录权限: logs
2025-06-16 10:42:26,752 - APS-System - INFO - 数据库权限修复成功
2025-06-16 10:42:28,484 - app.config.feature_flags - INFO - 已应用功能配置文件: production
2025-06-16 10:42:28,492 - app - INFO - ✅ API v2蓝图已注册
2025-06-16 10:42:28,496 - app - INFO - 应用启动
2025-06-16 10:42:28,497 - app.utils.scheduler - INFO - 已重新加载 0 个邮箱配置（应用实例未设置）
2025-06-16 10:42:28,506 - app.utils.scheduler - INFO - 邮件附件定时调度器已启动
2025-06-16 10:42:28,541 - app - INFO - 邮件附件定时任务调度器已启动
2025-06-16 10:42:28,583 - app.utils.scheduler - INFO - 全局定时任务已禁用，邮件调度器跟随禁用
2025-06-16 10:42:28,585 - app.utils.scheduler - INFO - 检测到全局定时任务已禁用，停止邮件调度器
2025-06-16 10:42:28,780 - app.services.scheduler_service - INFO - APScheduler调度器配置完成: timezone=Asia/Shanghai, max_workers=10
2025-06-16 10:42:28,816 - apscheduler.scheduler - INFO - Scheduler started
2025-06-16 10:42:28,822 - app.services.scheduler_service - INFO - 从数据库加载了 0 个任务
2025-06-16 10:42:28,824 - app.services.scheduler_service - INFO - APScheduler调度器启动成功
2025-06-16 10:42:28,825 - app - INFO - ✅ APScheduler统一调度器启动成功
2025-06-16 10:42:28,826 - APS-System - INFO - 应用创建成功
2025-06-16 10:42:28,827 - APS-System - INFO - ==================================================
2025-06-16 10:42:28,827 - APS-System - INFO - 🚀 APS车规芯片终测智能调度平台 v2.1
2025-06-16 10:42:28,827 - APS-System - INFO - 📊 100% MySQL数据库架构
2025-06-16 10:42:28,827 - APS-System - INFO - 🌐 访问地址: http://1********:5000
2025-06-16 10:42:28,827 - APS-System - INFO - ==================================================
2025-06-16 10:42:28,827 - APS-System - INFO - 运行模式: 开发环境
2025-06-16 10:42:28,843 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://1********:5000
2025-06-16 10:42:28,843 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-16 10:42:31,034 - werkzeug - INFO - 1******** - - [16/Jun/2025 10:42:31] "[32mGET / HTTP/1.1[0m" 302 -
2025-06-16 10:42:31,053 - werkzeug - INFO - 1******** - - [16/Jun/2025 10:42:31] "GET /auth/login?next=/ HTTP/1.1" 200 -
2025-06-16 10:42:31,548 - werkzeug - INFO - 1******** - - [16/Jun/2025 10:42:31] "GET /static/css/login/bootstrap.min.css HTTP/1.1" 200 -
2025-06-16 10:42:31,755 - werkzeug - INFO - 1******** - - [16/Jun/2025 10:42:31] "GET /static/js/login/bootstrap.bundle.min.js HTTP/1.1" 200 -
2025-06-16 10:42:31,781 - werkzeug - INFO - 1******** - - [16/Jun/2025 10:42:31] "GET /static/css/all-5.15.4.min.css HTTP/1.1" 200 -
2025-06-16 10:42:31,787 - werkzeug - INFO - 1******** - - [16/Jun/2025 10:42:31] "GET /static/css/compatibility.css HTTP/1.1" 200 -
2025-06-16 10:42:32,145 - werkzeug - INFO - 1******** - - [16/Jun/2025 10:42:32] "GET /static/webfonts/fa-solid-900.woff2 HTTP/1.1" 200 -
2025-06-16 10:42:32,638 - werkzeug - INFO - 1******** - - [16/Jun/2025 10:42:32] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-06-16 10:42:33,358 - app.auth.routes - INFO - 尝试登录用户: admin
2025-06-16 10:42:33,372 - app.auth.routes - INFO - 找到用户: admin, 角色: admin, 密码哈希: scrypt:32768:8:1$X8JU5hxR74hpH0tP$36e5b028b335f5d2f9f21e9178a0d529cca80459428b1128d44d8ac646d8f16cdd0823ea12d58ce5bbae1dd243eeb7a50c06f55d6688542dae997f7c75e6ee24
2025-06-16 10:42:33,566 - app.models - INFO - 用户 admin 密码验证结果: True
2025-06-16 10:42:33,567 - app.auth.routes - INFO - 密码验证结果: True
2025-06-16 10:42:33,568 - app.auth.routes - INFO - 用户 admin 登录成功
2025-06-16 10:42:33,587 - werkzeug - INFO - 1******** - - [16/Jun/2025 10:42:33] "[32mPOST /auth/login HTTP/1.1[0m" 302 -
2025-06-16 10:42:33,616 - werkzeug - INFO - 1******** - - [16/Jun/2025 10:42:33] "GET /index HTTP/1.1" 200 -
2025-06-16 10:42:33,645 - werkzeug - INFO - 1******** - - [16/Jun/2025 10:42:33] "GET /static/css/base/bootstrap.min.css HTTP/1.1" 200 -
2025-06-16 10:42:33,968 - werkzeug - INFO - 1******** - - [16/Jun/2025 10:42:33] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-16 10:42:33,970 - werkzeug - INFO - 1******** - - [16/Jun/2025 10:42:33] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-16 10:42:33,972 - werkzeug - INFO - 1******** - - [16/Jun/2025 10:42:33] "GET /static/js/base/bootstrap.bundle.min.js HTTP/1.1" 200 -
2025-06-16 10:42:33,976 - werkzeug - INFO - 1******** - - [16/Jun/2025 10:42:33] "GET /static/js/menu-optimizer.js HTTP/1.1" 200 -
2025-06-16 10:42:34,291 - werkzeug - INFO - 1******** - - [16/Jun/2025 10:42:34] "[36mGET /static/webfonts/fa-solid-900.woff2 HTTP/1.1[0m" 304 -
2025-06-16 10:42:34,312 - werkzeug - INFO - 1******** - - [16/Jun/2025 10:42:34] "GET /menu HTTP/1.1" 200 -
2025-06-16 10:42:35,747 - werkzeug - INFO - 1******** - - [16/Jun/2025 10:42:35] "GET /production/semi-auto HTTP/1.1" 200 -
2025-06-16 10:42:36,044 - werkzeug - INFO - 1******** - - [16/Jun/2025 10:42:36] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-16 10:42:36,091 - werkzeug - INFO - 1******** - - [16/Jun/2025 10:42:36] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-16 10:42:36,092 - werkzeug - INFO - 1******** - - [16/Jun/2025 10:42:36] "GET /static/css/core/main.css HTTP/1.1" 200 -
2025-06-16 10:42:36,097 - werkzeug - INFO - 1******** - - [16/Jun/2025 10:42:36] "GET /static/css/daygrid/main.css HTTP/1.1" 200 -
2025-06-16 10:42:36,097 - werkzeug - INFO - 1******** - - [16/Jun/2025 10:42:36] "GET /static/css/timegrid/main.css HTTP/1.1" 200 -
2025-06-16 10:42:36,101 - werkzeug - INFO - 1******** - - [16/Jun/2025 10:42:36] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-16 10:42:36,356 - werkzeug - INFO - 1******** - - [16/Jun/2025 10:42:36] "GET /static/js/xlsx.full.min.js HTTP/1.1" 200 -
2025-06-16 10:42:36,400 - werkzeug - INFO - 1******** - - [16/Jun/2025 10:42:36] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-16 10:42:36,406 - werkzeug - INFO - 1******** - - [16/Jun/2025 10:42:36] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-16 10:42:36,766 - werkzeug - INFO - 1******** - - [16/Jun/2025 10:42:36] "GET /api/production/get-import-path HTTP/1.1" 200 -
2025-06-16 10:42:36,802 - app - INFO - 成功连接到MySQL数据库: aps
2025-06-16 10:42:36,839 - werkzeug - INFO - 1******** - - [16/Jun/2025 10:42:36] "GET /api/production/imported-files HTTP/1.1" 200 -
2025-06-16 10:42:37,422 - werkzeug - INFO - 1******** - - [16/Jun/2025 10:42:37] "GET /production/auto HTTP/1.1" 200 -
2025-06-16 10:42:37,713 - werkzeug - INFO - 1******** - - [16/Jun/2025 10:42:37] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-16 10:42:37,762 - werkzeug - INFO - 1******** - - [16/Jun/2025 10:42:37] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-16 10:42:37,763 - werkzeug - INFO - 1******** - - [16/Jun/2025 10:42:37] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-16 10:42:37,764 - werkzeug - INFO - 1******** - - [16/Jun/2025 10:42:37] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-16 10:42:37,765 - werkzeug - INFO - 1******** - - [16/Jun/2025 10:42:37] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-16 10:42:38,127 - werkzeug - INFO - 1******** - - [16/Jun/2025 10:42:38] "GET /api/production/schedule-history HTTP/1.1" 200 -
2025-06-16 10:42:38,800 - werkzeug - INFO - 1******** - - [16/Jun/2025 10:42:38] "GET /production/algorithm HTTP/1.1" 200 -
2025-06-16 10:42:39,093 - werkzeug - INFO - 1******** - - [16/Jun/2025 10:42:39] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-16 10:42:39,145 - werkzeug - INFO - 1******** - - [16/Jun/2025 10:42:39] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-16 10:42:39,148 - werkzeug - INFO - 1******** - - [16/Jun/2025 10:42:39] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-16 10:42:39,156 - werkzeug - INFO - 1******** - - [16/Jun/2025 10:42:39] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-16 10:42:39,160 - werkzeug - INFO - 1******** - - [16/Jun/2025 10:42:39] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-16 10:42:39,165 - werkzeug - INFO - 1******** - - [16/Jun/2025 10:42:39] "[36mGET /static/js/xlsx.full.min.js HTTP/1.1[0m" 304 -
2025-06-16 10:42:39,538 - werkzeug - INFO - 1******** - - [16/Jun/2025 10:42:39] "[33mGET /api/production/history-times HTTP/1.1[0m" 404 -
2025-06-16 10:42:40,148 - werkzeug - INFO - 1******** - - [16/Jun/2025 10:42:40] "GET /production/data HTTP/1.1" 200 -
2025-06-16 10:42:40,456 - werkzeug - INFO - 1******** - - [16/Jun/2025 10:42:40] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-16 10:42:40,492 - werkzeug - INFO - 1******** - - [16/Jun/2025 10:42:40] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-16 10:42:40,495 - werkzeug - INFO - 1******** - - [16/Jun/2025 10:42:40] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-16 10:42:40,499 - werkzeug - INFO - 1******** - - [16/Jun/2025 10:42:40] "GET /static/vendor/echarts/echarts.min.js HTTP/1.1" 200 -
2025-06-16 10:42:40,508 - werkzeug - INFO - 1******** - - [16/Jun/2025 10:42:40] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-16 10:42:40,509 - werkzeug - INFO - 1******** - - [16/Jun/2025 10:42:40] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-16 10:42:40,890 - werkzeug - INFO - 1******** - - [16/Jun/2025 10:42:40] "[33mGET /api/production/chart-data?table=Lot_WIP HTTP/1.1[0m" 404 -
2025-06-16 10:42:40,893 - werkzeug - INFO - 1******** - - [16/Jun/2025 10:42:40] "[33mGET /api/production/table-data?table=Lot_WIP&page=1&size=20 HTTP/1.1[0m" 404 -
2025-06-16 10:42:43,438 - werkzeug - INFO - 1******** - - [16/Jun/2025 10:42:43] "GET /production/priority-matching HTTP/1.1" 200 -
2025-06-16 10:42:43,728 - werkzeug - INFO - 1******** - - [16/Jun/2025 10:42:43] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-16 10:42:43,776 - werkzeug - INFO - 1******** - - [16/Jun/2025 10:42:43] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-16 10:42:43,778 - werkzeug - INFO - 1******** - - [16/Jun/2025 10:42:43] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-16 10:42:43,781 - werkzeug - INFO - 1******** - - [16/Jun/2025 10:42:43] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-16 10:42:43,790 - werkzeug - INFO - 1******** - - [16/Jun/2025 10:42:43] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-16 10:42:46,690 - werkzeug - INFO - 1******** - - [16/Jun/2025 10:42:46] "GET /resources/hardware HTTP/1.1" 200 -
2025-06-16 10:42:46,986 - werkzeug - INFO - 1******** - - [16/Jun/2025 10:42:46] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-16 10:42:47,033 - werkzeug - INFO - 1******** - - [16/Jun/2025 10:42:47] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-16 10:42:47,033 - werkzeug - INFO - 1******** - - [16/Jun/2025 10:42:47] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-16 10:42:47,037 - werkzeug - INFO - 1******** - - [16/Jun/2025 10:42:47] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-16 10:42:47,043 - werkzeug - INFO - 1******** - - [16/Jun/2025 10:42:47] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-16 10:42:47,372 - werkzeug - INFO - 1******** - - [16/Jun/2025 10:42:47] "[33mGET /api/user-filter-presets?page_type=hardware_resources HTTP/1.1[0m" 404 -
2025-06-16 10:42:47,381 - app - INFO - 成功连接到MySQL数据库: aps
2025-06-16 10:42:47,403 - werkzeug - INFO - 1******** - - [16/Jun/2025 10:42:47] "GET /api/resources/data/eqp_status?page=1&per_page=50 HTTP/1.1" 200 -
2025-06-16 10:42:48,593 - werkzeug - INFO - 1******** - - [16/Jun/2025 10:42:48] "GET /resources/specs HTTP/1.1" 200 -
2025-06-16 10:42:48,896 - werkzeug - INFO - 1******** - - [16/Jun/2025 10:42:48] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-16 10:42:48,943 - werkzeug - INFO - 1******** - - [16/Jun/2025 10:42:48] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-16 10:42:48,948 - werkzeug - INFO - 1******** - - [16/Jun/2025 10:42:48] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-16 10:42:48,951 - werkzeug - INFO - 1******** - - [16/Jun/2025 10:42:48] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-16 10:42:48,955 - werkzeug - INFO - 1******** - - [16/Jun/2025 10:42:48] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-16 10:42:49,281 - werkzeug - INFO - 1******** - - [16/Jun/2025 10:42:49] "[33mGET /api/user-filter-presets?page_type=et_ft_test_spec HTTP/1.1[0m" 404 -
2025-06-16 10:42:49,297 - app - INFO - 成功连接到MySQL数据库: aps
2025-06-16 10:42:49,329 - werkzeug - INFO - 1******** - - [16/Jun/2025 10:42:49] "GET /api/resources/data/et_ft_test_spec?page=1&per_page=50 HTTP/1.1" 200 -
2025-06-16 10:42:51,742 - werkzeug - INFO - 1******** - - [16/Jun/2025 10:42:51] "GET /resources/tester HTTP/1.1" 200 -
2025-06-16 10:42:52,032 - werkzeug - INFO - 1******** - - [16/Jun/2025 10:42:52] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-16 10:42:52,079 - werkzeug - INFO - 1******** - - [16/Jun/2025 10:42:52] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-16 10:42:52,082 - werkzeug - INFO - 1******** - - [16/Jun/2025 10:42:52] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-16 10:42:52,083 - werkzeug - INFO - 1******** - - [16/Jun/2025 10:42:52] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-16 10:42:52,086 - werkzeug - INFO - 1******** - - [16/Jun/2025 10:42:52] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-16 10:42:52,410 - werkzeug - INFO - 1******** - - [16/Jun/2025 10:42:52] "[33mGET /api/user-filter-presets?page_type=TCC_INV HTTP/1.1[0m" 404 -
2025-06-16 10:42:52,434 - app - INFO - 成功连接到MySQL数据库: aps
2025-06-16 10:42:52,461 - werkzeug - INFO - 1******** - - [16/Jun/2025 10:42:52] "GET /api/resources/data/TCC_INV?page=1&per_page=50 HTTP/1.1" 200 -
2025-06-16 10:42:53,966 - werkzeug - INFO - 1******** - - [16/Jun/2025 10:42:53] "GET /resources/uph HTTP/1.1" 200 -
2025-06-16 10:42:54,262 - werkzeug - INFO - 1******** - - [16/Jun/2025 10:42:54] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-16 10:42:54,310 - werkzeug - INFO - 1******** - - [16/Jun/2025 10:42:54] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-16 10:42:54,312 - werkzeug - INFO - 1******** - - [16/Jun/2025 10:42:54] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-16 10:42:54,316 - werkzeug - INFO - 1******** - - [16/Jun/2025 10:42:54] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-16 10:42:54,316 - werkzeug - INFO - 1******** - - [16/Jun/2025 10:42:54] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-16 10:42:54,635 - werkzeug - INFO - 1******** - - [16/Jun/2025 10:42:54] "[33mGET /api/user-filter-presets?page_type=ET_UPH_EQP HTTP/1.1[0m" 404 -
2025-06-16 10:42:54,643 - app - INFO - 成功连接到MySQL数据库: aps
2025-06-16 10:42:54,655 - werkzeug - INFO - 1******** - - [16/Jun/2025 10:42:54] "GET /api/resources/data/ET_UPH_EQP?page=1&per_page=50 HTTP/1.1" 200 -
2025-06-16 10:42:56,264 - werkzeug - INFO - 1******** - - [16/Jun/2025 10:42:56] "GET /resources/product-cycle HTTP/1.1" 200 -
2025-06-16 10:42:56,555 - werkzeug - INFO - 1******** - - [16/Jun/2025 10:42:56] "[36mGET /static/css/base/bootstrap.min.css HTTP/1.1[0m" 304 -
2025-06-16 10:42:56,648 - werkzeug - INFO - 1******** - - [16/Jun/2025 10:42:56] "[36mGET /static/css/all-5.15.4.min.css HTTP/1.1[0m" 304 -
2025-06-16 10:42:56,650 - werkzeug - INFO - 1******** - - [16/Jun/2025 10:42:56] "[36mGET /static/css/compatibility.css HTTP/1.1[0m" 304 -
2025-06-16 10:42:56,656 - werkzeug - INFO - 1******** - - [16/Jun/2025 10:42:56] "[36mGET /static/js/base/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
2025-06-16 10:42:56,661 - werkzeug - INFO - 1******** - - [16/Jun/2025 10:42:56] "[36mGET /static/js/menu-optimizer.js HTTP/1.1[0m" 304 -
2025-06-16 10:42:56,992 - werkzeug - INFO - 1******** - - [16/Jun/2025 10:42:56] "[33mGET /api/user-filter-presets?page_type=CT HTTP/1.1[0m" 404 -
2025-06-16 10:42:56,998 - app - INFO - 成功连接到MySQL数据库: aps
2025-06-16 10:42:57,030 - werkzeug - INFO - 1******** - - [16/Jun/2025 10:42:57] "GET /api/resources/data/CT?page=1&per_page=50 HTTP/1.1" 200 -
