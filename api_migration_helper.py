#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
API迁移助手工具

用于辅助API接口迁移工作，包括：
1. 分析旧接口并提取路由、参数、响应结构
2. 为新接口生成代码模板
3. 修改旧接口，添加废弃标记和重定向
"""

import os
import re
import json
import argparse
from datetime import datetime

class APIMigrationHelper:
    def __init__(self):
        self.source_api_dir = 'app/api'
        self.target_api_dir = 'app/api_v2'
        self.deprecated_map_file = 'app/config/deprecated_api_map.json'
        self.migration_report_file = 'docs/api_audit/migration_report.md'
        self.migration_log_file = 'logs/api_migration.log'
        self.endpoint_pattern = re.compile(r'@(?:bp|api_v\d+|.*?_bp)\.route\([\'"]([^\'"]*)[\'"]')
        self.method_pattern = re.compile(r'methods=\[(.*?)\]')
        self.function_pattern = re.compile(r'def\s+([a-zA-Z0-9_]+)\s*\(')
        
        # 确保目录存在
        os.makedirs('docs/api_audit', exist_ok=True)
        os.makedirs('logs', exist_ok=True)
        
        # 初始化映射配置
        self.deprecated_map = self._load_deprecated_map()
    
    def _load_deprecated_map(self):
        """加载废弃API映射配置"""
        try:
            if os.path.exists(self.deprecated_map_file):
                with open(self.deprecated_map_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return {"说明": "API映射配置", "版本": "1.0.0", "映射关系": {}, "重定向配置": {"启用自动重定向": False}}
        except Exception as e:
            print(f"无法加载废弃API映射配置: {e}")
            return {"说明": "API映射配置", "版本": "1.0.0", "映射关系": {}, "重定向配置": {"启用自动重定向": False}}
    
    def _save_deprecated_map(self):
        """保存废弃API映射配置"""
        try:
            with open(self.deprecated_map_file, 'w', encoding='utf-8') as f:
                json.dump(self.deprecated_map, f, ensure_ascii=False, indent=2)
            print(f"✅ 废弃API映射已保存到 {self.deprecated_map_file}")
        except Exception as e:
            print(f"❌ 保存废弃API映射失败: {e}")
    
    def _log_migration(self, message):
        """记录迁移日志"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        with open(self.migration_log_file, 'a', encoding='utf-8') as f:
            f.write(f"[{timestamp}] {message}\n")
    
    def analyze_endpoint(self, file_path, endpoint):
        """分析单个端点的实现细节"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 查找端点对应的路由定义
            endpoint_escaped = re.escape(endpoint)
            route_pattern = re.compile(rf'@.*?route\([\'"{endpoint_escaped}[\'"].*?\)\s*?def\s+([a-zA-Z0-9_]+)\s*\(', re.DOTALL)
            route_match = route_pattern.search(content)
            
            if not route_match:
                return None
                
            function_name = route_match.group(1)
            
            # 提取HTTP方法
            methods_match = re.search(rf'@.*?route\([\'"{endpoint_escaped}[\'"].*?methods=\[(.*?)\]', content, re.DOTALL)
            methods = ['GET']  # 默认方法
            if methods_match:
                methods_str = methods_match.group(1)
                methods = [m.strip('\'\" ') for m in methods_str.split(',')]
            
            # 查找函数实现
            function_pattern = re.compile(rf'def\s+{function_name}\s*\(.*?\).*?:(.*?)(?:def\s+|$)', re.DOTALL)
            function_match = function_pattern.search(content)
            
            if not function_match:
                return None
                
            function_body = function_match.group(1).strip()
            
            # 分析参数和返回值
            param_pattern = re.compile(r'request\.(?:args|form|json)\.get\([\'"]([^\'"]+)[\'"]')
            params = set(param_pattern.findall(function_body))
            
            return_pattern = re.compile(r'return\s+(?:jsonify\(|json\.dumps\()?({.*?})', re.DOTALL)
            return_matches = return_pattern.findall(function_body)
            
            # 分析复杂度
            complexity = len(function_body.split('\n'))
            db_access = 'db.session' in function_body or '.query' in function_body
            
            return {
                'endpoint': endpoint,
                'function_name': function_name,
                'methods': methods,
                'params': list(params),
                'return_examples': return_matches[:2] if return_matches else [],
                'complexity': complexity,
                'has_db_access': db_access,
                'file_path': file_path
            }
        except Exception as e:
            print(f"❌ 分析端点 {endpoint} 失败: {e}")
            return None
    
    def generate_migration_template(self, endpoint_info, new_endpoint):
        """为端点生成迁移模板代码"""
        if not endpoint_info:
            return ""
            
        methods_str = ", ".join(f"'{m}'" for m in endpoint_info['methods'])
        params_list = endpoint_info['params']
        
        # 确定放置新接口的模块
        module = "other"
        if '/production/' in new_endpoint:
            module = "production"
        elif '/orders/' in new_endpoint:
            module = "orders"
        elif '/resources/' in new_endpoint:
            module = "resources"
        elif '/system/' in new_endpoint:
            module = "system"
        elif '/auth/' in new_endpoint:
            module = "auth"
            
        # 生成函数名
        path_parts = new_endpoint.split('/')
        function_name = '_'.join(p for p in path_parts if p and p != 'api' and p != 'v2')
        function_name = function_name.replace('-', '_')
        
        template = f"""
# 为 {endpoint_info['endpoint']} 迁移到 {new_endpoint} 的代码模板
# 放置在 app/api_v2/{module}/routes.py 或适当的模块文件中

@{module}_bp.route('{new_endpoint.replace(f'/api/v2/{module}', '')}', methods=[{methods_str}])
def {function_name}():
    \"\"\"
    {endpoint_info['function_name']} 的替代实现
    
    从 {endpoint_info['file_path']} 迁移而来
    \"\"\"
    # 获取参数
"""
        
        # 添加参数提取代码
        for param in params_list:
            template += f"    {param} = request.args.get('{param}')\n"
            
        if endpoint_info['has_db_access']:
            template += """
    # 数据库操作
    try:
        # TODO: 实现数据库操作逻辑
        pass
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500
"""
        
        template += """
    # TODO: 实现业务逻辑
    
    return jsonify({
        'success': True,
        'data': []
    })
"""
        return template
    
    def mark_endpoint_deprecated(self, file_path, endpoint, new_endpoint):
        """标记端点为废弃"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.readlines()
                
            endpoint_escaped = re.escape(endpoint)
            route_pattern = re.compile(rf'@.*?route\([\'"{endpoint_escaped}[\'"](.*?)\)')
            
            # 找到函数定义的行
            for i, line in enumerate(content):
                if re.search(route_pattern, line):
                    # 找到对应的函数名
                    for j in range(i, min(i+5, len(content))):
                        func_match = re.search(r'def\s+([a-zA-Z0-9_]+)\s*\(', content[j])
                        if func_match:
                            function_name = func_match.group(1)
                            
                            # 添加废弃装饰器
                            import_line = "from app.utils.api_deprecation import deprecated_api\n"
                            if import_line not in ''.join(content[:20]):
                                # 在文件头部添加导入语句
                                for k, line in enumerate(content):
                                    if line.startswith('from ') or line.startswith('import '):
                                        content.insert(k, import_line)
                                        break
                            
                            # 添加废弃装饰器
                            decorator = f"@deprecated_api(new_endpoint='{new_endpoint}', removal_version='3.0', removal_date='2025-12-31')\n"
                            content.insert(i, decorator)
                            
                            # 写回文件
                            with open(file_path, 'w', encoding='utf-8') as f:
                                f.writelines(content)
                                
                            self._log_migration(f"已标记 {endpoint} -> {new_endpoint} 为废弃")
                            return True
            
            print(f"❌ 未找到端点 {endpoint} 的定义")
            return False
            
        except Exception as e:
            print(f"❌ 标记端点 {endpoint} 为废弃失败: {e}")
            return False
    
    def add_migration_mapping(self, old_endpoint, new_endpoint):
        """添加迁移映射关系"""
        if "映射关系" not in self.deprecated_map:
            self.deprecated_map["映射关系"] = {}
            
        self.deprecated_map["映射关系"][old_endpoint] = new_endpoint
        self._save_deprecated_map()
        self._log_migration(f"添加映射: {old_endpoint} -> {new_endpoint}")
        
    def remove_migration_mapping(self, old_endpoint):
        """移除迁移映射关系"""
        if "映射关系" in self.deprecated_map and old_endpoint in self.deprecated_map["映射关系"]:
            del self.deprecated_map["映射关系"][old_endpoint]
            self._save_deprecated_map()
            self._log_migration(f"移除映射: {old_endpoint}")
    
    def find_endpoint_file(self, endpoint):
        """查找包含指定端点的文件"""
        for root, _, files in os.walk(self.source_api_dir):
            for file in files:
                if file.endswith('.py'):
                    file_path = os.path.join(root, file)
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            content = f.read()
                            endpoint_escaped = re.escape(endpoint)
                            if re.search(rf'@.*?route\([\'"{endpoint_escaped}[\'"](.*?)\)', content):
                                return file_path
                    except:
                        pass
        return None
    
    def scan_module_endpoints(self, module_path):
        """扫描模块中的所有端点"""
        endpoints = []
        
        for root, _, files in os.walk(module_path):
            for file in files:
                if file.endswith('.py'):
                    file_path = os.path.join(root, file)
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            content = f.read()
                            
                        # 提取蓝图前缀
                        blueprint_prefix = None
                        bp_match = re.search(r'Blueprint\([^,]+,\s*[^,]+,\s*url_prefix=[\'"]([^\'"]*)[\'"]', content)
                        if bp_match:
                            blueprint_prefix = bp_match.group(1)
                            
                        # 提取路由
                        matches = self.endpoint_pattern.finditer(content)
                        for match in matches:
                            endpoint = match.group(1)
                            
                            # 生成完整URL路径
                            full_path = endpoint
                            if blueprint_prefix and not endpoint.startswith('/'):
                                if blueprint_prefix.endswith('/') and endpoint.startswith('/'):
                                    full_path = f"{blueprint_prefix}{endpoint[1:]}"
                                else:
                                    full_path = f"{blueprint_prefix}/{endpoint}"
                                    
                            # 获取HTTP方法
                            methods_match = re.search(rf'@.*?route\([\'"{re.escape(endpoint)}[\'"].*?methods=\[(.*?)\]', content, re.DOTALL)
                            methods = ['GET']  # 默认方法
                            if methods_match:
                                methods_str = methods_match.group(1)
                                methods = [m.strip('\'\" ') for m in methods_str.split(',')]
                                
                            # 获取函数名
                            func_match = re.search(rf'@.*?route\([\'"{re.escape(endpoint)}[\'"].*?\)\s*?def\s+([a-zA-Z0-9_]+)\s*\(', content, re.DOTALL)
                            func_name = func_match.group(1) if func_match else "unknown"
                            
                            endpoints.append({
                                'endpoint': full_path,
                                'methods': methods,
                                'function': func_name,
                                'file': os.path.relpath(file_path)
                            })
                    except Exception as e:
                        print(f"分析文件 {file_path} 失败: {e}")
                        
        return endpoints

    def generate_migration_report(self):
        """生成迁移报告"""
        # 扫描旧API和新API
        old_endpoints = self.scan_module_endpoints(self.source_api_dir)
        new_endpoints = self.scan_module_endpoints(self.target_api_dir)
        
        # 统计数据
        total_old = len(old_endpoints)
        total_new = len(new_endpoints)
        
        # 获取映射关系
        mapped_old_endpoints = set(self.deprecated_map.get("映射关系", {}).keys())
        mapped_new_endpoints = set(self.deprecated_map.get("映射关系", {}).values())
        
        # 计算迁移进度
        migrated_count = len(mapped_old_endpoints)
        migration_progress = round(migrated_count / total_old * 100, 2) if total_old > 0 else 0
        
        # 查找未迁移的端点
        unmigrated_endpoints = [e for e in old_endpoints if e['endpoint'] not in mapped_old_endpoints]
        
        # 生成报告
        with open(self.migration_report_file, 'w', encoding='utf-8') as f:
            f.write(f"# API迁移报告\n\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            f.write("## 统计摘要\n\n")
            f.write(f"- 旧API端点总数: {total_old}\n")
            f.write(f"- 新API端点总数: {total_new}\n")
            f.write(f"- 已完成迁移端点: {migrated_count}\n")
            f.write(f"- 待迁移端点数: {total_old - migrated_count}\n")
            f.write(f"- 迁移进度: {migration_progress}%\n\n")
            
            f.write("## 已完成迁移的端点\n\n")
            f.write("| 旧端点 | 新端点 | 状态 |\n")
            f.write("| --- | --- | --- |\n")
            for old_ep, new_ep in self.deprecated_map.get("映射关系", {}).items():
                # 检查新端点是否存在
                new_exists = any(e['endpoint'] == new_ep for e in new_endpoints)
                status = "✅ 已实现" if new_exists else "❌ 新接口未实现"
                f.write(f"| {old_ep} | {new_ep} | {status} |\n")
            f.write("\n")
            
            f.write("## 待迁移端点\n\n")
            f.write("| 端点 | HTTP方法 | 功能 | 所在文件 |\n")
            f.write("| --- | --- | --- | --- |\n")
            for endpoint in unmigrated_endpoints:
                f.write(f"| {endpoint['endpoint']} | {', '.join(endpoint['methods'])} | {endpoint['function']} | {endpoint['file']} |\n")
                
        print(f"✅ 迁移报告已生成: {self.migration_report_file}")
        return self.migration_report_file

def main():
    parser = argparse.ArgumentParser(description='API迁移助手工具')
    subparsers = parser.add_subparsers(dest='command', help='命令')
    
    # 扫描命令
    scan_parser = subparsers.add_parser('scan', help='扫描API端点')
    scan_parser.add_argument('--module', choices=['old', 'new', 'both'], default='both', help='要扫描的模块')
    
    # 分析命令
    analyze_parser = subparsers.add_parser('analyze', help='分析单个端点')
    analyze_parser.add_argument('endpoint', help='要分析的端点路径')
    
    # 映射命令
    map_parser = subparsers.add_parser('map', help='添加端点映射')
    map_parser.add_argument('old_endpoint', help='旧端点路径')
    map_parser.add_argument('new_endpoint', help='新端点路径')
    
    # 生成迁移模板命令
    template_parser = subparsers.add_parser('template', help='生成迁移模板')
    template_parser.add_argument('old_endpoint', help='旧端点路径')
    template_parser.add_argument('new_endpoint', help='新端点路径')
    
    # 标记废弃命令
    deprecate_parser = subparsers.add_parser('deprecate', help='标记端点为废弃')
    deprecate_parser.add_argument('old_endpoint', help='旧端点路径')
    deprecate_parser.add_argument('new_endpoint', help='新端点路径')
    
    # 生成报告命令
    report_parser = subparsers.add_parser('report', help='生成迁移报告')
    
    args = parser.parse_args()
    
    helper = APIMigrationHelper()
    
    if args.command == 'scan':
        if args.module in ['old', 'both']:
            print("扫描旧API端点...")
            old_endpoints = helper.scan_module_endpoints(helper.source_api_dir)
            print(f"发现 {len(old_endpoints)} 个旧API端点")
        
        if args.module in ['new', 'both']:
            print("扫描新API端点...")
            new_endpoints = helper.scan_module_endpoints(helper.target_api_dir)
            print(f"发现 {len(new_endpoints)} 个新API端点")
    
    elif args.command == 'analyze':
        file_path = helper.find_endpoint_file(args.endpoint)
        if file_path:
            endpoint_info = helper.analyze_endpoint(file_path, args.endpoint)
            if endpoint_info:
                print(json.dumps(endpoint_info, indent=2, ensure_ascii=False))
            else:
                print(f"❌ 无法分析端点: {args.endpoint}")
        else:
            print(f"❌ 未找到端点: {args.endpoint}")
    
    elif args.command == 'map':
        helper.add_migration_mapping(args.old_endpoint, args.new_endpoint)
        print(f"✅ 已添加映射: {args.old_endpoint} -> {args.new_endpoint}")
    
    elif args.command == 'template':
        file_path = helper.find_endpoint_file(args.old_endpoint)
        if file_path:
            endpoint_info = helper.analyze_endpoint(file_path, args.old_endpoint)
            if endpoint_info:
                template = helper.generate_migration_template(endpoint_info, args.new_endpoint)
                print(template)
            else:
                print(f"❌ 无法分析端点: {args.old_endpoint}")
        else:
            print(f"❌ 未找到端点: {args.old_endpoint}")
    
    elif args.command == 'deprecate':
        file_path = helper.find_endpoint_file(args.old_endpoint)
        if file_path:
            if helper.mark_endpoint_deprecated(file_path, args.old_endpoint, args.new_endpoint):
                helper.add_migration_mapping(args.old_endpoint, args.new_endpoint)
                print(f"✅ 已成功标记端点 {args.old_endpoint} 为废弃并指向 {args.new_endpoint}")
            else:
                print(f"❌ 标记端点 {args.old_endpoint} 为废弃失败")
        else:
            print(f"❌ 未找到端点: {args.old_endpoint}")
    
    elif args.command == 'report':
        report_file = helper.generate_migration_report()
        print(f"✅ 迁移报告已生成: {report_file}")
    
    else:
        parser.print_help()

if __name__ == "__main__":
    main() 