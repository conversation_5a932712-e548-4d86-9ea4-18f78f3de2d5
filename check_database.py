import pymysql

def check_database():
    # 数据库配置
    configs = {
        'aps': {
            'host': 'localhost',
            'user': 'root',
            'password': 'Flzx3000',
            'database': 'aps',
            'charset': 'utf8mb4'
        },
        'aps_system': {
            'host': 'localhost',
            'user': 'root',
            'password': 'Flzx3000',
            'database': 'aps_system',
            'charset': 'utf8mb4'
        }
    }
    
    for db_name, config in configs.items():
        print(f"\n=== 检查 {db_name} 数据库 ===")
        try:
            conn = pymysql.connect(**config)
            print(f"✅ 成功连接到 {db_name}")
            
            with conn.cursor() as cursor:
                # 检查devicepriorityconfig表
                cursor.execute("SHOW TABLES LIKE 'devicepriorityconfig'")
                device_exists = cursor.fetchone() is not None
                print(f"devicepriorityconfig表: {'存在' if device_exists else '不存在'}")
                
                if device_exists:
                    cursor.execute("SELECT COUNT(*) FROM devicepriorityconfig")
                    count = cursor.fetchone()[0]
                    print(f"  记录数: {count}")
                    
                    cursor.execute("DESCRIBE devicepriorityconfig")
                    columns = cursor.fetchall()
                    print(f"  表结构:")
                    for col in columns:
                        print(f"    {col[0]}: {col[1]}")
                    
                    if count > 0:
                        cursor.execute("SELECT * FROM devicepriorityconfig LIMIT 3")
                        rows = cursor.fetchall()
                        print(f"  前3条数据:")
                        for i, row in enumerate(rows):
                            print(f"    第{i+1}行: {row}")
                
                # 检查lotpriorityconfig表
                cursor.execute("SHOW TABLES LIKE 'lotpriorityconfig'")
                lot_exists = cursor.fetchone() is not None
                print(f"lotpriorityconfig表: {'存在' if lot_exists else '不存在'}")
                
                if lot_exists:
                    cursor.execute("SELECT COUNT(*) FROM lotpriorityconfig")
                    count = cursor.fetchone()[0]
                    print(f"  记录数: {count}")
                    
                    cursor.execute("DESCRIBE lotpriorityconfig")
                    columns = cursor.fetchall()
                    print(f"  表结构:")
                    for col in columns:
                        print(f"    {col[0]}: {col[1]}")
                    
                    if count > 0:
                        cursor.execute("SELECT * FROM lotpriorityconfig LIMIT 3")
                        rows = cursor.fetchall()
                        print(f"  前3条数据:")
                        for i, row in enumerate(rows):
                            print(f"    第{i+1}行: {row}")
            
            conn.close()
            
        except Exception as e:
            print(f"❌ 连接 {db_name} 失败: {e}")

if __name__ == "__main__":
    check_database()
