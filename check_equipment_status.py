#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

print("检查设备状态...")

try:
    from app.services.data_source_manager import DataSourceManager
    
    dm = DataSourceManager()
    
    # 获取设备数据
    equipment_result = dm.get_table_data('EQP_STATUS')
    
    if equipment_result.get('success'):
        equipment_data = equipment_result.get('data', [])
        print(f"获取到 {len(equipment_data)} 台设备")
        
        # 分析设备状态
        status_counts = {}
        for idx, eqp in enumerate(equipment_data[:10]):  # 只看前10台设备
            status = eqp.get('STATUS', 'Unknown')
            tester_id = eqp.get('TESTER_ID', 'Unknown')
            handler_id = eqp.get('HANDLER_ID', 'Unknown')
            device = eqp.get('DEVICE', 'Unknown')
            
            print(f"设备 {idx+1}: TESTER_ID={tester_id}, HANDLER_ID={handler_id}, DEVICE={device}, STATUS={status}")
            
            # 统计状态
            if status not in status_counts:
                status_counts[status] = 0
            status_counts[status] += 1
        
        print(f"\n设备状态统计:")
        for status, count in status_counts.items():
            print(f"  {status}: {count} 台")
        
        # 查看哪些状态值可能代表可用
        print(f"\n可能的可用状态检查:")
        available_statuses = ['0', 'IDLE', 'RUN', 'READY', 'ONLINE', 'ACTIVE']
        for status in available_statuses:
            count = status_counts.get(status, 0)
            print(f"  {status}: {count} 台")
        
        print(f"\n所有唯一状态值: {list(status_counts.keys())}")
        
    else:
        print(f"获取设备数据失败: {equipment_result.get('error')}")

except Exception as e:
    print(f"检查过程中发生错误: {e}")
    import traceback
    traceback.print_exc()

print("\n检查完成") 