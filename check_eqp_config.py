#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

print("检查设备配置信息...")

try:
    from app.services.data_source_manager import DataSourceManager
    
    dm = DataSourceManager()
    
    # 获取设备数据
    result = dm.get_table_data('EQP_STATUS')
    if result.get('success'):
        data = result.get('data', [])[:10]  # 取前10条
        
        print(f"\n=== EQP_STATUS 配置信息分析 ===")
        for idx, eqp in enumerate(data):
            print(f"\n设备 {idx+1}:")
            print(f"  HANDLER_ID: {eqp.get('HANDLER_ID')}")
            print(f"  TESTER_ID: {eqp.get('TESTER_ID')}")
            print(f"  DEVICE: {eqp.get('DEVICE')}")
            print(f"  HB_PN: {eqp.get('HB_PN')}")
            print(f"  TB_PN: {eqp.get('TB_PN')}")
            print(f"  KIT_PN: {eqp.get('KIT_PN')}")
            print(f"  STATUS: {eqp.get('STATUS')}")
            
        # 统计配置情况
        print(f"\n=== 配置完整性统计 ===")
        total = len(data)
        has_hb = sum(1 for d in data if d.get('HB_PN'))
        has_tb = sum(1 for d in data if d.get('TB_PN'))
        has_kit = sum(1 for d in data if d.get('KIT_PN'))
        
        print(f"总设备数: {total}")
        print(f"有HB_PN配置: {has_hb}/{total} ({has_hb/total*100:.1f}%)")
        print(f"有TB_PN配置: {has_tb}/{total} ({has_tb/total*100:.1f}%)")
        print(f"有KIT_PN配置: {has_kit}/{total} ({has_kit/total*100:.1f}%)")
        
    else:
        print("获取设备数据失败")
        
except Exception as e:
    print(f"错误: {e}")
    import traceback
    traceback.print_exc() 