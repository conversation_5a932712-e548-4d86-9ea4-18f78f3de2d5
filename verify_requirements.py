#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
验证requirements.txt文件是否可以被正常解析
"""

import os
import sys
import re

def verify_requirements():
    requirements_file = "requirements.txt"
    
    print(f"正在验证 {requirements_file} 文件...")
    
    if not os.path.exists(requirements_file):
        print(f"错误: {requirements_file} 文件不存在")
        return False
    
    try:
        with open(requirements_file, 'r', encoding='utf-8') as f:
            content = f.read()
            lines = content.split('\n')
            
        print(f"成功读取文件，共 {len(lines)} 行")
        
        # 分析包名
        packages = []
        for line in lines:
            line = line.strip()
            if not line or line.startswith('#'):
                continue
                
            # 提取包名和版本
            match = re.match(r'^([a-zA-Z0-9_\-.]+)([=<>]+.*)?', line.split('#')[0].strip())
            if match:
                package_name = match.group(1)
                version_spec = match.group(2) if match.group(2) else ""
                packages.append((package_name, version_spec))
        
        print(f"成功解析 {len(packages)} 个软件包")
        print("\n前10个软件包:")
        for i, (name, version) in enumerate(packages[:10]):
            print(f"  {i+1}. {name}{version}")
            
        print("\n文件编码正常，可以被Python解析")
        return True
    except UnicodeDecodeError as e:
        print(f"文件编码错误: {e}")
        return False
    except Exception as e:
        print(f"验证过程出错: {e}")
        return False

if __name__ == "__main__":
    verify_requirements() 