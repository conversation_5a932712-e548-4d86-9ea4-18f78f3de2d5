# APS车规芯片终测智能调度平台 - API接口文档

**版本**: v2.1  
**最后更新**: 2025年6月20日  
**系统架构**: Flask + MySQL + Bootstrap  

---

## 📋 目录

1. [接口概览](#接口概览)
2. [认证系统API](#认证系统api)
3. [生产管理API](#生产管理api)
4. [订单管理API](#订单管理api)
5. [资源管理API](#资源管理api)
6. [系统管理API](#系统管理api)
7. [AI助手API](#ai助手api)
8. [邮件附件API](#邮件附件api)
9. [WebSocket API](#websocket-api)
10. [数据结构定义](#数据结构定义)

---

## 🔍 接口概览

### API版本说明

APS系统支持两个版本的API：

- **API v1** (`/api/*`): 传统版本，保持向后兼容
- **API v2** (`/api/v2/*`): 新版本，功能更完善，推荐使用

系统支持API版本自动降级：当v2接口不可用时，自动降级到v1接口。

### 统一响应格式

```json
{
  "success": true,
  "data": {},
  "message": "操作成功",
  "timestamp": "2025-06-20T15:30:00Z",
  "api_version": "2.0"
}
```

### 错误响应格式

```json
{
  "success": false,
  "error": "错误代码",
  "message": "错误描述",
  "details": {},
  "timestamp": "2025-06-20T15:30:00Z"
}
```

---

## 🔐 认证系统API

### 用户登录
- **路径**: `/auth/login`
- **方法**: `POST`
- **说明**: 用户身份验证

**请求参数**:
```json
{
  "username": "admin",
  "password": "password"
}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "user": {
      "username": "admin",
      "role": "admin",
      "permissions": ["read", "write", "admin"]
    },
    "session_token": "abc123..."
  }
}
```

### 用户管理

| 功能 | 路径 | 方法 | 权限 |
|------|------|------|------|
| 获取用户列表 | `/api/auth/users` | GET | 管理员 |
| 创建用户 | `/api/auth/users` | POST | 管理员 |
| 更新用户 | `/api/auth/users/{username}` | PUT | 管理员 |
| 删除用户 | `/api/auth/users/{username}` | DELETE | 管理员 |
| 获取用户权限 | `/api/v2/auth/users/{username}/permissions` | GET | 管理员 |
| 更新用户权限 | `/api/v2/auth/users/{username}/permissions` | PUT | 管理员 |

### 权限管理

| 功能 | 路径 | 方法 | 说明 |
|------|------|------|------|
| 获取菜单设置 | `/api/v2/auth/menu-settings` | GET | 获取用户菜单权限配置 |
| ~~更新菜单设置~~ | ~~`/api/menu/settings`~~ | ~~POST~~ | ⚠️ 已废弃 - 使用配置文件管理 |

### 菜单系统API

| 功能 | 路径 | 方法 | 前端页面 |
|------|------|------|----------|
| 获取用户菜单 | `/menu` | GET | `base.html` (所有页面) |
| 获取菜单设置 | `/api/v2/auth/menu-settings` | GET | 权限管理页面 |
| 清理缓存 | `/clear_cache` | GET | 缓存清理工具 |

**前端调用示例**:
```javascript
// base.html 中的菜单加载
fetch('/menu', {
  signal: controller.signal,
  cache: 'force-cache'
})
.then(response => response.json())
.then(data => {
  const menuData = data.menu || data;
  buildMenuDOM(menuData);
});

// 清理缓存
fetch('/clear_cache')
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      console.log('缓存已清理');
    }
  });
```

---

## 🏭 生产管理API

### 排产调度

| 功能 | 路径 | 方法 | 前端页面 |
|------|------|------|----------|
| 自动排产 | `/api/v2/production/orders` | POST | `/production/auto` |
| 半自动排产 | `/api/production/auto-schedule` | POST | `/production/semi-auto` |
| 执行排产 | `/api/v2/production/scheduling/execute` | POST | `/production/algorithm` |
| 排产历史 | `/api/v2/production/scheduling/history` | GET | `/production/auto` |
| 导出排产 | `/api/v2/production/scheduling/export` | POST | `/production/auto` |

**前端调用示例**:
```javascript
// 自动排产 - production/auto.html
fetch('/api/production/auto-schedule', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    schedule_date: "2025-06-20",
    algorithm: "genetic"
  })
});

// 导出排产结果
fetch('/api/production/export-schedule', {
  method: 'POST',
  body: formData
});
```

### 批次管理

#### 等待批次 (WIP Lots)

| 功能 | 路径 | 方法 | 前端页面 |
|------|------|------|----------|
| 获取等待批次 | `/api/v2/production/wait-lots/` | GET | `/production/wait-lots` |
| 创建等待批次 | `/api/v2/production/wait-lots/` | POST | `/production/wait-lots` |
| 更新等待批次 | `/api/v2/production/wait-lots/{id}` | PUT | `/production/wait-lots` |
| 删除等待批次 | `/api/v2/production/wait-lots/{id}` | DELETE | `/production/wait-lots` |
| 移动到已排产 | `/api/v2/production/wait-lots/move-to-scheduled` | POST | `/production/wait-lots` |
| 获取列结构 | `/api/v2/production/wait-lots/columns` | GET | `/production/wait-lots` |

**前端调用示例**:
```javascript
// wait-lots.html 中的调用
fetch('/api/v2/production/wait-lots/move-to-scheduled', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ lot_ids: selectedIds })
});
```

#### 完成批次 (Done Lots)

| 功能 | 路径 | 方法 | 前端页面 |
|------|------|------|----------|
| 获取完成批次 | `/api/v2/production/done-lots/` | GET | `/production/done-lots` |
| 创建完成批次 | `/api/v2/production/done-lots/` | POST | `/production/done-lots` |
| 更新完成批次 | `/api/v2/production/done-lots/{id}` | PUT | `/production/done-lots` |
| 删除完成批次 | `/api/v2/production/done-lots/{id}` | DELETE | `/production/done-lots` |
| 移动到等待 | `/api/v2/production/done-lots/move-to-waiting` | POST | `/production/done-lots` |

### 优先级管理

| 功能 | 路径 | 方法 | 前端页面 |
|------|------|------|----------|
| 产品优先级查询 | `/api/v2/production/priority/product` | GET | `/production/priority-settings` |
| 产品优先级设置 | `/api/v2/production/priority/product` | POST | `/production/priority-settings` |
| 批次优先级查询 | `/api/v2/production/priority/lot` | GET | `/production/lot-priority` |
| 设备优先级查询 | `/api/v2/production/priority/device` | GET | `/production/device-priority` |
| 优先级匹配 | `/api/v2/production/priority/match-batches` | POST | `/production/priority-settings` |
| 应用匹配结果 | `/api/v2/production/priority/apply-matches` | POST | `/production/priority-settings` |
| 匹配报告 | `/api/v2/production/priority/matching-report` | GET | `/production/priority-settings` |
| 批次信息 | `/api/v2/production/priority/batch-info/{batch_id}` | GET | `/production/priority-settings` |
| 上传优先级设置 | `/api/v2/production/priority-settings/upload` | POST | 多个优先级页面 |

**前端调用示例**:
```javascript
// priority-settings.html 中的上传
fetch('/api/v2/production/priority-settings/upload', {
  method: 'POST',
  body: formData
});

// lot-priority.html 和 device-priority.html 中的上传
fetch('/api/v2/production/priority-settings/upload', {
  method: 'POST',
  body: formData
});
```

### 数据源管理

| 功能 | 路径 | 方法 | 前端页面 |
|------|------|------|----------|
| 切换数据源 | `/api/v2/production/data-source/switch` | POST | 多个生产页面 |
| 数据源状态 | `/api/v2/production/data-source/status` | GET | 多个生产页面 |
| 验证数据源 | `/api/v2/production/data-source/validate` | POST | 多个生产页面 |
| 数据源指标 | `/api/v2/production/data-source/metrics` | GET | 多个生产页面 |

---

## 📦 订单管理API

### Excel解析器

| 功能 | 路径 | 方法 | 前端页面 |
|------|------|------|----------|
| 优化解析器 | `/api/v2/production/parse-excel-optimized` | POST | `/orders/optimized-parser` |
| 解析进度 | `/api/v2/production/parse-progress/{task_id}` | GET | `/orders/optimized-parser` |
| 重复确认 | `/api/v2/production/duplicate-confirm` | POST | `/orders/optimized-parser` |
| 性能统计 | `/api/v2/production/performance-stats` | GET | `/orders/optimized-parser` |
| 清理任务 | `/api/v2/production/cleanup-tasks` | POST | `/orders/optimized-parser` |

**前端调用示例**:
```javascript
// optimized-parser.html 中的解析
const response = await fetch('/api/v2/production/parse-excel-optimized', {
  method: 'POST',
  body: formData
});

// 查询解析进度
const response = await fetch(`/api/v2/production/parse-progress/${taskId}`);

// 确认重复处理
const response = await fetch('/api/v2/production/duplicate-confirm', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ task_id: taskId, action: 'confirm' })
});
```

### 订单处理

| 功能 | 路径 | 方法 | 说明 |
|------|------|------|------|
| 启动订单处理 | `/api/v2/production/order-processing/start` | POST | 启动订单处理任务 |
| 控制处理任务 | `/api/v2/production/order-processing/control/{task_id}` | POST | 暂停/恢复/停止 |
| 任务状态查询 | `/api/v2/production/order-processing/status/{task_id}` | GET | 查询任务状态 |
| 任务列表 | `/api/v2/production/order-processing/list` | GET | 获取处理任务列表 |
| 处理统计 | `/api/v2/production/order-processing/stats` | GET | 获取处理统计 |
| 健康检查 | `/api/v2/production/order-processing/health` | GET | 系统健康状态 |

### 订单汇总预览API

| 功能 | 路径 | 方法 | 前端页面 |
|------|------|------|----------|
| 获取汇总数据 | `/api/orders/summary-data` | GET | `/orders/summary-preview` |
| 更新订单分类 | `/api/orders/update-classification` | POST | `/orders/summary-preview` |
| 分类统计信息 | `/api/orders/classification-stats` | GET | `/orders/summary-preview` |
| 生成工程汇总表 | `/api/orders/generate-engineering-summary` | POST | `/orders/summary-preview` |
| 生成量产汇总表 | `/api/orders/generate-production-summary` | POST | `/orders/summary-preview` |
| 预览汇总表 | `/api/orders/preview-summary` | GET | `/orders/summary-preview` |
| 下载汇总表 | `/api/orders/download-summary/{filename}` | GET | `/orders/summary-preview` |

**前端调用示例**:
```javascript
// summary_preview.html 中的汇总数据获取
const response = await fetch('/api/orders/summary-data');

// 更新订单分类
const response = await fetch('/api/orders/update-classification', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    order_ids: selectedOrderIds,
    classification: 'engineering'
  })
});

// 生成工程汇总表
const response = await fetch('/api/orders/generate-engineering-summary', {
  method: 'POST'
});

// 生成量产汇总表
const response = await fetch('/api/orders/generate-production-summary', {
  method: 'POST'
});
```

### 传统订单API

| 功能 | 路径 | 方法 | 前端页面 |
|------|------|------|----------|
| 扫描Lot类型 | `/api/orders/scan-lot-types` | POST | classification_fixes.js |
| 分类规则 | `/api/orders/classification-rules` | GET | classification_fixes.js |
| 预览订单文件 | `/api/orders/preview-file` | GET | `/orders/semi-auto` |

**前端调用示例**:
```javascript
// classification_fixes.js 中的扫描
fetch('/api/orders/scan-lot-types', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    source_dir: sourceDir,
    search_subdirs: searchSubdirs
  })
});

// 预览订单文件
fetch('/api/orders/preview-file?file_path=' + encodeURIComponent(filePath))
  .then(response => response.json());
```

---

## 🗂️ 资源管理API

### 通用资源操作

| 功能 | 路径 | 方法 | 说明 |
|------|------|------|------|
| 获取资源数据 | `/api/v2/resources/data/{table_name}` | GET | 分页查询表数据 |
| 获取列结构 | `/api/v2/resources/columns/{table_name}` | GET | 获取表结构信息 |
| 创建记录 | `/api/v2/resources/data/{table_name}` | POST | 创建新记录 |
| 更新记录 | `/api/v2/resources/data/{table_name}` | PUT | 更新记录 |
| 删除记录 | `/api/v2/resources/data/{table_name}/{record_id}` | DELETE | 删除单个记录 |
| 批量删除 | `/api/v2/resources/data/{table_name}/batch` | DELETE | 批量删除记录 |

### 查询参数

- `page`: 页码，从1开始
- `per_page`: 每页记录数，默认50
- `search`: 搜索关键词
- `sort_by`: 排序字段
- `sort_order`: 排序方向 (asc/desc)

### 具体资源表映射

| 资源类型 | 表名 | 前端页面 | API端点 |
|----------|------|----------|---------|
| 设备状态 | `eqp_status` | `/resources/eqp_status` | `/api/v2/resources/data/eqp_status` |
| 硬件配置 | `hardware` | `/resources/hardware` | `/api/v2/resources/data/hardware` |
| 测试规格 | `et_ft_test_spec` | `/resources/specs` | `/api/v2/resources/data/et_ft_test_spec` |
| 测试设备 | `tester` | `/resources/tester` | `/api/v2/resources/data/tester` |
| UPH配置 | `et_uph_eqp` | `/resources/uph` | `/api/v2/resources/data/et_uph_eqp` |
| 产品周期 | `ct` | `/resources/product-cycle` | `/api/v2/resources/data/ct` |
| 在制品批次 | `wip_lot` | `/wip/by-batch` | `/api/v2/resources/data/wip_lot` |
| 设备优先级 | `devicepriorityconfig` | `/production/device-priority` | `/api/v2/resources/data/devicepriorityconfig` |
| 批次优先级 | `lotpriorityconfig` | `/production/lot-priority` | `/api/v2/resources/data/lotpriorityconfig` |

**前端调用示例**:
```javascript
// base_resource.html 通用资源模板
const url = `/api/v2/resources/data/${TABLE_NAME}?` + new URLSearchParams(params);
fetch(url).then(response => response.json());

// 创建记录
fetch(`/api/v2/resources/data/${TABLE_NAME}`, {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify(formData)
});

// 删除记录
fetch(`/api/v2/resources/data/${TABLE_NAME}/${primaryValue}`, {
  method: 'DELETE'
});

// 批量删除
fetch(`/api/v2/resources/data/${TABLE_NAME}/batch`, {
  method: 'DELETE',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ ids: selectedIds })
});
```

### 用户过滤预设

| 功能 | 路径 | 方法 | 前端页面 |
|------|------|------|----------|
| 获取过滤预设 | `/api/user-filter-presets` | GET | `/wip/by-batch` |
| 创建过滤预设 | `/api/user-filter-presets` | POST | `/wip/by-batch` |
| 更新过滤预设 | `/api/user-filter-presets` | PUT | `/wip/by-batch` |
| 删除过滤预设 | `/api/user-filter-presets` | DELETE | `/wip/by-batch` |

**前端调用示例**:
```javascript
// by_batch.html 中的过滤预设
fetch('/api/user-filter-presets?page_type=wip_lot')
  .then(response => response.json());

// 保存过滤预设
fetch('/api/user-filter-presets', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify(presetData)
});
```

---

## ⚙️ 系统管理API

### 系统设置

| 功能 | 路径 | 方法 | 前端页面 |
|------|------|------|----------|
| AI设置管理 | `/api/v2/system/ai-settings` | GET, POST | `/system/settings` |
| 传统AI设置 | `/api/ai-settings` | GET, POST | `/system/settings` |
| 系统配置 | `/api/v2/system/settings` | POST | `/system/settings` |
| 邮件调度器 | `/api/v2/system/email-scheduler` | GET, POST | `/system/settings` |
| AI功能测试 | `/api/ai-test` | POST | `/system/settings` |
| AI状态查询 | `/api/ai-status` | GET | `/system/settings` |
| 全局调度器配置 | `/api/global-scheduler/config` | GET, POST | `/system/settings` |
| 重启调度器 | `/api/global-scheduler/restart-all` | POST | `/system/settings` |
| 调度器日志 | `/api/global-scheduler/logs` | GET | `/system/settings` |
| 清理缓存 | `/clear_cache` | GET | `/system/settings` |

**前端调用示例**:
```javascript
// settings.html 中的AI设置管理
fetch('/api/v2/system/ai-settings')
  .then(response => response.json());

fetch('/api/v2/system/ai-settings', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify(aiSettings)
});

// AI功能测试
fetch('/api/ai-test', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ type: 'database' })
});

// 全局调度器配置
fetch('/api/global-scheduler/config', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify(config)
});
```

### 数据库配置

| 功能 | 路径 | 方法 | 前端页面 |
|------|------|------|----------|
| 获取配置列表 | `/api/v2/system/database-config/configs` | GET | `/system/database-config` |
| 创建配置 | `/api/v2/system/database-config/configs` | POST | `/system/database-config` |
| 更新配置 | `/api/v2/system/database-config/configs/{id}` | PUT | `/system/database-config` |
| 删除配置 | `/api/v2/system/database-config/configs/{id}` | DELETE | `/system/database-config` |
| 测试连接 | `/api/v2/system/database-config/configs/{id}/test` | POST | `/system/database-config` |
| 批量测试 | `/api/v2/system/database-config/configs/test` | POST | `/system/database-config` |
| 配置类型 | `/api/v2/system/database-config/configs/types` | GET | `/system/database-config` |
| 字段映射 | `/api/v2/system/database-config/mappings` | GET, POST | `/system/database-config` |
| 数据库连接测试 | `/api/v2/system/database/test-connection` | POST | `/system/database-config` |

**前端调用示例**:
```javascript
// database_config.html 中的配置管理
fetch('/api/v2/system/database-config/configs')
  .then(response => response.json());

// 测试数据库连接
fetch('/api/v2/system/database-config/configs/test', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify(dbConfig)
});

// 批量测试连接
fetch('/api/v2/system/database-config/configs/test-batch', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ config_ids: selectedIds })
});
```

### 监控和日志

| 功能 | 路径 | 方法 | 前端页面 |
|------|------|------|----------|
| 系统指标 | `/api/v2/system/monitoring/metrics` | GET | `/system/performance` |
| 系统日志 | `/api/system_logs` | GET | `/system/logs` |
| 日志统计 | `/api/log_stats` | GET | `/system/logs` |
| 清理日志 | `/api/cleanup_logs` | POST | `/system/logs` |
| 基础日志 | `/api/logs` | GET | 通用日志接口 |

**前端调用示例**:
```javascript
// performance_monitor.html 中的监控
const response = await fetch('/api/v2/system/monitoring/metrics');

// system_logs.html 中的日志管理
const response = await fetch(`/api/system_logs?${params}`);

// 日志统计
const response = await fetch('/api/log_stats');

// 清理日志
const response = await fetch('/api/cleanup_logs', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ days: cleanupDays })
});
```

### 缓存管理

| 功能 | 路径 | 方法 | 前端页面 |
|------|------|------|----------|
| 清理系统缓存 | `/clear_cache` | GET | 系统管理 |
| 缓存清理工具 | `/static/clear_cache_tool.html` | GET | 独立工具页面 |
| 通用缓存清理 | `/static/clear_cache.html` | GET | 独立工具页面 |

**缓存清理工具功能**:
- **SessionStorage清理**: 清理菜单相关的会话存储
- **LocalStorage清理**: 清理菜单相关的本地存储  
- **菜单缓存刷新**: 强制重新加载菜单数据
- **完整缓存清理**: 清理所有浏览器缓存数据

**前端调用示例**:
```javascript
// 清理SessionStorage中的菜单缓存
function clearMenuSessionStorage() {
  const keysToRemove = [];
  for (let i = 0; i < sessionStorage.length; i++) {
    const key = sessionStorage.key(i);
    if (key && (key.startsWith('aps_menu_') || key.includes('menu'))) {
      keysToRemove.push(key);
    }
  }
  keysToRemove.forEach(key => sessionStorage.removeItem(key));
}

// 刷新菜单缓存
if (window.clearMenuCache && window.refreshMenu) {
  window.clearMenuCache();
  window.refreshMenu();
}
```

### 仪表板

| 功能 | 路径 | 方法 | 前端页面 |
|------|------|------|----------|
| 仪表板统计 | `/api/v2/system/dashboard/stats` | GET | `/index` |
| 仪表板图表 | `/api/v2/system/dashboard/charts` | GET | `/index` |

**前端调用示例**:
```javascript
// index.html 中的仪表板
const response = await fetch('/api/v2/system/dashboard/stats');
const chartResponse = await fetch('/api/v2/system/dashboard/charts');
```

---

## 🤖 AI助手API

### AI对话

| 功能 | 路径 | 方法 | 前端页面 |
|------|------|------|----------|
| AI聊天 | `/api/ai/chat` | POST | `/ai-assistant` |
| AI测试 | `/api/ai-test` | POST | `/system/settings` |
| AI状态 | `/api/ai-status` | GET | `/system/settings` |

**请求参数** (AI聊天):
```json
{
  "message": "请检查数据库连接状态",
  "history": [
    {"role": "user", "content": "上一条消息"},
    {"role": "assistant", "content": "AI回复"}
  ],
  "mode": "database"
}
```

**支持的模式**:
- `standard`: 标准对话模式
- `r1`: 高级推理模式
- `web_search`: 联网搜索模式
- `database`: 数据库查询模式

### Dify集成

| 功能 | 路径 | 方法 | 说明 |
|------|------|------|------|
| Dify代理 | `/api/dify-proxy/*` | ALL | Dify服务代理接口 |
| 嵌入脚本 | `/api/dify-proxy/embed.min.js` | GET | Dify前端嵌入脚本 |
| 聊天消息 | `/api/dify-proxy/v1/chat-messages` | POST | 转发聊天消息到Dify |
| 对话列表 | `/api/dify-proxy/v1/conversations` | GET | 获取对话列表 |
| 配置信息 | `/api/dify-proxy/config` | GET | 获取Dify配置 |
| 连接测试 | `/api/dify-proxy/test-connection` | POST | 测试Dify连接 |

**前端调用示例**:
```javascript
// AI助手中的Dify集成
fetch('/api/dify-proxy/test-connection', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ server: 'http://localhost:3000' })
});
```

**前端调用示例**:
```javascript
fetch('/api/dify-proxy/test-connection', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ server: 'http://localhost:3000' })
});
```

---

## 📧 邮件附件API

### 邮箱配置

| 功能 | 路径 | 方法 | 前端页面 |
|------|------|------|----------|
| 获取邮箱配置 | `/api/email_configs` | GET | `/orders/semi-auto`, `/orders/auto` |
| 获取单个配置 | `/api/email_configs/{id}` | GET | `/orders/semi-auto`, `/orders/auto` |
| 创建邮箱配置 | `/api/email_configs` | POST | `/orders/semi-auto`, `/orders/auto` |
| 更新邮箱配置 | `/api/email_configs/{id}` | PUT | `/orders/semi-auto`, `/orders/auto` |
| 删除邮箱配置 | `/api/email_configs/{id}` | DELETE | `/orders/semi-auto`, `/orders/auto` |
| 测试邮箱连接 | `/api/email_configs/{id}/test` | POST | `/orders/semi-auto`, `/orders/auto` |
| 获取附件 | `/api/email_configs/{id}/fetch` | POST | `/orders/semi-auto`, `/orders/auto` |

**前端调用示例**:
```javascript
// orders_semi_auto.html 和 auto.html 中的邮箱配置
const apiUrl = `/api/email_configs?_=${timestamp}`;
fetch(apiUrl, {
  method: 'GET',
  headers: {
    'Accept': 'application/json',
    'X-Requested-With': 'XMLHttpRequest'
  },
  credentials: 'same-origin'
});

// 获取附件
fetch(`/api/email_configs/${emailConfigId}/fetch`, {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ days: fetchDays })
});
```

### 附件管理

| 功能 | 路径 | 方法 | 前端页面 |
|------|------|------|----------|
| 获取附件列表 | `/api/email_attachments` | GET | email_attachment.js |
| 获取附件详情 | `/api/email_attachments/{id}` | GET | email_attachment.js |
| 处理附件 | `/api/email_attachments/{id}/process` | POST | email_attachment.js |

### 订单数据

| 功能 | 路径 | 方法 | 前端页面 |
|------|------|------|----------|
| 获取订单数据 | `/api/order_data` | GET | `/orders/semi-auto`, `/orders/auto` |
| 获取订单详情 | `/api/order_data/{id}` | GET | `/orders/semi-auto`, `/orders/auto` |
| 删除订单 | `/api/order_data/{id}` | DELETE | `/orders/semi-auto`, `/orders/auto` |

---

## 🔌 WebSocket API

### 实时通信

| 功能 | 路径 | 协议 | 说明 |
|------|------|------|------|
| WebSocket连接 | `/socket.io/` | WebSocket | 实时数据推送和通信 |

#### 支持的事件
- `connect`: 连接建立
- `disconnect`: 连接断开  
- `join_room`: 加入房间
- `leave_room`: 离开房间
- `task_update`: 任务状态更新
- `system_alert`: 系统警报

---

## 📊 数据结构定义

### 用户对象
```json
{
  "id": 1,
  "username": "admin",
  "email": "<EMAIL>", 
  "role": "admin",
  "is_active": true,
  "created_at": "2025-06-20T10:00:00Z",
  "permissions": ["read", "write", "admin"]
}
```

### 批次对象
```json
{
  "id": 1,
  "lot_id": "LOT001",
  "product_code": "P001",
  "quantity": 1000,
  "priority": 1,
  "status": "waiting",
  "due_date": "2025-06-25",
  "created_at": "2025-06-20T10:00:00Z",
  "updated_at": "2025-06-20T10:00:00Z"
}
```

### 设备对象
```json
{
  "id": 1,
  "equipment_id": "EQP001",
  "equipment_name": "测试设备1",
  "status": "running",
  "location": "产线A",
  "uph": 100,
  "last_maintenance": "2025-06-15",
  "created_at": "2025-06-20T10:00:00Z"
}
```

### 订单对象
```json
{
  "id": 1,
  "order_id": "ORD001",
  "customer": "客户A",
  "product_code": "P001", 
  "quantity": 5000,
  "delivery_date": "2025-06-30",
  "status": "processing",
  "created_at": "2025-06-20T10:00:00Z"
}
```

---

## 🔄 API使用示例

### JavaScript Client (推荐)

```javascript
// 使用统一API客户端
const client = new APSAPIClient({
  baseURL: window.location.origin,
  timeout: 30000,
  enableV2: true
});

// 获取资源数据
const response = await client.get('/resources/data/eqp_status', {
  page: 1,
  per_page: 50
});

// 创建资源记录
const newRecord = await client.post('/resources/data/eqp_status', {
  equipment_id: 'EQP002',
  status: 'idle'
});
```

### 直接Fetch调用

```javascript
// GET请求
const response = await fetch('/api/v2/resources/data/eqp_status?page=1', {
  method: 'GET',
  headers: {
    'Content-Type': 'application/json',
    'X-Requested-With': 'XMLHttpRequest'
  },
  credentials: 'same-origin'
});

// POST请求
const createResponse = await fetch('/api/v2/resources/data/eqp_status', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ equipment_id: 'EQP002', status: 'idle' }),
  credentials: 'same-origin'
});
```

---

## 📝 接口调用规范

### 请求头要求

```http
Content-Type: application/json
X-Requested-With: XMLHttpRequest  
Accept: application/json
```

### 分页参数

- `page`: 页码，从1开始
- `per_page`: 每页记录数，默认50，最大1000
- `search`: 搜索关键词
- `sort_by`: 排序字段
- `sort_order`: 排序方向 (`asc` 或 `desc`)

### 错误处理

```javascript
try {
  const response = await fetch('/api/v2/resources/data/eqp_status');
  if (!response.ok) {
    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
  }
  const data = await response.json();
  if (!data.success) {
    throw new Error(data.message);
  }
  return data.data;
} catch (error) {
  console.error('API调用失败:', error);
  showToast('操作失败：' + error.message, 'error');
}
```

---

## 🚀 性能优化建议

1. **使用API v2**: 优先使用v2版本接口，性能更好
2. **合理分页**: 避免一次请求过多数据
3. **缓存策略**: 对静态数据进行适当缓存  
4. **并发控制**: 避免同时发起过多请求
5. **错误重试**: 实现合理的重试机制
6. **统一客户端**: 使用APSAPIClient统一管理API调用

---

## 📈 API统计总览

### API数量统计

| 模块 | API v1数量 | API v2数量 | 前端页面数量 |
|------|-----------|-----------|-------------|
| 认证系统 | 12 | 3 | 2 |
| 生产管理 | 15 | 28 | 8 |
| 订单管理 | 15 | 12 | 5 |
| 资源管理 | 5 | 24 | 9 |
| 系统管理 | 23 | 15 | 5 |
| AI助手 | 8 | 2 | 2 |
| 邮件附件 | 15 | 0 | 3 |
| 菜单系统 | 3 | 0 | 所有页面 |
| **总计** | **96** | **84** | **34** |

### 前后端对应关系汇总

| 前端页面类型 | 主要使用的API版本 | API调用方式 |
|-------------|-----------------|------------|
| 资源管理页面 | API v2 | 统一资源模板 |
| 生产管理页面 | API v2 | 专用API端点 |
| 订单管理页面 | API v1 + v2 | 混合使用 |
| 系统设置页面 | API v1 + v2 | 功能特定 |
| 仪表板页面 | API v2 | 现代化接口 |

---

---

## 🔖 附录

### 常用API快速参考

```javascript
// 获取设备状态
GET /api/v2/resources/data/eqp_status

// 获取等待批次  
GET /api/v2/production/wait-lots/

// 执行自动排产
POST /api/production/auto-schedule

// AI助手对话
POST /api/ai/chat

// 获取系统日志
GET /api/system_logs

// 测试数据库连接
POST /api/v2/system/database/test-connection

// 获取用户菜单
GET /menu

// 订单汇总数据
GET /api/orders/summary-data

// 生成工程汇总表
POST /api/orders/generate-engineering-summary

// 清理系统缓存
GET /clear_cache
```

### 开发环境配置

```bash
# 启动开发服务器
python run.py

# API测试地址
http://localhost:5000/api/

# WebSocket测试地址  
ws://localhost:5000/socket.io/
```

### API版本对比

| 特性 | API v1 | API v2 |
|------|--------|--------|
| 响应格式 | 不统一 | 统一JSON格式 |
| 错误处理 | 基础 | 详细错误信息 |
| 分页支持 | 部分支持 | 全面支持 |
| 数据验证 | 基础 | 严格验证 |
| 性能优化 | 一般 | 优化良好 |
| 文档完整性 | 部分 | 完整 |

---

**最后更新**: 2025年6月22日  
**文档状态**: ✅ 完整  
**接口总数**: 180个  
**页面总数**: 34个

--