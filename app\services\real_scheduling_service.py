#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真实业务逻辑的智能排产服务
实现基于DEVICE+STAGE配置匹配的排产算法
"""

import logging
import time
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple
from collections import defaultdict

logger = logging.getLogger(__name__)

class RealSchedulingService:
    """基于真实业务逻辑的智能排产服务"""
    
    def __init__(self):
        from app.services.data_source_manager import DataSourceManager
        self.data_manager = DataSourceManager()
        self.equipment_workload = {}  # 设备工作负载跟踪

        # 缓存机制 - 避免重复查询配置数据
        self._device_priority_cache = None
        self._lot_priority_cache = None
        self._cache_timestamp = None
        self._cache_timeout = 300  # 5分钟

        # 权重配置缓存
        self._weights_cache = None
        self._weights_cache_timestamp = None
        self._weights_cache_timeout = 300  # 5分钟权重缓存

        # 默认权重配置（作为后备）
        self.default_weights = {
            'tech_match_weight': 25.0,      # 技术匹配度权重
            'load_balance_weight': 20.0,    # 负载均衡权重
            'deadline_weight': 25.0,        # 交期紧迫度权重
            'value_efficiency_weight': 20.0, # 产值效率权重
            'business_priority_weight': 10.0, # 业务优先级权重
            'minor_changeover_time': 45,     # 小改机时间(分钟)
            'major_changeover_time': 120,    # 大改机时间(分钟)
            'initial_setup_time': 60,        # 初始化时间(分钟，空闲设备)
            'urgent_threshold': 8,           # 紧急阈值(小时)
            'normal_threshold': 24,          # 正常阈值(小时)
            'critical_threshold': 72         # 关键阈值(小时)
        }

        self._uph_cache = None  # 缓存UPH数据
        self._uph_cache_timestamp = None
        self._uph_cache_timeout = 300  # 5分钟

        # 当前策略设置
        self._current_strategy = 'intelligent'

    def set_strategy(self, strategy_name='intelligent'):
        """设置当前排产策略"""
        self._current_strategy = strategy_name
        # 清除权重缓存，强制重新加载
        self._weights_cache = None
        self._weights_cache_timestamp = None
        logger.info(f"🎯 排产策略已设置为: {strategy_name}")

    def get_current_weights(self, user_id=None, strategy_name='intelligent'):
        """获取当前权重配置（支持缓存和策略）"""
        import time
        current_time = time.time()

        # 生成缓存键（包含策略名称）
        cache_key = f"{user_id}_{strategy_name}"

        # 检查缓存是否有效
        if (self._weights_cache_timestamp and
            current_time - self._weights_cache_timestamp < self._weights_cache_timeout and
            self._weights_cache is not None and
            getattr(self, '_weights_cache_key', None) == cache_key):
            return self._weights_cache

        try:
            # 从数据库获取权重配置
            from app.models import SchedulingConfig
            weights = SchedulingConfig.get_strategy_weights(strategy_name=strategy_name, user_id=user_id)

            # 更新缓存
            self._weights_cache = weights
            self._weights_cache_timestamp = current_time
            self._weights_cache_key = cache_key

            logger.debug(f"🔄 权重配置已更新 [策略: {strategy_name}]: {weights}")
            return weights

        except Exception as e:
            logger.warning(f"获取动态权重配置失败，使用默认配置: {e}")
            # 如果获取失败，使用默认权重
            return self.default_weights

    def _load_priority_configs(self):
        """加载优先级配置数据到缓存"""
        import time
        current_time = time.time()
        
        # 检查缓存是否有效
        if (self._cache_timestamp and 
            current_time - self._cache_timestamp < self._cache_timeout and
            self._device_priority_cache is not None and
            self._lot_priority_cache is not None):
            return  # 缓存仍然有效
        
        logger.info("🔄 加载优先级配置数据到缓存...")
        
        # 加载设备优先级配置
        device_config_result = self.data_manager.get_table_data('devicepriorityconfig', per_page=1000)
        if device_config_result.get('success'):
            self._device_priority_cache = device_config_result.get('data', [])
            logger.info(f"✅ 缓存设备优先级配置: {len(self._device_priority_cache)} 条")
        else:
            self._device_priority_cache = []
            logger.warning("⚠️ 设备优先级配置加载失败")
        
        # 加载批次优先级配置
        lot_config_result = self.data_manager.get_table_data('lotpriorityconfig', per_page=1000)
        if lot_config_result.get('success'):
            self._lot_priority_cache = lot_config_result.get('data', [])
            logger.info(f"✅ 缓存批次优先级配置: {len(self._lot_priority_cache)} 条")
        else:
            self._lot_priority_cache = []
            logger.warning("⚠️ 批次优先级配置加载失败")
        
        # 更新缓存时间戳
        self._cache_timestamp = current_time
        
    def get_lot_configuration_requirements(self, lot: Dict) -> Optional[Dict]:
        """
        获取批次配置需求
        - BAKING阶段：无需复杂配置，所有烘箱都可以处理
        - LSTR阶段：需要根据PKG_PN匹配合适的编带机
        - 其他阶段：通过 DEVICE + STAGE 查询 ET_FT_TEST_SPEC
        
        Args:
            lot: 待排产批次信息
            
        Returns:
            Dict: 配置需求信息 {HB_PN, TB_PN, HANDLER_CONFIG, PKG_PN} 或 None
        """
        try:
            device = lot.get('DEVICE', '').strip()
            stage = lot.get('STAGE', '').strip()
            pkg_pn = lot.get('PKG_PN', '').strip()
            
            if not device or not stage:
                logger.warning(f"批次 {lot.get('LOT_ID')} 缺少DEVICE或STAGE信息")
                return None
            
            # 特殊处理：BAKING阶段（烘箱）
            if 'BAKING' in stage.upper():
                logger.debug(f"批次 {lot.get('LOT_ID')} BAKING阶段，使用简化配置")
                return {
                    'DEVICE': device,
                    'STAGE': stage,
                    'PKG_PN': pkg_pn,
                    'HB_PN': '',
                    'TB_PN': '',
                    'KIT_PN': '',
                    'HANDLER_CONFIG': '',
                    'TESTER': '',
                    'UPH': 1000,  # 默认UPH
                    'TEST_SPEC_SOURCE': 'BAKING_SIMPLIFIED'
                }
            
            # 特殊处理：LSTR阶段（纯编带机）
            if 'LSTR' in stage.upper():
                # LSTR需要PKG_PN信息来匹配编带机
                if not pkg_pn:
                    logger.warning(f"LSTR批次 {lot.get('LOT_ID')} 缺少PKG_PN信息，无法匹配编带机")
                    return None
                
                logger.debug(f"批次 {lot.get('LOT_ID')} LSTR阶段，PKG_PN={pkg_pn}")
                return {
                    'DEVICE': device,
                    'STAGE': stage,
                    'PKG_PN': pkg_pn,
                    'HB_PN': '',
                    'TB_PN': '',
                    'KIT_PN': '',
                    'HANDLER_CONFIG': '',
                    'TESTER': '',
                    'UPH': 2000,  # 编带机通常UPH较高
                    'TEST_SPEC_SOURCE': 'LSTR_PKG_MATCH'
                }
            
            # 常规处理：从ET_FT_TEST_SPEC获取配置
            # 查询测试规范（获取完整数据，避免分页限制）
            specs_result = self.data_manager.get_table_data('ET_FT_TEST_SPEC', per_page=1000)
            if not specs_result.get('success'):
                logger.error("无法获取测试规范数据")
                return None
                
            specs_data = specs_result.get('data', [])
            
            # 查找匹配的配置（DEVICE + STAGE + APPROVAL_STATE='Released'）
            # 支持STAGE字段的智能匹配
            for spec in specs_data:
                spec_device = spec.get('DEVICE', '').strip()
                spec_stage = spec.get('STAGE', '').strip()
                spec_approval = spec.get('APPROVAL_STATE', '').strip()
                
                if (spec_device == device and 
                    self._is_stage_match(stage, spec_stage) and
                    spec_approval == 'Released'):
                    
                    config = {
                        'DEVICE': device,
                        'STAGE': stage,  # 保留原始批次STAGE
                        'HB_PN': spec.get('HB_PN', '').strip(),
                        'TB_PN': spec.get('TB_PN', '').strip(), 
                        'HANDLER_CONFIG': spec.get('HANDLER', '').strip(),  # 来自ET_FT_TEST_SPEC，可能不是真实配置
                        'PKG_PN': spec.get('PKG_PN', '').strip(),
                        'TESTER': spec.get('TESTER', '').strip(),
                        'UPH': spec.get('UPH', 0),
                        'TEST_SPEC_SOURCE': 'ET_FT_TEST_SPEC'
                    }
                    
                    logger.debug(f"批次 {lot.get('LOT_ID')} 找到测试规范配置: {config}")
                    
                    # 关键修复：使用缓存方式加载UPH，避免重复查询
                    current_time = time.time()
                    if (self._uph_cache is None or
                        self._uph_cache_timestamp is None or
                        current_time - self._uph_cache_timestamp > self._uph_cache_timeout):
                        self._uph_cache, _ = self.data_manager.get_uph_data()
                        self._uph_cache_timestamp = current_time
                    
                    uph_key = f"{device}|{stage}"
                    authoritative_uph = self._uph_cache.get(uph_key, {}).get('UPH')

                    if authoritative_uph:
                        config['UPH'] = authoritative_uph
                        logger.debug(f"批次 {lot.get('LOT_ID')} 从 et_uph_eqp 获取到权威UPH: {authoritative_uph}")
                    else:
                        config['UPH'] = spec.get('UPH', 0)
                        logger.debug(f"批次 {lot.get('LOT_ID')} 未在 et_uph_eqp 找到UPH，使用 et_ft_test_spec 的备用UPH: {config['UPH']}")

                    # 查询配方文件获取真实的KIT和设备配置信息
                    kit_info = self.get_kit_configuration(device, spec_stage, config.get('PKG_PN'))
                    if kit_info:
                        # 用配方文件中的真实配置覆盖测试规范中的配置
                        if kit_info.get('HANDLER_CONFIG_RECIPE'):
                            config['HANDLER_CONFIG'] = kit_info['HANDLER_CONFIG_RECIPE']
                            logger.debug(f"批次 {lot.get('LOT_ID')} 使用配方文件中的真实HANDLER_CONFIG: {kit_info['HANDLER_CONFIG_RECIPE']}")
                        
                        config.update(kit_info)
                        logger.debug(f"批次 {lot.get('LOT_ID')} 找到KIT配置: {kit_info}")
                    
                    return config
            
            logger.warning(f"批次 {lot.get('LOT_ID')} 未找到匹配的测试规范 (DEVICE={device}, STAGE={stage})")
            return None
            
        except Exception as e:
            logger.error(f"获取批次配置需求失败: {e}")
            return None
    
    def _is_stage_match(self, lot_stage: str, spec_stage: str) -> bool:
        """
        智能匹配STAGE字段
        处理不同命名规范的STAGE字段匹配
        
        Args:
            lot_stage: 批次的STAGE (如 HOT-FT, COLD-FT, ROOM-TTR-FT)
            spec_stage: 测试规范的STAGE (如 Hot, Cold, ROOM-TTR)
            
        Returns:
            bool: 是否匹配
        """
        try:
            # 完全匹配
            if lot_stage == spec_stage:
                return True
            
            # 大小写不敏感匹配
            if lot_stage.upper() == spec_stage.upper():
                return True
                
            # 温度测试匹配规则
            temp_mapping = {
                'HOT-FT': ['Hot', 'HOT', 'hot'],
                'COLD-FT': ['Cold', 'COLD', 'cold'],
                'ROOM-TTR-FT': ['ROOM-TTR', 'Room-TTR', 'room-ttr'],
                'ROOM-TEST-FT': ['ROOM-TEST', 'Room-Test', 'room-test'], 
                'TRIM-FT': ['TRIM', 'Trim', 'trim'],
                'BAKING2': ['BAKING', 'Baking', 'baking'],
                'LSTR': ['LSTR', 'Lstr', 'lstr']
            }
            
            # 检查批次STAGE是否有对应的测试规范STAGE
            if lot_stage in temp_mapping:
                return spec_stage in temp_mapping[lot_stage]
            
            # 反向匹配：如果测试规范STAGE在映射表的值中，检查是否匹配
            for lot_pattern, spec_patterns in temp_mapping.items():
                if spec_stage in spec_patterns and lot_stage == lot_pattern:
                    return True
            
            # 部分匹配：去掉后缀 -FT 再比较
            if lot_stage.endswith('-FT'):
                base_lot_stage = lot_stage[:-3]  # 去掉 -FT
                if base_lot_stage.upper() == spec_stage.upper():
                    return True
                # 进一步处理 ROOM-TTR vs ROOM-TTR-FT
                if base_lot_stage == spec_stage:
                    return True
            
            return False
            
        except Exception as e:
            logger.error(f"STAGE匹配失败: {e}")
            return False
    
    def get_kit_configuration(self, device: str, stage: str, pkg_pn: str) -> Optional[Dict]:
        """
        获取KIT配置信息
        通过 DEVICE + STAGE + PKG_PN 查询 et_recipe_file
        
        Args:
            device: 设备类型
            stage: 阶段
            pkg_pn: 封装零件号
            
        Returns:
            Dict: KIT配置信息 {KIT_PN, SOCKET_PN} 或 None
        """
        try:
            # 查询配方文件（使用缓存优化）
            recipe_data_dict, recipe_source = self.data_manager.get_recipe_file_data()
            if not recipe_data_dict:
                logger.warning("无法获取配方文件数据，跳过KIT配置查询")
                return None
                
            # 转换为列表格式进行查找
            recipe_data = list(recipe_data_dict.values())
            
            # 查找匹配的KIT配置
            for recipe in recipe_data:
                if (recipe.get('DEVICE', '').strip() == device and 
                    recipe.get('STAGE', '').strip() == stage and
                    recipe.get('PKG_PN', '').strip() == pkg_pn and
                    recipe.get('APPROVAL_STATE', '').strip() == 'Released'):
                    
                    return {
                        'KIT_PN': recipe.get('KIT_PN', '').strip(),
                        'SOCKET_PN': recipe.get('SOCKET_PN', '').strip(),
                        'HANDLER_CONFIG_RECIPE': recipe.get('HANDLER_CONFIG', '').strip()
                    }
            
            logger.debug(f"未找到KIT配置 (DEVICE={device}, STAGE={stage}, PKG_PN={pkg_pn})")
            return None
            
        except Exception as e:
            logger.error(f"获取KIT配置失败: {e}")
            return None
    
    def calculate_equipment_match_score(self, lot_requirements: Dict, equipment: Dict) -> Tuple[int, str, int]:
        """
        基于真实案例优化的智能设备匹配评分系统 (V3 - 业界最佳实践)
        
        参考：
        1. TDM Test Scheduler - 车规芯片终测智能调度
        2. OCAP优化 - 半导体制造失控行动计划
        3. 多目标智能优化算法
        
        Args:
            lot_requirements: 批次配置需求
            equipment: 设备当前配置
            
        Returns:
            Tuple[int, str, int]: (匹配分数, 匹配类型, 改机时间分钟)
        """
        try:
            eqp_status = equipment.get('STATUS', '').strip()
            handler_id = equipment.get('HANDLER_ID', '')

            # 规则0: 设备不可用 (DOWN)
            if eqp_status == 'DOWN':
                return 0, "设备不可用(DOWN)", 9999

            # 获取批次和设备的关键硬件信息
            req_kit = lot_requirements.get('KIT_PN', '').strip()
            req_hb = lot_requirements.get('HB_PN', '').strip()
            req_tb = lot_requirements.get('TB_PN', '').strip()
            req_handler_config = lot_requirements.get('HANDLER_CONFIG', '').strip()
            
            eqp_kit = equipment.get('KIT_PN', '').strip()
            eqp_hb = equipment.get('HB_PN', '').strip()
            eqp_tb = equipment.get('TB_PN', '').strip()
            eqp_handler_config = equipment.get('HANDLER_CONFIG', '').strip()

            # 智能匹配度计算
            hardware_match_score = self._calculate_hardware_compatibility(
                req_kit, req_hb, req_tb, eqp_kit, eqp_hb, eqp_tb
            )
            
            # 配置兼容性评估
            config_compatibility = self._calculate_config_compatibility(
                req_handler_config, eqp_handler_config, lot_requirements.get('STAGE', '')
            )
            
            # 设备历史性能评估
            equipment_performance = self._get_equipment_performance_score(handler_id)
            
            # 当前工作负载评估
            current_load_factor = self._get_current_load_factor(handler_id)

            # === 基于真实案例的智能匹配规则 ===
            
            # 完美匹配：硬件+配置完全一致
            if hardware_match_score >= 95 and config_compatibility >= 90:
                if eqp_status == 'Run':
                    # 考虑设备性能和负载
                    final_score = min(100, 100 + equipment_performance - current_load_factor)
                    return int(final_score), "完美衔接(智能)", 0
                elif eqp_status == 'IDLE':
                    final_score = min(98, 98 + equipment_performance - current_load_factor)
                    return int(final_score), "立即上机(智能)", 0
                elif eqp_status == 'Wait':
                    final_score = min(95, 95 + equipment_performance - current_load_factor)
                    return int(final_score), "优先等待(智能)", 0
                else:
                    final_score = min(92, 92 + equipment_performance - current_load_factor)
                    return int(final_score), "快速准备(智能)", 3

            # 高度兼容：主要硬件匹配，配置可调
            elif hardware_match_score >= 80 and config_compatibility >= 70:
                changeover_time = self._calculate_intelligent_changeover_time(
                    hardware_match_score, config_compatibility, eqp_status
                )
                final_score = min(85, int(hardware_match_score * 0.8 + config_compatibility * 0.2) + equipment_performance - current_load_factor)
                return int(final_score), "智能小改机", changeover_time

            # 中等兼容：需要一定改机
            elif hardware_match_score >= 60:
                changeover_time = self._calculate_intelligent_changeover_time(
                    hardware_match_score, config_compatibility, eqp_status
                )
                
                if eqp_status == 'IDLE':
                    final_score = min(75, int(hardware_match_score * 0.7 + config_compatibility * 0.3) + equipment_performance - current_load_factor)
                    return int(final_score), "空闲智能配置", changeover_time
                else:
                    final_score = min(65, int(hardware_match_score * 0.6 + config_compatibility * 0.4) + equipment_performance - current_load_factor)
                    return int(final_score), "中断智能改机", changeover_time + 30

            # 低兼容性：大改机或专用设备
            else:
                changeover_time = self._calculate_intelligent_changeover_time(
                    hardware_match_score, config_compatibility, eqp_status
                )
                
                if eqp_status == 'IDLE':
                    final_score = max(30, min(55, int(hardware_match_score * 0.5 + config_compatibility * 0.5) + equipment_performance - current_load_factor))
                    return int(final_score), "大改机(空闲)", changeover_time
                else:
                    final_score = max(20, min(45, int(hardware_match_score * 0.4 + config_compatibility * 0.6) + equipment_performance - current_load_factor))
                    return int(final_score), "大改机(中断)", changeover_time + 60

        except Exception as e:
            logger.error(f"计算智能设备匹配评分失败: {e}")
            return 0, "匹配计算异常", 9999

    def _calculate_hardware_compatibility(self, req_kit: str, req_hb: str, req_tb: str, 
                                        eqp_kit: str, eqp_hb: str, eqp_tb: str) -> float:
        """计算硬件兼容性评分"""
        try:
            score = 0.0
            total_weight = 0.0
            
            # KIT匹配权重最高（50%）
            kit_weight = 50.0
            if req_kit and eqp_kit:
                if req_kit == eqp_kit:
                    score += kit_weight
                elif self._is_kit_family_compatible(req_kit, eqp_kit):
                    score += kit_weight * 0.8  # 同系列KIT 80%兼容
                total_weight += kit_weight
            
            # HB匹配权重（30%）
            hb_weight = 30.0
            if req_hb and eqp_hb:
                if req_hb == eqp_hb:
                    score += hb_weight
                elif self._is_hb_compatible(req_hb, eqp_hb):
                    score += hb_weight * 0.6  # 兼容HB 60%分数
                total_weight += hb_weight
            
            # TB匹配权重（20%）
            tb_weight = 20.0
            if req_tb and eqp_tb:
                if req_tb == eqp_tb:
                    score += tb_weight
                elif self._is_tb_compatible(req_tb, eqp_tb):
                    score += tb_weight * 0.7  # 兼容TB 70%分数
                total_weight += tb_weight
            
            return (score / total_weight * 100) if total_weight > 0 else 50.0
            
        except Exception as e:
            logger.error(f"计算硬件兼容性失败: {e}")
            return 50.0

    def _calculate_config_compatibility(self, req_config: str, eqp_config: str, stage: str) -> float:
        """计算配置兼容性评分"""
        try:
            if not req_config or not eqp_config:
                return 70.0  # 无配置信息时给予中等分数
            
            if req_config == eqp_config:
                return 100.0  # 完全匹配
            
            # 基于STAGE的特殊兼容性规则
            if 'BAKING' in stage.upper():
                return 95.0  # 烘箱通常兼容性很高
            elif 'LSTR' in stage.upper():
                return 85.0  # 编带机配置相对灵活
            elif 'FT' in stage.upper():
                # 终测配置相对严格
                if self._is_ft_config_compatible(req_config, eqp_config):
                    return 75.0
                else:
                    return 40.0
            
            return 60.0  # 默认中等兼容性
            
        except Exception as e:
            logger.error(f"计算配置兼容性失败: {e}")
            return 60.0

    def _get_equipment_performance_score(self, handler_id: str) -> float:
        """获取设备历史性能评分"""
        try:
            # 这里可以集成设备历史数据分析
            # 暂时返回基于设备ID的简单评分
            if not handler_id:
                return 0.0
            
            # 基于设备编号的性能假设（实际应该从历史数据计算）
            performance_factors = {
                'reliability': 0.4,  # 可靠性权重
                'efficiency': 0.3,   # 效率权重
                'quality': 0.3       # 质量权重
            }
            
            # 简化的性能评分逻辑（实际应该查询历史数据）
            base_score = hash(handler_id) % 10  # 0-9的基础分
            normalized_score = (base_score / 9.0) * 10.0  # 标准化到0-10分
            
            return min(10.0, max(-5.0, normalized_score - 5.0))  # -5到+5的调整范围
            
        except Exception as e:
            logger.error(f"获取设备性能评分失败: {e}")
            return 0.0

    def _get_current_load_factor(self, handler_id: str) -> float:
        """获取当前负载因子"""
        try:
            current_load = self.equipment_workload.get(handler_id, 0.0)
            
            # 负载因子计算：轻负载加分，重负载减分
            if current_load < 8.0:  # 轻负载
                return -2.0  # 加分
            elif current_load < 16.0:  # 中等负载
                return 0.0
            elif current_load < 24.0:  # 重负载
                return 3.0  # 减分
            else:  # 超负载
                return 8.0  # 大幅减分
                
        except Exception as e:
            logger.error(f"获取负载因子失败: {e}")
            return 0.0

    def _calculate_intelligent_changeover_time(self, hardware_score: float, config_score: float, status: str) -> int:
        """智能计算改机时间"""
        try:
            base_time = 30  # 基础改机时间
            
            # 基于硬件兼容性调整
            if hardware_score >= 90:
                hardware_factor = 0.5
            elif hardware_score >= 70:
                hardware_factor = 1.0
            elif hardware_score >= 50:
                hardware_factor = 1.5
            else:
                hardware_factor = 2.0
            
            # 基于配置兼容性调整
            if config_score >= 80:
                config_factor = 0.8
            elif config_score >= 60:
                config_factor = 1.2
            else:
                config_factor = 1.8
            
            # 基于设备状态调整
            if status == 'IDLE':
                status_factor = 1.0
            elif status == 'Wait':
                status_factor = 1.2
            elif status == 'Run':
                status_factor = 1.5
            else:
                status_factor = 1.3
            
            total_time = int(base_time * hardware_factor * config_factor * status_factor)
            return min(300, max(5, total_time))  # 限制在5-300分钟范围内
            
        except Exception as e:
            logger.error(f"计算智能改机时间失败: {e}")
            return 60

    def _is_kit_family_compatible(self, req_kit: str, eqp_kit: str) -> bool:
        """判断KIT是否属于同一系列"""
        try:
            if not req_kit or not eqp_kit:
                return False
            
            # 提取KIT系列标识（假设前缀相同表示同系列）
            req_prefix = req_kit.split('-')[0] if '-' in req_kit else req_kit[:3]
            eqp_prefix = eqp_kit.split('-')[0] if '-' in eqp_kit else eqp_kit[:3]
            
            return req_prefix == eqp_prefix
            
        except Exception as e:
            logger.error(f"判断KIT系列兼容性失败: {e}")
            return False

    def _is_hb_compatible(self, req_hb: str, eqp_hb: str) -> bool:
        """判断HB是否兼容"""
        try:
            if not req_hb or not eqp_hb:
                return False
            
            # HB兼容性规则（可根据实际情况调整）
            # 例如：某些HB可以向下兼容
            return req_hb.startswith(eqp_hb[:2]) or eqp_hb.startswith(req_hb[:2])
            
        except Exception as e:
            logger.error(f"判断HB兼容性失败: {e}")
            return False

    def _is_tb_compatible(self, req_tb: str, eqp_tb: str) -> bool:
        """判断TB是否兼容"""
        try:
            if not req_tb or not eqp_tb:
                return False
            
            # TB兼容性规则（可根据实际情况调整）
            return req_tb.startswith(eqp_tb[:2]) or eqp_tb.startswith(req_tb[:2])
            
        except Exception as e:
            logger.error(f"判断TB兼容性失败: {e}")
            return False

    def _is_ft_config_compatible(self, req_config: str, eqp_config: str) -> bool:
        """判断终测配置是否兼容"""
        try:
            if not req_config or not eqp_config:
                return False
            
            # 终测配置兼容性规则（通常比较严格）
            # 可以根据实际的配置命名规则调整
            return req_config.split('_')[0] == eqp_config.split('_')[0]
            
        except Exception as e:
            logger.error(f"判断终测配置兼容性失败: {e}")
            return False
    
    def calculate_load_balance_score(self, equipment: Dict, processing_time: float, changeover_time: int) -> float:
        """
        智能负载均衡评分 (基于真实案例优化)
        
        参考：半导体制造中的动态负载均衡算法
        考虑设备利用率、队列长度、历史性能等多个因素
        """
        try:
            handler_id = equipment.get('HANDLER_ID', '')
            
            # 获取当前设备负载
            current_load = self.equipment_workload.get(handler_id, 0.0)
            
            # 计算新增负载（改机时间转为小时）
            changeover_hours = changeover_time / 60.0
            total_new_load = current_load + changeover_hours + processing_time
            
            # 动态负载阈值计算（基于设备类型和能力）
            equipment_capacity = self._get_equipment_capacity(equipment)
            max_load = equipment_capacity.get('max_daily_hours', 24.0)
            optimal_load = equipment_capacity.get('optimal_load_ratio', 0.75) * max_load
            
            # 多层次负载评分
            load_ratio = total_new_load / max_load
            
            if load_ratio <= 0.3:  # 轻负载区间
                base_score = 100.0
                # 考虑设备空置成本
                idle_penalty = min(10.0, (0.3 - load_ratio) * 20)
                return max(90.0, base_score - idle_penalty)
                
            elif load_ratio <= 0.6:  # 理想负载区间
                return 100.0  # 最优区间
                
            elif load_ratio <= 0.8:  # 高负载区间
                return 90.0 - (load_ratio - 0.6) * 50  # 线性递减
                
            elif load_ratio <= 1.0:  # 满负载区间
                return 70.0 - (load_ratio - 0.8) * 100  # 快速递减
                
            else:  # 超负载区间
                overload_penalty = (load_ratio - 1.0) * 200
                return max(0.0, 50.0 - overload_penalty)
            
        except Exception as e:
            logger.error(f"计算智能负载均衡评分失败: {e}")
            return 50.0

    def _get_equipment_capacity(self, equipment: Dict) -> Dict:
        """获取设备产能信息"""
        try:
            eqp_type = equipment.get('EQP_TYPE', '').upper()
            
            # 基于设备类型的产能配置
            capacity_config = {
                'TESTER': {
                    'max_daily_hours': 22.0,  # 测试机可以长时间运行
                    'optimal_load_ratio': 0.85,
                    'maintenance_hours': 2.0
                },
                'HANDLER': {
                    'max_daily_hours': 20.0,  # 分选机需要更多维护时间
                    'optimal_load_ratio': 0.75,
                    'maintenance_hours': 4.0
                },
                'BAKING': {
                    'max_daily_hours': 24.0,  # 烘箱可以连续运行
                    'optimal_load_ratio': 0.90,
                    'maintenance_hours': 0.5
                },
                'LSTR': {
                    'max_daily_hours': 16.0,  # 编带机人工操作较多
                    'optimal_load_ratio': 0.70,
                    'maintenance_hours': 2.0
                }
            }
            
            return capacity_config.get(eqp_type, {
                'max_daily_hours': 20.0,
                'optimal_load_ratio': 0.75,
                'maintenance_hours': 2.0
            })
            
        except Exception as e:
            logger.error(f"获取设备产能信息失败: {e}")
            return {
                'max_daily_hours': 20.0,
                'optimal_load_ratio': 0.75,
                'maintenance_hours': 2.0
            }
    
    def calculate_deadline_urgency_score(self, lot: Dict, processing_time: float) -> float:
        """
        智能交期紧迫度评分 (基于真实案例优化)
        
        参考：车规芯片制造中的多维度交期管理
        综合考虑客户优先级、合同交期、库存风险等因素
        """
        try:
            # 1. 客户优先级检查 (最高优先级)
            priority_level = lot.get('PRIORITY', '').strip().lower()
            customer_type = lot.get('CUSTOMER_TYPE', '').strip().upper()  # VIP, NORMAL, etc.
            
            # VIP客户或关键项目特殊处理
            if customer_type == 'VIP' or priority_level == '0':
                return 180.0  # 最高优先级
            elif priority_level == '1':
                return 150.0  # 高优先级
            elif priority_level == '2':
                return 120.0  # 中等优先级
            
            # 2. 合同交期分析
            delivery_date = lot.get('DELIVERY_DATE') or lot.get('REQ_DATE') or lot.get('DUE_DATE')
            
            if delivery_date:
                urgency_score = self._calculate_delivery_urgency(delivery_date, processing_time)
                
                # 3. 库存风险评估
                inventory_risk = self._calculate_inventory_risk(lot)
                
                # 4. 生产连续性考虑
                continuity_bonus = self._calculate_production_continuity_bonus(lot)
                
                # 综合评分
                final_score = urgency_score + inventory_risk + continuity_bonus
                return min(200.0, max(20.0, final_score))
            
            # 5. 无交期信息时的智能FIFO
            fifo_score = self._calculate_intelligent_fifo_score(lot)
            
            # 6. 批次类型优先级
            lot_type_bonus = self._get_lot_type_priority_bonus(lot.get('LOT_TYPE', ''))
            
            return min(100.0, max(30.0, fifo_score + lot_type_bonus))
                
        except Exception as e:
            logger.error(f"计算智能交期紧迫度评分失败: {e}")
            return 50.0

    def _calculate_delivery_urgency(self, delivery_date, processing_time: float) -> float:
        """计算交期紧迫度"""
        try:
            # 解析交期
            if isinstance(delivery_date, str):
                try:
                    delivery_dt = datetime.strptime(delivery_date, '%Y-%m-%d')
                except ValueError:
                    try:
                        delivery_dt = datetime.strptime(delivery_date, '%Y-%m-%d %H:%M:%S')
                    except ValueError:
                        return 50.0  # 解析失败，返回中等评分
            else:
                delivery_dt = delivery_date
            
            # 计算剩余时间
            current_time = datetime.now()
            completion_time = current_time + timedelta(hours=processing_time)
            remaining_hours = (delivery_dt - completion_time).total_seconds() / 3600
            
            # 智能紧迫度评分
            if remaining_hours < -24:  # 超期超过1天
                return 200.0  # 最高紧急
            elif remaining_hours < 0:  # 已超期但不超过1天
                return 180.0
            elif remaining_hours < 4:  # 4小时内
                return 160.0
            elif remaining_hours < 8:  # 8小时内
                return 140.0
            elif remaining_hours < 24:  # 24小时内
                return 120.0
            elif remaining_hours < 48:  # 48小时内
                return 100.0
            elif remaining_hours < 72:  # 72小时内
                return 80.0
            elif remaining_hours < 168:  # 1周内
                return 60.0
            else:  # 1周以上
                return 40.0
                
        except Exception as e:
            logger.error(f"计算交期紧迫度失败: {e}")
            return 50.0

    def _calculate_inventory_risk(self, lot: Dict) -> float:
        """计算库存风险评分"""
        try:
            # 获取库存相关信息
            good_qty = float(lot.get('GOOD_QTY', 0) or 0)
            device = lot.get('DEVICE', '')
            
            # 基于数量的风险评估
            qty_risk = 0.0
            if good_qty > 10000:  # 大批量
                qty_risk = 15.0
            elif good_qty > 5000:  # 中批量
                qty_risk = 10.0
            elif good_qty > 1000:  # 小批量
                qty_risk = 5.0
            
            # 基于产品类型的风险评估（车规芯片通常风险较高）
            device_risk = 0.0
            if 'AUTO' in device.upper() or 'CAR' in device.upper():
                device_risk = 10.0  # 车规产品风险较高
            elif 'CONSUMER' in device.upper():
                device_risk = 5.0   # 消费级产品风险中等
            
            return min(20.0, qty_risk + device_risk)
            
        except Exception as e:
            logger.error(f"计算库存风险失败: {e}")
            return 0.0

    def _calculate_production_continuity_bonus(self, lot: Dict) -> float:
        """计算生产连续性奖励"""
        try:
            device = lot.get('DEVICE', '')
            stage = lot.get('STAGE', '')
            
            # 检查是否有同类产品正在生产（提高连续性）
            # 这里简化处理，实际应该查询当前生产状态
            continuity_key = f"{device}_{stage}"
            
            # 基于工艺流程的连续性奖励
            if 'FT' in stage.upper():  # 终测工艺连续性重要
                return 8.0
            elif 'BAKING' in stage.upper():  # 烘箱工艺相对独立
                return 3.0
            elif 'LSTR' in stage.upper():  # 编带工艺
                return 5.0
            
            return 0.0
            
        except Exception as e:
            logger.error(f"计算生产连续性奖励失败: {e}")
            return 0.0

    def _calculate_intelligent_fifo_score(self, lot: Dict) -> float:
        """智能FIFO评分"""
        try:
            lot_id = lot.get('LOT_ID', '')
            create_time = lot.get('CREATE_TIME') or lot.get('START_TIME')
            
            # 优先使用创建时间
            if create_time:
                if isinstance(create_time, str):
                    try:
                        create_dt = datetime.strptime(create_time, '%Y-%m-%d %H:%M:%S')
                    except ValueError:
                        try:
                            create_dt = datetime.strptime(create_time, '%Y-%m-%d')
                        except ValueError:
                            create_dt = None
                else:
                    create_dt = create_time
                
                if create_dt:
                    # 基于等待时间的FIFO评分
                    waiting_hours = (datetime.now() - create_dt).total_seconds() / 3600
                    if waiting_hours > 72:  # 等待超过3天
                        return 80.0
                    elif waiting_hours > 48:  # 等待超过2天
                        return 70.0
                    elif waiting_hours > 24:  # 等待超过1天
                        return 60.0
                    else:
                        return 50.0
            
            # 基于LOT_ID的FIFO（备用方案）
            if lot_id:
                import re
                numbers = re.findall(r'\d+', lot_id)
                if numbers:
                    lot_number = int(numbers[-1])
                    # 较小的编号获得较高的优先级
                    base_score = 55.0
                    fifo_factor = min(10.0, (lot_number % 1000) / 100.0)
                    return max(45.0, base_score - fifo_factor)
            
            return 50.0  # 默认FIFO评分
            
        except Exception as e:
            logger.error(f"计算智能FIFO评分失败: {e}")
            return 50.0

    def _get_lot_type_priority_bonus(self, lot_type: str) -> float:
        """获取批次类型优先级奖励"""
        try:
            lot_type = lot_type.strip() if lot_type else ''
            
            # 基于批次类型的优先级设置
            if lot_type == '工程批':
                return 15.0  # 工程批优先级较高
            elif lot_type == '量产批':
                return 10.0  # 量产批正常优先级
            elif 'PILOT' in lot_type.upper():
                return 12.0  # 试产批
            elif 'QUAL' in lot_type.upper():
                return 18.0  # 认证批优先级最高
            
            return 5.0  # 默认奖励
            
        except Exception as e:
            logger.error(f"获取批次类型优先级奖励失败: {e}")
            return 5.0
    
    def calculate_value_efficiency_score(self, lot: Dict, processing_time: float) -> float:
        """计算产值效率评分"""
        try:
            # 获取批次产值信息
            good_qty = float(lot.get('GOOD_QTY', 0) or 0)
            price = float(lot.get('PRICE', 0) or lot.get('UNIT_PRICE', 0) or 0)
            
            if good_qty <= 0 or processing_time <= 0:
                return 50.0  # 无效数据，默认中等评分
            
            # 计算每小时产值
            total_value = good_qty * price
            value_per_hour = total_value / processing_time if processing_time > 0 else 0
            
            # 基准产值率（可配置）
            base_value_rate = 10000.0  # 每小时10000元为基准
            
            # 产值效率评分
            efficiency_ratio = value_per_hour / base_value_rate
            score = min(100.0, efficiency_ratio * 100)
            
            return max(20.0, score)  # 最低20分
            
        except Exception as e:
            logger.error(f"计算产值效率评分失败: {e}")
            return 50.0
    
    def calculate_business_priority_score(self, lot: Dict) -> float:
        """计算业务优先级评分"""
        try:
            device = lot.get('DEVICE', '')
            lot_id = lot.get('LOT_ID', '')
            
            # 1. 查询产品优先级配置
            device_priority = self._get_device_priority(device)
            
            # 2. 查询批次优先级配置  
            lot_priority = self._get_lot_priority(lot_id)
            
            # 3. FIFO评分（基于工单号）
            fifo_score = self._get_fifo_score(lot_id)
            
            # 综合优先级评分
            priority_score = (device_priority * 0.4 + 
                            lot_priority * 0.4 + 
                            fifo_score * 0.2)
            
            return max(20.0, min(100.0, priority_score))
            
        except Exception as e:
            logger.error(f"计算业务优先级评分失败: {e}")
            return 50.0
    
    def _get_device_priority(self, device: str) -> float:
        """获取产品优先级评分 - 使用缓存优化"""
        try:
            # 确保缓存已加载
            self._load_priority_configs()
            
            # 从缓存中查找
            for config in self._device_priority_cache:
                if config.get('DEVICE', '').strip() == device.strip():
                    priority = float(config.get('PRIORITY', 50) or 50)
                    return min(100.0, max(0.0, priority))
            return 50.0
        except Exception:
            return 50.0
    
    def _get_lot_priority(self, lot_id: str) -> float:
        """获取批次优先级评分 - 使用缓存优化"""
        try:
            # 确保缓存已加载
            self._load_priority_configs()
            
            # 从缓存中查找
            for config in self._lot_priority_cache:
                # 这里可以根据批次ID模式匹配
                device_pattern = config.get('DEVICE', '').strip()
                if device_pattern and device_pattern in lot_id:
                    priority = float(config.get('PRIORITY', 50) or 50)
                    return min(100.0, max(0.0, priority))
            return 50.0
        except Exception:
            return 50.0
    
    def _get_fifo_score(self, lot_id: str) -> float:
        """获取FIFO评分（基于工单号）"""
        try:
            # 提取工单号中的数字部分
            import re
            numbers = re.findall(r'\d+', lot_id)
            if numbers:
                # 数字越小，优先级越高
                lot_number = int(numbers[-1])  # 取最后一个数字
                # 简化计算：假设工单号在1-9999范围内
                fifo_score = max(20.0, 100.0 - (lot_number / 100.0))
                return min(100.0, fifo_score)
            return 50.0
        except Exception:
            return 50.0
    
    def check_same_product_continuation(self, lot_requirements: Dict, equipment: Dict) -> bool:
        """检查是否可以同产品续排"""
        try:
            handler_id = equipment.get('HANDLER_ID', '')
            req_device = lot_requirements.get('DEVICE', '')
            eqp_device = equipment.get('DEVICE', '')
            
            # 检查设备是否正在处理相同的产品
            if eqp_device and req_device:
                eqp_base = eqp_device.split('_')[0] if '_' in eqp_device else eqp_device
                req_base = req_device.split('_')[0] if '_' in req_device else req_device
                return eqp_base == req_base
                
            return False
            
        except Exception as e:
            logger.error(f"检查同产品续排失败: {e}")
            return False
    
    def _extract_kit_requirements(self, lot_requirements: Dict) -> Dict:
        """
        从批次需求中提取KIT要求和对应的HANDLER_CONFIG
        基于KIT编码规则分析设备类型需求
        """
        try:
            kit_pn = lot_requirements.get('KIT_PN', '').strip()
            required_handler_config = lot_requirements.get('HANDLER_CONFIG', '').strip()
            stage = lot_requirements.get('STAGE', '').strip()
            
            # 如果直接有HANDLER_CONFIG，优先使用
            if required_handler_config and required_handler_config != 'PnP':
                return {
                    'required_handler_config': required_handler_config,
                    'kit_based_config': None,
                    'equipment_type_hint': self._get_equipment_type_from_config(required_handler_config)
                }
            
            # 基于KIT编码分析设备要求
            kit_config = None
            if kit_pn:
                # 分析KIT后缀确定HANDLER_CONFIG
                if kit_pn.endswith('-TS'):
                    kit_config = 'C6800T_S'
                elif kit_pn.endswith('-HB'):
                    kit_config = 'C6800H_B'
                elif kit_pn.endswith('-TG'):
                    kit_config = 'C6800T_G'
                elif kit_pn.endswith('-TB'):
                    kit_config = 'C6800T_B'
                elif kit_pn.endswith('-T'):
                    kit_config = 'C6800T'
                elif 'CKC-' in kit_pn and not any(kit_pn.endswith(suffix) for suffix in ['-TS', '-HB', '-TG', '-TB', '-T']):
                    kit_config = 'C6800H'  # 默认处理机
            
            # 基于STAGE分析设备类型需求
            stage_upper = stage.upper()
            equipment_type_hint = None
            
            if any(test_stage in stage_upper for test_stage in ['HOT-FT', 'COLD-FT', 'ROOM-TTR-FT', 'ROOM-TEST-FT']):
                equipment_type_hint = '平移式分选机'  # 测试类阶段
            elif 'TRIM-FT' in stage_upper:
                equipment_type_hint = '烧录机'  # 编程类阶段
            elif 'BAKING' in stage_upper:
                equipment_type_hint = '烘箱'  # 烘烤阶段
            elif 'LSTR' in stage_upper:
                equipment_type_hint = '纯编带机'  # 纯编带阶段
            
            return {
                'required_handler_config': required_handler_config if required_handler_config != 'PnP' else None,
                'kit_based_config': kit_config,
                'equipment_type_hint': equipment_type_hint
            }
            
        except Exception as e:
            logger.error(f"提取KIT要求失败: {e}")
            return {
                'required_handler_config': None,
                'kit_based_config': None,
                'equipment_type_hint': None
            }
    
    def _get_equipment_type_from_config(self, handler_config: str) -> str:
        """根据HANDLER_CONFIG推断设备类型"""
        if 'IPS' in handler_config:
            return '烧录机'
        elif 'C6800' in handler_config:
            return '平移式分选机'
        elif 'C9D' in handler_config:
            return '重力式分选机'
        elif any(config in handler_config for config in ['SKD', 'F1850', 'H1618']):
            return '转盘式分选机'
        else:
            return '未知'

    def _is_equipment_type_compatible(self, eqp_type: str, eqp_config: str, req_config: str, req_stage: str) -> bool:
        """
        检查设备类型是否与批次需求兼容
        
        Args:
            eqp_type: 设备类型 (如 '烧录机', '测试机')
            eqp_config: 设备配置 (如 'IPS5800S', 'STS8200')  
            req_config: 需求配置 (如 'PnP', 'STS8200')
            req_stage: 需求阶段 (如 'HOT-FT', 'TRIM-FT', 'BAKING2')
            
        Returns:
            bool: 是否兼容
        """
        try:
            # 1. 基础类型匹配检查
            stage_upper = req_stage.upper()
            
            # 测试类型的阶段需要测试设备 (注意：TRIM-FT是编程阶段，不是测试)
            test_stages = ['HOT-FT', 'COLD-FT', 'ROOM-TTR-FT', 'ROOM-TEST-FT', 'HOT', 'COLD', 'ROOM-TTR', 'ROOM-TEST']
            if any(stage in stage_upper for stage in test_stages):
                # 需要测试机 - 烧录机绝对不能做测试
                if eqp_type == '烧录机' or 'HANK' in eqp_config or 'IPS' in eqp_config:
                    return False  # 烧录机/编程机不能做测试
                # 测试需要测试机配置
                if req_config == 'PnP':
                    # PnP测试需要测试设备，不能用烧录设备
                    if 'IPS' in eqp_config or eqp_type == '烧录机':
                        return False
                # 检查测试机配置
                if 'STS' in req_config and 'STS' not in eqp_config:
                    return False
            
            # 烧录/编程类型的阶段需要烧录设备  
            program_stages = ['TRIM-FT', 'TRIM', 'BAKING2', 'BAKING', 'LSTR', 'PROGRAM', 'BURN', 'WRITE']
            if any(stage in stage_upper for stage in program_stages):
                # 需要烧录机 - 测试机绝对不能做烧录
                if eqp_type == '测试机' or eqp_type == '平移式分选机' or 'HCHC' in eqp_config or 'STS' in eqp_config:
                    return False  # 测试机/分选机不能做烧录
                # 烧录需要烧录机配置  
                if req_config == 'PnP':
                    # PnP烧录需要烧录设备，不能用测试设备
                    if 'STS' in eqp_config or eqp_type == '测试机':
                        return False
                # 检查烧录机配置
                if 'IPS' in req_config and 'IPS' not in eqp_config:
                    return False
            
            # 2. 配置兼容性检查
            if req_config and eqp_config:
                # PnP配置检查
                if req_config == 'PnP':
                    # PnP要求自动化程度高的设备
                    if 'IPS' in eqp_config or 'STS' in eqp_config:
                        return True
                    return False
                
                # 精确配置匹配
                if req_config == eqp_config:
                    return True
                
                # 兼容配置检查 (如IPS5800S兼容IPS5800)
                if req_config in eqp_config or eqp_config in req_config:
                    return True
                    
                # 同系列设备兼容 (如STS8200和STS8250)
                req_base = req_config[:6] if len(req_config) >= 6 else req_config
                eqp_base = eqp_config[:6] if len(eqp_config) >= 6 else eqp_config
                if req_base == eqp_base:
                    return True
            
            # 3. 无配置要求时，认为兼容
            if not req_config:
                return True
                
            return False
            
        except Exception as e:
            logger.error(f"设备类型兼容性检查失败: {e}")
            # 出错时保守返回False，避免错误分配
            return False
    
    def find_suitable_equipment(self, lot: Dict, lot_requirements: Dict) -> List[Dict]:
        """为批次查找合适的设备"""
        try:
            # 获取所有可用设备（获取完整数据，避免分页限制）
            equipment_result = self.data_manager.get_table_data('EQP_STATUS', per_page=1000)
            if not equipment_result.get('success'):
                logger.error("无法获取设备状态数据")
                return []
            
            all_equipment = equipment_result.get('data', [])
            
            # 筛选可用设备（包括空闲设备、运行中设备等）
            # 修复：Run状态设备应该参与排产，通过PRIORITY控制执行顺序
            available_statuses = ['IDLE', 'Run', 'Wait', '0', 'READY', 'ONLINE', 'SetupRun', '']
            available_equipment = [eqp for eqp in all_equipment 
                                 if eqp.get('STATUS', '').strip() in available_statuses]
            
            if not available_equipment:
                logger.warning("没有可用设备")
                return []
            
            # 计算批次预计加工时间
            try:
                good_qty = float(lot.get('GOOD_QTY', 0) or 0)
                uph = float(lot_requirements.get('UPH', 1000) or 1000)
                processing_time = good_qty / max(uph, 1) if good_qty > 0 else 1.0
            except (ValueError, TypeError):
                processing_time = 1.0
            
            equipment_scores = []
            
            for equipment in available_equipment:
                # 1. 技术匹配度评分
                match_score, match_type, changeover_time = self.calculate_equipment_match_score(lot_requirements, equipment)
                
                if match_score == 0:  # 不匹配的设备跳过
                    continue
                
                # 2. 负载均衡评分
                load_score = self.calculate_load_balance_score(equipment, processing_time, changeover_time)
                
                # 3. 交期紧迫度评分
                deadline_score = self.calculate_deadline_urgency_score(lot, processing_time)
                
                # 4. 产值效率评分
                value_score = self.calculate_value_efficiency_score(lot, processing_time)
                
                # 5. 业务优先级评分
                priority_score = self.calculate_business_priority_score(lot)
                
                # 6. 同产品续排加分
                continuation_bonus = 20.0 if self.check_same_product_continuation(lot_requirements, equipment) else 0.0
                
                # 综合评分计算 - 使用动态权重配置
                # 从请求参数中获取策略名称，默认为intelligent
                strategy_name = getattr(self, '_current_strategy', 'intelligent')
                weights = self.get_current_weights(strategy_name=strategy_name)
                comprehensive_score = (
                    match_score * weights['tech_match_weight'] / 100.0 +
                    load_score * weights['load_balance_weight'] / 100.0 +
                    deadline_score * weights['deadline_weight'] / 100.0 +
                    value_score * weights['value_efficiency_weight'] / 100.0 +
                    priority_score * weights['business_priority_weight'] / 100.0 +
                    continuation_bonus
                )
                
                equipment_scores.append({
                    'equipment': equipment,
                    'comprehensive_score': comprehensive_score,
                    'match_score': match_score,
                    'match_type': match_type,
                    'changeover_time': changeover_time,
                    'load_score': load_score,
                    'deadline_score': deadline_score,
                    'value_score': value_score,
                    'priority_score': priority_score,
                    'continuation_bonus': continuation_bonus,
                    'processing_time': processing_time,
                    'selection_reason': f"{match_type}({match_score}分); 负载({load_score:.1f}分); 交期({deadline_score:.1f}分)"
                })
            
            # 按综合评分降序排列
            equipment_scores.sort(key=lambda x: x['comprehensive_score'], reverse=True)
            
            return equipment_scores
            
        except Exception as e:
            logger.error(f"查找合适设备失败: {e}")
            return []
    
    def _generate_estimated_due_date(self, lot: Dict, processing_time: float) -> str:
        """
        为没有交期的批次生成预计完成时间作为DUE_DATE
        """
        try:
            current_time = datetime.now()
            estimated_completion = current_time + timedelta(hours=processing_time)
            return estimated_completion.strftime('%Y-%m-%d %H:%M:%S')
        except Exception:
            # 默认24小时后完成
            estimated_completion = datetime.now() + timedelta(hours=24)
            return estimated_completion.strftime('%Y-%m-%d %H:%M:%S')
    
    def execute_real_scheduling(self, algorithm: str = 'intelligent') -> List[Dict]:
        """执行真实业务逻辑的排产"""
        try:
            start_time = time.time()
            logger.info(f"🚀 开始执行真实排产算法 ({algorithm})...")
            
            # 0. 预加载优先级配置缓存 - 避免重复查询
            self._load_priority_configs()
            
            # 1. 获取待排产批次（确保获取整表数据，避免分页限制）
            wait_lots, wait_source = self.data_manager.get_wait_lot_data()
            logger.info(f"📋 从{wait_source}获取到 {len(wait_lots)} 个待排产批次（整表数据）")
            
            if not wait_lots:
                logger.warning("⚠️ 没有待排产批次")
                return []
            
            schedule_results = []
            
            for idx, lot in enumerate(wait_lots):
                lot_id = lot.get('LOT_ID', f'LOT_{idx+1}')
                logger.debug(f"🔍 处理批次 {lot_id}")
                
                # 2. 获取批次配置需求
                lot_requirements = self.get_lot_configuration_requirements(lot)
                if not lot_requirements:
                    logger.warning(f"⚠️ 批次 {lot_id} 配置需求获取失败，跳过")
                    continue
                
                # 3. 查找合适设备
                equipment_candidates = self.find_suitable_equipment(lot, lot_requirements)
                if not equipment_candidates:
                    logger.warning(f"⚠️ 批次 {lot_id} 未找到合适设备")
                    continue
                
                # 4. 选择最佳设备
                best_candidate = equipment_candidates[0]
                best_equipment = best_candidate['equipment']
                handler_id = best_equipment.get('HANDLER_ID', f'H{(idx % 10) + 1:02d}')
                
                # 5. 更新设备负载
                processing_time = best_candidate['processing_time']
                changeover_time = best_candidate['changeover_time']
                total_time = processing_time + (changeover_time / 60.0)
                
                current_load = self.equipment_workload.get(handler_id, 0.0)
                self.equipment_workload[handler_id] = current_load + total_time
                
                # 6. 生成排产结果
                # 检查是否有交期，没有则生成预计完成时间
                original_due_date = lot.get('DELIVERY_DATE') or lot.get('REQ_DATE') or lot.get('DUE_DATE')
                if not original_due_date:
                    # 没有交期，生成预计完成时间
                    estimated_due_date = self._generate_estimated_due_date(lot, processing_time)
                    priority_status = 'n'  # 默认不紧急
                else:
                    estimated_due_date = original_due_date
                    priority_status = lot.get('PRIORITY', '').strip() or 'n'
                
                schedule_result = {
                    'LOT_ID': lot_id,
                    'DEVICE': lot.get('DEVICE', ''),
                    'STAGE': lot.get('STAGE', ''),
                    'GOOD_QTY': lot.get('GOOD_QTY', 0),
                    'HANDLER_ID': handler_id,
                    'TESTER_ID': best_equipment.get('TESTER_ID', ''),
                    'HB_PN_REQUIRED': lot_requirements.get('HB_PN', ''),
                    'TB_PN_REQUIRED': lot_requirements.get('TB_PN', ''),
                    'KIT_PN_REQUIRED': lot_requirements.get('KIT_PN', ''),
                    'HB_PN_CURRENT': best_equipment.get('HB_PN', ''),
                    'TB_PN_CURRENT': best_equipment.get('TB_PN', ''),
                    'KIT_PN_CURRENT': best_equipment.get('KIT_PN', ''),
                    'DUE_DATE': estimated_due_date,
                    'PRIORITY': priority_status,
                    'COMPREHENSIVE_SCORE': round(best_candidate['comprehensive_score'], 2),
                    'MATCH_TYPE': best_candidate['match_type'],
                    'CHANGEOVER_TIME': changeover_time,
                    'PROCESSING_TIME': round(processing_time, 2),
                    'SELECTION_REASON': best_candidate['selection_reason'],
                    'ALGORITHM': algorithm,
                    'SCHEDULED_TIME': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                }
                
                schedule_results.append(schedule_result)
                
                logger.debug(f"✅ 批次 {lot_id} -> 设备 {handler_id} (评分: {best_candidate['comprehensive_score']:.2f}, {best_candidate['match_type']})")
            
            # 7. 保存排产结果到数据库
            if schedule_results:
                self._save_to_database(schedule_results)
            
            end_time = time.time()
            logger.info(f"🎉 真实排产完成！处理 {len(schedule_results)} 个批次，耗时 {end_time-start_time:.2f} 秒")
            
            return schedule_results
            
        except Exception as e:
            logger.error(f"❌ 执行真实排产失败: {e}")
            import traceback
            traceback.print_exc()
            return []
    
    def _save_to_database(self, schedule_results: List[Dict]) -> None:
        """保存排产结果到lotprioritydone表"""
        try:
            # 导入数据库连接工具
            from app.utils.db_helper import get_mysql_connection
            
            # 连接MySQL数据库
            conn = get_mysql_connection()
            cursor = conn.cursor()
            
            # 清空现有数据
            cursor.execute("DELETE FROM lotprioritydone")
            logger.info("🗑️ 已清空 lotprioritydone 表")
            
            # 插入新数据 (基础传递字段 + 排产结果字段，包含PO_ID)
            insert_query = """
            INSERT INTO lotprioritydone (
                LOT_ID, LOT_TYPE, GOOD_QTY, PROD_ID, DEVICE, CHIP_ID, PKG_PN, 
                PO_ID, STAGE, WIP_STATE, PROC_STATE, HOLD_STATE, FLOW_ID, FLOW_VER,
                FAC_ID, CREATE_TIME, PRIORITY, HANDLER_ID
            ) VALUES (
                %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
            )
            """
            
            # 直接使用SQL获取完整的原始批次数据（避免数据源管理器字段限制）
            wait_conn = get_mysql_connection()
            wait_cursor = wait_conn.cursor()
            
            wait_cursor.execute("""
                SELECT LOT_ID, LOT_TYPE, GOOD_QTY, PROD_ID, DEVICE, CHIP_ID, PKG_PN, 
                       PO_ID, STAGE, WIP_STATE, PROC_STATE, HOLD_STATE, FLOW_ID, FLOW_VER,
                       FAC_ID, CREATE_TIME
                FROM ET_WAIT_LOT 
                WHERE GOOD_QTY > 0 AND LOT_ID IS NOT NULL
            """)
            wait_lots_raw = wait_cursor.fetchall()
            wait_cursor.close()
            wait_conn.close()
            
            # 将原始数据转换为字典格式
            columns = ['LOT_ID', 'LOT_TYPE', 'GOOD_QTY', 'PROD_ID', 'DEVICE', 'CHIP_ID', 'PKG_PN', 
                      'PO_ID', 'STAGE', 'WIP_STATE', 'PROC_STATE', 'HOLD_STATE', 'FLOW_ID', 'FLOW_VER',
                      'FAC_ID', 'CREATE_TIME']
            wait_lots = []
            for row in wait_lots_raw:
                wait_lots.append(dict(zip(columns, row)))
            
            # 创建批次ID到原始数据的映射
            lot_map = {lot.get('LOT_ID'): lot for lot in wait_lots}
            
            # 按设备分组，分配PRIORITY执行顺序
            equipment_groups = {}
            for result in schedule_results:
                handler_id = result.get('HANDLER_ID', '')
                if handler_id not in equipment_groups:
                    equipment_groups[handler_id] = []
                equipment_groups[handler_id].append(result)
            
            # 为每个设备组内的批次分配1,2,3...的PRIORITY序号
            processed_results = []
            for handler_id, group_results in equipment_groups.items():
                # 按综合评分排序（高分优先执行）
                group_results.sort(key=lambda x: x.get('COMPREHENSIVE_SCORE', 0), reverse=True)
                
                for priority_order, result in enumerate(group_results, 1):
                    result['EXECUTION_PRIORITY'] = priority_order  # 设置执行顺序
                    processed_results.append(result)
            
            # 准备数据
            insert_data = []
            for result in processed_results:
                # 查找原始批次信息
                original_lot = lot_map.get(result.get('LOT_ID'))
                
                # 传递完整字段，包含PO_ID
                record = (
                    result.get('LOT_ID', ''),                       # LOT_ID
                    '量产批',                                        # LOT_TYPE (默认值)
                    result.get('GOOD_QTY', 0),                      # GOOD_QTY
                    result.get('DEVICE', ''),                       # PROD_ID (使用DEVICE作为默认值)
                    result.get('DEVICE', ''),                       # DEVICE
                    original_lot.get('CHIP_ID', '') if original_lot else '',   # CHIP_ID
                    original_lot.get('PKG_PN', '') if original_lot else '',    # PKG_PN
                    original_lot.get('PO_ID', '') if original_lot else '',     # PO_ID (从待排产表传递)
                    result.get('STAGE', ''),                        # STAGE
                    original_lot.get('WIP_STATE', '') if original_lot else '', # WIP_STATE
                    original_lot.get('PROC_STATE', '') if original_lot else '', # PROC_STATE
                    original_lot.get('HOLD_STATE', 0) if original_lot else 0,  # HOLD_STATE
                    original_lot.get('FLOW_ID', '') if original_lot else '',   # FLOW_ID
                    original_lot.get('FLOW_VER', '') if original_lot else '',  # FLOW_VER
                    original_lot.get('FAC_ID', '') if original_lot else '',    # FAC_ID
                    datetime.now().strftime('%Y-%m-%d %H:%M:%S'),               # CREATE_TIME (使用当前时间)
                    int(result.get('EXECUTION_PRIORITY', 1)),       # PRIORITY (同设备执行顺序1,2,3...，整数类型)
                    result.get('HANDLER_ID', '')                    # HANDLER_ID (排产算法生成)
                )
                insert_data.append(record)
            
            # 批量插入
            cursor.executemany(insert_query, insert_data)
            conn.commit()
            
            logger.info(f"✅ 成功保存 {len(insert_data)} 条排产记录到 lotprioritydone 表")
            
            cursor.close()
            conn.close()
            
        except Exception as e:
            logger.error(f"❌ 保存排产结果到数据库失败: {e}")
            import traceback
            traceback.print_exc()