#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
初始化Dify设置脚本
将默认的Dify配置添加到系统设置中
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models import SystemSetting
from datetime import datetime

def init_dify_settings():
    """初始化Dify设置"""
    app, _ = create_app()
    
    with app.app_context():
        try:
            # 默认Dify设置
            default_settings = [
                {
                    'key': 'enable_chatbot',
                    'value': 'true',
                    'description': '启用Dify聊天机器人',
                    'category': 'chatbot'
                },
                {
                    'key': 'chatbot_token',
                    'value': 'uV72gGRdNz0eP7ac',
                    'description': 'Dify聊天机器人Token',
                    'category': 'chatbot'
                },
                {
                    'key': 'chatbot_server',
                    'value': 'http://************',
                    'description': 'Dify服务器地址',
                    'category': 'chatbot'
                },
                {
                    'key': 'chatbot_color',
                    'value': '#b72424',
                    'description': '聊天机器人按钮颜色',
                    'category': 'chatbot'
                },
                {
                    'key': 'chatbot_integration_type',
                    'value': 'script',
                    'description': '聊天机器人集成方式',
                    'category': 'chatbot'
                }
            ]
            
            # 检查并添加设置
            for setting_data in default_settings:
                existing_setting = SystemSetting.query.filter_by(key=setting_data['key']).first()
                
                if existing_setting:
                    print(f"设置 {setting_data['key']} 已存在，跳过")
                    continue
                
                # 创建新设置
                new_setting = SystemSetting(
                    key=setting_data['key'],
                    value=setting_data['value'],
                    description=setting_data['description'],
                    setting_type=setting_data['category'],  # 使用setting_type字段
                    updated_at=datetime.now()
                )
                
                db.session.add(new_setting)
                print(f"✅ 添加设置: {setting_data['key']} = {setting_data['value']}")
            
            # 提交更改
            db.session.commit()
            print("\n🎉 Dify设置初始化完成！")
            
            # 显示当前所有chatbot相关设置
            print("\n当前Dify相关设置:")
            chatbot_settings = SystemSetting.query.filter_by(setting_type='chatbot').all()
            for setting in chatbot_settings:
                print(f"  - {setting.key}: {setting.value}")
                
        except Exception as e:
            db.session.rollback()
            print(f"❌ 初始化Dify设置失败: {e}")
            return False
    
    return True

if __name__ == "__main__":
    print("🚀 开始初始化Dify设置...")
    
    if init_dify_settings():
        print("\n✅ 初始化完成！")
        print("\n接下来您可以:")
        print("1. 访问 http://localhost:5000/system/settings 配置Dify设置")
        print("2. 在AI助手中点击'数据库查询'按钮测试Dify集成")
        print("3. 确保Dify服务器 (http://************) 正常运行")
    else:
        print("\n❌ 初始化失败，请检查错误信息")
        sys.exit(1) 