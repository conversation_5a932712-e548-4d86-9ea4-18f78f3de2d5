# 迭代开发指南

## 核心理念

本指南为项目开发人员提供一套标准的、可重复的迭代开发操作流程。遵循本指南有助于保持代码库的整洁、减少错误，并确保团队协作的顺畅。开发过程的核心是 **理解 -> 规划 -> 执行 -> 反思**。

## 第一步：任务开始前 - 理解

1.  **熟悉项目**：在开始任何编码之前，必须首先浏览项目根目录下的 `README.md` 和 `docs/` 目录下的相关文档。
2.  **理解目标**：深刻理解项目的总体目标、当前架构和已实现的功能。
3.  **创建/更新文档**：如果 `README.md` 不存在或信息陈旧，你的第一个任务就是创建或更新它。`README.md` 是我们项目对外的"说明书"，也是对内的"共识记录"，必须保持最新。

## 第二步：任务执行中 - 规划与编码

### A. 处理新需求

1.  **需求分析**：参考《产品需求PRD规则.md》，彻底理解用户需求。
2.  **规划实现**：
    *   思考满足需求的最佳路径，优先选择复用现有代码和简单方案。
    *   遵循SOLID原则和设计模式，规划代码结构。
3.  **编码实现**：
    *   遵循《前端设计规则.md》和《后端设计规则.md》。
    *   为关键逻辑添加必要的注释和日志监控。

### B. 解决Bug

1.  **阅读上下文**：完整阅读出现Bug的相关代码文件，理解其功能和逻辑。
2.  **提出假设**：思考导致Bug的根本原因，并提出初步的解决方案。
3.  **迭代修复**：实施解决方案并测试。如果失败，总结经验，调整方案，再次尝试。
4.  **启动"系统二"**：如果一个Bug经过两次尝试仍未解决，立即停止并启动深度思考模式：
    *   系统性地列出所有可能的原因假设。
    *   为每个假设设计验证方法。
    *   提供三种不同的解决方案，并附上优缺点分析，交由团队或用户决策。

### C. 文件编辑策略

为了优化效率和减少错误，请根据文件大小和复杂度选择不同的编辑策略：

*   **小文件 (< 200行)**：直接使用 `edit_file` 工具进行修改。
*   **中文件 (200-400行)**：使用 `search_replace` 工具分段编辑，每次只修改一个逻辑块。
*   **大文件 (> 400行)**：
    1.  使用 `PowerShell` 命令 `New-Item -Path "path/to/new_file.ext" -ItemType File` 创建一个空文件。
    2.  使用 `search_replace` 工具分块、分功能地将代码内容添加到新文件中。
*   **复杂文件 (如模板)**：按功能模块顺序处理，例如：先处理 `CSS` 部分，然后是 `HTML` 结构，最后是 `JavaScript` 逻辑。

## 第三步：任务完成后 - 反思

1.  **代码审查**：在提交代码前，自我审查代码是否遵循所有开发规则。
2.  **反思改进**：思考本次任务中是否存在可以改进的流程或代码结构。
3.  **更新文档**：将任何新的发现、改进点或重要的架构决策更新到 `README.md` 中。 