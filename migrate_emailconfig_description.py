#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
EmailConfig表添加description字段的数据库迁移脚本
"""

import pymysql
import sys

def migrate_emailconfig_table():
    try:
        # 连接到数据库
        connection = pymysql.connect(
            host='localhost',
            user='root',
            password='WWWwww123!',
            database='aps_system',
            charset='utf8mb4'
        )
        
        with connection.cursor() as cursor:
            # 检查description字段是否已存在
            cursor.execute("""
                SELECT COUNT(*) as count
                FROM information_schema.COLUMNS 
                WHERE TABLE_SCHEMA = 'aps_system' 
                AND TABLE_NAME = 'email_configs' 
                AND COLUMN_NAME = 'description'
            """)
            
            result = cursor.fetchone()
            if result[0] > 0:
                print("✅ description字段已存在")
                return True
            
            # 添加description字段
            cursor.execute("""
                ALTER TABLE email_configs 
                ADD COLUMN description TEXT NULL COMMENT '配置描述信息' 
                AFTER fetch_days
            """)
            
            connection.commit()
            print("✅ 成功添加description字段到email_configs表")
            return True
            
    except Exception as e:
        print(f"❌ 数据库迁移失败: {str(e)}")
        return False
    finally:
        if 'connection' in locals():
            connection.close()

if __name__ == "__main__":
    migrate_emailconfig_table() 