#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WIP批次字段配置和API路由修复脚本
解决字段映射和路由不存在问题

"""

import os
import sys
import json
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('wip_lot_config_fix.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class WIPLotConfigFixer:
    """WIP批次配置修复器"""
    
    def update_field_config(self):
        """更新字段配置文件"""
        try:
            config_file = "instance/field_config.json"
            
            # 读取现有配置
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            # 更新wip_lot表配置
            wip_lot_config = {
                "display_name": "WIP批次管理",
                "database": "aps",  # 确保连接到aps主业务数据库
                "primary_key": "id",
                "business_key": "LOT_ID",
                "fields": [
                    "id", "LOT_ID", "LOT_TYPE", "DET_LOT_TYPE", "LOT_QTY", "SUB_QTY",
                    "UNIT", "SUB_UNIT", "WIP_STATE", "PROC_STATE", "HOLD_STATE", 
                    "RW_STATE", "REPAIR_STATE", "QC_STATE", "PROD_ID", "DEVICE", 
                    "CHIP_ID", "PKG_PN", "STAGE", "PROC_RULE_ID", "PRP_ID", "FLOW_ID",
                    "PRP_VER", "FLOW_VER", "OPER_VER", "EQP_ID", "SUB_EQP_ID", 
                    "PORT_ID", "AREA_ID", "LOC_ID", "CARR_ID", "MAIN_EQP_ID",
                    "AUXILIARY_EQP_ID", "RESV_EQP_ID", "LOT_IN_QTY", "LOT_OUT_QTY",
                    "GOOD_QTY", "NG_QTY", "ACT_QTY", "ORT_QTY", "IQC_QTY", "UPH",
                    "ORT_SAMP_QTY", "IQC_SAMP_QTY", "STRM_QTY", "STRM_SAMP_QTY",
                    "ROOT_LOT_ID", "PARENT_LOT_ID", "CHILD_LOT_ID", "CUST_LOT_ID",
                    "MERGE_LOT_ID", "WORK_ORDER_ID", "WORK_ORDER_VER", "PO_ID",
                    "BOM_ID", "BOM_VER", "FAC_ID", "SUB_FAC", "LOT_OWNER",
                    "OPER_CHANGE_TIME", "JOB_START_TIME", "JOB_END_TIME", 
                    "PLAN_START_DATE", "PLAN_DUE_DATE", "RELEASE_TIME", "SHIP_TIME",
                    "CREATE_TIME", "HOT_TYPE", "PRIORITY_LEVEL", "PRIORITY_ORDER",
                    "PILOT_TYPE", "RECIPE_ID", "SUB_RECIPE_ID", "TEST_SPEC_ID",
                    "TEST_SPEC_NAME", "TEST_SPEC_VER", "RETEST_YN", "RETEST_FLOW_ID",
                    "DUT_ID", "PACK_SPEC_ID", "PACK_SPEC_VER", "CONTAINER_ID",
                    "WAREHOUSE_CONTAINER_ID", "TRACK_CARD_ID", "MARK_ID", "GRADE",
                    "REASON_GRP", "REASON_CODE", "LOT_JUDGE", "FULL_INSP_QC",
                    "USE_SUB_LOT", "STR_FLAG", "OA_FLAG", "SEAL_FLAG", "SPLIT_TYPE",
                    "HALF_LOT_HOLD", "RELEASE_HOLD_TYPE", "DATA_CONFIRM_HOLD_YN",
                    "EVENT", "EVENT_KEY", "EVENT_TIME", "EVENT_USER", "EVENT_MSG",
                    "CREATE_USER", "created_at", "updated_at"
                ],
                "datetime_fields": [
                    "PLAN_START_DATE", "PLAN_DUE_DATE", "created_at", "updated_at"
                ],
                "field_count": 90,
                "auto_discovered": True,
                "last_discovery": datetime.now().isoformat(),
                "hidden_fields": [],
                "readonly_fields": ["id", "CREATE_TIME"],
                "required_fields": ["LOT_ID"],
                "description": "在制品批次管理表，包含完整业务字段，连接到aps主业务数据库"
            }
            
            # 更新配置
            config["tables"]["wip_lot"] = wip_lot_config
            
            # 保存配置
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            
            logger.info("✅ 成功更新字段配置文件")
            return True
            
        except Exception as e:
            logger.error(f"❌ 更新字段配置失败: {e}")
            return False
    
    def fix_menu_route(self):
        """修复菜单配置中的路由"""
        try:
            menu_file = "app/config/menu_config.py"
            
            # 读取现有内容
            with open(menu_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 替换路由
            old_route = "'route': '/api/v3/universal/wip_lot'"
            new_route = "'route': '/wip/by-batch'"
            
            if old_route in content:
                content = content.replace(old_route, new_route)
                
                # 保存修改
                with open(menu_file, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                logger.info("✅ 成功修复菜单路由配置")
            else:
                logger.info("ℹ️ 菜单路由配置未找到或已修复")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 修复菜单路由失败: {e}")
            return False
    
    def update_wip_batch_template(self):
        """更新WIP批次页面模板，确保正确显示字段"""
        try:
            template_file = "app/templates/wip/by_batch.html"
            
            if not os.path.exists(template_file):
                logger.warning(f"模板文件不存在: {template_file}")
                return False
            
            # 读取现有模板
            with open(template_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 确保使用正确的API端点和字段映射
            js_section = '''
// WIP批次数据API配置
const WIP_API_CONFIG = {
    baseUrl: '/api/v2/tables/wip_lot',
    database: 'aps',  // 确保连接到aps主业务数据库
    displayFields: [
        'LOT_ID', 'DEVICE', 'STAGE', 'WIP_STATE', 'PROC_STATE', 
        'LOT_QTY', 'GOOD_QTY', 'NG_QTY', 'PRIORITY_LEVEL', 
        'PLAN_DUE_DATE', 'created_at'
    ],
    searchFields: ['LOT_ID', 'DEVICE', 'STAGE', 'WIP_STATE']
};

// 加载WIP批次数据
function loadWipData() {
    showLoading(true);
    
    fetch(`${WIP_API_CONFIG.baseUrl}/data?database=${WIP_API_CONFIG.database}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                renderWipTable(data.data);
                updateRecordCount(data.data.length);
            } else {
                showError('加载WIP批次数据失败: ' + (data.error || '未知错误'));
            }
        })
        .catch(error => {
            showError('网络错误: ' + error.message);
        })
        .finally(() => {
            showLoading(false);
        });
}

// 渲染WIP表格
function renderWipTable(data) {
    const tableBody = document.getElementById('tableBody');
    const tableHeaders = document.getElementById('tableHeaders');
    
    if (!data || data.length === 0) {
        tableBody.innerHTML = '<tr><td colspan="20" class="text-center">暂无数据</td></tr>';
        return;
    }
    
    // 渲染表头
    tableHeaders.innerHTML = WIP_API_CONFIG.displayFields.map(field => 
        `<th>${getFieldDisplayName(field)}</th>`
    ).join('');
    
    // 渲染数据行
    tableBody.innerHTML = data.map(record => 
        `<tr>
            ${WIP_API_CONFIG.displayFields.map(field => 
                `<td>${formatFieldValue(record[field], field)}</td>`
            ).join('')}
        </tr>`
    ).join('');
}

// 获取字段显示名称
function getFieldDisplayName(field) {
    const fieldNames = {
        'LOT_ID': '批次号',
        'DEVICE': '产品型号', 
        'STAGE': '工序',
        'WIP_STATE': 'WIP状态',
        'PROC_STATE': '工艺状态',
        'LOT_QTY': '批次数量',
        'GOOD_QTY': '良品数量',
        'NG_QTY': '不良数量',
        'PRIORITY_LEVEL': '优先级',
        'PLAN_DUE_DATE': '计划交期',
        'created_at': '创建时间'
    };
    return fieldNames[field] || field;
}

// 格式化字段值
function formatFieldValue(value, field) {
    if (value === null || value === undefined) {
        return '-';
    }
    
    if (['PLAN_DUE_DATE', 'created_at'].includes(field)) {
        return new Date(value).toLocaleString('zh-CN');
    }
    
    return value;
}

// 显示加载状态
function showLoading(show) {
    const overlay = document.getElementById('loadingOverlay');
    if (overlay) {
        overlay.style.display = show ? 'flex' : 'none';
    }
}

// 显示错误信息
function showError(message) {
    console.error(message);
    // 可以添加更好的错误显示UI
    alert('错误: ' + message);
}

// 更新记录数量
function updateRecordCount(count) {
    const countElement = document.getElementById('recordCount');
    if (countElement) {
        countElement.textContent = `${count} 条记录`;
    }
}

// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    loadWipData();
    
    // 添加刷新按钮事件
    const refreshBtn = document.getElementById('refreshBtn');
    if (refreshBtn) {
        refreshBtn.addEventListener('click', loadWipData);
    }
});
'''
            
            # 查找并替换JavaScript部分
            script_start = content.find('<script>')
            script_end = content.find('</script>', script_start)
            
            if script_start != -1 and script_end != -1:
                # 替换JavaScript内容
                content = content[:script_start + 8] + js_section + content[script_end:]
            else:
                # 在页面末尾添加JavaScript
                content = content.replace('{% endblock %}', js_section + '\n</script>\n{% endblock %}')
            
            # 保存修改后的模板
            with open(template_file, 'w', encoding='utf-8') as f:
                f.write(content)
            
            logger.info("✅ 成功更新WIP批次页面模板")
            return True
            
        except Exception as e:
            logger.error(f"❌ 更新页面模板失败: {e}")
            return False
    
    def run_fix(self):
        """运行配置修复流程"""
        logger.info("🚀 开始WIP批次配置修复流程...")
        
        try:
            # 1. 更新字段配置
            logger.info("⚙️ 步骤1: 更新字段配置...")
            if not self.update_field_config():
                return False
            
            # 2. 修复菜单路由
            logger.info("🔗 步骤2: 修复菜单路由...")
            if not self.fix_menu_route():
                return False
            
            # 3. 更新页面模板
            logger.info("📄 步骤3: 更新页面模板...")
            if not self.update_wip_batch_template():
                logger.warning("⚠️ 更新页面模板失败，但不影响主要功能")
            
            logger.info("🎉 WIP批次配置修复完成！")
            logger.info("✅ 主要修复内容:")
            logger.info("   - 更新字段配置，确保连接到aps主业务数据库")
            logger.info("   - 修复菜单路由配置，指向正确的页面")
            logger.info("   - 更新页面模板，正确显示业务字段")
            logger.info("")
            logger.info("🔄 下一步操作:")
            logger.info("   1. 重启Flask应用")
            logger.info("   2. 访问: http://localhost:5000/wip/by-batch")
            logger.info("   3. 检查WIP批次数据显示是否正常")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 配置修复流程失败: {e}")
            return False

def main():
    """主函数"""
    print("🔧 WIP批次配置修复工具")
    print("=" * 60)
    
    try:
        fixer = WIPLotConfigFixer()
        success = fixer.run_fix()
        
        if success:
            print("\n✅ 配置修复完成！请重启应用测试。")
            return 0
        else:
            print("\n❌ 配置修复失败！请查看日志了解详细错误信息。")
            return 1
            
    except KeyboardInterrupt:
        print("\n⏸️ 用户取消操作")
        return 1
    except Exception as e:
        print(f"\n💥 严重错误: {e}")
        return 1

if __name__ == '__main__':
    exit(main()) 