from flask import Blueprint, jsonify, request
from flask_login import login_required, current_user
from app import db
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

# 创建优先级配置API蓝图
priority_bp = Blueprint('priority_api_v2', __name__, url_prefix='/api/v2/production/priority')

@priority_bp.route('/health')
def health_check():
    """优先级配置模块健康检查"""
    return jsonify({
        'status': 'ok',
        'service': 'priority_api_v2',
        'timestamp': datetime.now().isoformat()
    })

@priority_bp.route('/product', methods=['GET'])
@login_required
def get_product_priority():
    """获取产品优先级配置"""
    try:
        # 这里可以添加具体的产品优先级逻辑
        return jsonify({
            'success': True,
            'data': [],
            'message': '产品优先级配置获取成功'
        })
    except Exception as e:
        logger.error(f"获取产品优先级配置失败: {str(e)}")
        return jsonify({'error': '获取产品优先级配置失败'}), 500

@priority_bp.route('/product', methods=['POST'])
@login_required
def create_product_priority():
    """创建产品优先级配置"""
    try:
        data = request.get_json()
        
        # 这里可以添加具体的创建逻辑
        return jsonify({
            'success': True,
            'message': '产品优先级配置创建成功'
        }), 201
        
    except Exception as e:
        logger.error(f"创建产品优先级配置失败: {str(e)}")
        return jsonify({'error': '创建产品优先级配置失败'}), 500

@priority_bp.route('/lot', methods=['GET'])
@login_required
def get_lot_priority():
    """获取批次优先级配置"""
    try:
        return jsonify({
            'success': True,
            'data': [],
            'message': '批次优先级配置获取成功'
        })
    except Exception as e:
        logger.error(f"获取批次优先级配置失败: {str(e)}")
        return jsonify({'error': '获取批次优先级配置失败'}), 500

@priority_bp.route('/device', methods=['GET'])
@login_required
def get_device_priority():
    """获取设备优先级配置"""
    try:
        return jsonify({
            'success': True,
            'data': [],
            'message': '设备优先级配置获取成功'
        })
    except Exception as e:
        logger.error(f"获取设备优先级配置失败: {str(e)}")
        return jsonify({'error': '获取设备优先级配置失败'}), 500

@priority_bp.route('/match-batches', methods=['POST'])
@login_required
def match_batches():
    """批量匹配待排产批次到优先级配置 - API v2版本"""
    try:
        data = request.get_json() or {}
        
        # 获取参数
        strategy = data.get('strategy', 'intelligent')
        confidence_threshold = data.get('confidence_threshold', 0.5)
        batch_filter = data.get('batch_filter', {})
        apply_results = data.get('apply_results', False)
        
        logger.info(f"API v2批次匹配: strategy={strategy}, threshold={confidence_threshold}")
        
        # 验证策略
        valid_strategies = ['exact', 'fuzzy', 'intelligent']
        if strategy not in valid_strategies:
            return jsonify({
                'success': False,
                'error': f'无效的匹配策略: {strategy}，支持的策略: {valid_strategies}'
            }), 400
        
        # 验证置信度阈值
        if not 0 <= confidence_threshold <= 1:
            return jsonify({
                'success': False,
                'error': '置信度阈值必须在0-1之间'
            }), 400
        
        # 模拟匹配结果（实际项目中这里应该调用真实的匹配服务）
        mock_results = [
            {
                'batch_id': 'BATCH001',
                'config_id': 1,
                'confidence_score': 0.85,
                'match_strategy': strategy,
                'match_details': '基于产品类型的精确匹配',
                'priority_level': 'HIGH',
                'priority_order': 1,
                'suggested_actions': ['立即排产'],
                'device': 'DEVICE_A',
                'stage': 'FT',
                'quantity': 100,
                'pkg_pn': 'PKG001',
                'chip_id': 'CHIP001'
            },
            {
                'batch_id': 'BATCH002',
                'config_id': 2,
                'confidence_score': 0.72,
                'match_strategy': strategy,
                'match_details': '基于设备类型的模糊匹配',
                'priority_level': 'MEDIUM',
                'priority_order': 2,
                'suggested_actions': ['等待高优先级完成'],
                'device': 'DEVICE_B',
                'stage': 'FT',
                'quantity': 200,
                'pkg_pn': 'PKG002',
                'chip_id': 'CHIP002'
            }
        ]
        
        # 过滤置信度低于阈值的结果
        filtered_results = [r for r in mock_results if r['confidence_score'] >= confidence_threshold]
        
        # 生成匹配报告
        total_batches = len(mock_results)
        matched_batches = len(filtered_results)
        match_rate = (matched_batches / total_batches * 100) if total_batches > 0 else 0
        
        match_report = {
            'total_batches': total_batches,
            'matched_batches': matched_batches,
            'match_rate': round(match_rate, 2),
            'confidence_threshold': confidence_threshold,
            'strategy_used': strategy
        }
        
        # 如果需要直接应用结果
        applied_count = 0
        if apply_results:
            applied_count = len(filtered_results)
        
        response_data = {
            'success': True,
            'message': f'匹配完成，共处理 {total_batches} 个批次，匹配 {matched_batches} 个',
            'match_results': filtered_results,
            'match_report': match_report,
            'applied_count': applied_count if apply_results else 0,
            'execution_time': datetime.now().isoformat()
        }
        
        logger.info(f"API v2批次匹配完成: 总数={total_batches}, 匹配率={match_rate}%")
        
        return jsonify(response_data)
        
    except Exception as e:
        logger.error(f"API v2批次匹配失败: {e}", exc_info=True)
        return jsonify({
            'success': False,
            'error': f'匹配失败: {str(e)}'
        }), 500

@priority_bp.route('/apply-matches', methods=['POST'])
@login_required
def apply_matches():
    """应用匹配结果 - API v2版本"""
    try:
        data = request.get_json() or {}
        
        # 获取参数
        match_results = data.get('match_results', [])
        confidence_threshold = data.get('confidence_threshold', 0.5)
        force_apply = data.get('force_apply', False)
        
        if not match_results:
            return jsonify({
                'success': False,
                'error': '没有提供匹配结果'
            }), 400
        
        logger.info(f"API v2应用匹配结果: {len(match_results)} 个结果")
        
        # 过滤符合条件的结果
        applicable_results = []
        for result in match_results:
            confidence = result.get('confidence_score', 0)
            if confidence >= confidence_threshold or force_apply:
                applicable_results.append(result)
        
        # 模拟应用过程
        applied_count = 0
        errors = []
        
        for result in applicable_results:
            try:
                batch_id = result.get('batch_id')
                config_id = result.get('config_id')
                priority_level = result.get('priority_level')
                
                # 这里应该是实际的数据库更新操作
                # 模拟成功应用
                applied_count += 1
                logger.info(f"应用匹配结果: {batch_id} -> 配置{config_id}, 优先级{priority_level}")
                
            except Exception as e:
                error_msg = f"应用批次 {result.get('batch_id')} 失败: {str(e)}"
                errors.append(error_msg)
                logger.error(error_msg)
        
        response_data = {
            'success': True,
            'message': f'成功应用 {applied_count} 个匹配结果',
            'applied_count': applied_count,
            'total_results': len(match_results),
            'applicable_results': len(applicable_results),
            'errors': errors,
            'applied_at': datetime.now().isoformat()
        }
        
        if errors:
            response_data['warning'] = f'部分应用失败: {len(errors)} 个错误'
        
        return jsonify(response_data)
        
    except Exception as e:
        logger.error(f"API v2应用匹配结果失败: {e}", exc_info=True)
        return jsonify({
            'success': False,
            'error': f'应用失败: {str(e)}'
        }), 500

@priority_bp.route('/matching-report', methods=['GET'])
@login_required
def get_matching_report():
    """获取匹配结果报告 - API v2版本"""
    try:
        # 获取查询参数
        strategy = request.args.get('strategy', 'intelligent')
        confidence_threshold = float(request.args.get('confidence_threshold', 0.5))
        include_details = request.args.get('include_details', 'false').lower() == 'true'
        
        # 模拟报告数据
        report = {
            'total_batches': 150,
            'matched_batches': 120,
            'match_rate': 80.0,
            'confidence_threshold': confidence_threshold,
            'strategy_used': strategy,
            'high_priority_matches': 45,
            'medium_priority_matches': 50,
            'low_priority_matches': 25,
            'generated_at': datetime.now().isoformat()
        }
        
        # 如果需要详细信息
        if include_details:
            report['match_details'] = [
                {
                    'batch_id': f'BATCH{i:03d}',
                    'matched': i <= 120,
                    'confidence_score': round(0.6 + (i % 40) * 0.01, 3),
                    'priority_level': ['HIGH', 'MEDIUM', 'LOW'][i % 3]
                }
                for i in range(1, 151)
            ]
        
        return jsonify({
            'success': True,
            'report': report
        })
        
    except Exception as e:
        logger.error(f"API v2获取匹配报告失败: {e}", exc_info=True)
        return jsonify({
            'success': False,
            'error': f'获取报告失败: {str(e)}'
        }), 500

@priority_bp.route('/batch-info/<batch_id>', methods=['GET'])
@login_required
def get_batch_info(batch_id):
    """获取批次详细信息 - API v2版本"""
    try:
        # 模拟批次信息
        batch_info = {
            'batch_id': batch_id,
            'device': 'DEVICE_A',
            'stage': 'FT',
            'quantity': 100,
            'pkg_pn': 'PKG001',
            'chip_id': 'CHIP001',
            'status': 'WAITING',
            'created_at': '2025-06-16T10:00:00',
            'priority_config': {
                'config_id': 1,
                'priority_level': 'HIGH',
                'priority_order': 1
            }
        }
        
        return jsonify({
            'success': True,
            'batch_info': batch_info
        })
        
    except Exception as e:
        logger.error(f"API v2获取批次信息失败: {e}", exc_info=True)
        return jsonify({
            'success': False,
            'error': f'获取批次信息失败: {str(e)}'
        }), 500 