#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
订单Excel解析服务
负责解析宜欣生产订单Excel文件并按照Lot Type分类汇总
"""

import os
import json
import logging
import pandas as pd
import datetime
from pathlib import Path
from typing import List, Dict, Any, Tuple, Optional
import shutil
import re

logger = logging.getLogger(__name__)

class OrderExcelParser:
    """宜欣订单Excel解析器
    
    专门用于解析downloads目录下的宜欣生产订单Excel文件，
    根据Lot Type字段自动分类为工程类型或量产类型，
    并汇总到对应的汇总表文件中。
    """
    
    def __init__(self, downloads_dir: str = "downloads", search_subdirs: bool = True):
        """初始化解析器
        
        Args:
            downloads_dir: 源文件目录路径，默认为downloads
            search_subdirs: 是否搜索子目录，默认为True
        """
        self.downloads_dir = downloads_dir
        self.search_subdirs = search_subdirs
        self.email_attachments_dir = os.path.join(downloads_dir, "email_attachments")
        
        # 分类配置 - 支持基于规则的分类
        self.classification_rules = {}  # Lot Type -> 分类 的映射规则
        
        # 备用的关键词匹配（当没有规则时使用）
        self.production_keywords = ['量产-P', '量产批', '小批量-PE', 
                                  'PROD', 'Production', '量產']
        
        # 汇总表文件
        self.engineering_summary_file = os.path.join(downloads_dir, "FT工程订单汇总表.xlsx")
        self.production_summary_file = os.path.join(downloads_dir, "FT量产订单汇总表.xlsx")
        
        # 宜欣订单识别关键词
        self.yixin_keywords = ['宜欣', '生产订单', '宜欣生产订单']
    
    def scan_order_files(self) -> Dict[str, Any]:
        """扫描可处理的Excel文件
        
        Returns:
            dict: 扫描结果，包含文件列表和分类信息
        """
        try:
            result = {
                'status': 'success',
                'yixin_order_files': [],
                'summary_files': {},
                'total_yixin_files': 0,
                'search_paths': [],
                'message': ''
            }
            
            # 确定搜索路径
            search_paths = []
            if os.path.exists(self.downloads_dir):
                search_paths.append(self.downloads_dir)
            if os.path.exists(self.email_attachments_dir):
                search_paths.append(self.email_attachments_dir)
            
            # 如果启用子目录搜索，添加子目录
            if self.search_subdirs:
                for root_path in [self.downloads_dir]:
                    if os.path.exists(root_path):
                        for root, dirs, files in os.walk(root_path):
                            if root not in search_paths:
                                search_paths.append(root)
            
            result['search_paths'] = search_paths
            
            # 扫描所有路径中的宜欣订单文件
            for search_path in search_paths:
                try:
                    if os.path.exists(search_path):
                        all_files = os.listdir(search_path)
                        for file in all_files:
                            # 检查是否为宜欣订单文件
                            if any(keyword in file for keyword in self.yixin_keywords) and file.endswith('.xls'):
                                file_path = os.path.join(search_path, file)
                                # 避免重复添加同名文件
                                if not any(existing['name'] == file for existing in result['yixin_order_files']):
                                    file_info = {
                                        'name': file,
                                        'path': file_path,
                                        'directory': search_path,
                                        'size': os.path.getsize(file_path) if os.path.exists(file_path) else 0,
                                        'modified': datetime.datetime.fromtimestamp(os.path.getmtime(file_path)).strftime('%Y-%m-%d %H:%M:%S') if os.path.exists(file_path) else ''
                                    }
                                    result['yixin_order_files'].append(file_info)
                except Exception as e:
                    logger.warning(f"扫描路径失败 {search_path}: {e}")
                    continue
            
            result['total_yixin_files'] = len(result['yixin_order_files'])
            
            # 检查汇总表文件
            for file_type, file_path in [
                ('engineering', self.engineering_summary_file),
                ('production', self.production_summary_file)
            ]:
                if os.path.exists(file_path):
                    df = pd.read_excel(file_path)
                    result['summary_files'][file_type] = {
                        'exists': True,
                        'path': file_path,
                        'rows': len(df),
                        'columns': len(df.columns),
                        'last_modified': datetime.datetime.fromtimestamp(os.path.getmtime(file_path)).strftime('%Y-%m-%d %H:%M:%S')
                    }
                else:
                    result['summary_files'][file_type] = {
                        'exists': False,
                        'path': file_path
                    }
            
            result['message'] = f"找到 {result['total_yixin_files']} 个宜欣订单文件"
            return result
            
        except Exception as e:
            logger.error(f"扫描文件失败: {str(e)}")
            return {
                'status': 'error',
                'message': f"扫描文件失败: {str(e)}",
                'yixin_order_files': [],
                'summary_files': {},
                'total_yixin_files': 0
            }
    
    def parse_order_file(self, file_path: str) -> Dict[str, Any]:
        """解析单个宜欣订单文件
        
        Args:
            file_path: Excel文件路径
            
        Returns:
            dict: 解析结果
        """
        try:
            # 读取Excel文件（无表头模式）
            df = pd.read_excel(file_path, engine='xlrd', header=None)
            
            # 根据分析结果，表头在第13行（索引12），数据从第15行（索引14）开始
            if df.shape[0] < 15:
                return {
                    'status': 'error',
                    'message': f'文件格式不正确，行数不足: {df.shape[0]}',
                    'data': []
                }
            
            # 获取表头（第13行，索引12）
            headers = df.iloc[12].values
            
            # 清理表头，处理nan值
            clean_headers = []
            for i, header in enumerate(headers):
                if pd.isna(header):
                    clean_headers.append(f'Column_{i}')  # 为nan列名分配默认名称
                else:
                    clean_headers.append(str(header))
            
            # 构建数据DataFrame，从第15行（索引14）开始
            data_df = df.iloc[14:].copy()
            data_df.columns = clean_headers
            data_df = data_df.reset_index(drop=True)
            
            # 过滤掉完全空白的行
            data_df = data_df.dropna(how='all')
            
            # 提取有效数据
            parsed_data = []
            for _, row in data_df.iterrows():
                # 安全获取关键字段值
                order_no = None
                lot_type = None
                
                # 安全获取关键字段值
                order_no = None
                lot_type = None
                
                # 使用最安全的方式获取数据
                if '订单号' in data_df.columns:
                    try:
                        order_no = row['订单号']
                        if pd.isna(order_no):
                            order_no = None
                        else:
                            order_no = str(order_no).strip()
                            if order_no in ['', 'nan', 'NaN']:
                                order_no = None
                    except Exception:
                        order_no = None
                
                if 'Lot Type' in data_df.columns:
                    try:
                        lot_type = row['Lot Type']
                        if pd.isna(lot_type):
                            lot_type = None
                        else:
                            lot_type = str(lot_type).strip()
                            if lot_type in ['', 'nan', 'NaN']:
                                lot_type = None
                    except Exception:
                        lot_type = None
                
                # 检查关键字段是否有效
                order_no_valid = order_no is not None and order_no != ''
                lot_type_valid = lot_type is not None and lot_type != ''
                
                if order_no_valid and lot_type_valid:
                    record = {}
                    for col in clean_headers:
                        if col and str(col) != 'nan':  # 确保列名有效
                            try:
                                if col in data_df.columns:
                                    value = row[col]
                                else:
                                    value = ''
                                
                                # 确保值可以JSON序列化
                                if pd.isna(value):
                                    record[str(col)] = ''
                                elif isinstance(value, (pd.Series, pd.DataFrame)):
                                    record[str(col)] = str(value)
                                else:
                                    record[str(col)] = str(value) if value is not None else ''
                            except Exception:
                                record[str(col)] = ''
                    
                    # 添加解析元数据
                    record['源文件'] = os.path.basename(file_path)
                    record['导入时间'] = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    
                    # 分类Lot Type
                    lot_type_str = str(lot_type).strip() if lot_type is not None else ''
                    record['分类结果'] = self._classify_lot_type(lot_type_str)
                    
                    parsed_data.append(record)
            
            return {
                'status': 'success',
                'message': f'成功解析 {len(parsed_data)} 条数据',
                'data': parsed_data,
                'file_name': os.path.basename(file_path)
            }
            
        except Exception as e:
            logger.error(f"解析文件失败 {file_path}: {str(e)}")
            return {
                'status': 'error',
                'message': f'解析文件失败: {str(e)}',
                'data': [],
                'file_name': os.path.basename(file_path) if file_path else ''
            }
    
    def _classify_lot_type(self, lot_type: str) -> str:
        """根据Lot Type分类
        
        分类规则：
        1. 优先使用自定义分类规则（精确匹配）
        2. 如果没有规则，使用关键词匹配
        3. 默认归为工程
        
        Args:
            lot_type: Lot Type字段值
            
        Returns:
            str: 分类结果 ('工程', '量产')
        """
        if not lot_type:
            lot_type = '<空值>'
        
        lot_type = lot_type.strip()
        
        # 优先使用自定义分类规则（精确匹配）
        if self.classification_rules and lot_type in self.classification_rules:
            return self.classification_rules[lot_type]
        
        # 处理空值的特殊情况
        if lot_type == '<空值>' and '<空值>' in self.classification_rules:
            return self.classification_rules['<空值>']
        
        # 如果没有自定义规则，使用关键词匹配（向后兼容）
        if not self.classification_rules:
            lot_type_lower = lot_type.lower()
            for keyword in self.production_keywords:
                if keyword.lower() in lot_type_lower:
                    return '量产'
        
        # 默认归为工程
        return '工程'
    
    def batch_parse_files(self, file_paths: List[str] = None) -> Dict[str, Any]:
        """批量解析文件
        
        Args:
            file_paths: 文件路径列表，为None时自动扫描所有宜欣订单文件
            
        Returns:
            dict: 批量处理结果
        """
        try:
            if file_paths is None:
                # 自动扫描文件
                scan_result = self.scan_order_files()
                if scan_result['status'] != 'success':
                    return scan_result
                
                file_paths = [file_info['path'] for file_info in scan_result['yixin_order_files']]
            
            results = {
                'status': 'success',
                'processed_files': 0,
                'failed_files': 0,
                'total_records': 0,
                'engineering_records': 0,
                'production_records': 0,
                'unknown_records': 0,
                'file_results': [],
                'engineering_data': [],
                'production_data': [],
                'unknown_data': []
            }
            
            for file_path in file_paths:
                parse_result = self.parse_order_file(file_path)
                
                file_result = {
                    'file_name': os.path.basename(file_path),
                    'status': parse_result['status'],
                    'message': parse_result['message'],
                    'records': len(parse_result['data']) if parse_result['status'] == 'success' else 0
                }
                
                if parse_result['status'] == 'success':
                    results['processed_files'] += 1
                    results['total_records'] += len(parse_result['data'])
                    
                    # 按分类结果分组
                    for record in parse_result['data']:
                        classification = record.get('分类结果', '未知')
                        if classification == '工程':
                            results['engineering_data'].append(record)
                            results['engineering_records'] += 1
                        elif classification == '量产':
                            results['production_data'].append(record)
                            results['production_records'] += 1
                        else:
                            results['unknown_data'].append(record)
                            results['unknown_records'] += 1
                else:
                    results['failed_files'] += 1
                
                results['file_results'].append(file_result)
            
            results['message'] = f"处理完成：成功 {results['processed_files']} 个文件，失败 {results['failed_files']} 个文件，共 {results['total_records']} 条记录"
            
            return results
            
        except Exception as e:
            logger.error(f"批量解析失败: {str(e)}")
            return {
                'status': 'error',
                'message': f"批量解析失败: {str(e)}",
                'processed_files': 0,
                'failed_files': 0,
                'total_records': 0,
                'file_results': []
            }
    
    def append_to_summary_files(self, parse_results: Dict[str, Any]) -> Dict[str, Any]:
        """将解析结果追加到汇总表文件
        
        Args:
            parse_results: 批量解析的结果
            
        Returns:
            dict: 追加操作结果
        """
        try:
            append_results = {
                'status': 'success',
                'engineering_appended': 0,
                'production_appended': 0,
                'messages': []
            }
            
            # 追加工程数据
            if parse_results.get('engineering_data'):
                engineering_result = self._append_to_file(
                    parse_results['engineering_data'], 
                    self.engineering_summary_file,
                    '工程'
                )
                append_results['engineering_appended'] = engineering_result['appended']
                append_results['messages'].append(engineering_result['message'])
            
            # 追加量产数据
            if parse_results.get('production_data'):
                production_result = self._append_to_file(
                    parse_results['production_data'], 
                    self.production_summary_file,
                    '量产'
                )
                append_results['production_appended'] = production_result['appended']
                append_results['messages'].append(production_result['message'])
            
            append_results['message'] = '; '.join(append_results['messages'])
            
            return append_results
            
        except Exception as e:
            logger.error(f"追加数据到汇总表失败: {str(e)}")
            return {
                'status': 'error',
                'message': f"追加数据到汇总表失败: {str(e)}",
                'engineering_appended': 0,
                'production_appended': 0
            }
    
    def _append_to_file(self, data: List[Dict], file_path: str, data_type: str) -> Dict[str, Any]:
        """追加数据到指定汇总表文件
        
        Args:
            data: 要追加的数据列表
            file_path: 汇总表文件路径
            data_type: 数据类型（工程/量产）
            
        Returns:
            dict: 追加操作结果
        """
        try:
            if not data:
                return {
                    'appended': 0,
                    'message': f"{data_type}数据为空，跳过追加"
                }
            
            # 转换为DataFrame
            new_df = pd.DataFrame(data)
            
            if os.path.exists(file_path):
                # 读取现有数据
                existing_df = pd.read_excel(file_path)
                
                # 确保列结构一致
                for col in existing_df.columns:
                    if col not in new_df.columns:
                        new_df[col] = ''
                
                for col in new_df.columns:
                    if col not in existing_df.columns:
                        existing_df[col] = ''
                
                # 重新排序列
                columns_order = existing_df.columns.tolist()
                for col in new_df.columns:
                    if col not in columns_order:
                        columns_order.append(col)
                
                new_df = new_df[columns_order]
                existing_df = existing_df[columns_order]
                
                # 合并数据
                combined_df = pd.concat([existing_df, new_df], ignore_index=True)
            else:
                combined_df = new_df
            
            # 保存到文件
            combined_df.to_excel(file_path, index=False)
            
            return {
                'appended': len(data),
                'message': f"成功向{data_type}汇总表追加 {len(data)} 条数据"
            }
            
        except Exception as e:
            logger.error(f"追加数据到 {file_path} 失败: {str(e)}")
            return {
                'appended': 0,
                'message': f"追加数据到{data_type}汇总表失败: {str(e)}"
            }
    
    def auto_parse_and_classify(self) -> Dict[str, Any]:
        """自动扫描、解析和分类的完整流程
        
        Returns:
            dict: 完整处理结果
        """
        try:
            # 第一步：扫描文件
            scan_result = self.scan_order_files()
            if scan_result['status'] != 'success':
                return scan_result
            
            if scan_result['total_yixin_files'] == 0:
                return {
                    'status': 'success',
                    'message': '未找到需要处理的宜欣订单文件',
                    'scan_result': scan_result,
                    'parse_result': None,
                    'append_result': None
                }
            
            # 第二步：批量解析
            parse_result = self.batch_parse_files()
            if parse_result['status'] != 'success':
                return {
                    'status': 'error',
                    'message': f"解析失败: {parse_result['message']}",
                    'scan_result': scan_result,
                    'parse_result': parse_result,
                    'append_result': None
                }
            
            # 第三步：追加到汇总表
            append_result = self.append_to_summary_files(parse_result)
            
            return {
                'status': 'success',
                'message': f"自动解析分类完成：{parse_result['message']}；{append_result['message']}",
                'scan_result': scan_result,
                'parse_result': parse_result,
                'append_result': append_result
            }
            
        except Exception as e:
            logger.error(f"自动解析分类失败: {str(e)}")
            return {
                'status': 'error',
                'message': f"自动解析分类失败: {str(e)}",
                'scan_result': None,
                'parse_result': None,
                'append_result': None
            }

    def get_file_preview(self, file_path: str, max_rows: int = 10) -> Dict[str, Any]:
        """
        获取Excel文件预览信息
        
        Args:
            file_path: 文件路径
            max_rows: 最大预览行数
            
        Returns:
            文件预览信息
        """
        try:
            excel_data = pd.read_excel(file_path, sheet_name=None, engine='openpyxl')
            
            preview_data = {
                'filename': os.path.basename(file_path),
                'file_size': os.path.getsize(file_path),
                'sheet_count': len(excel_data),
                'sheets': {}
            }
            
            for sheet_name, df in excel_data.items():
                if df.empty:
                    continue
                
                # 获取前几行数据预览
                preview_rows = df.head(max_rows).to_dict('records')
                
                preview_data['sheets'][sheet_name] = {
                    'columns': df.columns.tolist(),
                    'row_count': len(df),
                    'preview_rows': preview_rows,
                    'lot_type_column': self._find_lot_type_column(df.columns)
                }
            
            return preview_data
            
        except Exception as e:
            logger.error(f"获取文件预览失败: {e}")
            raise 