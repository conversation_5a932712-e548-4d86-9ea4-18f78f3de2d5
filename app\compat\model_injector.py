# -*- coding: utf-8 -*-
"""
模型注入器

在运行时动态替换传统模型为兼容代理，实现透明的模型切换。
支持功能开关控制，确保零风险的渐进式迁移。

设计模式：依赖注入 (Dependency Injection)
- 在运行时动态替换模型依赖
- 支持功能开关控制
- 保持完整的向后兼容性

Author: AI Assistant
Date: 2025-01-14
"""

import sys
import logging
from typing import Dict, Any, Optional
from flask import current_app

from .legacy_model_proxy import (
    ET_WAIT_LOT_Proxy,
    WIP_LOT_Proxy,
    LOT_WIP_Proxy,
    Test_Spec_Proxy
)

logger = logging.getLogger(__name__)


class ModelInjector:
    """模型注入器 - 动态替换传统模型为兼容代理"""
    
    def __init__(self):
        self.original_models = {}
        self.proxy_models = {
            'v_et_wait_lot_unified': ET_WAIT_LOT_Proxy,
            'v_wip_lot_unified': WIP_LOT_Proxy,
            'LOT_WIP': LOT_WIP_Proxy,
            'Test_Spec': Test_Spec_Proxy
        }
        self.injection_active = False
        self.injected_modules = set()
    
    def is_unified_models_enabled(self) -> bool:
        """检查是否启用统一模型"""
        try:
            return current_app.config.get('ENABLE_UNIFIED_MODELS', False)
        except RuntimeError:
            # 在应用上下文外，返回False
            return False
    
    def backup_original_models(self):
        """备份原始模型"""
        try:
            from app import models
            
            for model_name in self.proxy_models.keys():
                if hasattr(models, model_name):
                    self.original_models[model_name] = getattr(models, model_name)
                    logger.debug(f"备份原始模型: {model_name}")
        except ImportError as e:
            logger.warning(f"备份原始模型失败: {e}")
    
    def inject_proxy_models(self):
        """注入代理模型"""
        if not self.is_unified_models_enabled():
            logger.debug("统一模型未启用，跳过代理注入")
            return False
        
        if self.injection_active:
            logger.debug("代理模型已注入，跳过重复注入")
            return True
        
        try:
            # 备份原始模型
            self.backup_original_models()
            
            # 注入代理模型到app.models模块
            from app import models
            
            for model_name, proxy_class in self.proxy_models.items():
                if hasattr(models, model_name):
                    # 创建代理实例
                    proxy_instance = proxy_class()
                    
                    # 替换模型
                    setattr(models, model_name, proxy_instance)
                    logger.info(f"注入代理模型: {model_name} -> {proxy_class.__name__}")
            
            # 注入到其他可能导入模型的模块
            self._inject_to_modules()
            
            self.injection_active = True
            logger.info("代理模型注入完成")
            return True
            
        except Exception as e:
            logger.error(f"代理模型注入失败: {e}")
            self.restore_original_models()
            return False
    
    def _inject_to_modules(self):
        """注入到其他模块"""
        # 需要注入的模块列表
        target_modules = [
            'app.api.routes',
            'app.api.production_api',
            'app.api.priority_matching_api',
            'app.api.resources',
            'app.services.priority_matching_service',
        ]
        
        for module_name in target_modules:
            try:
                if module_name in sys.modules:
                    module = sys.modules[module_name]
                    
                    for model_name, proxy_class in self.proxy_models.items():
                        if hasattr(module, model_name):
                            proxy_instance = proxy_class()
                            setattr(module, model_name, proxy_instance)
                            logger.debug(f"注入到模块 {module_name}: {model_name}")
                    
                    self.injected_modules.add(module_name)
                    
            except Exception as e:
                logger.warning(f"注入到模块 {module_name} 失败: {e}")
    
    def restore_original_models(self):
        """恢复原始模型"""
        if not self.injection_active:
            logger.debug("代理模型未注入，无需恢复")
            return
        
        try:
            # 恢复app.models模块
            from app import models
            
            for model_name, original_model in self.original_models.items():
                if hasattr(models, model_name):
                    setattr(models, model_name, original_model)
                    logger.debug(f"恢复原始模型: {model_name}")
            
            # 恢复其他模块
            self._restore_modules()
            
            self.injection_active = False
            self.injected_modules.clear()
            logger.info("原始模型恢复完成")
            
        except Exception as e:
            logger.error(f"原始模型恢复失败: {e}")
    
    def _restore_modules(self):
        """恢复其他模块"""
        for module_name in self.injected_modules:
            try:
                if module_name in sys.modules:
                    module = sys.modules[module_name]
                    
                    for model_name, original_model in self.original_models.items():
                        if hasattr(module, model_name):
                            setattr(module, model_name, original_model)
                            logger.debug(f"恢复模块 {module_name}: {model_name}")
                            
            except Exception as e:
                logger.warning(f"恢复模块 {module_name} 失败: {e}")
    
    def get_injection_status(self) -> Dict[str, Any]:
        """获取注入状态"""
        return {
            'injection_active': self.injection_active,
            'unified_models_enabled': self.is_unified_models_enabled(),
            'injected_modules': list(self.injected_modules),
            'proxy_models': list(self.proxy_models.keys()),
            'original_models_backed_up': len(self.original_models) > 0
        }
    
    def toggle_injection(self, enable: bool = None) -> bool:
        """切换注入状态"""
        if not self.is_unified_models_enabled():
            logger.warning("统一模型未启用，无法切换注入状态")
            return False
        
        if enable is None:
            enable = not self.injection_active
        
        if enable:
            return self.inject_proxy_models()
        else:
            self.restore_original_models()
            return True


# 创建全局模型注入器实例
model_injector = ModelInjector()


def init_model_injection(app):
    """初始化模型注入"""
    with app.app_context():
        if app.config.get('ENABLE_UNIFIED_MODELS', False):
            logger.info("启用统一模型，开始注入代理模型")
            success = model_injector.inject_proxy_models()
            if success:
                logger.info("模型注入成功")
            else:
                logger.error("模型注入失败，使用传统模型")
        else:
            logger.info("统一模型未启用，使用传统模型")


def get_model_injection_status():
    """获取模型注入状态"""
    return model_injector.get_injection_status()


def toggle_model_injection(enable: bool = None):
    """切换模型注入状态"""
    return model_injector.toggle_injection(enable) 