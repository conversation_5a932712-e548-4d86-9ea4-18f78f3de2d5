# API迁移报告

生成时间: 2025-06-27 12:39:38

## 统计摘要

- 旧API端点总数: 113
- 新API端点总数: 141
- 已完成迁移端点: 9
- 待迁移端点数: 104
- 迁移进度: 7.96%

## 已完成迁移的端点

| 旧端点 | 新端点 | 状态 |
| --- | --- | --- |
| /api/production/save-order | /api/v2/orders/create | ❌ 新接口未实现 |
| /api/production/save-priority-done | /api/v2/production/priority/done | ❌ 新接口未实现 |
| /api/orders/parse-excel | /api/v2/orders/excel/parse | ❌ 新接口未实现 |
| /api/production/history-data | /api/v2/production/history/data | ❌ 新接口未实现 |
| /api/production/batch/upload | /api/v2/production/lots/upload | ❌ 新接口未实现 |
| /api/test-database-connection | /api/v2/system/database/test | ❌ 新接口未实现 |
| /api/production/imported-files | /api/v2/production/files/imported | ❌ 新接口未实现 |
| /api/production/file-data | /api/v2/production/files/data | ❌ 新接口未实现 |
| /api/production/export-file | /api/v2/production/files/export | ❌ 新接口未实现 |

## 待迁移端点

| 端点 | HTTP方法 | 功能 | 所在文件 |
| --- | --- | --- | --- |
| /ai/chat | POST | unknown | app\api\ai_assistant.py |
| /ai/db_chat | POST | unknown | app\api\ai_database_assistant.py |
| /auth/users | GET | unknown | app\api\auth.py |
| /auth/users | GET | unknown | app\api\auth.py |
| /auth/users/<username> | GET | unknown | app\api\auth.py |
| /auth/users/<username> | GET | unknown | app\api\auth.py |
| /auth/users/<username> | GET | unknown | app\api\auth.py |
| /embed.min.js | POST | get_embed_script | app\api\dify_proxy.py |
| /v1/chat-messages | POST | get_embed_script | app\api\dify_proxy.py |
| /v1/conversations | POST | get_embed_script | app\api\dify_proxy.py |
| /v1/conversations/<conversation_id>/messages | POST | get_embed_script | app\api\dify_proxy.py |
| /config | POST | get_embed_script | app\api\dify_proxy.py |
| /test-connection | POST | get_embed_script | app\api\dify_proxy.py |
| /api/email_configs | GET | unknown | app\api\email_attachment.py |
| /api/email_configs/<int:config_id> | GET | unknown | app\api\email_attachment.py |
| /api/email_configs | GET | unknown | app\api\email_attachment.py |
| /api/email_configs/<int:config_id> | GET | unknown | app\api\email_attachment.py |
| /api/email_configs/<int:config_id> | GET | unknown | app\api\email_attachment.py |
| /api/email_configs/<int:config_id>/test | GET | unknown | app\api\email_attachment.py |
| /api/email_configs/test | GET | unknown | app\api\email_attachment.py |
| /api/email_configs/<int:config_id>/preview | GET | unknown | app\api\email_attachment.py |
| /api/email_configs/<int:config_id>/fetch | GET | unknown | app\api\email_attachment.py |
| /api/email_attachments | GET | unknown | app\api\email_attachment.py |
| /api/email_attachments/<int:attachment_id> | GET | unknown | app\api\email_attachment.py |
| /api/email_attachments/<int:attachment_id>/process | GET | unknown | app\api\email_attachment.py |
| /api/order_data | GET | unknown | app\api\email_attachment.py |
| /api/order_data/stats | GET | unknown | app\api\email_attachment.py |
| /api/order_data/<int:order_id> | GET | unknown | app\api\email_attachment.py |
| /api/order_data/<int:order_id> | GET | unknown | app\api\email_attachment.py |
| /api/order_data/export | GET | unknown | app\api\email_attachment.py |
| /api/email_attachments/<int:attachment_id>/download | GET | unknown | app\api\email_attachment.py |
| /api/email_attachments/batch_process | GET | unknown | app\api\email_attachment.py |
| /api/order_data/download_summary | GET | unknown | app\api\email_attachment.py |
| /api/excel_mappings | GET | unknown | app\api\email_attachment.py |
| /api/excel_mappings/<int:mapping_id> | GET | unknown | app\api\email_attachment.py |
| /api/excel_mappings/<int:mapping_id> | GET | unknown | app\api\email_attachment.py |
| /api/excel_mappings/<int:mapping_id> | GET | unknown | app\api\email_attachment.py |
| /api/email_attachments/dashboard | GET | unknown | app\api\email_attachment.py |
| /api/order_data/start | POST | unknown | app\api\order_processing_api.py |
| /api/order_data/<task_id>/pause | POST | unknown | app\api\order_processing_api.py |
| /api/order_data/<task_id>/stop | POST | unknown | app\api\order_processing_api.py |
| /api/order_data/<task_id>/status | POST | unknown | app\api\order_processing_api.py |
| /api/order_data/stats | POST | unknown | app\api\order_processing_api.py |
| /production/schedules/<int:schedule_id> | PUT | get_history_data | app\api\routes.py |
| /production/schedules/<int:schedule_id> | PUT | get_history_data | app\api\routes.py |
| /production/batch/upload | PUT | get_history_data | app\api\routes.py |
| /production/save-order | PUT | get_history_data | app\api\routes.py |
| /production/save-priority-done | PUT | get_history_data | app\api\routes.py |
| /production/import-from-directory | PUT | get_history_data | app\api\routes.py |
| /test-database-connection | PUT | get_history_data | app\api\routes.py |
| /database-status | PUT | get_history_data | app\api\routes.py |
| /production/history-data | PUT | get_history_data | app\api\routes.py |
| /test-all-database-connections | PUT | get_history_data | app\api\routes.py |
| /production/auto-schedule/stop | PUT | get_history_data | app\api\routes.py |
| /production/save-import-path | PUT | get_history_data | app\api\routes.py |
| /production/get-import-path | PUT | get_history_data | app\api\routes.py |
| /production/imported-files | PUT | get_history_data | app\api\routes.py |
| /production/file-data/<filename> | PUT | get_history_data | app\api\routes.py |
| /production/export-file/<filename> | PUT | get_history_data | app\api\routes.py |
| /production/import-progress | PUT | get_history_data | app\api\routes.py |
| /production/delete-file | PUT | get_history_data | app\api\routes.py |
| /check-mysql-databases | PUT | get_history_data | app\api\routes.py |
| /user-filter-presets | PUT | get_history_data | app\api\routes.py |
| /user-filter-presets/<int:preset_id> | PUT | get_history_data | app\api\routes.py |
| /production/auto-schedule | PUT | get_history_data | app\api\routes.py |
| /production/manual-schedule | PUT | get_history_data | app\api\routes.py |
| /production/save-schedule-history | PUT | get_history_data | app\api\routes.py |
| /production/schedule-history | PUT | get_history_data | app\api\routes.py |
| /production/export-schedule | PUT | get_history_data | app\api\routes.py |
| /api/production/history-times | PUT | get_history_data | app\api\routes.py |
| /orders/parse-excel | PUT | get_history_data | app\api\routes.py |
| /orders/scan-files | PUT | get_history_data | app\api\routes.py |
| /orders/preview-file | PUT | get_history_data | app\api\routes.py |
| /orders/scan-lot-types | PUT | get_history_data | app\api\routes.py |
| /orders/classification-rules | PUT | get_history_data | app\api\routes.py |
| /orders/parse-progress/<task_id> | PUT | get_history_data | app\api\routes.py |
| /tables | GET | get_supported_tables | app\api\routes_v3.py |
| /tables/<table_name>/columns | GET | get_supported_tables | app\api\routes_v3.py |
| /tables/<table_name>/info | GET | get_supported_tables | app\api\routes_v3.py |
| /tables/<table_name>/structure | GET | get_supported_tables | app\api\routes_v3.py |
| /tables/<table_name>/data | GET | get_supported_tables | app\api\routes_v3.py |
| /tables/<table_name>/data | GET | get_supported_tables | app\api\routes_v3.py |
| /tables/<table_name>/data/<record_id> | GET | get_supported_tables | app\api\routes_v3.py |
| /tables/<table_name>/data/<record_id> | GET | get_supported_tables | app\api\routes_v3.py |
| /tables/<table_name>/data/batch | GET | get_supported_tables | app\api\routes_v3.py |
| /tables/<table_name>/validate | GET | get_supported_tables | app\api\routes_v3.py |
| /config/tables/<table_name> | GET | get_supported_tables | app\api\routes_v3.py |
| /cache/clear | GET | get_supported_tables | app\api\routes_v3.py |
| /migration/status | GET | get_supported_tables | app\api\routes_v3.py |
| /migration/test-page | GET | get_supported_tables | app\api\routes_v3.py |
| /page/<table_name> | GET | get_supported_tables | app\api\routes_v3.py |
| /navigation | GET | get_supported_tables | app\api\routes_v3.py |
| /universal/<table_name> | GET | get_supported_tables | app\api\routes_v3.py |
| /tables/<table_name>/export | GET | get_supported_tables | app\api\routes_v3.py |
| /status | GET | unknown | app\api\scheduler_api.py |
| /jobs | GET | unknown | app\api\scheduler_api.py |
| /jobs | GET | unknown | app\api\scheduler_api.py |
| /test | GET | unknown | app\api\scheduler_api.py |
| /jobs/<job_id> | GET | unknown | app\api\scheduler_api.py |
| /jobs/<job_id>/run | GET | unknown | app\api\scheduler_api.py |
| /logs | GET | unknown | app\api\scheduler_api.py |
| /start | GET | unknown | app\api\scheduler_api.py |
| /stop | GET | unknown | app\api\scheduler_api.py |
| /wait-lots | GET | get_wait_lots | app\api\unified_production_api.py |
| /wip-lots | GET | get_wait_lots | app\api\unified_production_api.py |
| /lots/<lot_id> | GET | get_wait_lots | app\api\unified_production_api.py |
| /lots/<lot_id>/priority | GET | get_wait_lots | app\api\unified_production_api.py |
| /lots/<lot_id>/equipment | GET | get_wait_lots | app\api\unified_production_api.py |
| /scheduling | GET | get_wait_lots | app\api\unified_production_api.py |
| /test-specs | GET | get_wait_lots | app\api\unified_production_api.py |
| /test-specs/<spec_id> | GET | get_wait_lots | app\api\unified_production_api.py |
| /statistics | GET | get_wait_lots | app\api\unified_production_api.py |
| /health | GET | get_wait_lots | app\api\unified_production_api.py |
