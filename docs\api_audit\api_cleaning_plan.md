# APS系统 API 清理计划

## 现状问题

- 系统同时存在多个API版本：`/api`、`/api/v2`、`/api/v3`
- 旧API分散在多个大文件中，如`routes.py`(2370行)和`routes_v3.py`(668行)
- 接口职责不清晰，存在大量功能重复或近似的接口
- 缺少统一的文档和规范，难以维护

## 清理目标

1. **统一规范**：采用模块化的`api_v2`架构作为标准
2. **消除冗余**：删除已被替代的旧接口
3. **保持兼容**：针对关键业务保留兼容接口
4. **可追踪**：确保每个旧接口都有明确的迁移路径或被明确标记为废弃

## 接口分类

### 待迁移接口
这些接口应当从旧版API迁移到新版API结构中：

- [ ] `/api/production/schedules/*` → `/api/v2/production/schedules/*`
- [ ] `/api/production/batch/upload` → `/api/v2/production/lots/upload`
- [ ] `/api/test-database-connection` → `/api/v2/system/database/test`

### 待替换接口
这些接口的功能与新接口重复，应标记为废弃并引导用户使用新接口：

- [ ] `/api/production/save-order` → `/api/v2/orders/create`
- [ ] `/api/production/save-priority-done` → `/api/v2/production/priority/done`
- [ ] `/api/orders/parse-excel` → `/api/v2/orders/excel/parse`

### 待保留接口
这些接口是核心业务流程的一部分，暂时保留但应逐步迁移：

- [ ] `/api/production/auto-schedule`（排产主接口）
- [ ] `/api/production/manual-schedule`（手动排产）
- [ ] `/api/production/history-data`（历史数据）

## 实施步骤

1. **分析阶段**
   - 生成所有API端点的完整列表
   - 为每个端点标注使用状态和迁移路径

2. **接口拆分**
   - 拆分`routes.py`中的路由到模块化结构
   - 迁移`routes_v3.py`中的通用功能到资源路由

3. **废弃处理**
   - 为废弃接口添加警告响应
   - 添加重定向到新接口

4. **前端适配**
   - 更新前端代码，使用新的API端点
   - 处理API响应结构变化

5. **文档更新**
   - 生成新的API文档
   - 提供迁移指南

## 兼容性保证

为确保系统在迁移期间正常运行，将采取以下措施：

1. 关键路径接口保持双重支持
2. 使用日志跟踪旧接口的使用情况
3. 分批次更新，先从非关键功能开始

## 时间规划

- 第1周：完成接口分析和规划
- 第2-3周：处理非关键功能接口
- 第4-5周：处理核心业务接口
- 第6周：前端适配和全面测试
- 第7周：文档更新和维护指南 