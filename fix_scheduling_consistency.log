2025-06-27 14:19:41,293 - INFO - 🚀 开始排产数据一致性修复...
2025-06-27 14:19:41,351 - INFO - 🔍 开始分析排产数据不一致问题...
2025-06-27 14:19:41,354 - ERROR - ❌ 分析数据失败: (pymysql.err.OperationalError) (1045, "Access denied for user 'root'@'localhost' (using password: YES)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-27 14:19:41,355 - INFO - 
🔧 开始修复数据结构问题...
2025-06-27 14:19:41,356 - ERROR - ❌ 修复数据结构失败: (pymysql.err.OperationalError) (1045, "Access denied for user 'root'@'localhost' (using password: YES)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-27 14:19:41,357 - INFO - 
🔗 更新API字段映射...
2025-06-27 14:19:41,357 - INFO -   📝 更新API查询字段...
2025-06-27 14:19:41,358 - INFO -   ✅ API文件已更新，备份保存为: app/api_v2/production/done_lots_api.py.backup_20250627_141941
2025-06-27 14:19:41,359 - INFO - 
📊 生成数据一致性报告...
2025-06-27 14:19:41,360 - ERROR - ❌ 生成报告失败: (pymysql.err.OperationalError) (1045, "Access denied for user 'root'@'localhost' (using password: YES)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-27 14:19:41,360 - INFO - 
✅ 排产数据一致性修复完成！
2025-06-27 14:19:41,360 - INFO - 
🔧 修复建议:
2025-06-27 14:19:41,360 - INFO - 1. 重启应用服务以应用API更改
2025-06-27 14:19:41,360 - INFO - 2. 刷新前端页面缓存
2025-06-27 14:19:41,361 - INFO - 3. 验证排产结果和已排产批次管理页面的数据一致性
2025-06-27 14:19:41,361 - INFO - 4. 检查comprehensive_score字段是否正确显示
2025-06-27 14:21:11,129 - INFO - 🚀 开始排产数据一致性修复...
2025-06-27 14:21:11,179 - INFO - 🔍 开始分析排产数据不一致问题...
2025-06-27 14:21:11,184 - INFO - 
📋 检查已排产表结构:
2025-06-27 14:21:11,186 - INFO -   • id: int (NOT NULL)
2025-06-27 14:21:11,187 - INFO -   • PRIORITY: varchar(50) (NULL)
2025-06-27 14:21:11,187 - INFO -   • HANDLER_ID: varchar(100) (NULL)
2025-06-27 14:21:11,187 - INFO -   • LOT_ID: varchar(100) (NULL)
2025-06-27 14:21:11,187 - INFO -   • LOT_TYPE: varchar(50) (NULL)
2025-06-27 14:21:11,187 - INFO -   • GOOD_QTY: int (NULL)
2025-06-27 14:21:11,188 - INFO -   • PROD_ID: varchar(100) (NULL)
2025-06-27 14:21:11,188 - INFO -   • DEVICE: varchar(100) (NULL)
2025-06-27 14:21:11,189 - INFO -   • CHIP_ID: varchar(100) (NULL)
2025-06-27 14:21:11,189 - INFO -   • PKG_PN: varchar(100) (NULL)
2025-06-27 14:21:11,189 - INFO -   • PO_ID: varchar(100) (NULL)
2025-06-27 14:21:11,189 - INFO -   • STAGE: varchar(50) (NULL)
2025-06-27 14:21:11,189 - INFO -   • WIP_STATE: varchar(50) (NULL)
2025-06-27 14:21:11,189 - INFO -   • PROC_STATE: varchar(50) (NULL)
2025-06-27 14:21:11,189 - INFO -   • HOLD_STATE: varchar(50) (NULL)
2025-06-27 14:21:11,189 - INFO -   • FLOW_ID: varchar(100) (NULL)
2025-06-27 14:21:11,189 - INFO -   • FLOW_VER: varchar(50) (NULL)
2025-06-27 14:21:11,190 - INFO -   • RELEASE_TIME: datetime (NULL)
2025-06-27 14:21:11,190 - INFO -   • FAC_ID: varchar(50) (NULL)
2025-06-27 14:21:11,190 - INFO -   • CREATE_TIME: datetime (NULL)
2025-06-27 14:21:11,190 - INFO -   • created_at: datetime (NULL)
2025-06-27 14:21:11,190 - INFO -   • updated_at: datetime (NULL)
2025-06-27 14:21:11,190 - INFO -   • match_type: varchar(50) (NULL)
2025-06-27 14:21:11,190 - INFO -   • comprehensive_score: decimal(5,2) (NULL)
2025-06-27 14:21:11,191 - INFO -   • processing_time: decimal(10,2) (NULL)
2025-06-27 14:21:11,191 - INFO -   • changeover_time: decimal(10,2) (NULL)
2025-06-27 14:21:11,191 - INFO -   • algorithm_version: varchar(50) (NULL)
2025-06-27 14:21:11,191 - INFO - 
📊 检查数据完整性:
2025-06-27 14:21:11,193 - INFO -   • 总记录数: 121
2025-06-27 14:21:11,193 - INFO -   • 有PRIORITY的记录: 121 (100.0%)
2025-06-27 14:21:11,193 - INFO -   • 有LOT_ID的记录: 121 (100.0%)
2025-06-27 14:21:11,193 - INFO -   • 有HANDLER_ID的记录: 121 (100.0%)
2025-06-27 14:21:11,194 - INFO -   • 有DEVICE的记录: 121 (100.0%)
2025-06-27 14:21:11,194 - INFO -   • 有GOOD_QTY的记录: 121 (100.0%)
2025-06-27 14:21:11,194 - INFO -   • 有PO_ID的记录: 0 (0.0%)
2025-06-27 14:21:11,194 - INFO -   • 有RELEASE_TIME的记录: 0 (0.0%)
2025-06-27 14:21:11,194 - INFO - 
🔢 检查PRIORITY字段数据:
2025-06-27 14:21:11,196 - INFO -   • PRIORITY=1, COUNT=1, HANDLER_ID=ABAK-O-001-3004
2025-06-27 14:21:11,196 - INFO -   • PRIORITY=1, COUNT=1, HANDLER_ID=HANK-C-001-5800
2025-06-27 14:21:11,196 - INFO -   • PRIORITY=1, COUNT=1, HANDLER_ID=HCHC-C-012-6800
2025-06-27 14:21:11,196 - INFO -   • PRIORITY=1, COUNT=1, HANDLER_ID=HCHC-C-013-6800
2025-06-27 14:21:11,197 - INFO -   • PRIORITY=1, COUNT=1, HANDLER_ID=HCHC-C-015-6800
2025-06-27 14:21:11,197 - INFO -   • PRIORITY=1, COUNT=1, HANDLER_ID=HCHC-C-020-6800
2025-06-27 14:21:11,197 - INFO -   • PRIORITY=1, COUNT=1, HANDLER_ID=HCHC-C-021-6800
2025-06-27 14:21:11,197 - INFO -   • PRIORITY=1, COUNT=1, HANDLER_ID=HCHC-C-024-6800
2025-06-27 14:21:11,198 - INFO -   • PRIORITY=1, COUNT=1, HANDLER_ID=HCHC-C-025-6800
2025-06-27 14:21:11,198 - INFO -   • PRIORITY=1, COUNT=1, HANDLER_ID=HCHC-C-026-6800
2025-06-27 14:21:11,198 - INFO -   • PRIORITY=1, COUNT=1, HANDLER_ID=HCHC-C-027-6800
2025-06-27 14:21:11,198 - INFO -   • PRIORITY=1, COUNT=1, HANDLER_ID=HCHC-C-028-6800
2025-06-27 14:21:11,198 - INFO -   • PRIORITY=1, COUNT=1, HANDLER_ID=HCHC-C-030-6800
2025-06-27 14:21:11,198 - INFO -   • PRIORITY=1, COUNT=1, HANDLER_ID=HCHC-O-004-6800
2025-06-27 14:21:11,198 - INFO -   • PRIORITY=1, COUNT=1, HANDLER_ID=HCHC-O-005-6800
2025-06-27 14:21:11,198 - INFO -   • PRIORITY=1, COUNT=1, HANDLER_ID=HJHT-O-003-9808
2025-06-27 14:21:11,198 - INFO -   • PRIORITY=1, COUNT=1, HANDLER_ID=HMTS-C-002-1850
2025-06-27 14:21:11,198 - INFO -   • PRIORITY=1, COUNT=1, HANDLER_ID=LRTR-C-001-1360
2025-06-27 14:21:11,198 - INFO -   • PRIORITY=2, COUNT=1, HANDLER_ID=ABAK-O-001-3004
2025-06-27 14:21:11,199 - INFO -   • PRIORITY=2, COUNT=1, HANDLER_ID=HCHC-C-013-6800
2025-06-27 14:21:11,199 - INFO - 
🗂️ 检查字段映射:
2025-06-27 14:21:11,200 - INFO -   • 样本1: PRIORITY=1, LOT_ID=YX2500002579, HANDLER_ID=HCHC-C-030-6800
2025-06-27 14:21:11,200 - INFO -            DEVICE=LYW6119Ea5QFND-SDA1_TR1, GOOD_QTY=9300, PO_ID=None
2025-06-27 14:21:11,200 - INFO -            STAGE=ROOM-TEST-FT, CREATE_TIME=2025-06-26 09:18:29
2025-06-27 14:21:11,200 - INFO -   • 样本2: PRIORITY=1, LOT_ID=YX2500002567, HANDLER_ID=HJHT-O-003-9808
2025-06-27 14:21:11,201 - INFO -            DEVICE=JWQ85213-C244QFNA-SJA1_TR1, GOOD_QTY=66281, PO_ID=None
2025-06-27 14:21:11,201 - INFO -            STAGE=HOT-FT, CREATE_TIME=2025-06-25 17:29:30
2025-06-27 14:21:11,201 - INFO -   • 样本3: PRIORITY=1, LOT_ID=YX2500002553, HANDLER_ID=HCHC-C-025-6800
2025-06-27 14:21:11,201 - INFO -            DEVICE=JWQ950132DFNA_TR1, GOOD_QTY=27490, PO_ID=None
2025-06-27 14:21:11,201 - INFO -            STAGE=HOT-FT, CREATE_TIME=2025-06-25 09:16:57
2025-06-27 14:21:11,201 - INFO -   • 样本4: PRIORITY=2, LOT_ID=YX2500002552, HANDLER_ID=HCHC-C-025-6800
2025-06-27 14:21:11,201 - INFO -            DEVICE=JWQ950132DFNA_TR1, GOOD_QTY=17517, PO_ID=None
2025-06-27 14:21:11,202 - INFO -            STAGE=HOT-FT, CREATE_TIME=2025-06-25 09:16:49
2025-06-27 14:21:11,202 - INFO -   • 样本5: PRIORITY=3, LOT_ID=YX2500002551, HANDLER_ID=HCHC-C-025-6800
2025-06-27 14:21:11,202 - INFO -            DEVICE=JWQ950132DFNA_TR1, GOOD_QTY=17683, PO_ID=None
2025-06-27 14:21:11,202 - INFO -            STAGE=HOT-FT, CREATE_TIME=2025-06-25 09:16:33
2025-06-27 14:21:11,202 - INFO - 
🔧 开始修复数据结构问题...
2025-06-27 14:21:11,203 - INFO - 📝 检查并添加扩展字段...
2025-06-27 14:21:11,204 - INFO -   ✓ 字段已存在: comprehensive_score
2025-06-27 14:21:11,205 - INFO -   ✓ 字段已存在: processing_time
2025-06-27 14:21:11,206 - INFO -   ✓ 字段已存在: changeover_time
2025-06-27 14:21:11,207 - INFO -   ✓ 字段已存在: algorithm_version
2025-06-27 14:21:11,208 - INFO -   ✓ 字段已存在: match_type
2025-06-27 14:21:11,233 - INFO -   ✅ 添加字段: priority_score
2025-06-27 14:21:11,251 - INFO -   ✅ 添加字段: estimated_hours
2025-06-27 14:21:11,264 - INFO -   ✅ 添加字段: equipment_status
2025-06-27 14:21:11,264 - INFO - 
🔢 修复PRIORITY字段数据类型...
2025-06-27 14:21:11,265 - INFO -   • 当前PRIORITY字段类型: varchar
2025-06-27 14:21:11,265 - INFO -   🔄 转换PRIORITY字段为整数类型...
2025-06-27 14:21:11,310 - INFO -   ✅ PRIORITY字段类型转换完成
2025-06-27 14:21:11,310 - INFO - 
🔗 更新API字段映射...
2025-06-27 14:21:11,311 - INFO -   ✓ API字段映射已是最新版本
2025-06-27 14:21:11,311 - INFO - 
📊 生成数据一致性报告...
2025-06-27 14:21:11,314 - ERROR - ❌ 生成报告失败: Object of type Decimal is not JSON serializable
2025-06-27 14:21:11,314 - INFO - 
✅ 排产数据一致性修复完成！
2025-06-27 14:21:11,314 - INFO - 
🔧 修复建议:
2025-06-27 14:21:11,314 - INFO - 1. 重启应用服务以应用API更改
2025-06-27 14:21:11,315 - INFO - 2. 刷新前端页面缓存
2025-06-27 14:21:11,315 - INFO - 3. 验证排产结果和已排产批次管理页面的数据一致性
2025-06-27 14:21:11,315 - INFO - 4. 检查comprehensive_score字段是否正确显示
