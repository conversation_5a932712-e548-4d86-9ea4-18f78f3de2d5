#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
APS排产系统快速验证测试
验证改进的排产逻辑是否正常工作
"""

import sys
import os
import time
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

print("=" * 60)
print("APS排产系统快速验证测试")
print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
print("=" * 60)

# 1. 测试导入模块
print("\n1. 测试导入模块...")
try:
    from app.services.data_source_manager import DataSourceManager
    print("  ✓ 数据源管理器导入成功")
except Exception as e:
    print(f"  ✗ 数据源管理器导入失败: {e}")
    sys.exit(1)

try:
    from app.services.improved_scheduling_service import ImprovedSchedulingService
    print("  ✓ 改进排产服务导入成功")
except Exception as e:
    print(f"  ✗ 改进排产服务导入失败: {e}")
    sys.exit(1)

# 2. 测试数据连接
print("\n2. 测试数据连接...")
try:
    data_manager = DataSourceManager()
    
    # 测试MySQL表
    tables_to_test = ['ET_WAIT_LOT', 'EQP_STATUS', 'ET_UPH_EQP']
    for table in tables_to_test:
        try:
            data = data_manager.get_table_data(table)
            print(f"  ✓ {table}: {len(data)}条记录")
        except Exception as e:
            print(f"  ✗ {table}: 连接失败 - {e}")
            
except Exception as e:
    print(f"  ✗ 数据源管理器初始化失败: {e}")

# 3. 测试排产算法
print("\n3. 测试排产算法...")
try:
    scheduler = ImprovedSchedulingService()
    
    algorithms = ['deadline', 'product', 'value', 'intelligent']
    
    for algorithm in algorithms:
        try:
            print(f"  测试 {algorithm} 算法...")
            start_time = time.time()
            result = scheduler.execute_scheduling(algorithm)
            end_time = time.time()
            
            execution_time = end_time - start_time
            lot_count = len(result) if result else 0
            
            print(f"    ✓ {algorithm}: {lot_count}个批次, 耗时{execution_time:.2f}秒")
            
            # 分析前10个结果
            if result and len(result) > 0:
                print(f"    前3个批次分配:")
                for i, item in enumerate(result[:3]):
                    lot_id = item.get('LOT_ID', 'N/A')
                    handler_id = item.get('HANDLER_ID', 'N/A')
                    device = item.get('DEVICE', 'N/A')
                    match_score = item.get('match_score', 0)
                    print(f"      {i+1}. {lot_id} -> {handler_id} (产品:{device}, 匹配度:{match_score:.1f})")
            
        except Exception as e:
            print(f"    ✗ {algorithm}: 执行失败 - {e}")
            
except Exception as e:
    print(f"  ✗ 排产服务初始化失败: {e}")

# 4. 设备分配分析
print("\n4. 设备分配分析...")
try:
    scheduler = ImprovedSchedulingService()
    result = scheduler.execute_scheduling('intelligent')
    
    if result and len(result) > 0:
        # 统计设备分配情况
        equipment_stats = {}
        device_stats = {}
        
        for item in result:
            handler_id = item.get('HANDLER_ID', 'UNKNOWN')
            device = item.get('DEVICE', 'UNKNOWN')
            match_score = item.get('match_score', 0)
            
            # 设备统计
            if handler_id not in equipment_stats:
                equipment_stats[handler_id] = {'count': 0, 'total_score': 0}
            equipment_stats[handler_id]['count'] += 1
            equipment_stats[handler_id]['total_score'] += match_score
            
            # 产品统计
            if device not in device_stats:
                device_stats[device] = {'count': 0, 'handlers': set()}
            device_stats[device]['count'] += 1
            device_stats[device]['handlers'].add(handler_id)
        
        print(f"  总批次数: {len(result)}")
        print(f"  使用设备数: {len(equipment_stats)}")
        
        # 检查是否有明显的循环分配问题
        handler_list = [item.get('HANDLER_ID', '') for item in result[:20]]
        is_sequential = True
        for i in range(1, min(10, len(handler_list))):
            expected_handler = f"H{i+1:02d}"
            if handler_list[i] != expected_handler:
                is_sequential = False
                break
        
        if is_sequential:
            print("  ❌ 检测到顺序分配问题！设备分配仍然是简单的循环模式")
            print("     这表明改进的设备匹配算法没有正常工作")
        else:
            print("  ✅ 设备分配看起来合理，不是简单的循环模式")
        
        # 显示设备分配统计
        print("\n  设备分配统计:")
        for handler_id, stats in sorted(equipment_stats.items()):
            avg_score = stats['total_score'] / stats['count'] if stats['count'] > 0 else 0
            print(f"    {handler_id}: {stats['count']}个批次, 平均匹配度: {avg_score:.1f}")
        
        # 显示产品分配统计
        print("\n  产品分配统计:")
        for device, stats in sorted(device_stats.items()):
            handlers_count = len(stats['handlers'])
            print(f"    {device}: {stats['count']}个批次, 分配到{handlers_count}台设备")
            
    else:
        print("  ✗ 无排产结果")
        
except Exception as e:
    print(f"  ✗ 设备分配分析失败: {e}")

# 5. 结论
print("\n" + "=" * 60)
print("测试结论:")
print("=" * 60)

print("✅ 如果看到上述测试都成功执行，说明改进的排产系统已经正常工作")
print("✅ 关键检查点：设备分配是否为智能匹配而非简单循环")
print("✅ 如果设备分配合理，表明已解决了原有的设备分配缺陷")

print(f"\n测试完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
print("=" * 60) 