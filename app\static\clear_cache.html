<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>清理菜单缓存 - AEC-FT ICP</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #b72424;
            text-align: center;
        }
        .btn {
            background-color: #b72424;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px 5px;
            font-size: 16px;
        }
        .btn:hover {
            background-color: #9a1f1f;
        }
        .status {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            font-weight: bold;
        }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 4px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 菜单缓存清理工具</h1>
        
        <div style="text-align: center; margin: 30px 0;">
            <button class="btn" onclick="clearAllCache()">清理所有缓存</button>
            <button class="btn" onclick="clearMenuCache()">仅清理菜单缓存</button>
            <button class="btn" onclick="testMenuAPI()">测试菜单API</button>
            <button class="btn" onclick="forceRefresh()">强制刷新页面</button>
        </div>
        
        <div id="status"></div>
        
        <div style="margin-top: 30px;">
            <h3>📝 操作说明:</h3>
            <ul>
                <li><strong>清理所有缓存</strong>: 清理浏览器中的所有本地存储数据</li>
                <li><strong>仅清理菜单缓存</strong>: 只清理菜单相关的缓存数据</li>
                <li><strong>测试菜单API</strong>: 测试菜单API是否正常工作</li>
                <li><strong>强制刷新页面</strong>: 清理缓存后重新加载主页</li>
            </ul>
        </div>
        
        <div style="margin-top: 30px;">
            <h3>🔍 如果菜单仍然有问题，请检查:</h3>
            <ul>
                <li>浏览器开发者工具 Console 是否有错误</li>
                <li>Network 标签页中 /menu 请求是否成功</li>
                <li>Application 标签页中 Session Storage 是否被清理</li>
                <li>是否需要重启 Flask 应用</li>
            </ul>
        </div>
    </div>
    
    <script>
        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.innerHTML = `<div class="${type}">${message}</div>`;
        }
        
        function clearAllCache() {
            try {
                // 清理所有存储
                localStorage.clear();
                sessionStorage.clear();
                
                // 清理cookie（如果可能）
                document.cookie.split(";").forEach(function(c) { 
                    document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/"); 
                });
                
                showStatus('✅ 所有缓存已清理！请重新登录系统。', 'success');
            } catch (error) {
                showStatus(`❌ 清理缓存失败: ${error.message}`, 'error');
            }
        }
        
        function clearMenuCache() {
            try {
                // 查找并删除菜单相关的缓存
                const keys = Object.keys(sessionStorage);
                let removedCount = 0;
                
                keys.forEach(key => {
                    if (key.startsWith('aps_menu_data_') || key.startsWith('aps_menu_version_')) {
                        sessionStorage.removeItem(key);
                        removedCount++;
                    }
                });
                
                if (removedCount > 0) {
                    showStatus(`✅ 已清理 ${removedCount} 个菜单缓存项`, 'success');
                } else {
                    showStatus('ℹ️ 未找到菜单缓存数据', 'info');
                }
            } catch (error) {
                showStatus(`❌ 清理菜单缓存失败: ${error.message}`, 'error');
            }
        }
        
        async function testMenuAPI() {
            try {
                showStatus('🔄 正在测试菜单API...', 'info');
                
                const response = await fetch('/menu');
                
                if (response.ok) {
                    const menuData = await response.json();
                    const version = menuData.version || 'Unknown';
                    const menuCount = menuData.menu ? menuData.menu.length : 0;
                    
                    showStatus(`✅ 菜单API测试成功！<br>版本: ${version}<br>菜单项: ${menuCount}`, 'success');
                } else {
                    showStatus(`❌ 菜单API测试失败: HTTP ${response.status}`, 'error');
                }
            } catch (error) {
                showStatus(`❌ 菜单API测试失败: ${error.message}`, 'error');
            }
        }
        
        function forceRefresh() {
            clearMenuCache();
            setTimeout(() => {
                window.location.href = '/';
            }, 1000);
        }
        
        // 页面加载时显示当前缓存状态
        window.onload = function() {
            const cacheKeys = Object.keys(sessionStorage).filter(key => 
                key.startsWith('aps_menu_data_') || key.startsWith('aps_menu_version_')
            );
            
            if (cacheKeys.length > 0) {
                showStatus(`ℹ️ 检测到 ${cacheKeys.length} 个菜单缓存项`, 'info');
            } else {
                showStatus('ℹ️ 未检测到菜单缓存', 'info');
            }
        };
    </script>
</body>
</html>