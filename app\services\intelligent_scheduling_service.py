"""
智能排产服务 - 正确的算法逻辑实现
整合Excel导入数据和MySQL基础数据作为排产算法的数据前提
"""

import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from sqlalchemy import text
from app import db
from app.utils.db_helper import get_mysql_connection
import pandas as pd
import os

logger = logging.getLogger(__name__)

class IntelligentSchedulingService:
    """智能排产服务类 - 整合多数据源的完整排产算法"""
    
    def __init__(self):
        self.excel_path = r"D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\Excellist2025.06.05"
        
    def execute_intelligent_scheduling(self, algorithm: str = 'intelligent', 
                                     optimization_target: str = 'balanced') -> Dict[str, Any]:
        """
        执行智能排产算法 - 整合Excel和MySQL数据源，智能切换
        
        核心逻辑：
        1. 从ET_WAIT_LOT获取待排产批次（MySQL优先，Excel备用）
        2. 通过ET_FT_TEST_SPEC匹配测试规范（MySQL优先，Excel备用）
        3. 通过ET_RECIPE_FILE匹配设备要求（MySQL优先，Excel备用）
        4. 结合EQP_STATUS检查机器状态（MySQL优先，Excel备用）
        5. 通过ET_UPH_EQP计算预计完成时间（MySQL优先，Excel备用）
        6. 结合devicepriorityconfig和lotpriorityconfig进行优先级排序（MySQL优先，Excel备用）
        7. 生成lotprioritydone指导实际生产
        
        Args:
            algorithm: 排产算法类型 ('intelligent', 'deadline', 'product', 'value')
            optimization_target: 优化目标 ('time', 'balanced', 'efficiency')
            
        Returns:
            Dict: 排产结果
        """
        try:
            logger.info(f"🚀 开始执行智能排产算法: {algorithm}, 优化目标: {optimization_target}")
            
            # 初始化数据源管理器
            from app.services.data_source_manager import DataSourceManager
            data_manager = DataSourceManager()
            
            # 获取数据源状态
            data_source_status = data_manager.get_data_source_status()
            logger.info(f"📊 数据源状态: MySQL={data_source_status['mysql_available']}, Excel={data_source_status['excel_available']}")
            
            # 步骤1: 获取待排产批次数据 (ET_WAIT_LOT)
            wait_lots, wait_lots_source = data_manager.get_wait_lot_data()
            
            # 步骤2: 获取测试规范数据 (ET_FT_TEST_SPEC)
            test_specs, test_specs_source = data_manager.get_test_spec_data()
            
            # 步骤3: 获取工艺配方数据 (ET_RECIPE_FILE)
            recipe_files, recipe_files_source = data_manager.get_recipe_file_data()
            
            # 步骤4: 获取设备状态数据 (EQP_STATUS)
            equipment_status, equipment_status_source = data_manager.get_equipment_status_data()
            
            # 步骤5: 获取UPH数据 (ET_UPH_EQP)
            uph_data, uph_data_source = data_manager.get_uph_data()
            
            # 步骤6: 获取优先级配置 (devicepriorityconfig + lotpriorityconfig)
            priority_configs, priority_configs_source = data_manager.get_priority_configs()
            
            # 检查数据完整性
            if not wait_lots:
                return {
                    'success': False,
                    'error': '没有找到待排产批次数据',
                    'schedule_data': [],
                    'data_sources': data_source_status
                }
            
            # 步骤7: 执行智能匹配和排产
            schedule_result = self._execute_comprehensive_scheduling(
                wait_lots, test_specs, recipe_files, equipment_status, 
                uph_data, priority_configs, algorithm, optimization_target
            )
            
            logger.info(f"✅ 智能排产完成，生成 {len(schedule_result)} 条排产记录")
            
            return {
                'success': True,
                'total_lots': len(wait_lots),
                'scheduled_lots': len(schedule_result),
                'algorithm': algorithm,
                'optimization_target': optimization_target,
                'schedule_data': schedule_result,
                'execution_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'data_sources': {
                    'wait_lots': f"{len(wait_lots)} ({wait_lots_source})",
                    'test_specs': f"{len(test_specs)} ({test_specs_source})",
                    'recipe_files': f"{len(recipe_files)} ({recipe_files_source})",
                    'equipment_status': f"{len(equipment_status)} ({equipment_status_source})",
                    'uph_data': f"{len(uph_data)} ({uph_data_source})",
                    'priority_configs': f"设备:{len(priority_configs.get('device', {}))}, 批次:{len(priority_configs.get('lot', {}))} ({priority_configs_source})",
                    'mysql_available': data_source_status['mysql_available'],
                    'excel_available': data_source_status['excel_available'],
                    'recommended_source': data_source_status['recommended_source']
                }
            }
            
        except Exception as e:
            logger.error(f"❌ 智能排产执行失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'schedule_data': []
            }
    
    def _get_wait_lot_data(self) -> List[Dict]:
        """获取待排产批次数据 (ET_WAIT_LOT)"""
        try:
            query = text("""
                SELECT 
                    LOT_ID,
                    DEVICE,
                    STAGE,
                    GOOD_QTY,
                    PKG_PN,
                    CHIP_ID,
                    CREATE_TIME,
                    FAC_ID,
                    FLOW_ID,
                    FLOW_VER,
                    WIP_STATE,
                    PROC_STATE,
                    HOLD_STATE
                FROM `v_et_wait_lot_unified`
                WHERE GOOD_QTY > 0
                  AND LOT_ID IS NOT NULL 
                  AND LOT_ID != ''
                ORDER BY CREATE_TIME ASC
            """)
            
            result = db.session.execute(query)
            wait_lots = []
            
            for row in result.fetchall():
                wait_lots.append({
                    'LOT_ID': row[0] or '',
                    'DEVICE': row[1] or '',
                    'STAGE': row[2] or '',
                    'GOOD_QTY': row[3] or 0,
                    'PKG_PN': row[4] or '',
                    'CHIP_ID': row[5] or '',
                    'CREATE_TIME': row[6],
                    'FAC_ID': row[7] or '',
                    'FLOW_ID': row[8] or '',
                    'FLOW_VER': row[9] or '',
                    'WIP_STATE': row[10] or '',
                    'PROC_STATE': row[11] or '',
                    'HOLD_STATE': row[12] or ''
                })
            
            return wait_lots
            
        except Exception as e:
            logger.error(f"获取待排产批次数据失败: {e}")
            return []
    
    def _get_test_spec_data(self) -> Dict[str, Dict]:
        """获取测试规范数据 (ET_FT_TEST_SPEC)"""
        try:
            query = text("""
                SELECT 
                    DEVICE,
                    STAGE,
                    PKG_PN,
                    CHIP_ID,
                    TEST_SPEC,
                    TEST_TIME,
                    SPEC_VERSION
                FROM `v_et_ft_test_spec_unified`
                WHERE DEVICE IS NOT NULL AND DEVICE != ''
            """)
            
            result = db.session.execute(query)
            test_specs = {}
            
            for row in result.fetchall():
                device = row[0] or ''
                stage = row[1] or ''
                pkg_pn = row[2] or ''
                chip_id = row[3] or ''
                
                # 创建多级匹配键
                keys = [
                    f"{device}|{stage}|{pkg_pn}|{chip_id}",  # 完全匹配
                    f"{device}|{stage}|{pkg_pn}",           # 设备+工序+封装
                    f"{device}|{stage}",                    # 设备+工序
                    f"{device}"                             # 仅设备
                ]
                
                spec_data = {
                    'DEVICE': device,
                    'STAGE': stage,
                    'PKG_PN': pkg_pn,
                    'CHIP_ID': chip_id,
                    'TEST_SPEC': row[4] or '',
                    'TEST_TIME': row[5] or 60,  # 默认60秒
                    'SPEC_VERSION': row[6] or ''
                }
                
                # 存储到所有匹配键中
                for key in keys:
                    if key not in test_specs:
                        test_specs[key] = spec_data
            
            return test_specs
            
        except Exception as e:
            logger.error(f"获取测试规范数据失败: {e}")
            return {}
    
    def _get_recipe_file_data(self) -> Dict[str, Dict]:
        """获取工艺配方数据 (ET_RECIPE_FILE)"""
        try:
            query = text("""
                SELECT 
                    DEVICE,
                    STAGE,
                    PKG_PN,
                    CHIP_ID,
                    RECIPE_FILE,
                    RECIPE_VERSION,
                    TESTER_TYPE,
                    HANDLER_TYPE
                FROM `v_et_recipe_file_unified`
                WHERE DEVICE IS NOT NULL AND DEVICE != ''
            """)
            
            result = db.session.execute(query)
            recipe_files = {}
            
            for row in result.fetchall():
                device = row[0] or ''
                stage = row[1] or ''
                pkg_pn = row[2] or ''
                chip_id = row[3] or ''
                
                # 创建多级匹配键
                keys = [
                    f"{device}|{stage}|{pkg_pn}|{chip_id}",  # 完全匹配
                    f"{device}|{stage}|{pkg_pn}",           # 设备+工序+封装
                    f"{device}|{stage}",                    # 设备+工序
                    f"{device}"                             # 仅设备
                ]
                
                recipe_data = {
                    'DEVICE': device,
                    'STAGE': stage,
                    'PKG_PN': pkg_pn,
                    'CHIP_ID': chip_id,
                    'RECIPE_FILE': row[4] or '',
                    'RECIPE_VERSION': row[5] or '',
                    'TESTER_TYPE': row[6] or '',
                    'HANDLER_TYPE': row[7] or ''
                }
                
                # 存储到所有匹配键中
                for key in keys:
                    if key not in recipe_files:
                        recipe_files[key] = recipe_data
            
            return recipe_files
            
        except Exception as e:
            logger.error(f"获取工艺配方数据失败: {e}")
            return {}
    
    def _get_equipment_status_data(self) -> Dict[str, Dict]:
        """获取设备状态数据 (EQP_STATUS)"""
        try:
            query = text("""
                SELECT 
                    EQP_ID,
                    EQP_NAME,
                    EQP_STATUS,
                    TESTER_ID,
                    HANDLER_ID,
                    FAC_ID,
                    UPDATE_TIME
                FROM `v_eqp_status_unified`
                WHERE EQP_STATUS IS NOT NULL
            """)
            
            result = db.session.execute(query)
            equipment_status = {}
            
            for row in result.fetchall():
                eqp_id = row[0] or ''
                eqp_status = row[2] or ''
                
                equipment_status[eqp_id] = {
                    'EQP_ID': eqp_id,
                    'EQP_NAME': row[1] or '',
                    'v_eqp_status_unified': eqp_status,
                    'TESTER_ID': row[3] or '',
                    'HANDLER_ID': row[4] or '',
                    'FAC_ID': row[5] or '',
                    'UPDATE_TIME': row[6],
                    'available': eqp_status.upper() in ['RUN', 'IDLE', 'SETUP', 'READY']  # 可用状态
                }
            
            return equipment_status
            
        except Exception as e:
            logger.error(f"获取设备状态数据失败: {e}")
            return {}
    
    def _get_uph_data(self) -> Dict[str, Dict]:
        """获取UPH数据 (ET_UPH_EQP)"""
        try:
            query = text("""
                SELECT 
                    DEVICE,
                    STAGE,
                    PKG_PN,
                    UPH,
                    SORTER_MODEL,
                    FAC_ID,
                    TESTER_TYPE,
                    HANDLER_TYPE
                FROM `v_et_uph_eqp_unified`
                WHERE UPH > 0 AND DEVICE IS NOT NULL AND DEVICE != ''
            """)
            
            result = db.session.execute(query)
            uph_data = {}
            
            for row in result.fetchall():
                device = row[0] or ''
                stage = row[1] or ''
                pkg_pn = row[2] or ''
                
                # 创建多级匹配键
                keys = [
                    f"{device}|{stage}|{pkg_pn}",  # 完全匹配
                    f"{device}|{stage}",           # 设备+工序
                    f"{device}"                    # 仅设备
                ]
                
                uph_info = {
                    'DEVICE': device,
                    'STAGE': stage,
                    'PKG_PN': pkg_pn,
                    'UPH': row[3] or 1000,
                    'SORTER_MODEL': row[4] or '',
                    'FAC_ID': row[5] or '',
                    'TESTER_TYPE': row[6] or '',
                    'HANDLER_TYPE': row[7] or ''
                }
                
                # 存储到所有匹配键中，优先级从高到低
                for key in keys:
                    if key not in uph_data:
                        uph_data[key] = uph_info
            
            return uph_data
            
        except Exception as e:
            logger.error(f"获取UPH数据失败: {e}")
            return {}
    
    def _get_priority_configs(self) -> Dict[str, Dict]:
        """获取优先级配置数据 (devicepriorityconfig + lotpriorityconfig)"""
        try:
            conn = get_mysql_connection()
            cursor = conn.cursor(dictionary=True)
            
            # 获取设备优先级配置 (devicepriorityconfig)
            device_priority = {}
            try:
                cursor.execute("SELECT * FROM device_priority_config")
                for row in cursor.fetchall():
                    device = row.get('DEVICE', '')
                    stage = row.get('STAGE', '')
                    
                    # 创建多级匹配键
                    keys = [
                        f"{device}|{stage}",  # 设备+工序
                        f"{device}"           # 仅设备
                    ]
                    
                    for key in keys:
                        if key not in device_priority:
                            device_priority[key] = row
                            
            except Exception as e:
                logger.warning(f"获取设备优先级配置失败: {e}")
            
            # 获取批次优先级配置 (lotpriorityconfig)
            lot_priority = {}
            try:
                cursor.execute("SELECT * FROM lot_priority_config")
                for row in cursor.fetchall():
                    lot_id = row.get('LOT_ID', '')
                    if lot_id:
                        lot_priority[lot_id] = row
            except Exception as e:
                logger.warning(f"获取批次优先级配置失败: {e}")
            
            cursor.close()
            conn.close()
            
            return {
                'device': device_priority,
                'lot': lot_priority
            }
            
        except Exception as e:
            logger.error(f"获取优先级配置失败: {e}")
            return {'device': {}, 'lot': {}}
    
    def _execute_comprehensive_scheduling(self, wait_lots: List[Dict], test_specs: Dict, 
                                        recipe_files: Dict, equipment_status: Dict,
                                        uph_data: Dict, priority_configs: Dict,
                                        algorithm: str, optimization_target: str) -> List[Dict]:
        """执行综合排产算法"""
        try:
            # 为每个批次进行综合匹配和评分
            enriched_lots = []
            
            for lot in wait_lots:
                # 智能匹配各种数据
                matched_data = self._match_comprehensive_data(
                    lot, test_specs, recipe_files, equipment_status, uph_data
                )
                
                # 计算综合优先级分数
                priority_score = self._calculate_comprehensive_priority(
                    lot, priority_configs, matched_data, algorithm
                )
                
                # 计算预计完成时间
                estimated_completion = self._calculate_estimated_completion(
                    lot, matched_data['uph_info'], matched_data['test_spec']
                )
                
                # 构建增强的批次数据
                enriched_lot = {
                    **lot,
                    **matched_data,
                    'priority_score': priority_score,
                    'estimated_completion': estimated_completion,
                    'algorithm': algorithm,
                    'optimization_target': optimization_target
                }
                
                enriched_lots.append(enriched_lot)
            
            # 根据算法策略排序
            sorted_lots = self._sort_by_comprehensive_algorithm(
                enriched_lots, algorithm, optimization_target
            )
            
            # 生成最终排产结果
            schedule_result = self._generate_final_schedule_result(sorted_lots)
            
            return schedule_result
            
        except Exception as e:
            logger.error(f"综合排产算法执行失败: {e}")
            return []
    
    def _match_comprehensive_data(self, lot: Dict, test_specs: Dict, recipe_files: Dict, 
                                equipment_status: Dict, uph_data: Dict) -> Dict:
        """综合匹配所有相关数据"""
        device = lot.get('DEVICE', '')
        stage = lot.get('STAGE', '')
        pkg_pn = lot.get('PKG_PN', '')
        chip_id = lot.get('CHIP_ID', '')
        
        # 匹配测试规范
        test_spec = self._find_best_match(test_specs, [
            f"{device}|{stage}|{pkg_pn}|{chip_id}",
            f"{device}|{stage}|{pkg_pn}",
            f"{device}|{stage}",
            f"{device}"
        ], {
            'DEVICE': device, 'STAGE': stage, 'PKG_PN': pkg_pn, 'CHIP_ID': chip_id,
            'TEST_SPEC': 'DEFAULT_SPEC', 'TEST_TIME': 60, 'SPEC_VERSION': '1.0'
        })
        
        # 匹配工艺配方
        recipe_file = self._find_best_match(recipe_files, [
            f"{device}|{stage}|{pkg_pn}|{chip_id}",
            f"{device}|{stage}|{pkg_pn}",
            f"{device}|{stage}",
            f"{device}"
        ], {
            'DEVICE': device, 'STAGE': stage, 'PKG_PN': pkg_pn, 'CHIP_ID': chip_id,
            'RECIPE_FILE': 'DEFAULT_RECIPE', 'RECIPE_VERSION': '1.0',
            'TESTER_TYPE': 'AUTO', 'HANDLER_TYPE': 'AUTO'
        })
        
        # 匹配UPH数据
        uph_info = self._find_best_match(uph_data, [
            f"{device}|{stage}|{pkg_pn}",
            f"{device}|{stage}",
            f"{device}"
        ], {
            'DEVICE': device, 'STAGE': stage, 'PKG_PN': pkg_pn,
            'UPH': 1000, 'SORTER_MODEL': 'DEFAULT', 'FAC_ID': 'YX',
            'TESTER_TYPE': 'AUTO', 'HANDLER_TYPE': 'AUTO'
        })
        
        # 匹配可用设备
        available_equipment = self._match_available_equipment(
            lot, equipment_status, recipe_file, uph_info
        )
        
        return {
            'test_spec': test_spec,
            'recipe_file': recipe_file,
            'uph_info': uph_info,
            'available_equipment': available_equipment,
            'match_quality': self._calculate_match_quality(test_spec, recipe_file, uph_info, available_equipment)
        }
    
    def _find_best_match(self, data_dict: Dict, keys: List[str], default: Dict) -> Dict:
        """在数据字典中查找最佳匹配"""
        for key in keys:
            if key in data_dict:
                return data_dict[key]
        return default
    
    def _match_available_equipment(self, lot: Dict, equipment_status: Dict, 
                                 recipe_file: Dict, uph_info: Dict) -> List[Dict]:
        """匹配可用设备"""
        available_equipment = []
        
        required_tester_type = recipe_file.get('TESTER_TYPE', '')
        required_handler_type = recipe_file.get('HANDLER_TYPE', '')
        
        for eqp_id, equipment in equipment_status.items():
            if not equipment.get('available', False):
                continue
            
            # 计算设备匹配分数
            match_score = 0
            
            # 测试机类型匹配
            if (required_tester_type and 
                required_tester_type.upper() in equipment.get('TESTER_ID', '').upper()):
                match_score += 40
            
            # 分选机类型匹配
            if (required_handler_type and 
                required_handler_type.upper() in equipment.get('HANDLER_ID', '').upper()):
                match_score += 40
            
            # 工厂匹配
            if equipment.get('FAC_ID', '') == lot.get('FAC_ID', ''):
                match_score += 20
            
            equipment_info = {
                **equipment,
                'match_score': match_score,
                'uph': uph_info.get('UPH', 1000)
            }
            
            available_equipment.append(equipment_info)
        
        # 按匹配分数排序
        available_equipment.sort(key=lambda x: x['match_score'], reverse=True)
        
        return available_equipment[:10]  # 返回前10个最匹配的设备
    
    def _calculate_comprehensive_priority(self, lot: Dict, priority_configs: Dict, 
                                        matched_data: Dict, algorithm: str) -> float:
        """计算综合优先级分数"""
        base_score = 50.0  # 基础分数
        
        # 设备优先级 (devicepriorityconfig)
        device = lot.get('DEVICE', '')
        stage = lot.get('STAGE', '')
        
        device_config = self._find_best_match(priority_configs.get('device', {}), [
            f"{device}|{stage}",
            f"{device}"
        ], {})
        
        device_priority = device_config.get('PRIORITY', 'MEDIUM')
        if device_priority == 'HIGH':
            base_score += 30
        elif device_priority == 'MEDIUM':
            base_score += 15
        elif device_priority == 'LOW':
            base_score += 5
        
        # 批次优先级 (lotpriorityconfig)
        lot_id = lot.get('LOT_ID', '')
        lot_config = priority_configs.get('lot', {}).get(lot_id, {})
        lot_priority = lot_config.get('PRIORITY', 'MEDIUM')
        
        if lot_priority == 'HIGH':
            base_score += 25
        elif lot_priority == 'MEDIUM':
            base_score += 10
        elif lot_priority == 'LOW':
            base_score += 2
        
        # 数量权重
        good_qty = lot.get('GOOD_QTY', 0)
        if algorithm == 'value':
            # 产值优先：数量大的优先级高
            base_score += min(good_qty / 1000, 20)
        elif algorithm == 'deadline':
            # 交期优先：数量小的优先级高
            base_score += max(20 - good_qty / 1000, 0)
        
        # 时间权重
        create_time = lot.get('CREATE_TIME')
        if create_time:
            try:
                if isinstance(create_time, str):
                    create_dt = datetime.strptime(create_time, '%Y-%m-%d %H:%M:%S')
                else:
                    create_dt = create_time
                
                days_waiting = (datetime.now() - create_dt).days
                base_score += min(days_waiting * 2, 15)  # 等待时间越长优先级越高
            except:
                pass
        
        # 匹配质量权重
        match_quality = matched_data.get('match_quality', 0)
        base_score += match_quality * 0.2  # 匹配质量影响权重
        
        return round(base_score, 2)
    
    def _calculate_estimated_completion(self, lot: Dict, uph_info: Dict, 
                                      test_spec: Dict) -> Dict:
        """计算预计完成时间"""
        good_qty = lot.get('GOOD_QTY', 0)
        uph = uph_info.get('UPH', 1000)
        test_time = test_spec.get('TEST_TIME', 60)  # 秒
        
        # 计算加工时间（小时）
        processing_hours = good_qty / uph if uph > 0 else 0
        
        # 计算总测试时间（小时）
        total_test_hours = (good_qty * test_time) / 3600 if test_time > 0 else 0
        
        # 预计完成时间 = max(加工时间, 测试时间)
        estimated_hours = max(processing_hours, total_test_hours)
        
        # 计算完成日期
        start_time = datetime.now()
        completion_time = start_time + timedelta(hours=estimated_hours)
        
        return {
            'processing_hours': round(processing_hours, 2),
            'total_test_hours': round(total_test_hours, 2),
            'estimated_hours': round(estimated_hours, 2),
            'start_time': start_time.strftime('%Y-%m-%d %H:%M:%S'),
            'completion_time': completion_time.strftime('%Y-%m-%d %H:%M:%S'),
            'completion_date': completion_time.strftime('%Y-%m-%d')
        }
    
    def _calculate_match_quality(self, test_spec: Dict, recipe_file: Dict, 
                               uph_info: Dict, available_equipment: List[Dict]) -> float:
        """计算匹配质量分数"""
        quality_score = 0.0
        
        # 测试规范匹配质量
        if test_spec.get('TEST_SPEC', '') != 'DEFAULT_SPEC':
            quality_score += 25
        
        # 工艺配方匹配质量
        if recipe_file.get('RECIPE_FILE', '') != 'DEFAULT_RECIPE':
            quality_score += 25
        
        # UPH数据匹配质量
        if uph_info.get('UPH', 0) > 1000:
            quality_score += 25
        
        # 设备匹配质量
        if available_equipment and available_equipment[0].get('match_score', 0) > 50:
            quality_score += 25
        
        return round(quality_score, 2)
    
    def _sort_by_comprehensive_algorithm(self, enriched_lots: List[Dict], algorithm: str, 
                                       optimization_target: str) -> List[Dict]:
        """根据综合算法策略排序"""
        if algorithm == 'deadline':
            # 交期优先：优先级高、完成时间短的先排
            enriched_lots.sort(key=lambda x: (
                -x['priority_score'],
                x['estimated_completion']['estimated_hours'],
                x['GOOD_QTY']
            ))
        elif algorithm == 'product':
            # 产品优先：按产品分组，优先级高的先排
            enriched_lots.sort(key=lambda x: (
                x['DEVICE'],
                x['STAGE'],
                -x['priority_score']
            ))
        elif algorithm == 'value':
            # 产值优先：优先级高、数量大的先排
            enriched_lots.sort(key=lambda x: (
                -x['priority_score'],
                -x['GOOD_QTY'],
                x['estimated_completion']['estimated_hours']
            ))
        else:  # intelligent
            # 智能策略：综合考虑优先级、匹配质量、完成时间
            enriched_lots.sort(key=lambda x: (
                -x['priority_score'],
                -x['match_quality'],
                x['estimated_completion']['estimated_hours']
            ))
        
        return enriched_lots
    
    def _generate_final_schedule_result(self, sorted_lots: List[Dict]) -> List[Dict]:
        """生成最终排产结果 (lotprioritydone格式)"""
        schedule_result = []
        equipment_workload = {}  # 设备工作负载跟踪
        
        for index, lot in enumerate(sorted_lots):
            # 选择最佳设备
            best_equipment = None
            available_equipment = lot.get('available_equipment', [])
            
            if available_equipment:
                # 选择工作负载最小的设备
                min_workload = float('inf')
                for equipment in available_equipment:
                    eqp_id = equipment.get('EQP_ID', '')
                    current_workload = equipment_workload.get(eqp_id, 0)
                    if current_workload < min_workload:
                        min_workload = current_workload
                        best_equipment = equipment
                
                # 更新设备工作负载
                if best_equipment:
                    eqp_id = best_equipment.get('EQP_ID', '')
                    estimated_hours = lot['estimated_completion']['estimated_hours']
                    equipment_workload[eqp_id] = equipment_workload.get(eqp_id, 0) + estimated_hours
            
            # 构建排产记录 (按lotprioritydone.xlsx格式)
            schedule_item = {
                'ORDER': index + 1,
                'HANDLER_ID': best_equipment.get('HANDLER_ID', f'H{(index % 10) + 1:02d}') if best_equipment else f'H{(index % 10) + 1:02d}',
                'LOT_ID': lot['LOT_ID'],
                'LOT_TYPE': 'PRODUCTION',
                'GOOD_QTY': lot['GOOD_QTY'],
                'PROD_ID': lot.get('DEVICE', ''),
                'DEVICE': lot['DEVICE'],
                'CHIP_ID': lot['CHIP_ID'],
                'PKG_PN': lot['PKG_PN'],
                'PO_ID': '',
                'STAGE': lot['STAGE'],
                'WIP_STATE': lot.get('WIP_STATE', ''),
                'PROC_STATE': lot.get('PROC_STATE', ''),
                'HOLD_STATE': lot.get('HOLD_STATE', ''),
                'FLOW_ID': lot.get('FLOW_ID', ''),
                'FLOW_VER': lot.get('FLOW_VER', ''),
                'RELEASE_TIME': lot['estimated_completion']['start_time'],
                'FAC_ID': lot.get('FAC_ID', 'YX'),
                'CREATE_TIME': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                # 扩展信息用于分析
                'priority_score': lot['priority_score'],
                'match_quality': lot['match_quality'],
                'estimated_hours': lot['estimated_completion']['estimated_hours'],
                'completion_time': lot['estimated_completion']['completion_time'],
                'uph': lot['uph_info']['UPH'],
                'test_spec': lot['test_spec']['TEST_SPEC'],
                'recipe_file': lot['recipe_file']['RECIPE_FILE'],
                'equipment_id': best_equipment.get('EQP_ID', '') if best_equipment else '',
                'tester_id': best_equipment.get('TESTER_ID', '') if best_equipment else ''
            }
            
            schedule_result.append(schedule_item)
        
        return schedule_result