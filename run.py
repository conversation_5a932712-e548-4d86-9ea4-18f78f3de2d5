#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
APS 车规芯片终测智能调度平台 - 应用启动脚本
版本: v2.0 (Flask + MySQL 架构)
"""

from app import create_app
import sys
import os
import logging
import webbrowser
from threading import Timer
import traceback

# 确保必要目录存在
for directory in ['logs', 'instance', 'static/exports', 'downloads']:
    os.makedirs(directory, exist_ok=True)

# 配置日志系统
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s [in %(pathname)s:%(lineno)d]',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('logs/app.log', encoding='utf-8')
    ]
)
logger = logging.getLogger('APS-Platform')

def get_app_path():
    """获取应用程序路径，处理PyInstaller打包和开发环境"""
    if getattr(sys, 'frozen', False):
        # 运行在PyInstaller打包环境
        return os.path.dirname(sys.executable)
    else:
        # 运行在常规Python环境
        return os.path.dirname(os.path.abspath(__file__))

def check_mysql_connection():
    """检查MySQL数据库连接和表结构"""
    try:
        # 导入必要模块
        import pymysql
        
        # 使用项目标准的数据库连接配置
        mysql_config = {
            'host': 'localhost',
            'port': 3306,
            'user': 'root',
            'password': 'WWWwww123!',
            'charset': 'utf8mb4'
        }
        
        # 需要检查的数据库和关键表
        database_checks = {
            'aps': [
                'et_wait_lot', 'wip_lot', 'eqp_status', 'et_ft_test_spec',
                'ET_UPH_EQP', 'ct', 'tcc_inv', 'lotprioritydone'
            ],
            'aps_system': [
                'users', 'user_permissions', 'menu_permissions',
                'devicepriorityconfig', 'lotpriorityconfig'  # 优先级配置表在aps_system中
            ]
        }
        
        # 连接MySQL服务器
        conn = pymysql.connect(**mysql_config)
        cursor = conn.cursor()
        
        missing_items = []
        
        for db_name, required_tables in database_checks.items():
            # 检查数据库是否存在
            cursor.execute(f"SHOW DATABASES LIKE '{db_name}'")
            if not cursor.fetchone():
                logger.error(f"数据库 '{db_name}' 不存在")
                missing_items.append(f"数据库: {db_name}")
                continue
            
            logger.info(f"数据库 '{db_name}' 存在")
            
            # 检查关键表是否存在
            cursor.execute(f"USE {db_name}")
            for table_name in required_tables:
                cursor.execute(f"SHOW TABLES LIKE '{table_name}'")
                if cursor.fetchone():
                    logger.debug(f"表 '{db_name}.{table_name}' 存在")
                else:
                    logger.warning(f"表 '{db_name}.{table_name}' 不存在")
                    missing_items.append(f"表: {db_name}.{table_name}")
        
        conn.close()
        
        if missing_items:
            logger.error("❌ 数据库检查发现问题:")
            for item in missing_items:
                logger.error(f"   - {item}")
            logger.error("请运行: python run.py init-db")
            return False
        
        logger.info("MySQL 数据库和表结构检查完成")
        return True
        
    except ImportError as e:
        logger.error(f"❌ 缺少必要模块: {e}")
        logger.error("请运行: pip install -r requirements.txt")
        return False
    except Exception as e:
        logger.error(f"❌ MySQL 数据库检查失败: {e}")
        logger.error("请检查:")
        logger.error("  1. MySQL 服务器是否运行")
        logger.error("  2. 用户名密码是否正确")
        logger.error("  3. 网络连接是否正常")
        return False

def check_runtime_environment():
    """检查运行时环境和依赖"""
    try:
        # 检查Python版本
        python_version = sys.version_info
        if python_version < (3, 7):
            logger.error("❌ Python版本过低，需要Python 3.7+")
            return False
        
        logger.info(f"✅ Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
        
        # 检查关键依赖
        required_modules = ['flask', 'pymysql', 'sqlalchemy', 'requests', 'pandas']
        missing_modules = []
        
        for module in required_modules:
            try:
                __import__(module)
                logger.debug(f"✅ 模块 {module} 已安装")
            except ImportError:
                missing_modules.append(module)
                logger.error(f"❌ 模块 {module} 未安装")
        
        if missing_modules:
            logger.error("请安装缺失的依赖:")
            logger.error("pip install -r requirements.txt")
            return False
        
        # 检查关键目录和文件
        critical_paths = {
            'app/': '应用主目录',
            'app/services/data_source_manager.py': '数据源管理器',
            'app/api_v2/': 'API v2接口',
            'app/templates/': '模板目录',
            'app/static/': '静态资源',
            'config/__init__.py': '配置文件'
        }
        
        for path, description in critical_paths.items():
            if os.path.exists(path):
                logger.debug(f"✅ {description}: {path}")
            else:
                logger.error(f"❌ {description}不存在: {path}")
                return False
        
        logger.info("✅ 运行时环境检查完成")
        return True
        
    except Exception as e:
        logger.error(f"❌ 环境检查失败: {e}")
        return False

def create_application():
    """创建APS应用实例"""
    try:
        # 设置工作目录
        app_path = get_app_path()
        logger.info(f"🏠 应用路径: {app_path}")
        os.chdir(app_path)
        
        # 检查运行时环境
        logger.info("🔍 检查运行时环境...")
        if not check_runtime_environment():
            logger.error("❌ 运行时环境检查失败")
            return None
        
        # 检查MySQL数据库
        logger.info("🔍 检查MySQL数据库...")
        if not check_mysql_connection():
            logger.error("❌ 数据库检查失败")
            logger.error("💡 请先运行: python run.py init-db")
            return None
        
        # 创建Flask应用实例
        logger.info("创建Flask应用...")
        app_result = create_app()
        
        if app_result is None:
            logger.error("❌ Flask应用创建失败")
            return None
        
        # 解包应用和SocketIO实例
        if isinstance(app_result, tuple):
            app, socketio = app_result
        else:
            app = app_result
            socketio = None
        
        # 验证关键服务
        with app.app_context():
            try:
                # 检查数据源管理器
                from app.services.data_source_manager import DataSourceManager
                manager = DataSourceManager()
                status = manager.get_data_source_status()
                logger.info(f"📊 数据源状态: MySQL={'可用' if status.get('mysql_available') else '不可用'}")
                
                # 检查API路由
                from flask import url_for
                logger.debug("🔗 API路由注册正常")
                
            except Exception as e:
                logger.warning(f"⚠️ 服务验证部分失败: {e}")
        
        logger.info("应用创建成功")
        return app, socketio
        
    except Exception as e:
        logger.error(f"❌ 应用创建失败: {e}")
        traceback.print_exc()
        return None

def main():
    """主启动函数"""
    try:
        # 显示启动横幅
        print("APS 车规芯片终测智能调度平台 v2.0")
                
        # 检查命令行参数
        if len(sys.argv) > 1:
            if sys.argv[1] == 'init-db':
                logger.info("🔧 正在初始化MySQL数据库...")
                try:
                    from init_db import main as init_main
                    success = init_main()
                    if success:
                        logger.info("✅ 数据库初始化完成")
                        print("\n🎉 数据库初始化成功！现在可以启动应用:")
                        print("   python run.py")
                    else:
                        logger.error("❌ 数据库初始化失败")
                    sys.exit(0 if success else 1)
                except ImportError as e:
                    logger.error(f"❌ 无法导入初始化模块: {e}")
                    sys.exit(1)
                    
            elif sys.argv[1] in ['--help', '-h', 'help']:
                print("APS 车规芯片终测智能调度平台 - 使用说明")
                print("\n命令:")
                print("  python run.py              # 启动应用服务器")
                print("  python run.py init-db      # 初始化MySQL数据库")
                sys.exit(0)
                
            elif sys.argv[1] == 'migrate':
                logger.info("🔄 执行数据库迁移...")
                try:
                    from migrate_priority_tables import migrate_priority_tables
                    migrate_priority_tables()
                    logger.info("✅ 数据库迁移完成")
                    sys.exit(0)
                except Exception as e:
                    logger.error(f"❌ 数据库迁移失败: {e}")
                    sys.exit(1)
        
        # 创建应用实例
        logger.info("正在启动APS平台...")
        app_result = create_application()
        if app_result is None:
            logger.error("❌ 应用启动失败")
            print("\n💡 解决建议:")
            print("1. 确保MySQL服务正在运行")
            print("2. 检查数据库连接配置")
            print("3. 运行数据库初始化: python run.py init-db")
            sys.exit(1)
        
        # 解包应用和SocketIO实例
        if isinstance(app_result, tuple):
            app, socketio = app_result
        else:
            app = app_result
            socketio = None
        
        # 启动服务器
        host = '127.0.0.1'
        port = 5000
        
        logger.info("APS平台启动成功!")
        logger.info(f"Web访问地址: http://{host}:{port}")
        logger.info("默认账户: admin / admin")
        logger.info("支持Chrome, Firefox, Edge浏览器")
        logger.info("按 Ctrl+C 停止服务")
        
        # 判断运行环境
        is_production = getattr(sys, 'frozen', False)
        if not is_production:
            # 开发环境：延迟打开浏览器
            logger.info("开发模式，将自动打开浏览器...")
            Timer(2.0, lambda: webbrowser.open(f'http://{host}:{port}')).start()
        else:
            logger.info("生产模式")
        
        # 启动Flask应用
        if socketio:
            # 使用SocketIO运行，支持WebSocket
            socketio.run(
                app,
                host=host,
                port=port,
                debug=False,
                use_reloader=False,
                log_output=True
            )
        else:
            # fallback到普通Flask
            app.run(
                host=host,
                port=port,
                debug=False,
                threaded=True,
                use_reloader=False  # 避免重载器问题
            )
        
    except KeyboardInterrupt:
        logger.info("用户停止服务")
        print("\nAPS平台已安全停止")
        sys.exit(0)
    except Exception as e:
        logger.error(f"❌ 启动失败: {e}")
        print(f"\n启动错误: {e}")
        traceback.print_exc()
        sys.exit(1)

if __name__ == '__main__':
    main() 