{% extends 'base.html' %}

{% block title %}系统设置 - AEC-FT ICP{% endblock %}

{% block page_title %}系统设置{% endblock %}

{% block extra_css %}
<style>
    .settings-card {
        margin-bottom: 1.5rem;
    }
    
    .settings-header {
        margin-bottom: 1.5rem;
        border-bottom: 1px solid #eee;
        padding-bottom: 1rem;
    }
    
    .settings-section {
        margin-bottom: 2rem;
    }
    
    .form-switch {
        display: flex;
        align-items: center;
    }
    
    .form-switch .form-check-input {
        margin-right: 10px;
        width: 3em;
        height: 1.5em;
    }
    
    .setting-description {
        color: #6c757d;
        margin-top: 0.5rem;
    }
    
    .prompt-editor {
        min-height: 200px;
        font-family: monospace;
        font-size: 0.9rem;
    }
    
    .nav-tabs .nav-link {
        border: none;
        color: #495057;
        padding: 0.5rem 1rem;
        margin-right: 0.5rem;
    }
    
    .nav-tabs .nav-link.active {
        color: #dc3545;
        border-bottom: 2px solid #dc3545;
        background-color: transparent;
    }
    
    .status-badge {
        display: inline-block;
        padding: 0.25em 0.6em;
        font-size: 0.75em;
        font-weight: 700;
        line-height: 1;
        text-align: center;
        white-space: nowrap;
        vertical-align: baseline;
        border-radius: 0.25rem;
        margin-left: 0.5rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card settings-card">
                <div class="card-body">
                    <h4 class="settings-header">系统设置</h4>
                    
                    <!-- 设置导航标签 -->
                    <ul class="nav nav-tabs mb-4" id="settingsTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="scheduler-tab" data-bs-toggle="tab" data-bs-target="#scheduler" 
                                    type="button" role="tab" aria-controls="scheduler" aria-selected="true">
                                <i class="fas fa-clock me-1"></i>定时任务管理
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="chatbot-tab" data-bs-toggle="tab" data-bs-target="#chatbot" 
                                    type="button" role="tab" aria-controls="chatbot" aria-selected="false">
                                <i class="fas fa-comment-dots me-1"></i>聊天机器人
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="ai-settings-tab" data-bs-toggle="tab" data-bs-target="#ai-settings" 
                                    type="button" role="tab" aria-controls="ai-settings" aria-selected="false">
                                <i class="fas fa-database me-1"></i>AI数据库查询
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="data-sync-tab" data-bs-toggle="tab" data-bs-target="#data-sync" 
                                    type="button" role="tab" aria-controls="data-sync" aria-selected="false">
                                <i class="fas fa-sync-alt me-1"></i>数据同步
                            </button>
                        </li>
                    </ul>
                    
                    <!-- 设置内容 -->
                    <div class="tab-content" id="settingsTabContent">
                        <!-- 定时任务管理 -->
                        <div class="tab-pane fade show active" id="scheduler" role="tabpanel" aria-labelledby="scheduler-tab">
                            <div class="settings-section">
                                <h5>全局定时任务管理</h5>
                                <hr>
                                
                                <!-- 全局控制 -->
                                <div class="mb-4">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="enableGlobalScheduler" name="global_scheduler_enabled">
                                        <label class="form-check-label" for="enableGlobalScheduler">启用全局定时任务</label>
                                        <span class="status-badge" id="globalSchedulerStatus">检查中...</span>
                                    </div>
                                    <p class="setting-description">控制系统中所有定时任务的全局开关。关闭后，所有定时任务将停止运行。</p>
                                </div>

                                <!-- 任务状态概览 -->
                                <div class="mb-4">
                                    <h6>定时任务状态概览</h6>
                                    <div class="table-responsive">
                                        <table class="table table-striped table-sm">
                                            <thead>
                                                <tr>
                                                    <th>任务类型</th>
                                                    <th>状态</th>
                                                    <th>配置数量</th>
                                                    <th>下次运行</th>
                                                    <th>操作</th>
                                                </tr>
                                            </thead>
                                            <tbody id="schedulerTaskList">
                                                <!-- 动态加载 -->
                                            </tbody>
                                        </table>
                                    </div>
                                </div>

                                <!-- 操作按钮 -->
                                <div class="d-flex gap-2 mb-3">
                                    <button type="button" class="btn btn-success" id="saveSchedulerSettingsBtn">
                                        <i class="fas fa-save me-1"></i>保存设置
                                    </button>
                                    <button type="button" class="btn btn-info" id="refreshSchedulerStatusBtn">
                                        <i class="fas fa-sync me-1"></i>刷新状态
                                    </button>
                                    <button type="button" class="btn btn-warning" id="restartAllSchedulersBtn">
                                        <i class="fas fa-redo me-1"></i>重启所有任务
                                    </button>
                                </div>

                                <!-- 日志查看 -->
                                <div class="mb-4">
                                    <h6>定时任务日志</h6>
                                    <div class="card">
                                        <div class="card-body">
                                            <div id="schedulerLogs" style="max-height: 300px; overflow-y: auto; font-family: monospace; font-size: 0.9rem; background-color: #f8f9fa; padding: 1rem; border-radius: 0.25rem;">
                                                正在加载日志...
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 聊天机器人设置 -->
                        <div class="tab-pane fade" id="chatbot" role="tabpanel" aria-labelledby="chatbot-tab">
                            <div class="settings-section">
                                <h5>聊天机器人设置</h5>
                                <hr>
                                
                                <form id="chatbotSettingsForm" method="post" action="/system/save_settings">
                                    <div class="mb-4">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="enableChatbot" name="enable_chatbot" data-bs-toggle="collapse" data-bs-target="#chatbotSettings">
                                            <label class="form-check-label" for="enableChatbot">启用 Dify 聊天机器人</label>
                                        </div>
                                        <p class="setting-description">在页面右下角显示一个聊天机器人图标，用户可以通过它咨询问题。</p>
                                    </div>
                                    
                                    <div class="collapse" id="chatbotSettings">
                                        <div class="mb-3">
                                            <label for="chatbotToken" class="form-label">聊天机器人 Token</label>
                                            <input type="text" class="form-control" id="chatbotToken" name="chatbot_token" value="uV72gGRdNz0eP7ac">
                                            <div class="form-text">从 Dify.AI 获取的聊天机器人唯一标识符。</div>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="chatbotServer" class="form-label">聊天机器人服务器地址</label>
                                            <input type="text" class="form-control" id="chatbotServer" name="chatbot_server" value="http://************">
                                            <div class="form-text">Dify.AI 服务器的 URL 地址。</div>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="chatbotColor" class="form-label">聊天机器人按钮颜色</label>
                                            <input type="color" class="form-control form-control-color" id="chatbotColor" name="chatbot_color" value="#b72424">
                                            <div class="form-text">聊天机器人按钮的背景颜色。</div>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label class="form-label">集成方式</label>
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="integration_type" id="integrationScript" value="script" checked>
                                                <label class="form-check-label" for="integrationScript">
                                                    脚本方式（推荐）
                                                </label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="integration_type" id="integrationIframe" value="iframe">
                                                <label class="form-check-label" for="integrationIframe">
                                                    IFrame 方式
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <button type="submit" class="btn btn-primary">保存聊天机器人设置</button>
                                </form>
                            </div>
                        </div>
                        
                        <!-- AI数据库查询设置 -->
                        <div class="tab-pane fade" id="ai-settings" role="tabpanel" aria-labelledby="ai-settings-tab">
                            <div class="settings-section">
                                <h5>AI数据库查询设置</h5>
                                <hr>
                                
                                <form id="aiSettingsForm">
                                    <!-- 数据库设置部分 -->
                                    <div class="mb-4">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="enableDatabase" name="database.enabled">
                                            <label class="form-check-label" for="enableDatabase">启用数据库查询</label>
                                        </div>
                                        <p class="setting-description">允许AI助手查询系统数据库以回答用户问题。</p>
                                    </div>
                                    
                                    <!-- 数据库类型选择 -->
                                    <div class="mb-3">
                                        <label class="form-label">数据库类型</label>
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="database.type" id="dbTypeSQLite" value="sqlite" checked>
                                            <label class="form-check-label" for="dbTypeSQLite">SQLite (本地文件)</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="database.type" id="dbTypeMySQL" value="mysql">
                                            <label class="form-check-label" for="dbTypeMySQL">MySQL (服务器)</label>
                                        </div>
                                    </div>
                                    
                                    <!-- SQLite设置 -->
                                    <div id="sqliteSettings">
                                        <div class="mb-3">
                                            <label for="databasePath" class="form-label">SQLite数据库路径</label>
                                            <div class="input-group">
                                                <input type="text" class="form-control" id="databasePath" name="database.auto_db_path" placeholder="instance/auto.db">
                                                <span class="input-group-text" id="dbStatusIndicator">
                                                    <i class="fas fa-circle-notch fa-spin" style="display: none;"></i>
                                                    <i class="fas fa-check text-success" style="display: none;"></i>
                                                    <i class="fas fa-times text-danger" style="display: none;"></i>
                                                </span>
                                            </div>
                                            <div class="form-text">auto.db数据库的路径，相对于应用根目录。</div>
                                        </div>
                                    </div>
                                    
                                    <!-- MySQL设置 -->
                                    <div id="mysqlSettings" style="display: none;">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="mysqlHost" class="form-label">主机地址</label>
                                                    <input type="text" class="form-control" id="mysqlHost" name="database.mysql.host" placeholder="localhost或127.0.0.1" value="127.0.0.1">
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="mysqlPort" class="form-label">端口</label>
                                                    <input type="number" class="form-control" id="mysqlPort" name="database.mysql.port" placeholder="3306" value="3306">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="mysqlUser" class="form-label">用户名</label>
                                                    <input type="text" class="form-control" id="mysqlUser" name="database.mysql.user" placeholder="root" value="root">
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="mysqlPassword" class="form-label">密码</label>
                                                    <input type="password" class="form-control" id="mysqlPassword" name="database.mysql.password" placeholder="输入数据库密码" value="">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <label for="mysqlDatabase" class="form-label">数据库名</label>
                                            <input type="text" class="form-control" id="mysqlDatabase" name="database.mysql.database" placeholder="aps_system" value="aps_system">
                                        </div>
                                        <div class="mb-3">
                                            <button type="button" class="btn btn-outline-primary" id="testMySQLBtn">
                                                <i class="fas fa-database me-1"></i>测试MySQL连接
                                            </button>
                                            <span id="mysqlStatusIndicator" class="ms-2">
                                                <i class="fas fa-circle-notch fa-spin" style="display: none;"></i>
                                                <i class="fas fa-check text-success" style="display: none;"></i>
                                                <i class="fas fa-times text-danger" style="display: none;"></i>
                                            </span>
                                        </div>
                                    </div>
                                    
                                    <div class="alert alert-info mt-3">
                                        <i class="fas fa-info-circle me-2"></i>
                                        <strong>当前系统状态:</strong> <span id="currentDbType">未连接数据库</span>
                                        <button type="button" class="btn btn-sm btn-outline-primary ms-2" id="checkDbStatusBtn">
                                            <i class="fas fa-sync-alt me-1"></i>检查状态
                                        </button>
                                    </div>
                                    
                                    <!-- 数据库状态详情 -->
                                    <div id="databaseStatusDetails" class="mt-3" style="display: none;">
                                        <h6 class="mb-2">数据库连接详情:</h6>
                                        <div id="databaseStatusList" class="list-group small">
                                            <!-- 数据库状态将通过JS动态添加 -->
                                        </div>
                                    </div>
                                    
                                    <div class="mb-4">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="prioritizeDatabase" name="database.prioritize_database">
                                            <label class="form-check-label" for="prioritizeDatabase">优先使用数据库信息</label>
                                        </div>
                                        <p class="setting-description">在标准对话模式下，如果问题与数据库相关，优先查询数据库获取信息。</p>
                                    </div>
                                    
                                    <!-- 模型设置部分 -->
                                    <div class="mb-4">
                                        <h6 class="mb-3">模型参数设置</h6>
                                        
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="temperature" class="form-label">温度 (Temperature)</label>
                                                    <div class="d-flex align-items-center">
                                                        <input type="range" class="form-range flex-grow-1 me-2" id="temperature" name="database.model.temperature" min="0" max="1" step="0.1">
                                                        <span id="temperatureValue" class="badge bg-primary" style="width: 40px; text-align: center;">0.5</span>
                                                    </div>
                                                    <div class="form-text">控制AI回复的创造性，值越低回复越确定性，值越高回复越多样化。</div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="maxTokens" class="form-label">最大生成令牌数</label>
                                                    <input type="number" class="form-control" id="maxTokens" name="database.model.max_tokens" min="100" max="2000" step="50">
                                                    <div class="form-text">控制AI回复的最大长度，值越大回复越详细。</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- 提示词设置部分 -->
                                    <div class="mb-4">
                                        <h6 class="mb-3">系统提示词</h6>
                                        
                                        <div class="mb-3">
                                            <label for="systemPrompt" class="form-label">数据库查询系统提示词</label>
                                            <textarea class="form-control prompt-editor" id="systemPrompt" name="database.system_prompt" rows="8"></textarea>
                                            <div class="form-text">定义AI在数据库查询模式下的行为，使用 \n 表示换行。</div>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <button type="button" class="btn btn-outline-secondary" id="resetPromptBtn">
                                                <i class="fas fa-undo me-1"></i>恢复默认提示词
                                            </button>
                                        </div>
                                    </div>
                                    
                                    <div class="d-flex">
                                        <button type="button" class="btn btn-primary me-2" id="saveSettingsBtn">
                                            <i class="fas fa-save me-1"></i>保存AI设置
                                        </button>
                                        <button type="button" class="btn btn-outline-secondary" id="testConnectionBtn">
                                            <i class="fas fa-database me-1"></i>测试数据库连接
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                        
                        <!-- 数据同步设置 -->
                        <div class="tab-pane fade" id="data-sync" role="tabpanel" aria-labelledby="data-sync-tab">
                            <div class="settings-section">
                                <h5>智能数据同步设置</h5>
                                <hr>
                                
                                <form id="dataSyncSettingsForm">
                                    <!-- 同步开关 -->
                                    <div class="mb-4">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="enableSync" name="sync_enabled">
                                            <label class="form-check-label" for="enableSync">启用智能数据同步</label>
                                        </div>
                                        <p class="setting-description">自动从MySQL数据库同步数据到本地SQLite，支持Excel数据优先级管理。</p>
                                    </div>
                                    
                                    <!-- 同步方式选择 -->
                                    <div class="mb-4">
                                        <label class="form-label">同步方式</label>
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="sync_method" id="syncTimer" value="timer" checked>
                                            <label class="form-check-label" for="syncTimer">
                                                <i class="fas fa-clock me-2"></i>定时同步
                                                <small class="text-muted d-block">每隔指定时间检查MySQL数据变更</small>
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="sync_method" id="syncBinlog" value="binlog">
                                            <label class="form-check-label" for="syncBinlog">
                                                <i class="fas fa-bolt me-2"></i>实时同步 (Binlog)
                                                <small class="text-muted d-block">监听MySQL binlog，实时同步数据变更</small>
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="sync_method" id="syncManual" value="manual">
                                            <label class="form-check-label" for="syncManual">
                                                <i class="fas fa-hand-pointer me-2"></i>手动同步
                                                <small class="text-muted d-block">仅在用户手动触发时同步数据</small>
                                            </label>
                                        </div>
                                    </div>
                                    
                                    <!-- 同步间隔设置 -->
                                    <div class="mb-3" id="syncIntervalSettings">
                                        <label for="syncInterval" class="form-label">同步间隔 (分钟)</label>
                                        <input type="number" class="form-control" id="syncInterval" name="sync_interval" value="5" min="1" max="1440">
                                        <div class="form-text">定时同步模式下的检查间隔，建议5-30分钟</div>
                                    </div>
                                    
                                    <!-- 冲突处理策略 -->
                                    <div class="mb-4">
                                        <label class="form-label">数据冲突处理策略</label>
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="conflict_strategy" id="excelPriority" value="excel_priority" checked>
                                            <label class="form-check-label" for="excelPriority">
                                                <i class="fas fa-file-excel me-2 text-success"></i>Excel数据优先
                                                <small class="text-muted d-block">Excel上传的数据优先级最高，不会被MySQL数据覆盖</small>
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="conflict_strategy" id="mysqlPriority" value="mysql_priority">
                                            <label class="form-check-label" for="mysqlPriority">
                                                <i class="fas fa-database me-2 text-primary"></i>MySQL数据优先
                                                <small class="text-muted d-block">MySQL数据始终覆盖本地数据</small>
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="conflict_strategy" id="userConfirm" value="user_confirm">
                                            <label class="form-check-label" for="userConfirm">
                                                <i class="fas fa-user-check me-2 text-warning"></i>用户确认
                                                <small class="text-muted d-block">发生冲突时提示用户选择保留哪个数据</small>
                                            </label>
                                        </div>
                                    </div>
                                    
                                    <!-- 同步状态显示 -->
                                    <div class="mb-4">
                                        <h6 class="mb-3">同步状态</h6>
                                        <div class="card bg-light">
                                            <div class="card-body">
                                                <div class="row">
                                                    <div class="col-md-4">
                                                        <div class="d-flex align-items-center">
                                                            <i class="fas fa-circle me-2" id="syncStatusIcon"></i>
                                                            <span id="syncStatusText">未启用</span>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <small class="text-muted">最后同步时间</small>
                                                        <div id="lastSyncTime">从未同步</div>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <small class="text-muted">冲突数量</small>
                                                        <div id="conflictCount">0</div>
                                                    </div>
                                                </div>
                                                <div class="mt-3">
                                                    <button type="button" class="btn btn-sm btn-outline-primary" id="triggerSyncBtn">
                                                        <i class="fas fa-sync-alt me-1"></i>立即同步
                                                    </button>
                                                    <button type="button" class="btn btn-sm btn-outline-info" id="viewSyncLogBtn">
                                                        <i class="fas fa-list me-1"></i>查看日志
                                                    </button>
                                                    <button type="button" class="btn btn-sm btn-outline-warning" id="resolveConflictsBtn">
                                                        <i class="fas fa-exclamation-triangle me-1"></i>解决冲突
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- 表同步配置 -->
                                    <div class="mb-4">
                                        <h6 class="mb-3">表同步配置</h6>
                                        <div class="table-responsive">
                                            <table class="table table-sm">
                                                <thead>
                                                    <tr>
                                                        <th>表名</th>
                                                        <th>启用同步</th>
                                                        <th>最后同步</th>
                                                        <th>状态</th>
                                                        <th>操作</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="tableSyncConfig">
                                                    <!-- 动态生成表格行 -->
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                    
                                    <!-- 表映射配置 -->
                                    <div class="mb-4">
                                        <div class="d-flex justify-content-between align-items-center mb-3">
                                            <h6 class="mb-0">表映射配置</h6>
                                            <button type="button" class="btn btn-sm btn-outline-primary" id="addTableMappingBtn">
                                                <i class="fas fa-plus me-1"></i>添加映射
                                            </button>
                                        </div>
                                        <div class="alert alert-info mb-3">
                                            <i class="fas fa-info-circle me-2"></i>
                                            配置MySQL数据库表到SQLite数据库表的映射关系，用于数据同步。
                                        </div>
                                        <div class="table-responsive">
                                            <table class="table table-sm table-striped">
                                                <thead>
                                                    <tr>
                                                        <th style="width: 20%">SQLite表名</th>
                                                        <th style="width: 20%">MySQL表名</th>
                                                        <th style="width: 10%">启用</th>
                                                        <th style="width: 15%">同步策略</th>
                                                        <th style="width: 10%">主键</th>
                                                        <th style="width: 15%">描述</th>
                                                        <th style="width: 10%">操作</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="tableMappingConfig">
                                                    <!-- 动态生成表格行 -->
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                    
                                    <div class="d-flex">
                                        <button type="button" class="btn btn-primary me-2" id="saveSyncSettingsBtn">
                                            <i class="fas fa-save me-1"></i>保存同步设置
                                        </button>
                                        <button type="button" class="btn btn-outline-secondary" id="resetSyncSettingsBtn">
                                            <i class="fas fa-undo me-1"></i>重置为默认
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 保存成功提示 -->
<div class="position-fixed bottom-0 end-0 p-3" style="z-index: 5">
    <div id="saveToast" class="toast" role="alert" aria-live="assertive" aria-atomic="true" data-bs-delay="3000">
        <div class="toast-header bg-success text-white">
            <i class="fas fa-check-circle me-2"></i>
            <strong class="me-auto">成功</strong>
            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
        <div class="toast-body">
            设置已成功保存！
        </div>
    </div>
</div>

<!-- 测试连接结果模态框 -->
<div class="modal fade" id="testConnectionModal" tabindex="-1" aria-labelledby="testConnectionModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="testConnectionModalLabel">数据库连接测试</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="connectionTestResult">
                    <div class="text-center p-3">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">正在测试...</span>
                        </div>
                        <p class="mt-2">正在测试数据库连接，请稍候...</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<!-- 添加/编辑表映射模态框 -->
<div class="modal fade" id="tableMappingModal" tabindex="-1" aria-labelledby="tableMappingModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="tableMappingModalLabel">添加表映射</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="tableMappingForm">
                    <input type="hidden" id="mappingId" name="id">
                    
                                         <!-- 数据库连接状态 -->
                     <div class="alert alert-info" id="dbConnectionStatus">
                         <div class="d-flex justify-content-between align-items-center">
                             <span>
                                 <i class="fas fa-database me-2"></i>数据库连接状态
                             </span>
                             <button type="button" class="btn btn-sm btn-outline-info" id="refreshTablesBtn">
                                 <i class="fas fa-sync-alt"></i> 刷新
                             </button>
                         </div>
                         <div class="mt-2">
                             <div class="row">
                                 <div class="col-12 mb-2">
                                     <span id="sqliteConnectionStatus" class="badge bg-secondary me-2">SQLite: 检查中...</span>
                                     <small id="sqliteDbPath" class="text-muted"></small>
                                 </div>
                                 <div class="col-12">
                                     <span id="mysqlConnectionStatus" class="badge bg-secondary me-2">MySQL: 检查中...</span>
                                     <small id="mysqlDbPath" class="text-muted"></small>
                                 </div>
                             </div>
                         </div>
                     </div>
                    
                    <div class="mb-3">
                        <label for="sqliteTableSelect" class="form-label">SQLite表名 <span class="text-danger">*</span></label>
                        <select class="form-select" id="sqliteTableSelect" name="sqlite_table" required>
                            <option value="">请选择SQLite表...</option>
                        </select>
                        <div class="form-text">
                            <span id="sqliteTableInfo"></span>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="mysqlTableSelect" class="form-label">MySQL表名 <span class="text-danger">*</span></label>
                        <select class="form-select" id="mysqlTableSelect" name="mysql_table" required>
                            <option value="">请选择MySQL表...</option>
                        </select>
                        <div class="form-text">
                            <span id="mysqlTableInfo"></span>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="mappingEnabled" name="enabled" checked>
                            <label class="form-check-label" for="mappingEnabled">启用同步</label>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="syncStrategy" class="form-label">同步策略</label>
                        <select class="form-select" id="syncStrategy" name="sync_strategy">
                            <option value="full_sync">全量同步</option>
                            <option value="incremental">增量同步</option>
                            <option value="timestamp_based">基于时间戳</option>
                        </select>
                        <div class="form-text">选择数据同步的策略</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="primaryKey" class="form-label">主键字段</label>
                        <select class="form-select" id="primaryKey" name="primary_key">
                            <option value="id">id (默认)</option>
                        </select>
                        <div class="form-text">
                            <span id="primaryKeyInfo">基于选择的表自动检测主键字段</span>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="mappingDescription" class="form-label">描述</label>
                        <textarea class="form-control" id="mappingDescription" name="description" rows="2"
                                  placeholder="描述此表映射的用途..."></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="saveTableMappingBtn">
                    <i class="fas fa-save me-1"></i>保存
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 聊天机器人设置
        // 从本地存储加载设置
        const savedSettings = localStorage.getItem('chatbotSettings');
        if (savedSettings) {
            const settings = JSON.parse(savedSettings);
            
            // 设置表单值
            document.getElementById('enableChatbot').checked = settings.enabled;
            if (settings.enabled) {
                // 如果启用了聊天机器人，展开设置面板
                document.getElementById('chatbotSettings').classList.add('show');
            }
            
            document.getElementById('chatbotToken').value = settings.token || 'uV72gGRdNz0eP7ac';
            document.getElementById('chatbotServer').value = settings.server || 'http://************';
            document.getElementById('chatbotColor').value = settings.color || '#b72424';
            
            // 设置集成方式
            const integrationType = settings.integrationType || 'script';
            document.querySelector(`input[name="integration_type"][value="${integrationType}"]`).checked = true;
        }
        
        // 表单提交
        document.getElementById('chatbotSettingsForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            // 获取表单数据
            const enabled = document.getElementById('enableChatbot').checked;
            const token = document.getElementById('chatbotToken').value;
            const server = document.getElementById('chatbotServer').value;
            const color = document.getElementById('chatbotColor').value;
            const integrationType = document.querySelector('input[name="integration_type"]:checked').value;
            
            // 保存到本地存储
            const settings = {
                enabled: enabled,
                token: token,
                server: server,
                color: color,
                integrationType: integrationType
            };
            
            localStorage.setItem('chatbotSettings', JSON.stringify(settings));
            
            // 提示保存成功
            alert('聊天机器人设置已保存！请刷新页面以应用更改。');
        });
        
        // AI数据库查询设置
        // 元素引用
        const saveBtn = document.getElementById('saveSettingsBtn');
        const testConnectionBtn = document.getElementById('testConnectionBtn');
        const testMySQLBtn = document.getElementById('testMySQLBtn');
        const checkDbStatusBtn = document.getElementById('checkDbStatusBtn');
        const resetPromptBtn = document.getElementById('resetPromptBtn');
        const temperature = document.getElementById('temperature');
        const temperatureValue = document.getElementById('temperatureValue');
        const saveToast = new bootstrap.Toast(document.getElementById('saveToast'));
        const testConnectionModal = new bootstrap.Modal(document.getElementById('testConnectionModal'));
        const dbStatusIndicator = document.getElementById('dbStatusIndicator');
        const mysqlStatusIndicator = document.getElementById('mysqlStatusIndicator');
        const dbTypeSQLite = document.getElementById('dbTypeSQLite');
        const dbTypeMySQL = document.getElementById('dbTypeMySQL');
        const sqliteSettings = document.getElementById('sqliteSettings');
        const mysqlSettings = document.getElementById('mysqlSettings');
        const currentDbType = document.getElementById('currentDbType');
        
        // 默认提示词
        const defaultPrompt = "你是车规芯片终测智能调度平台的AI助手，专注于帮助用户解决生产排程、订单管理、资源分配等方面的问题。\n你具有查询数据库的能力，可以基于数据库中的实时数据回答用户问题。\n\n请遵循以下规则:\n1. 仅基于提供的数据库信息回答问题，不要编造不存在的数据\n2. 如果数据库中没有足够信息回答某个问题，明确告知用户\n3. 对于数据库中的数字和状态信息，尽可能给出简洁明了的解释\n4. 使用表格或列表格式组织信息，使回答更清晰易读";
        
        // 加载AI设置
        function loadAISettings() {
            // 加载AI设置
            fetch('/api/ai-settings')
                .then(response => response.json())
                .then(settings => {
                    // 填充表单
                    if (settings.database) {
                        document.getElementById('enableDatabase').checked = settings.database.enabled;
                        const databasePathEl = document.getElementById('databasePath');
                        const prioritizeDatabaseEl = document.getElementById('prioritizeDatabase');
                        
                        if (databasePathEl) databasePathEl.value = settings.database.auto_db_path;
                        if (prioritizeDatabaseEl) prioritizeDatabaseEl.checked = settings.database.prioritize_database;
                        
                        // 数据库类型
                        if (settings.database.type === 'mysql') {
                            dbTypeMySQL.checked = true;
                            sqliteSettings.style.display = 'none';
                            mysqlSettings.style.display = 'block';
                            
                            // 填充MySQL设置
                            if (settings.database.mysql) {
                                const mysqlHostEl = document.getElementById('mysqlHost');
                                const mysqlPortEl = document.getElementById('mysqlPort');
                                const mysqlUserEl = document.getElementById('mysqlUser');
                                const mysqlPasswordEl = document.getElementById('mysqlPassword');
                                const mysqlDatabaseEl = document.getElementById('mysqlDatabase');
                                const mysqlCharsetEl = document.getElementById('mysqlCharset');
                                
                                if (mysqlHostEl) mysqlHostEl.value = settings.database.mysql.host || '';
                                if (mysqlPortEl) mysqlPortEl.value = settings.database.mysql.port || 3306;
                                if (mysqlUserEl) mysqlUserEl.value = settings.database.mysql.user || '';
                                if (mysqlPasswordEl) mysqlPasswordEl.value = settings.database.mysql.password || '';
                                if (mysqlDatabaseEl) mysqlDatabaseEl.value = settings.database.mysql.database || 'aps';
                                if (mysqlCharsetEl) mysqlCharsetEl.value = settings.database.mysql.charset || 'utf8mb4';
                            }
                        } else {
                            dbTypeSQLite.checked = true;
                            sqliteSettings.style.display = 'block';
                            mysqlSettings.style.display = 'none';
                        }
                    }
                    
                    if (settings.model) {
                        const temperatureEl = document.getElementById('temperature');
                        const maxTokensEl = document.getElementById('maxTokens');
                        const topPEl = document.getElementById('topP');
                        const frequencyPenaltyEl = document.getElementById('frequencyPenalty');
                        const presencePenaltyEl = document.getElementById('presencePenalty');
                        
                        if (temperatureEl) temperatureEl.value = settings.model.temperature;
                        if (maxTokensEl) maxTokensEl.value = settings.model.max_tokens;
                        if (topPEl) topPEl.value = settings.model.top_p;
                        if (frequencyPenaltyEl) frequencyPenaltyEl.value = settings.model.frequency_penalty;
                        if (presencePenaltyEl) presencePenaltyEl.value = settings.model.presence_penalty;
                    }
                    
                    // 更新温度显示
                    updateTemperatureDisplay();
                })
                .catch(error => {
                    console.error('加载AI设置失败:', error);
                });
        }
        
        // 加载AI设置
        loadAISettings();
        
        // 检查当前数据库状态
        checkDatabaseStatus();
        
        // 温度滑块值更新
        temperature.addEventListener('input', function() {
            temperatureValue.textContent = this.value;
        });
        
        // 数据库类型切换
        dbTypeSQLite.addEventListener('change', function() {
            if (this.checked) {
                sqliteSettings.style.display = 'block';
                mysqlSettings.style.display = 'none';
            }
        });
        
        dbTypeMySQL.addEventListener('change', function() {
            if (this.checked) {
                sqliteSettings.style.display = 'none';
                mysqlSettings.style.display = 'block';
            }
        });
        
        // AI设置保存
        saveBtn.addEventListener('click', function() {
            saveAISettings();
        });
        
        // 测试SQLite数据库连接
        testConnectionBtn.addEventListener('click', function() {
            testAllDatabaseConnections();
        });
        
        // 测试MySQL数据库连接
        testMySQLBtn.addEventListener('click', function() {
            testMySQLConnection();
        });
        
        // 检查数据库状态
        checkDbStatusBtn.addEventListener('click', function() {
            checkDatabaseStatus();
        });
        
        // 恢复默认提示词
        resetPromptBtn.addEventListener('click', function() {
            const systemPromptEl = document.getElementById('systemPrompt');
            if (systemPromptEl) systemPromptEl.value = defaultPrompt;
        });
        
        // 获取AI设置表单数据
        function getAIFormData() {
            // 构建嵌套对象
            const data = {
                database: {
                    enabled: document.getElementById('enableDatabase').checked,
                    auto_db_path: document.getElementById('databasePath').value,
                    prioritize_database: document.getElementById('prioritizeDatabase').checked,
                    type: document.getElementById('dbTypeMySQL').checked ? 'mysql' : 'sqlite',
                    model: {
                        temperature: parseFloat(document.getElementById('temperature').value),
                        max_tokens: parseInt(document.getElementById('maxTokens').value)
                    },
                    system_prompt: document.getElementById('systemPrompt') ? document.getElementById('systemPrompt').value : ''
                }
            };
            
            // 如果是MySQL，添加MySQL配置
            if (data.database.type === 'mysql') {
                data.database.mysql = {
                    host: document.getElementById('mysqlHost').value,
                    port: parseInt(document.getElementById('mysqlPort').value),
                    user: document.getElementById('mysqlUser').value,
                    password: document.getElementById('mysqlPassword').value,
                    database: document.getElementById('mysqlDatabase').value
                };
            }
            
            return data;
        }
        
        // 保存AI设置
        function saveAISettings() {
            const settings = getAIFormData();
            
            // 保存AI设置到SQLite数据库
            fetch('/api/ai-settings', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(settings)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 显示成功提示
                    saveToast.show();
                    
                    // 检查数据库状态
                    setTimeout(checkDatabaseStatus, 1000);
                } else {
                    alert('保存AI设置失败: ' + data.message);
                }
            })
            .catch(error => {
                console.error('保存设置失败:', error);
                alert('保存设置失败: ' + error.message);
            });
        }
        
        // 测试所有数据库连接
        function testAllDatabaseConnections() {
            // 显示模态框
            testConnectionModal.show();
            const resultContainer = document.getElementById('connectionTestResult');
            
            // 显示加载中
            resultContainer.innerHTML = `
                <div class="text-center p-3">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">正在测试...</span>
                    </div>
                    <p class="mt-2">正在测试所有数据库连接，请稍候...</p>
                </div>
            `;
            
            // 显示加载状态
            updateDbStatusIndicator('loading');
            
            // 获取当前表单中的MySQL配置
            const mysql_settings = {
                host: document.getElementById('mysqlHost').value,
                port: document.getElementById('mysqlPort').value,
                user: document.getElementById('mysqlUser').value,
                password: document.getElementById('mysqlPassword').value,
                database: document.getElementById('mysqlDatabase').value,
                charset: 'utf8mb4'
            };
            
            // 发送测试请求
            fetch('/api/test-all-database-connections', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    mysql_settings: mysql_settings
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 连接成功，显示所有数据库的状态
                    let resultHTML = `
                        <div class="alert alert-success mb-3">
                            <i class="fas fa-check-circle me-2"></i>
                            <strong>测试完成!</strong>
                        </div>
                    `;
                    
                    // 遍历所有数据库结果
                    data.databases.forEach(db => {
                        if (db.status === 'connected') {
                            resultHTML += `
                                <div class="card mb-3">
                                    <div class="card-header bg-success text-white">
                                        <i class="fas fa-check-circle me-2"></i>
                                        ${db.name} (${db.type}) - 连接成功
                                    </div>
                                    <div class="card-body">
                                        <p><strong>数据库类型:</strong> ${db.type}</p>
                            `;
                            
                            // 根据数据库类型显示不同信息
                            if (db.type === 'SQLite') {
                                resultHTML += `
                                    <p><strong>路径:</strong> ${db.path}</p>
                                    <p><strong>表数量:</strong> ${db.table_count}</p>
                                `;
                            } else if (db.type === 'MySQL') {
                                resultHTML += `
                                    <p><strong>主机:</strong> ${db.host}:${db.port}</p>
                                    <p><strong>版本:</strong> ${db.version || '未知'}</p>
                                    <p><strong>表数量:</strong> ${db.table_count}</p>
                                `;
                            }
                            
                            // 如果有表信息，显示表列表
                            if (db.tables && db.tables.length > 0) {
                                resultHTML += `
                                    <div class="mt-3">
                                        <p><strong>数据表列表:</strong></p>
                                        <ul class="list-group small" style="max-height: 200px; overflow-y: auto;">
                                `;
                                
                                db.tables.forEach(table => {
                                    resultHTML += `<li class="list-group-item">${table}</li>`;
                                });
                                
                                resultHTML += `
                                        </ul>
                                    </div>
                                `;
                            }
                            
                            resultHTML += `
                                    </div>
                                </div>
                            `;
                        } else {
                            // 连接失败的数据库
                            resultHTML += `
                                <div class="card mb-3">
                                    <div class="card-header bg-danger text-white">
                                        <i class="fas fa-times-circle me-2"></i>
                                        ${db.name} (${db.type}) - 连接失败
                                    </div>
                                    <div class="card-body">
                                        <p><strong>数据库类型:</strong> ${db.type}</p>
                            `;
                            
                            // 根据数据库类型显示不同信息
                            if (db.type === 'SQLite') {
                                resultHTML += `<p><strong>路径:</strong> ${db.path}</p>`;
                            } else if (db.type === 'MySQL') {
                                resultHTML += `<p><strong>主机:</strong> ${db.host}:${db.port}</p>`;
                            }
                            
                            resultHTML += `
                                        <p class="text-danger"><strong>错误信息:</strong> ${db.error || '未知错误'}</p>
                                    </div>
                                </div>
                            `;
                        }
                    });
                    
                    resultContainer.innerHTML = resultHTML;
                    
                    // 更新状态指示器
                    updateDbStatusIndicator('success');
                } else {
                    // 测试失败
                    resultContainer.innerHTML = `
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>测试失败!</strong>
                            <p class="mb-0 mt-2">${data.error || '无法连接到数据库服务。'}</p>
                        </div>
                    `;
                    
                    // 更新状态指示器
                    updateDbStatusIndicator('error');
                }
            })
            .catch(error => {
                console.error('测试数据库连接失败:', error);
                resultContainer.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>测试失败!</strong>
                        <p class="mb-0 mt-2">请求出错: ${error.message}</p>
                    </div>
                `;
                
                // 更新状态指示器
                updateDbStatusIndicator('error');
            });
        }
        
        // 测试MySQL数据库连接
        function testMySQLConnection() {
            const mysqlConfig = {
                host: document.getElementById('mysqlHost').value,
                port: parseInt(document.getElementById('mysqlPort').value),
                user: document.getElementById('mysqlUser').value,
                password: document.getElementById('mysqlPassword').value,
                database: document.getElementById('mysqlDatabase').value
            };
            
            // 显示加载状态
            updateMySQLStatusIndicator('loading');
            
            // 发送测试请求
            fetch('/api/test-database-connection', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    type: 'mysql',
                    config: mysqlConfig
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 连接成功
                    updateMySQLStatusIndicator('success');
                    alert('MySQL连接成功！' + (data.message || ''));
                } else {
                    // 连接失败
                    updateMySQLStatusIndicator('error');
                    alert('MySQL连接失败: ' + (data.error || '无法连接到数据库，请检查配置。'));
                }
            })
            .catch(error => {
                console.error('测试MySQL连接失败:', error);
                updateMySQLStatusIndicator('error');
                alert('请求出错: ' + error.message);
            });
        }
        
        // 检查当前数据库状态
        function checkDatabaseStatus() {
            // 获取当前表单中的MySQL配置
            const mysql_settings = {
                host: document.getElementById('mysqlHost').value,
                port: document.getElementById('mysqlPort').value,
                user: document.getElementById('mysqlUser').value,
                password: document.getElementById('mysqlPassword').value,
                database: document.getElementById('mysqlDatabase').value,
                charset: 'utf8mb4'
            };
            
            fetch('/api/database-status', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    mysql_settings: mysql_settings
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 更新当前数据库类型显示
                    const currentType = data.current_type || 'sqlite';
                    currentDbType.innerHTML = `
                        <span class="badge bg-success">已连接</span> 
                        ${currentType.toUpperCase()} 
                        ${data.message ? `(${data.message})` : ''}
                    `;
                    
                    // 如果有数据库详情，显示详情区域
                    if (data.databases && data.databases.length > 0) {
                        const statusList = document.getElementById('databaseStatusList');
                        statusList.innerHTML = '';
                        
                        // 遍历所有数据库信息
                        data.databases.forEach(db => {
                            const statusClass = db.status === 'connected' ? 'bg-success' : 'bg-danger';
                            const statusText = db.status === 'connected' ? '已连接' : '连接失败';
                            
                            let dbDetails = '';
                            if (db.status === 'connected') {
                                if (db.type === 'SQLite') {
                                    dbDetails = `
                                        <div>路径: ${db.path}</div>
                                        <div>版本: ${db.version || '未知'}</div>
                                        <div>表数量: ${db.table_count || 0}</div>
                                    `;
                                } else if (db.type === 'MySQL') {
                                    dbDetails = `
                                        <div>主机: ${db.host}:${db.port}</div>
                                        <div>版本: ${db.version || '未知'}</div>
                                        <div>表数量: ${db.table_count || 0}</div>
                                    `;
                                }
                            } else {
                                dbDetails = `<div class="text-danger">错误: ${db.error || '未知错误'}</div>`;
                            }
                            
                            const dbItem = document.createElement('div');
                            dbItem.className = 'list-group-item';
                            dbItem.innerHTML = `
                                <div class="d-flex justify-content-between align-items-center">
                                    <strong>${db.name} (${db.type})</strong>
                                    <span class="badge ${statusClass}">${statusText}</span>
                                </div>
                                <div class="mt-2 small">
                                    ${dbDetails}
                                </div>
                            `;
                            
                            statusList.appendChild(dbItem);
                        });
                        
                        // 显示详情区域
                        document.getElementById('databaseStatusDetails').style.display = 'block';
                    } else {
                        document.getElementById('databaseStatusDetails').style.display = 'none';
                    }
                } else {
                    currentDbType.innerHTML = `
                        <span class="badge bg-danger">未连接</span> 
                        ${data.error || '请配置并测试数据库连接'}
                    `;
                    document.getElementById('databaseStatusDetails').style.display = 'none';
                }
            })
            .catch(error => {
                console.error('检查数据库状态失败:', error);
                currentDbType.innerHTML = `
                    <span class="badge bg-warning">未知</span> 
                    无法获取数据库状态
                `;
                document.getElementById('databaseStatusDetails').style.display = 'none';
            });
        }
        
        // 更新数据库状态指示器
        function updateDbStatusIndicator(status) {
            // 重置所有状态
            dbStatusIndicator.querySelector('.fa-circle-notch').style.display = 'none';
            dbStatusIndicator.querySelector('.fa-check').style.display = 'none';
            dbStatusIndicator.querySelector('.fa-times').style.display = 'none';
            
            // 设置当前状态
            if (status === 'loading') {
                dbStatusIndicator.querySelector('.fa-circle-notch').style.display = 'inline-block';
            } else if (status === 'success') {
                dbStatusIndicator.querySelector('.fa-check').style.display = 'inline-block';
            } else if (status === 'error') {
                dbStatusIndicator.querySelector('.fa-times').style.display = 'inline-block';
            }
        }
        
        // 更新MySQL状态指示器
        function updateMySQLStatusIndicator(status) {
            // 重置所有状态
            mysqlStatusIndicator.querySelector('.fa-circle-notch').style.display = 'none';
            mysqlStatusIndicator.querySelector('.fa-check').style.display = 'none';
            mysqlStatusIndicator.querySelector('.fa-times').style.display = 'none';
            
            // 设置当前状态
            if (status === 'loading') {
                mysqlStatusIndicator.querySelector('.fa-circle-notch').style.display = 'inline-block';
            } else if (status === 'success') {
                mysqlStatusIndicator.querySelector('.fa-check').style.display = 'inline-block';
            } else if (status === 'error') {
                mysqlStatusIndicator.querySelector('.fa-times').style.display = 'inline-block';
            }
        }
        
        // =============================================================================
        // 数据同步设置相关功能
        // =============================================================================
        
        // 元素引用
        const saveSyncSettingsBtn = document.getElementById('saveSyncSettingsBtn');
        const resetSyncSettingsBtn = document.getElementById('resetSyncSettingsBtn');
        const triggerSyncBtn = document.getElementById('triggerSyncBtn');
        const viewSyncLogBtn = document.getElementById('viewSyncLogBtn');
        const resolveConflictsBtn = document.getElementById('resolveConflictsBtn');
        const enableSync = document.getElementById('enableSync');
        const syncMethodRadios = document.querySelectorAll('input[name="sync_method"]');
        const syncIntervalSettings = document.getElementById('syncIntervalSettings');
        
        // 加载数据同步配置
        function loadSyncConfig() {
            fetch('/api/sync/config')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const config = data.config;
                        
                        // 更新表单值
                        enableSync.checked = config.sync_enabled === 'true';
                        document.getElementById('syncInterval').value = config.sync_interval || '5';
                        
                        // 同步方式
                        const syncMethod = config.sync_method || 'timer';
                        document.querySelector(`input[name="sync_method"][value="${syncMethod}"]`).checked = true;
                        
                        // 冲突策略
                        const conflictStrategy = config.conflict_strategy || 'excel_priority';
                        document.querySelector(`input[name="conflict_strategy"][value="${conflictStrategy}"]`).checked = true;
                        
                        // 更新界面显示
                        updateSyncIntervalVisibility();
                        updateSyncStatus();
                    }
                })
                .catch(error => {
                    console.error('加载同步配置失败:', error);
                });
        }
        
        // 更新同步状态显示
        function updateSyncStatus() {
            fetch('/api/sync/status')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const status = data.status;
                        
                        // 更新状态图标和文字
                        const statusIcon = document.getElementById('syncStatusIcon');
                        const statusText = document.getElementById('syncStatusText');
                        
                        if (status.sync_enabled && status.sync_running) {
                            statusIcon.className = 'fas fa-circle me-2 text-success';
                            statusText.textContent = '运行中';
                        } else if (status.sync_enabled) {
                            statusIcon.className = 'fas fa-circle me-2 text-warning';
                            statusText.textContent = '已启用(未运行)';
                        } else {
                            statusIcon.className = 'fas fa-circle me-2 text-muted';
                            statusText.textContent = '未启用';
                        }
                        
                        // 更新最后同步时间
                        const lastSyncTime = document.getElementById('lastSyncTime');
                        if (status.recent_logs && status.recent_logs.length > 0) {
                            const latestLog = status.recent_logs[0];
                            lastSyncTime.textContent = formatDateTime(latestLog.sync_time);
                        } else {
                            lastSyncTime.textContent = '从未同步';
                        }
                        
                        // 更新冲突数量
                        const conflictCount = document.getElementById('conflictCount');
                        const totalConflicts = Object.values(status.conflicts || {}).reduce((sum, count) => sum + count, 0);
                        conflictCount.textContent = totalConflicts;
                        
                        // 更新表同步配置
                        updateTableSyncConfig(status);
                    }
                })
                .catch(error => {
                    console.error('获取同步状态失败:', error);
                });
        }
        
        // 更新表同步配置显示
        function updateTableSyncConfig(status) {
            const tableConfigBody = document.getElementById('tableSyncConfig');
            
            const tables = ['eqp_status', 'et_ft_test_spec', 'ET_UPH_EQP', 'TCC_INV', 'CT'];
            
            tableConfigBody.innerHTML = '';
            
            tables.forEach(tableName => {
                const row = document.createElement('tr');
                
                // 最后同步时间
                let lastSync = '从未同步';
                let syncStatus = '未知';
                
                if (status.recent_logs) {
                    const tableLog = status.recent_logs.find(log => log.table_name === tableName);
                    if (tableLog) {
                        lastSync = formatDateTime(tableLog.sync_time);
                        syncStatus = tableLog.status === 'success' ? '成功' : '失败';
                    }
                }
                
                // 冲突数量
                const conflicts = status.conflicts && status.conflicts[tableName] ? status.conflicts[tableName] : 0;
                
                row.innerHTML = `
                    <td>${tableName}</td>
                    <td>
                        <div class="form-check form-switch">
                            <input class="form-check-input table-sync-toggle" type="checkbox" 
                                   data-table="${tableName}" checked>
                        </div>
                    </td>
                    <td><small>${lastSync}</small></td>
                    <td>
                        <span class="badge ${syncStatus === '成功' ? 'bg-success' : syncStatus === '失败' ? 'bg-danger' : 'bg-secondary'}">${syncStatus}</span>
                        ${conflicts > 0 ? `<span class="badge bg-warning ms-1">${conflicts}冲突</span>` : ''}
                    </td>
                    <td>
                        <button type="button" class="btn btn-sm btn-outline-primary sync-table-btn" 
                                data-table="${tableName}">
                            <i class="fas fa-sync-alt"></i>
                        </button>
                    </td>
                `;
                
                tableConfigBody.appendChild(row);
            });
            
            // 绑定单表同步按钮事件
            document.querySelectorAll('.sync-table-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const tableName = this.dataset.table;
                    triggerTableSync(tableName);
                });
            });
        }
        
        // 保存同步设置
        function saveSyncSettings() {
            const config = {
                sync_enabled: enableSync.checked ? 'true' : 'false',
                sync_method: document.querySelector('input[name="sync_method"]:checked').value,
                sync_interval: document.getElementById('syncInterval').value,
                conflict_strategy: document.querySelector('input[name="conflict_strategy"]:checked').value
            };
            
            fetch('/api/sync/config', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    config: config,
                    user_id: 'admin'  // TODO: 从登录状态获取真实用户ID
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 显示成功提示
                    saveToast.show();
                    
                    // 更新状态显示
                    setTimeout(() => {
                        updateSyncStatus();
                    }, 1000);
                } else {
                    alert('保存同步设置失败: ' + data.error);
                }
            })
            .catch(error => {
                console.error('保存同步设置失败:', error);
                alert('保存同步设置失败: ' + error.message);
            });
        }
        
        // 手动触发同步
        function triggerManualSync() {
            // 更新按钮状态
            triggerSyncBtn.disabled = true;
            triggerSyncBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>同步中...';
            
            fetch('/api/sync/trigger', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    user_id: 'admin'  // TODO: 从登录状态获取真实用户ID
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('手动同步完成！\n\n同步结果:\n' + formatSyncResult(data.result));
                    
                    // 更新状态显示
                    updateSyncStatus();
                } else {
                    alert('手动同步失败: ' + data.error);
                }
            })
            .catch(error => {
                console.error('手动同步失败:', error);
                alert('手动同步失败: ' + error.message);
            })
            .finally(() => {
                // 恢复按钮状态
                triggerSyncBtn.disabled = false;
                triggerSyncBtn.innerHTML = '<i class="fas fa-sync-alt me-1"></i>立即同步';
            });
        }
        
        // 触发单表同步
        function triggerTableSync(tableName) {
            const btn = document.querySelector(`.sync-table-btn[data-table="${tableName}"]`);
            
            // 更新按钮状态
            btn.disabled = true;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
            
            fetch('/api/sync/trigger', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    table_name: tableName,
                    user_id: 'admin'
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(`表 ${tableName} 同步完成！\n\n同步结果:\n${formatSyncResult(data.result)}`);
                    
                    // 更新状态显示
                    updateSyncStatus();
                } else {
                    alert(`表 ${tableName} 同步失败: ${data.error}`);
                }
            })
            .catch(error => {
                console.error('单表同步失败:', error);
                alert(`表 ${tableName} 同步失败: ${error.message}`);
            })
            .finally(() => {
                // 恢复按钮状态
                btn.disabled = false;
                btn.innerHTML = '<i class="fas fa-sync-alt"></i>';
            });
        }
        
        // 格式化同步结果
        function formatSyncResult(result) {
            if (typeof result === 'object') {
                let formatted = '';
                Object.keys(result).forEach(table => {
                    const tableResult = result[table];
                    if (tableResult.error) {
                        formatted += `${table}: 错误 - ${tableResult.error}\n`;
                    } else {
                        formatted += `${table}: 插入${tableResult.inserted || 0}条, 更新${tableResult.updated || 0}条, 冲突${tableResult.conflicts || 0}条\n`;
                    }
                });
                return formatted;
            }
            return JSON.stringify(result, null, 2);
        }
        
        // 格式化日期时间
        function formatDateTime(dateString) {
            if (!dateString) return '从未同步';
            
            const date = new Date(dateString);
            return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
        }
        
        // 更新同步间隔可见性
        function updateSyncIntervalVisibility() {
            const syncMethod = document.querySelector('input[name="sync_method"]:checked').value;
            syncIntervalSettings.style.display = (syncMethod === 'timer') ? 'block' : 'none';
        }
        
        // 绑定事件监听器
        saveSyncSettingsBtn.addEventListener('click', saveSyncSettings);
        triggerSyncBtn.addEventListener('click', triggerManualSync);
        
        // 同步方式改变时，更新间隔设置可见性
        syncMethodRadios.forEach(radio => {
            radio.addEventListener('change', updateSyncIntervalVisibility);
        });
        
        // 重置同步设置
        resetSyncSettingsBtn.addEventListener('click', function() {
            if (confirm('确定要重置为默认同步设置吗？')) {
                enableSync.checked = false;
                document.querySelector('input[name="sync_method"][value="timer"]').checked = true;
                document.getElementById('syncInterval').value = '5';
                document.querySelector('input[name="conflict_strategy"][value="excel_priority"]').checked = true;
                
                updateSyncIntervalVisibility();
            }
        });
        
        // 查看同步日志（简单实现）
        viewSyncLogBtn.addEventListener('click', function() {
            // 打开新窗口显示同步日志
            const logWindow = window.open('/api/sync/logs?per_page=50', '_blank', 'width=800,height=600');
            if (!logWindow) {
                alert('无法打开日志窗口，请检查浏览器弹窗拦截设置');
            }
        });
        
        // 解决冲突（简单实现）
        resolveConflictsBtn.addEventListener('click', function() {
            fetch('/api/sync/conflicts')
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.conflicts.length > 0) {
                        let message = `发现 ${data.conflicts.length} 个数据冲突：\n\n`;
                        data.conflicts.forEach((conflict, index) => {
                            message += `${index + 1}. 表：${conflict.table_name}, 记录ID：${conflict.record_id}\n`;
                        });
                        message += '\n点击确定将自动保留Excel数据，点击取消手动处理';
                        
                        if (confirm(message)) {
                            // 批量解决冲突 - 保留Excel数据
                            const promises = data.conflicts.map(conflict => 
                                fetch(`/api/sync/conflicts/${conflict.id}/resolve`, {
                                    method: 'POST',
                                    headers: { 'Content-Type': 'application/json' },
                                    body: JSON.stringify({
                                        resolution: 'keep_excel',
                                        user_id: 'admin'
                                    })
                                })
                            );
                            
                            Promise.all(promises)
                                .then(() => {
                                    alert('所有冲突已解决！');
                                    updateSyncStatus();
                                })
                                .catch(error => {
                                    alert('解决冲突时出错: ' + error.message);
                                });
                        }
                    } else {
                        alert('当前没有待处理的数据冲突');
                    }
                })
                .catch(error => {
                    console.error('获取冲突列表失败:', error);
                    alert('获取冲突列表失败: ' + error.message);
                });
        });
        
        // 页面加载时初始化数据同步配置
        loadSyncConfig();
        
        // =============================================================================
        // 表映射配置管理功能
        // =============================================================================
        
        // 元素引用
        const addTableMappingBtn = document.getElementById('addTableMappingBtn');
        const tableMappingModal = new bootstrap.Modal(document.getElementById('tableMappingModal'));
        const tableMappingForm = document.getElementById('tableMappingForm');
        const saveTableMappingBtn = document.getElementById('saveTableMappingBtn');
        const tableMappingConfig = document.getElementById('tableMappingConfig');
        const refreshTablesBtn = document.getElementById('refreshTablesBtn');
        const sqliteTableSelect = document.getElementById('sqliteTableSelect');
        const mysqlTableSelect = document.getElementById('mysqlTableSelect');
        const primaryKeySelect = document.getElementById('primaryKey');
        
        // 存储可用表的数据
        let availableTables = {
            sqlite_tables: [],
            mysql_tables: []
        };
        
        // 加载表映射配置
        function loadTableMappingConfig() {
            fetch('/api/sync/table-mapping')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        updateTableMappingDisplay(data.data.table_mappings || []);
                    } else {
                        console.error('加载表映射配置失败:', data.error);
                        tableMappingConfig.innerHTML = '<tr><td colspan="7" class="text-center text-danger">加载配置失败</td></tr>';
                    }
                })
                .catch(error => {
                    console.error('加载表映射配置失败:', error);
                    tableMappingConfig.innerHTML = '<tr><td colspan="7" class="text-center text-danger">网络错误</td></tr>';
                });
        }
        
                // 更新表映射配置显示
        function updateTableMappingDisplay(mappings) {
            if (mappings.length === 0) {
                tableMappingConfig.innerHTML = '<tr><td colspan="7" class="text-center text-muted">暂无表映射配置</td></tr>';
                return;
            }
            
            tableMappingConfig.innerHTML = mappings.map((mapping, index) => `
                <tr>
                    <td>${mapping.sqlite_table}</td>
                    <td>${mapping.mysql_table}</td>
                    <td>
                        <div class="form-check form-switch">
                            <input class="form-check-input table-mapping-toggle" type="checkbox" ${mapping.enabled ? 'checked' : ''} 
                                   data-sqlite-table="${mapping.sqlite_table}">
                        </div>
                    </td>
                    <td>
                        <span class="badge ${getSyncStrategyBadgeClass(mapping.sync_strategy)}">
                            ${getSyncStrategyText(mapping.sync_strategy)}
                        </span>
                    </td>
                    <td><code>${mapping.primary_key || 'id'}</code></td>
                    <td>
                        <span class="text-muted" title="${mapping.description || ''}">
                            ${truncateText(mapping.description || '无描述', 20)}
                        </span>
                    </td>
                    <td>
                        <button type="button" class="btn btn-sm btn-outline-primary me-1 edit-mapping-btn" 
                                data-mapping-id="${mapping.id}" title="编辑">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-danger delete-mapping-btn" 
                                data-sqlite-table="${mapping.sqlite_table}" title="删除">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                </tr>
            `).join('');
            
            // 绑定事件监听器
            bindTableMappingEvents();
        }
        
        // 绑定表映射相关的事件监听器
        function bindTableMappingEvents() {
            // 绑定开关切换事件
            document.querySelectorAll('.table-mapping-toggle').forEach(toggle => {
                toggle.addEventListener('change', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    const sqliteTable = this.dataset.sqliteTable;
                    const enabled = this.checked;
                    toggleTableMapping(sqliteTable, enabled);
                });
            });
            
            // 绑定编辑按钮事件
            document.querySelectorAll('.edit-mapping-btn').forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    const mappingId = parseInt(this.dataset.mappingId);
                    editTableMapping(mappingId);
                });
            });
            
            // 绑定删除按钮事件
            document.querySelectorAll('.delete-mapping-btn').forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    const sqliteTable = this.dataset.sqliteTable;
                    deleteTableMapping(sqliteTable);
                });
            });
        }
        
        // 获取同步策略的徽章样式
        function getSyncStrategyBadgeClass(strategy) {
            switch (strategy) {
                case 'full_sync': return 'bg-primary';
                case 'incremental': return 'bg-success';
                case 'timestamp_based': return 'bg-warning';
                default: return 'bg-secondary';
            }
        }
        
        // 获取同步策略的显示文本
        function getSyncStrategyText(strategy) {
            switch (strategy) {
                case 'full_sync': return '全量同步';
                case 'incremental': return '增量同步';
                case 'timestamp_based': return '时间戳';
                default: return strategy;
            }
        }
        
        // 截断文本
        function truncateText(text, maxLength) {
            if (text.length <= maxLength) return text;
            return text.substring(0, maxLength) + '...';
        }
        
        // 切换表映射启用状态
        function toggleTableMapping(sqliteTable, enabled) {
            fetch('/api/sync/table-mapping', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    config: {
                        table_mappings: [{
                            sqlite_table: sqliteTable,
                            enabled: enabled
                        }]
                    },
                    user_id: 'admin'
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 成功时显示简单的成功提示
                    console.log(`表映射 ${sqliteTable} ${enabled ? '已启用' : '已禁用'}`);
                    // 可以在这里添加成功的视觉反馈
                    showTemporaryMessage(`表映射 ${sqliteTable} ${enabled ? '已启用' : '已禁用'}`, 'success');
                } else {
                    alert('更新表映射状态失败: ' + data.error);
                    // 恢复开关状态
                    const checkbox = document.querySelector(`input[data-sqlite-table="${sqliteTable}"]`);
                    if (checkbox) checkbox.checked = !enabled;
                }
            })
            .catch(error => {
                console.error('更新表映射状态失败:', error);
                alert('更新表映射状态失败: ' + error.message);
                // 恢复开关状态
                const checkbox = document.querySelector(`input[data-sqlite-table="${sqliteTable}"]`);
                if (checkbox) checkbox.checked = !enabled;
            });
        }
        
        // 显示临时消息
        function showTemporaryMessage(message, type = 'info') {
            // 创建临时提示元素
            const alertDiv = document.createElement('div');
            
            // 根据类型设置样式
            let alertClass = 'alert-info';
            let iconClass = 'fas fa-info-circle';
            
            switch (type) {
                case 'success':
                    alertClass = 'alert-success';
                    iconClass = 'fas fa-check-circle';
                    break;
                case 'warning':
                    alertClass = 'alert-warning';
                    iconClass = 'fas fa-exclamation-triangle';
                    break;
                case 'error':
                case 'danger':
                    alertClass = 'alert-danger';
                    iconClass = 'fas fa-times-circle';
                    break;
                default:
                    alertClass = 'alert-info';
                    iconClass = 'fas fa-info-circle';
            }
            
            alertDiv.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
            alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px; max-width: 400px;';
            alertDiv.innerHTML = `
                <i class="${iconClass} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            document.body.appendChild(alertDiv);
            
            // 根据消息类型设置不同的消失时间
            const timeout = type === 'error' || type === 'danger' ? 5000 : 3000;
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.parentNode.removeChild(alertDiv);
                }
            }, timeout);
        }
        
        // 编辑表映射
        function editTableMapping(mappingId) {
            // 根据ID查找映射配置
            fetch('/api/sync/table-mapping')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const mapping = data.data.table_mappings.find(m => m.id === mappingId);
                        if (mapping) {
                            // 首先加载可用表列表，然后填充表单
                            loadAvailableTables().then(() => {
                                // 填充表单
                                document.getElementById('mappingId').value = mapping.id;
                                sqliteTableSelect.value = mapping.sqlite_table;
                                mysqlTableSelect.value = mapping.mysql_table;
                                document.getElementById('mappingEnabled').checked = mapping.enabled;
                                document.getElementById('syncStrategy').value = mapping.sync_strategy || 'full_sync';
                                document.getElementById('mappingDescription').value = mapping.description || '';
                                
                                // 更新模态框标题
                                document.getElementById('tableMappingModalLabel').textContent = '编辑表映射';
                                
                                // 禁用SQLite表名选择器（因为它作为主键不应该被修改）
                                sqliteTableSelect.disabled = true;
                                
                                // 根据选择的表更新主键选项
                                if (mapping.sqlite_table) {
                                    updatePrimaryKeyOptions('sqlite', mapping.sqlite_table);
                                    // 设置主键值（需要在加载主键选项后设置）
                                    setTimeout(() => {
                                        primaryKeySelect.value = mapping.primary_key || 'id';
                                    }, 500);
                                }
                                
                                // 显示模态框
                                tableMappingModal.show();
                            });
                        }
                    }
                })
                .catch(error => {
                    console.error('获取表映射配置失败:', error);
                    alert('获取表映射配置失败: ' + error.message);
                });
        }
        
        // 删除表映射
        function deleteTableMapping(sqliteTable) {
            if (confirm(`确定要删除表映射 "${sqliteTable}" 吗？\n\n删除后将不再同步此表的数据。`)) {
                fetch(`/api/sync/table-mapping/${sqliteTable}`, {
                    method: 'DELETE'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('表映射已删除');
                        loadTableMappingConfig(); // 重新加载配置
                    } else {
                        alert('删除表映射失败: ' + data.error);
                    }
                })
                .catch(error => {
                    console.error('删除表映射失败:', error);
                    alert('删除表映射失败: ' + error.message);
                });
            }
        }
        
        // 添加新表映射按钮事件
        addTableMappingBtn.addEventListener('click', function() {
            // 首先加载可用表列表
            loadAvailableTables().then(() => {
                // 重置表单
                tableMappingForm.reset();
                document.getElementById('mappingId').value = '';
                document.getElementById('mappingEnabled').checked = true;
                document.getElementById('syncStrategy').value = 'full_sync';
                
                // 重置选择器
                sqliteTableSelect.selectedIndex = 0;
                mysqlTableSelect.selectedIndex = 0;
                primaryKeySelect.innerHTML = '<option value="id">id (默认)</option>';
                primaryKeySelect.value = 'id';
                
                // 清空信息显示
                document.getElementById('sqliteTableInfo').textContent = '';
                document.getElementById('mysqlTableInfo').textContent = '';
                document.getElementById('primaryKeyInfo').textContent = '基于选择的表自动检测主键字段';
                
                // 更新模态框标题
                document.getElementById('tableMappingModalLabel').textContent = '添加表映射';
                
                // 启用SQLite表名选择器
                sqliteTableSelect.disabled = false;
                
                // 显示模态框
                tableMappingModal.show();
            }).catch(error => {
                alert('加载表列表失败，请稍后重试: ' + error.message);
            });
        });
        
        // 保存表映射按钮事件
        saveTableMappingBtn.addEventListener('click', function() {
            // 手动获取表单数据
            const mappingData = {
                id: document.getElementById('mappingId').value,
                sqlite_table: sqliteTableSelect.value,
                mysql_table: mysqlTableSelect.value,
                enabled: document.getElementById('mappingEnabled').checked,
                sync_strategy: document.getElementById('syncStrategy').value,
                primary_key: primaryKeySelect.value,
                description: document.getElementById('mappingDescription').value
            };
            
            // 验证必填字段
            if (!mappingData.sqlite_table || !mappingData.mysql_table) {
                alert('请选择SQLite表名和MySQL表名');
                return;
            }
            
            const isEdit = !!mappingData.id;
            let apiUrl = '/api/sync/table-mapping';
            let method = 'POST';
            let requestData = {
                config: {
                    table_mappings: [mappingData]
                },
                user_id: 'admin'
            };
            
            if (!isEdit) {
                // 新添加映射，使用add接口
                apiUrl = '/api/sync/table-mapping/add';
                requestData = {
                    ...mappingData,
                    user_id: 'admin'
                };
            }
            
            // 禁用保存按钮
            saveTableMappingBtn.disabled = true;
            saveTableMappingBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>保存中...';
            
            fetch(apiUrl, {
                method: method,
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(requestData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(isEdit ? '表映射已更新' : '表映射已添加');
                    tableMappingModal.hide();
                    loadTableMappingConfig(); // 重新加载配置
                } else {
                    alert((isEdit ? '更新' : '添加') + '表映射失败: ' + data.error);
                }
            })
            .catch(error => {
                console.error((isEdit ? '更新' : '添加') + '表映射失败:', error);
                alert((isEdit ? '更新' : '添加') + '表映射失败: ' + error.message);
            })
            .finally(() => {
                // 恢复保存按钮
                saveTableMappingBtn.disabled = false;
                saveTableMappingBtn.innerHTML = '<i class="fas fa-save me-1"></i>保存';
            });
        });
        
        // 加载可用表列表
        function loadAvailableTables() {
            return fetch('/api/sync/available-tables')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        availableTables = data;
                        updateDatabaseConnectionStatus(data);
                        updateTableSelects(data);
                        return data;
                    } else {
                        throw new Error(data.error || '获取表列表失败');
                    }
                })
                .catch(error => {
                    console.error('加载可用表列表失败:', error);
                    updateDatabaseConnectionStatus({
                        sqlite_status: 'error',
                        mysql_status: 'error',
                        sqlite_error: error.message,
                        mysql_error: error.message
                    });
                    throw error;
                });
        }
        
        // 更新数据库连接状态显示
        function updateDatabaseConnectionStatus(data) {
            const sqliteStatus = document.getElementById('sqliteConnectionStatus');
            const mysqlStatus = document.getElementById('mysqlConnectionStatus');
            const sqliteDbPath = document.getElementById('sqliteDbPath');
            const mysqlDbPath = document.getElementById('mysqlDbPath');
            
            // 更新SQLite状态
            sqliteStatus.className = 'badge me-2';
            if (data.sqlite_status === 'connected') {
                sqliteStatus.classList.add('bg-success');
                sqliteStatus.textContent = `SQLite: 已连接 (${data.sqlite_tables?.length || 0}个表)`;
                sqliteDbPath.textContent = '📁 instance/aps.db';
            } else if (data.sqlite_status === 'file_not_found') {
                sqliteStatus.classList.add('bg-warning');
                sqliteStatus.textContent = 'SQLite: 文件不存在';
                sqliteDbPath.textContent = '❌ instance/aps.db (文件不存在)';
            } else {
                sqliteStatus.classList.add('bg-danger');
                sqliteStatus.textContent = `SQLite: 连接失败`;
                sqliteDbPath.textContent = '⚠️ instance/aps.db (连接失败)';
            }
            
            // 更新MySQL状态
            mysqlStatus.className = 'badge me-2';
            if (data.mysql_status === 'connected') {
                mysqlStatus.classList.add('bg-success');
                mysqlStatus.textContent = `MySQL: 已连接 (${data.mysql_tables?.length || 0}个表)`;
                const dbName = data.mysql_database || 'aps';
                mysqlDbPath.textContent = `📊 数据库: ${dbName}`;
            } else if (data.mysql_status === 'not_configured') {
                mysqlStatus.classList.add('bg-secondary');
                mysqlStatus.textContent = 'MySQL: 未配置';
                mysqlDbPath.textContent = '⚙️ 请先在系统设置中配置MySQL连接';
            } else {
                mysqlStatus.classList.add('bg-danger');
                mysqlStatus.textContent = 'MySQL: 连接失败';
                mysqlDbPath.textContent = '❌ 连接失败，请检查配置';
            }
        }
        
        // 更新表选择下拉框
        function updateTableSelects(data) {
            // 更新SQLite表选择器
            sqliteTableSelect.innerHTML = '<option value="">请选择SQLite表...</option>';
            if (data.sqlite_tables && data.sqlite_tables.length > 0) {
                data.sqlite_tables.forEach(table => {
                    const option = document.createElement('option');
                    option.value = table.name;
                    option.textContent = `${table.name} (${table.row_count}行)`;
                    sqliteTableSelect.appendChild(option);
                });
            }
            
            // 更新MySQL表选择器
            mysqlTableSelect.innerHTML = '<option value="">请选择MySQL表...</option>';
            if (data.mysql_tables && data.mysql_tables.length > 0) {
                data.mysql_tables.forEach(table => {
                    const option = document.createElement('option');
                    option.value = table.name;
                    option.textContent = `${table.name} (${table.row_count}行)`;
                    if (table.comment) {
                        option.textContent += ` - ${table.comment}`;
                    }
                    mysqlTableSelect.appendChild(option);
                });
            }
        }
        
        // 获取表信息并更新主键选择器
        function updatePrimaryKeyOptions(tableType, tableName) {
            if (!tableName) {
                primaryKeySelect.innerHTML = '<option value="id">id (默认)</option>';
                document.getElementById('primaryKeyInfo').textContent = '请先选择表';
                return;
            }
            
            fetch(`/api/sync/table-info/${tableType}/${tableName}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // 更新主键选择器
                        primaryKeySelect.innerHTML = '';
                        
                        let primaryKeyFound = false;
                        data.columns.forEach(column => {
                            const option = document.createElement('option');
                            option.value = column.name;
                            option.textContent = `${column.name} (${column.type})`;
                            
                            if (column.primary_key) {
                                option.textContent += ' [主键]';
                                option.selected = true;
                                primaryKeyFound = true;
                            }
                            
                            primaryKeySelect.appendChild(option);
                        });
                        
                        if (!primaryKeyFound) {
                            // 如果没有找到主键，添加默认选项
                            const defaultOption = document.createElement('option');
                            defaultOption.value = 'id';
                            defaultOption.textContent = 'id (默认)';
                            defaultOption.selected = true;
                            primaryKeySelect.insertBefore(defaultOption, primaryKeySelect.firstChild);
                        }
                        
                        // 更新信息显示
                        const infoText = `${data.columns.length}个字段`;
                        if (tableType === 'sqlite') {
                            document.getElementById('sqliteTableInfo').textContent = infoText;
                        } else {
                            document.getElementById('mysqlTableInfo').textContent = infoText;
                        }
                        document.getElementById('primaryKeyInfo').textContent = primaryKeyFound ? '已自动检测到主键字段' : '未检测到主键，使用默认值';
                    }
                })
                .catch(error => {
                    console.error('获取表信息失败:', error);
                    const errorText = '获取表信息失败';
                    if (tableType === 'sqlite') {
                        document.getElementById('sqliteTableInfo').textContent = errorText;
                    } else {
                        document.getElementById('mysqlTableInfo').textContent = errorText;
                    }
                });
        }
        
        // 绑定表选择器变化事件
        sqliteTableSelect.addEventListener('change', function() {
            if (this.value) {
                updatePrimaryKeyOptions('sqlite', this.value);
            }
        });
        
        mysqlTableSelect.addEventListener('change', function() {
            if (this.value) {
                updatePrimaryKeyOptions('mysql', this.value);
                
                // 自动填充SQLite表名建议
                autoFillSQLiteTableName(this.value);
            }
        });
        
        // 自动填充SQLite表名
        function autoFillSQLiteTableName(mysqlTableName) {
            // 如果SQLite表选择器还没有选择值，则自动填充建议
            if (!sqliteTableSelect.value || sqliteTableSelect.value === '') {
                // 生成建议的SQLite表名（转换为小写，符合SQLite命名习惯）
                let suggestedName = mysqlTableName.toLowerCase();
                
                // 检查建议的表名是否在SQLite表列表中存在
                const sqliteTableExists = availableTables.sqlite_tables?.some(table => 
                    table.name.toLowerCase() === suggestedName
                );
                
                if (sqliteTableExists) {
                    // 如果存在完全匹配的表，直接选择
                    sqliteTableSelect.value = availableTables.sqlite_tables.find(table => 
                        table.name.toLowerCase() === suggestedName
                    ).name;
                    
                    // 触发SQLite表选择器的change事件来更新主键选项
                    sqliteTableSelect.dispatchEvent(new Event('change'));
                    
                    showTemporaryMessage(`已自动选择匹配的SQLite表: ${sqliteTableSelect.value}`, 'success');
                } else {
                    // 如果没有完全匹配，寻找部分匹配
                    const partialMatch = availableTables.sqlite_tables?.find(table => {
                        const tableName = table.name.toLowerCase();
                        const mysqlName = mysqlTableName.toLowerCase();
                        
                        // 检查是否包含相同的关键词
                        return tableName.includes(mysqlName.replace(/[_-]/g, '')) || 
                               mysqlName.includes(tableName.replace(/[_-]/g, ''));
                    });
                    
                    if (partialMatch) {
                        sqliteTableSelect.value = partialMatch.name;
                        sqliteTableSelect.dispatchEvent(new Event('change'));
                        showTemporaryMessage(`已自动选择相似的SQLite表: ${partialMatch.name}`, 'info');
                    } else {
                        // 提示用户手动选择
                        showTemporaryMessage(`未找到匹配的SQLite表，请手动选择`, 'warning');
                        
                        // 高亮SQLite选择器提示用户注意
                        sqliteTableSelect.classList.add('border-warning');
                        setTimeout(() => {
                            sqliteTableSelect.classList.remove('border-warning');
                        }, 3000);
                    }
                }
            }
        }
        
        // 刷新表列表按钮事件
        refreshTablesBtn.addEventListener('click', function() {
            this.disabled = true;
            this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 刷新中...';
            
            loadAvailableTables()
                .finally(() => {
                    this.disabled = false;
                    this.innerHTML = '<i class="fas fa-sync-alt"></i> 刷新';
                });
        });
        
        // 页面加载时初始化表映射配置和可用表列表
        Promise.all([
            loadTableMappingConfig(),
            loadAvailableTables()
        ]).catch(error => {
            console.error('初始化失败:', error);
        });
        
        // ===============================
        // 定时任务管理功能
        // ===============================
        
        // 获取定时任务管理的UI元素
        const enableGlobalScheduler = document.getElementById('enableGlobalScheduler');
        const globalSchedulerStatus = document.getElementById('globalSchedulerStatus');
        const schedulerTaskList = document.getElementById('schedulerTaskList');
        const schedulerLogs = document.getElementById('schedulerLogs');
        const saveSchedulerSettingsBtn = document.getElementById('saveSchedulerSettingsBtn');
        const refreshSchedulerStatusBtn = document.getElementById('refreshSchedulerStatusBtn');
        const restartAllSchedulersBtn = document.getElementById('restartAllSchedulersBtn');
        
        // 加载全局定时任务设置
        function loadGlobalSchedulerConfig() {
            fetch('/api/global-scheduler/config')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        enableGlobalScheduler.checked = data.config.enabled;
                        updateGlobalSchedulerStatus();
                        loadSchedulerTaskStatus();
                    }
                })
                .catch(error => {
                    console.error('加载全局定时任务配置失败:', error);
                });
        }
        
        // 更新全局定时任务状态显示
        function updateGlobalSchedulerStatus() {
            const isEnabled = enableGlobalScheduler.checked;
            globalSchedulerStatus.className = 'status-badge';
            
            if (isEnabled) {
                globalSchedulerStatus.classList.add('bg-success', 'text-white');
                globalSchedulerStatus.textContent = '已启用';
            } else {
                globalSchedulerStatus.classList.add('bg-danger', 'text-white');
                globalSchedulerStatus.textContent = '已禁用';
            }
        }
        
        // 加载各个定时任务的状态
        function loadSchedulerTaskStatus() {
            // 清空任务列表
            schedulerTaskList.innerHTML = '<tr><td colspan="5" class="text-center">加载中...</td></tr>';
            
            // 获取数据同步任务状态
            Promise.all([
                fetch('/api/sync/status').then(r => r.json()),
                fetch('/api/email_scheduler/status').then(r => r.json())
            ]).then(([syncStatus, emailStatus]) => {
                let html = '';
                
                // 数据同步任务
                html += `
                    <tr>
                        <td><i class="fas fa-sync-alt me-1"></i>数据同步</td>
                        <td><span class="badge ${syncStatus.success && syncStatus.status?.enabled === 'true' ? 'bg-success' : 'bg-secondary'}">${
                            syncStatus.success && syncStatus.status?.enabled === 'true' ? '运行中' : '已停止'
                        }</span></td>
                        <td>1</td>
                        <td>${syncStatus.success ? (syncStatus.status?.next_sync_time || '未知') : '-'}</td>
                        <td>
                            <button class="btn btn-sm btn-outline-primary" onclick="controlSchedulerTask('sync', '${syncStatus.success && syncStatus.status?.enabled === 'true' ? 'stop' : 'start'}')">
                                ${syncStatus.success && syncStatus.status?.enabled === 'true' ? '停止' : '启动'}
                            </button>
                        </td>
                    </tr>
                `;
                
                // 邮件附件任务
                if (emailStatus.status === 'success') {
                    const emailData = emailStatus.data;
                    html += `
                        <tr>
                            <td><i class="fas fa-envelope me-1"></i>邮件附件获取</td>
                            <td><span class="badge ${emailData.running ? 'bg-success' : 'bg-secondary'}">${
                                emailData.running ? '运行中' : '已停止'
                            }</span></td>
                            <td>${emailData.config_count || 0}</td>
                            <td>${emailData.next_runs && emailData.next_runs.length > 0 ? emailData.next_runs[0].next_run : '-'}</td>
                            <td>
                                <button class="btn btn-sm btn-outline-primary" onclick="controlSchedulerTask('email', '${emailData.running ? 'stop' : 'start'}')">
                                    ${emailData.running ? '停止' : '启动'}
                                </button>
                            </td>
                        </tr>
                    `;
                } else {
                    html += `
                        <tr>
                            <td><i class="fas fa-envelope me-1"></i>邮件附件获取</td>
                            <td><span class="badge bg-warning">错误</span></td>
                            <td>-</td>
                            <td>-</td>
                            <td>
                                <button class="btn btn-sm btn-outline-danger" disabled>错误</button>
                            </td>
                        </tr>
                    `;
                }
                
                schedulerTaskList.innerHTML = html;
            }).catch(error => {
                console.error('加载任务状态失败:', error);
                schedulerTaskList.innerHTML = '<tr><td colspan="5" class="text-center text-danger">加载失败</td></tr>';
            });
        }
        
        // 控制单个定时任务
        function controlSchedulerTask(taskType, action) {
            let apiUrl = '';
            let payload = { action: action };
            
            if (taskType === 'sync') {
                apiUrl = '/api/sync/' + action;
                payload = { user_id: 'admin' };
            } else if (taskType === 'email') {
                apiUrl = '/api/email_scheduler/control';
            }
            
            fetch(apiUrl, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(payload)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success || data.status === 'success') {
                    // 刷新状态
                    setTimeout(() => {
                        loadSchedulerTaskStatus();
                    }, 1000);
                } else {
                    alert(`操作失败: ${data.message || data.error || '未知错误'}`);
                }
            })
            .catch(error => {
                console.error('控制任务失败:', error);
                alert('操作失败: ' + error.message);
            });
        }
        
        // 保存全局定时任务设置
        function saveGlobalSchedulerSettings() {
            const config = {
                enabled: enableGlobalScheduler.checked
            };
            
            fetch('/api/global-scheduler/config', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ config: config })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('全局定时任务设置已保存');
                    updateGlobalSchedulerStatus();
                    
                    // 如果禁用了全局任务，刷新任务状态
                    if (!config.enabled) {
                        setTimeout(() => {
                            loadSchedulerTaskStatus();
                        }, 1000);
                    }
                } else {
                    alert('保存失败: ' + data.error);
                }
            })
            .catch(error => {
                console.error('保存设置失败:', error);
                alert('保存失败: ' + error.message);
            });
        }
        
        // 重启所有定时任务
        function restartAllSchedulers() {
            if (confirm('确定要重启所有定时任务吗？这将暂时中断正在进行的任务。')) {
                fetch('/api/global-scheduler/restart-all', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('所有定时任务已重启');
                        setTimeout(() => {
                            loadSchedulerTaskStatus();
                        }, 2000);
                    } else {
                        alert('重启失败: ' + data.error);
                    }
                })
                .catch(error => {
                    console.error('重启失败:', error);
                    alert('重启失败: ' + error.message);
                });
            }
        }
        
        // 加载定时任务日志
        function loadSchedulerLogs() {
            fetch('/api/global-scheduler/logs?limit=100')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const logs = data.logs || [];
                        if (logs.length > 0) {
                            schedulerLogs.innerHTML = logs.join('\n');
                            // 滚动到底部
                            schedulerLogs.scrollTop = schedulerLogs.scrollHeight;
                        } else {
                            schedulerLogs.innerHTML = '暂无日志记录';
                        }
                    } else {
                        schedulerLogs.innerHTML = '加载日志失败: ' + data.error;
                    }
                })
                .catch(error => {
                    console.error('加载日志失败:', error);
                    schedulerLogs.innerHTML = '加载日志失败: ' + error.message;
                });
        }
        
        // 绑定定时任务管理事件
        enableGlobalScheduler.addEventListener('change', updateGlobalSchedulerStatus);
        saveSchedulerSettingsBtn.addEventListener('click', saveGlobalSchedulerSettings);
        refreshSchedulerStatusBtn.addEventListener('click', () => {
            loadSchedulerTaskStatus();
            loadSchedulerLogs();
        });
        restartAllSchedulersBtn.addEventListener('click', restartAllSchedulers);
        
        // 将控制函数暴露到全局作用域
        window.controlSchedulerTask = controlSchedulerTask;
        
        // 页面加载时初始化定时任务管理
        loadGlobalSchedulerConfig();
        loadSchedulerLogs();
        
        // 定期刷新日志
        setInterval(() => {
            const activeTab = document.querySelector('.nav-link.active');
            if (activeTab && activeTab.id === 'scheduler-tab') {
                loadSchedulerLogs();
            }
        }, 10000); // 每10秒刷新一次
    });
</script>
{% endblock %} 