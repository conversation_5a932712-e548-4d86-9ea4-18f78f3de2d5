#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据迁移脚本：为排产算法权重配置添加策略支持
"""

import pymysql
import json
from datetime import datetime

def migrate_strategy_weights():
    """迁移现有权重配置数据，添加策略支持"""
    try:
        conn = pymysql.connect(host='localhost', user='root', password='WWWwww123!', charset='utf8mb4')
        cursor = conn.cursor()
        
        cursor.execute('USE aps_system')
        
        print("🔄 开始数据迁移...")
        
        # 1. 检查是否已经有strategy_name字段
        cursor.execute("SHOW COLUMNS FROM scheduling_config LIKE 'strategy_name'")
        if not cursor.fetchone():
            print("📝 添加strategy_name字段...")
            cursor.execute("""
                ALTER TABLE scheduling_config 
                ADD COLUMN strategy_name varchar(50) NOT NULL DEFAULT 'intelligent' COMMENT '排产策略名称'
                AFTER user_id
            """)
            
            # 更新唯一索引
            cursor.execute("DROP INDEX uk_user_config ON scheduling_config")
            cursor.execute("""
                CREATE UNIQUE INDEX uk_user_strategy_config 
                ON scheduling_config (user_id, strategy_name, config_name)
            """)
            print("✅ strategy_name字段添加成功")
        else:
            print("ℹ️  strategy_name字段已存在，跳过添加")
        
        # 2. 检查现有数据并更新
        cursor.execute("SELECT * FROM scheduling_config")
        existing_configs = cursor.fetchall()
        
        if existing_configs:
            print(f"📊 发现 {len(existing_configs)} 条现有配置")
            
            # 更新现有配置的strategy_name为intelligent（如果为空）
            cursor.execute("""
                UPDATE scheduling_config 
                SET strategy_name = 'intelligent' 
                WHERE strategy_name = '' OR strategy_name IS NULL
            """)
            print("✅ 现有配置已更新为intelligent策略")
        
        # 3. 为每个策略创建默认配置
        strategies = [
            {
                'name': 'intelligent',
                'label': '智能综合策略',
                'weights': {
                    'tech_match_weight': 25.0,
                    'load_balance_weight': 20.0,
                    'deadline_weight': 25.0,
                    'value_efficiency_weight': 20.0,
                    'business_priority_weight': 10.0
                }
            },
            {
                'name': 'deadline',
                'label': '交期优先策略',
                'weights': {
                    'tech_match_weight': 15.0,
                    'load_balance_weight': 10.0,
                    'deadline_weight': 50.0,
                    'value_efficiency_weight': 15.0,
                    'business_priority_weight': 10.0
                }
            },
            {
                'name': 'product',
                'label': '产品优先策略',
                'weights': {
                    'tech_match_weight': 40.0,
                    'load_balance_weight': 15.0,
                    'deadline_weight': 20.0,
                    'value_efficiency_weight': 15.0,
                    'business_priority_weight': 10.0
                }
            },
            {
                'name': 'value',
                'label': '产值优先策略',
                'weights': {
                    'tech_match_weight': 15.0,
                    'load_balance_weight': 15.0,
                    'deadline_weight': 20.0,
                    'value_efficiency_weight': 40.0,
                    'business_priority_weight': 10.0
                }
            }
        ]
        
        for strategy in strategies:
            # 检查是否已存在该策略的默认配置
            cursor.execute("""
                SELECT id FROM scheduling_config 
                WHERE user_id IS NULL AND strategy_name = %s AND config_name = %s
            """, (strategy['name'], f"default_{strategy['name']}_config"))
            
            if not cursor.fetchone():
                print(f"📝 创建 {strategy['label']} 默认配置...")
                
                weights = strategy['weights']
                cursor.execute("""
                    INSERT INTO scheduling_config (
                        config_name, user_id, strategy_name,
                        tech_match_weight, load_balance_weight, deadline_weight,
                        value_efficiency_weight, business_priority_weight,
                        minor_changeover_time, major_changeover_time,
                        urgent_threshold, normal_threshold, critical_threshold,
                        is_active, created_at, updated_at
                    ) VALUES (
                        %s, NULL, %s,
                        %s, %s, %s, %s, %s,
                        45, 120, 8, 24, 72,
                        1, NOW(), NOW()
                    )
                """, (
                    f"default_{strategy['name']}_config",
                    strategy['name'],
                    weights['tech_match_weight'],
                    weights['load_balance_weight'],
                    weights['deadline_weight'],
                    weights['value_efficiency_weight'],
                    weights['business_priority_weight']
                ))
                print(f"✅ {strategy['label']} 默认配置创建成功")
            else:
                print(f"ℹ️  {strategy['label']} 默认配置已存在，跳过创建")
        
        # 4. 验证迁移结果
        cursor.execute("SELECT strategy_name, COUNT(*) FROM scheduling_config GROUP BY strategy_name")
        results = cursor.fetchall()
        
        print("\n📊 迁移结果统计:")
        for strategy_name, count in results:
            print(f"  - {strategy_name}: {count} 条配置")
        
        conn.commit()
        print("\n🎉 数据迁移完成！")
        
        # 5. 显示所有配置
        cursor.execute("""
            SELECT id, config_name, user_id, strategy_name, 
                   tech_match_weight, load_balance_weight, deadline_weight,
                   value_efficiency_weight, business_priority_weight
            FROM scheduling_config 
            ORDER BY strategy_name, user_id
        """)
        
        configs = cursor.fetchall()
        print(f"\n📋 当前所有配置 ({len(configs)} 条):")
        print("ID | 配置名称 | 用户 | 策略 | 技术 | 负载 | 交期 | 产值 | 业务")
        print("-" * 80)
        for config in configs:
            user_display = config[2] or "默认"
            print(f"{config[0]:2d} | {config[1][:15]:15s} | {user_display[:6]:6s} | {config[3]:8s} | {config[4]:4.1f} | {config[5]:4.1f} | {config[6]:4.1f} | {config[7]:4.1f} | {config[8]:4.1f}")
        
    except Exception as e:
        print(f"❌ 迁移失败: {e}")
        if 'conn' in locals():
            conn.rollback()
    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    migrate_strategy_weights()
