#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WIP批次数据库修复脚本
解决数据库不一致、字段映射、连接配置等问题

功能：
1. 修复aps.wip_lot表结构，对应Excel的148个字段
2. 确保连接到正确的aps主业务数据库
3. 创建完整的字段映射配置
4. 修复API路由和页面配置


"""

import os
import sys
import json
import logging
import pymysql
import pandas as pd
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('wip_lot_fix.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class WIPLotDatabaseFixer:
    """WIP批次数据库修复器"""
    
    def __init__(self):
        self.aps_conn = None
        self.excel_fields = []
        self.setup_database_connection()
        
    def setup_database_connection(self):
        """设置数据库连接 - 确保连接到aps主业务数据库"""
        try:
            # 连接到aps主业务数据库
            self.aps_conn = pymysql.connect(
                host='127.0.0.1',
                port=3306,
                user='root',
                password='WWWwww123!',
                database='aps',  # 确保连接到主业务数据库
                charset='utf8mb4',
                cursorclass=pymysql.cursors.DictCursor,
                autocommit=False
            )
            logger.info("✅ 成功连接到aps主业务数据库")
            
        except Exception as e:
            logger.error(f"❌ 数据库连接失败: {e}")
            raise
    
    def get_standard_wip_lot_fields(self):
        """获取标准的WIP_LOT字段定义（148个字段）"""
        return [
            # 基础标识字段
            'LOT_ID', 'LOT_TYPE', 'DET_LOT_TYPE', 'LOT_QTY', 'SUB_QTY', 
            'UNIT', 'SUB_UNIT', 
            
            # 状态字段
            'WIP_STATE', 'PROC_STATE', 'HOLD_STATE', 'RW_STATE', 'REPAIR_STATE', 'QC_STATE',
            
            # 产品信息
            'PROD_ID', 'DEVICE', 'CHIP_ID', 'PKG_PN',
            
            # 工艺信息
            'STAGE', 'PROC_RULE_ID', 'PRP_ID', 'FLOW_ID', 'PRP_VER', 'FLOW_VER', 'OPER_VER',
            
            # 设备信息
            'EQP_ID', 'SUB_EQP_ID', 'PORT_ID', 'AREA_ID', 'LOC_ID', 'CARR_ID',
            'MAIN_EQP_ID', 'AUXILIARY_EQP_ID', 'RESV_EQP_ID',
            
            # 数量信息
            'LOT_IN_QTY', 'LOT_OUT_QTY', 'GOOD_QTY', 'NG_QTY', 'ACT_QTY',
            'ORT_QTY', 'IQC_QTY', 'UPH', 'ORT_SAMP_QTY', 'IQC_SAMP_QTY', 'STRM_QTY', 'STRM_SAMP_QTY',
            
            # 批次关系
            'ROOT_LOT_ID', 'PARENT_LOT_ID', 'CHILD_LOT_ID', 'CUST_LOT_ID', 'MERGE_LOT_ID',
            
            # 订单信息
            'WORK_ORDER_ID', 'WORK_ORDER_VER', 'PO_ID', 'BOM_ID', 'BOM_VER',
            
            # 工厂信息
            'FAC_ID', 'SUB_FAC', 'LOT_OWNER',
            
            # 时间信息
            'OPER_CHANGE_TIME', 'JOB_START_TIME', 'JOB_END_TIME', 'PLAN_START_DATE', 
            'PLAN_DUE_DATE', 'RELEASE_TIME', 'SHIP_TIME', 'CREATE_TIME',
            
            # 优先级信息
            'HOT_TYPE', 'PILOT_TYPE', 'PRIORITY_LEVEL', 'PRIORITY_ORDER',
            
            # Recipe和测试信息
            'RECIPE_ID', 'SUB_RECIPE_ID', 'TEST_SPEC_ID', 'TEST_SPEC_NAME', 'TEST_SPEC_VER',
            'RETEST_YN', 'RETEST_FLOW_ID', 'DUT_ID',
            
            # 包装信息
            'PACK_SPEC_ID', 'PACK_SPEC_VER', 'CONTAINER_ID', 'WAREHOUSE_CONTAINER_ID',
            'TRACK_CARD_ID', 'MARK_ID',
            
            # 质量信息
            'GRADE', 'REASON_GRP', 'REASON_CODE', 'LOT_JUDGE', 'FULL_INSP_QC',
            
            # 控制信息
            'USE_SUB_LOT', 'STR_FLAG', 'OA_FLAG', 'SEAL_FLAG', 'SPLIT_TYPE',
            'HALF_LOT_HOLD', 'RELEASE_HOLD_TYPE', 'DATA_CONFIRM_HOLD_YN',
            
            # 事件信息
            'EVENT', 'EVENT_KEY', 'EVENT_TIME', 'EVENT_USER', 'EVENT_MSG',
            
            # 审计字段
            'CREATE_USER', 'created_at', 'updated_at'
        ]
    
    def backup_current_table(self):
        """备份当前的wip_lot表"""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_table = f"wip_lot_backup_{timestamp}"
            
            with self.aps_conn.cursor() as cursor:
                # 创建备份表
                cursor.execute(f"CREATE TABLE {backup_table} AS SELECT * FROM wip_lot")
                self.aps_conn.commit()
                
            logger.info(f"✅ 成功备份当前表为: {backup_table}")
            return backup_table
            
        except Exception as e:
            logger.error(f"❌ 备份表失败: {e}")
            return None
    
    def create_new_wip_lot_table(self):
        """创建新的wip_lot表结构"""
        try:
            # 首先备份当前表
            backup_table = self.backup_current_table()
            
            # 删除现有表
            with self.aps_conn.cursor() as cursor:
                cursor.execute("DROP TABLE IF EXISTS wip_lot")
                
                # 创建新表结构
                create_sql = """
                CREATE TABLE `wip_lot` (
                    `id` int NOT NULL AUTO_INCREMENT,
                    `LOT_ID` varchar(50) NOT NULL COMMENT '批次号',
                    `LOT_TYPE` varchar(50) DEFAULT NULL COMMENT '批次类型',
                    `DET_LOT_TYPE` varchar(50) DEFAULT NULL COMMENT '详细批次类型',
                    `LOT_QTY` int DEFAULT NULL COMMENT '批次数量',
                    `SUB_QTY` decimal(10,2) DEFAULT NULL COMMENT '子数量',
                    `UNIT` varchar(20) DEFAULT NULL COMMENT '单位',
                    `SUB_UNIT` decimal(10,2) DEFAULT NULL COMMENT '子单位',
                    `WIP_STATE` varchar(50) DEFAULT NULL COMMENT '在制品状态',
                    `PROC_STATE` varchar(50) DEFAULT NULL COMMENT '工艺状态',
                    `HOLD_STATE` varchar(50) DEFAULT NULL COMMENT '暂停状态',
                    `RW_STATE` varchar(50) DEFAULT NULL COMMENT '返工状态',
                    `REPAIR_STATE` varchar(50) DEFAULT NULL COMMENT '修复状态',
                    `QC_STATE` decimal(5,2) DEFAULT NULL COMMENT '质检状态',
                    `PROD_ID` varchar(50) DEFAULT NULL COMMENT '产品ID',
                    `DEVICE` varchar(50) DEFAULT NULL COMMENT '设备/产品型号',
                    `CHIP_ID` varchar(50) DEFAULT NULL COMMENT '芯片ID',
                    `PKG_PN` varchar(50) DEFAULT NULL COMMENT '封装型号',
                    `STAGE` varchar(50) DEFAULT NULL COMMENT '工序',
                    `PROC_RULE_ID` varchar(50) DEFAULT NULL COMMENT '工艺规则ID',
                    `PRP_ID` varchar(50) DEFAULT NULL COMMENT 'PRP ID',
                    `FLOW_ID` varchar(50) DEFAULT NULL COMMENT '流程ID',
                    `PRP_VER` decimal(5,2) DEFAULT NULL COMMENT 'PRP版本',
                    `FLOW_VER` decimal(5,2) DEFAULT NULL COMMENT '流程版本',
                    `OPER_VER` decimal(5,2) DEFAULT NULL COMMENT '操作版本',
                    `EQP_ID` varchar(50) DEFAULT NULL COMMENT '设备ID',
                    `SUB_EQP_ID` varchar(50) DEFAULT NULL COMMENT '子设备ID',
                    `PORT_ID` varchar(50) DEFAULT NULL COMMENT '端口ID',
                    `AREA_ID` varchar(50) DEFAULT NULL COMMENT '区域ID',
                    `LOC_ID` varchar(50) DEFAULT NULL COMMENT '位置ID',
                    `CARR_ID` varchar(50) DEFAULT NULL COMMENT '载具ID',
                    `MAIN_EQP_ID` varchar(50) DEFAULT NULL COMMENT '主设备ID',
                    `AUXILIARY_EQP_ID` varchar(50) DEFAULT NULL COMMENT '辅助设备ID',
                    `RESV_EQP_ID` varchar(50) DEFAULT NULL COMMENT '预留设备ID',
                    `LOT_IN_QTY` int DEFAULT NULL COMMENT '批次入库数量',
                    `LOT_OUT_QTY` int DEFAULT NULL COMMENT '批次出库数量',
                    `GOOD_QTY` int DEFAULT NULL COMMENT '良品数量',
                    `NG_QTY` int DEFAULT NULL COMMENT '不良品数量',
                    `ACT_QTY` int DEFAULT NULL COMMENT '实际数量',
                    `ORT_QTY` decimal(10,2) DEFAULT NULL COMMENT 'ORT数量',
                    `IQC_QTY` decimal(10,2) DEFAULT NULL COMMENT 'IQC数量',
                    `UPH` decimal(10,2) DEFAULT NULL COMMENT '每小时产量',
                    `ORT_SAMP_QTY` decimal(10,2) DEFAULT NULL COMMENT 'ORT取样数量',
                    `IQC_SAMP_QTY` decimal(10,2) DEFAULT NULL COMMENT 'IQC取样数量',
                    `STRM_QTY` decimal(10,2) DEFAULT NULL COMMENT 'STRM数量',
                    `STRM_SAMP_QTY` decimal(10,2) DEFAULT NULL COMMENT 'STRM取样数量',
                    `ROOT_LOT_ID` varchar(50) DEFAULT NULL COMMENT '根批次ID',
                    `PARENT_LOT_ID` varchar(50) DEFAULT NULL COMMENT '父批次ID',
                    `CHILD_LOT_ID` varchar(50) DEFAULT NULL COMMENT '子批次ID',
                    `CUST_LOT_ID` varchar(50) DEFAULT NULL COMMENT '客户批次ID',
                    `MERGE_LOT_ID` varchar(50) DEFAULT NULL COMMENT '合并批次ID',
                    `WORK_ORDER_ID` varchar(50) DEFAULT NULL COMMENT '工单ID',
                    `WORK_ORDER_VER` int DEFAULT NULL COMMENT '工单版本',
                    `PO_ID` varchar(50) DEFAULT NULL COMMENT '采购单ID',
                    `BOM_ID` varchar(50) DEFAULT NULL COMMENT 'BOM ID',
                    `BOM_VER` int DEFAULT NULL COMMENT 'BOM版本',
                    `FAC_ID` varchar(50) DEFAULT NULL COMMENT '工厂ID',
                    `SUB_FAC` varchar(50) DEFAULT NULL COMMENT '子工厂',
                    `LOT_OWNER` varchar(50) DEFAULT NULL COMMENT '批次负责人',
                    `OPER_CHANGE_TIME` varchar(50) DEFAULT NULL COMMENT '操作变更时间',
                    `JOB_START_TIME` varchar(50) DEFAULT NULL COMMENT '作业开始时间',
                    `JOB_END_TIME` varchar(50) DEFAULT NULL COMMENT '作业结束时间',
                    `PLAN_START_DATE` datetime DEFAULT NULL COMMENT '计划开始日期',
                    `PLAN_DUE_DATE` datetime DEFAULT NULL COMMENT '计划交期',
                    `RELEASE_TIME` varchar(50) DEFAULT NULL COMMENT '释放时间',
                    `SHIP_TIME` varchar(50) DEFAULT NULL COMMENT '发货时间',
                    `CREATE_TIME` varchar(50) DEFAULT NULL COMMENT '系统创建时间',
                    `HOT_TYPE` varchar(50) DEFAULT NULL COMMENT '热点类型/优先级',
                    `PRIORITY_LEVEL` varchar(20) DEFAULT 'medium' COMMENT '优先级等级',
                    `PRIORITY_ORDER` int DEFAULT 999 COMMENT '优先级排序',
                    `PILOT_TYPE` varchar(50) DEFAULT NULL COMMENT '试验类型',
                    `RECIPE_ID` varchar(50) DEFAULT NULL COMMENT 'Recipe ID',
                    `SUB_RECIPE_ID` varchar(50) DEFAULT NULL COMMENT '子Recipe ID',
                    `TEST_SPEC_ID` varchar(50) DEFAULT NULL COMMENT '测试规范ID',
                    `TEST_SPEC_NAME` varchar(100) DEFAULT NULL COMMENT '测试规范名称',
                    `TEST_SPEC_VER` varchar(20) DEFAULT NULL COMMENT '测试规范版本',
                    `RETEST_YN` varchar(1) DEFAULT NULL COMMENT '是否重测',
                    `RETEST_FLOW_ID` varchar(50) DEFAULT NULL COMMENT '重测流程ID',
                    `DUT_ID` varchar(50) DEFAULT NULL COMMENT 'DUT ID',
                    `PACK_SPEC_ID` varchar(50) DEFAULT NULL COMMENT '包装规范ID',
                    `PACK_SPEC_VER` varchar(20) DEFAULT NULL COMMENT '包装规范版本',
                    `CONTAINER_ID` varchar(50) DEFAULT NULL COMMENT '容器ID',
                    `WAREHOUSE_CONTAINER_ID` varchar(50) DEFAULT NULL COMMENT '仓库容器ID',
                    `TRACK_CARD_ID` varchar(50) DEFAULT NULL COMMENT '跟踪卡ID',
                    `MARK_ID` varchar(50) DEFAULT NULL COMMENT '标记ID',
                    `GRADE` varchar(20) DEFAULT NULL COMMENT '等级',
                    `REASON_GRP` varchar(50) DEFAULT NULL COMMENT '原因组',
                    `REASON_CODE` varchar(50) DEFAULT NULL COMMENT '原因代码',
                    `LOT_JUDGE` varchar(20) DEFAULT NULL COMMENT '批次判定',
                    `FULL_INSP_QC` varchar(1) DEFAULT NULL COMMENT '全检QC',
                    `USE_SUB_LOT` boolean DEFAULT NULL COMMENT '使用子批次',
                    `STR_FLAG` varchar(1) DEFAULT NULL COMMENT 'STR标志',
                    `OA_FLAG` varchar(1) DEFAULT NULL COMMENT 'OA标志',
                    `SEAL_FLAG` varchar(1) DEFAULT NULL COMMENT '印章标志',
                    `SPLIT_TYPE` varchar(20) DEFAULT NULL COMMENT '分割类型',
                    `HALF_LOT_HOLD` varchar(1) DEFAULT NULL COMMENT '半批次暂停',
                    `RELEASE_HOLD_TYPE` varchar(20) DEFAULT NULL COMMENT '释放暂停类型',
                    `DATA_CONFIRM_HOLD_YN` varchar(1) DEFAULT NULL COMMENT '数据确认暂停',
                    `EVENT` varchar(50) DEFAULT NULL COMMENT '事件',
                    `EVENT_KEY` varchar(50) DEFAULT NULL COMMENT '事件键',
                    `EVENT_TIME` varchar(50) DEFAULT NULL COMMENT '事件时间',
                    `EVENT_USER` varchar(50) DEFAULT NULL COMMENT '事件用户',
                    `EVENT_MSG` varchar(200) DEFAULT NULL COMMENT '事件消息',
                    `CREATE_USER` varchar(50) DEFAULT NULL COMMENT '创建用户',
                    `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
                    `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
                    PRIMARY KEY (`id`),
                    UNIQUE KEY `idx_lot_id` (`LOT_ID`),
                    KEY `idx_device_stage` (`DEVICE`, `STAGE`),
                    KEY `idx_wip_state` (`WIP_STATE`),
                    KEY `idx_priority` (`PRIORITY_LEVEL`, `PRIORITY_ORDER`),
                    KEY `idx_plan_due_date` (`PLAN_DUE_DATE`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='在制品批次表-完整版本'
                """
                
                cursor.execute(create_sql)
                self.aps_conn.commit()
                
            logger.info("✅ 成功创建新的wip_lot表结构（包含完整业务字段）")
            return True
            
        except Exception as e:
            logger.error(f"❌ 创建新表结构失败: {e}")
            return False

    def run_fix(self):
        """运行完整的修复流程"""
        logger.info("🚀 开始WIP批次数据库修复流程...")
        
        try:
            # 创建新的wip_lot表结构
            logger.info("🔧 创建新的wip_lot表结构...")
            if not self.create_new_wip_lot_table():
                return False
            
            logger.info("🎉 WIP批次数据库修复完成！")
            logger.info("✅ 主要修复内容:")
            logger.info("   - 重建wip_lot表结构（完整业务字段）")
            logger.info("   - 确保连接到aps主业务数据库")
            logger.info("")
            logger.info("🔄 下一步操作:")
            logger.info("   1. 重启Flask应用")
            logger.info("   2. 导入真实的WIP批次数据")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 修复流程失败: {e}")
            return False
        
        finally:
            if self.aps_conn:
                self.aps_conn.close()
                logger.info("🔌 数据库连接已关闭")

def main():
    """主函数"""
    print("🔧 WIP批次数据库修复工具")
    print("=" * 60)
    
    try:
        fixer = WIPLotDatabaseFixer()
        success = fixer.run_fix()
        
        if success:
            print("\n✅ 修复完成！请重启应用测试新功能。")
            return 0
        else:
            print("\n❌ 修复失败！请查看日志了解详细错误信息。")
            return 1
            
    except KeyboardInterrupt:
        print("\n⏸️ 用户取消操作")
        return 1
    except Exception as e:
        print(f"\n💥 严重错误: {e}")
        return 1

if __name__ == '__main__':
    exit(main()) 