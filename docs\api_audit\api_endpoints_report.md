# API端点分析报告

生成时间: 2025-06-27 12:39:36

## 统计摘要

- **版本_未知**: 217
- **模块_其他**: 220
- **模块_认证授权**: 5
- **版本_v1**: 31
- **模块_生产管理**: 21
- **模块_订单管理**: 8
- **版本_v2**: 6
- **总端点数**: 254

## 按版本分类

### v1 版本 (31 个端点)

| 端点 | HTTP方法 | 功能 | 所在文件 |
| --- | --- | --- | --- |
| /api/email_attachments | GET | None | app\api\email_attachment.py |
| /api/email_attachments/<int:attachment_id> | GET | None | app\api\email_attachment.py |
| /api/email_attachments/<int:attachment_id>/download | GET | None | app\api\email_attachment.py |
| /api/email_attachments/<int:attachment_id>/process | GET | None | app\api\email_attachment.py |
| /api/email_attachments/batch_process | GET | None | app\api\email_attachment.py |
| /api/email_attachments/dashboard | GET | None | app\api\email_attachment.py |
| /api/email_configs | GET | None | app\api\email_attachment.py |
| /api/email_configs | GET | None | app\api\email_attachment.py |
| /api/email_configs/<int:config_id> | GET | None | app\api\email_attachment.py |
| /api/email_configs/<int:config_id> | GET | None | app\api\email_attachment.py |
| /api/email_configs/<int:config_id> | GET | None | app\api\email_attachment.py |
| /api/email_configs/<int:config_id>/fetch | GET | None | app\api\email_attachment.py |
| /api/email_configs/<int:config_id>/preview | GET | None | app\api\email_attachment.py |
| /api/email_configs/<int:config_id>/test | GET | None | app\api\email_attachment.py |
| /api/email_configs/test | GET | None | app\api\email_attachment.py |
| /api/excel_mappings | GET | None | app\api\email_attachment.py |
| /api/excel_mappings/<int:mapping_id> | GET | None | app\api\email_attachment.py |
| /api/excel_mappings/<int:mapping_id> | GET | None | app\api\email_attachment.py |
| /api/excel_mappings/<int:mapping_id> | GET | None | app\api\email_attachment.py |
| /api/order_data | GET | None | app\api\email_attachment.py |
| /api/order_data/<int:order_id> | GET | None | app\api\email_attachment.py |
| /api/order_data/<int:order_id> | GET | None | app\api\email_attachment.py |
| /api/order_data/<task_id>/pause | POST | None | app\api\order_processing_api.py |
| /api/order_data/<task_id>/status | POST | None | app\api\order_processing_api.py |
| /api/order_data/<task_id>/stop | POST | None | app\api\order_processing_api.py |
| /api/order_data/download_summary | GET | None | app\api\email_attachment.py |
| /api/order_data/export | GET | None | app\api\email_attachment.py |
| /api/order_data/start | POST | None | app\api\order_processing_api.py |
| /api/order_data/stats | GET | None | app\api\email_attachment.py |
| /api/order_data/stats | POST | None | app\api\order_processing_api.py |
| /api/production/history-times | PUT | get_history_data | app\api\routes.py |

### v2 版本 (6 个端点)

| 端点 | HTTP方法 | 功能 | 所在文件 |
| --- | --- | --- | --- |
| /api/v2/high-concurrency/config | POST | None | app\api_v2\orders\high_concurrency_api.py |
| /api/v2/high-concurrency/email-processing/start | POST | None | app\api_v2\orders\high_concurrency_api.py |
| /api/v2/high-concurrency/email-processing/status | POST | None | app\api_v2\orders\high_concurrency_api.py |
| /api/v2/high-concurrency/email-processing/stop | POST | None | app\api_v2\orders\high_concurrency_api.py |
| /api/v2/high-concurrency/performance/metrics | POST | None | app\api_v2\orders\high_concurrency_api.py |
| /api/v2/high-concurrency/test-data/scan | POST | None | app\api_v2\orders\high_concurrency_api.py |

### 未知 版本 (217 个端点)

| 端点 | HTTP方法 | 功能 | 所在文件 |
| --- | --- | --- | --- |
| / | GET | health_check | app\api_v2\production\wait_lots_api.py |
| / | GET | health_check | app\api_v2\production\wait_lots_api.py |
| /<int:lot_id> | GET | health_check | app\api_v2\production\wait_lots_api.py |
| /<int:lot_id> | GET | health_check | app\api_v2\production\wait_lots_api.py |
| /<int:lot_id> | GET | health_check | app\api_v2\production\wait_lots_api.py |
| /ai-config | POST | health_check | app\api_v2\system\routes.py |
| /ai-settings | POST | health_check | app\api_v2\system\routes.py |
| /ai/chat | POST | None | app\api\ai_assistant.py |
| /ai/db_chat | POST | None | app\api\ai_database_assistant.py |
| /apply-matches | GET | health_check | app\api_v2\production\priority_api.py |
| /attachments/<int:attachment_id>/process | POST | scan_attachments | app\api_v2\orders\semi_auto_api.py |
| /attachments/process-batch | POST | scan_attachments | app\api_v2\orders\semi_auto_api.py |
| /attachments/scan | POST | scan_attachments | app\api_v2\orders\semi_auto_api.py |
| /attachments/scan/local | POST | scan_attachments | app\api_v2\orders\semi_auto_api.py |
| /auth/users | GET | None | app\api\auth.py |
| /auth/users | GET | None | app\api\auth.py |
| /auth/users/<username> | GET | None | app\api\auth.py |
| /auth/users/<username> | GET | None | app\api\auth.py |
| /auth/users/<username> | GET | None | app\api\auth.py |
| /batch-info/<batch_id> | GET | health_check | app\api_v2\production\priority_api.py |
| /batch-update-classification | GET | None | app\api_v2\orders\summary_preview_api.py |
| /cache/clear | GET | get_supported_tables | app\api\routes_v3.py |
| /charts | GET | None | app\api_v2\system\dashboard.py |
| /check-mysql-databases | PUT | get_history_data | app\api\routes.py |
| /check-mysql-databases | POST | health_check | app\api_v2\system\routes.py |
| /classification-stats | GET | None | app\api_v2\orders\summary_preview_api.py |
| /cleanup-tasks | POST | None | app\api_v2\orders\optimized_parser_api.py |
| /columns | GET | health_check | app\api_v2\production\wait_lots_api.py |
| /columns/<table_name> | POST | resources_health_v2 | app\api_v2\resources\routes.py |
| /config | POST | get_embed_script | app\api\dify_proxy.py |
| /config/tables/<table_name> | GET | get_supported_tables | app\api\routes_v3.py |
| /configs | GET | None | app\api_v2\system\database_config.py |
| /configs | GET | None | app\api_v2\system\database_config.py |
| /configs/<int:config_id> | GET | None | app\api_v2\system\database_config.py |
| /configs/<int:config_id> | GET | None | app\api_v2\system\database_config.py |
| /configs/<int:config_id> | GET | None | app\api_v2\system\database_config.py |
| /configs/<int:config_id>/test | GET | None | app\api_v2\system\database_config.py |
| /configs/set-default/<int:config_id> | GET | None | app\api_v2\system\database_config.py |
| /configs/test | GET | None | app\api_v2\system\database_config.py |
| /configs/test-batch | GET | None | app\api_v2\system\database_config.py |
| /configs/types | GET | None | app\api_v2\system\database_config.py |
| /cp-summary | GET | None | app\api_v2\orders\order_data_api.py |
| /cp-summary/export | GET | None | app\api_v2\orders\order_data_api.py |
| /create | GET | get_wip_lot_data | app\api_v2\wip_lot_api.py |
| /data | GET | get_wip_lot_data | app\api_v2\wip_lot_api.py |
| /data-source/equipment-status | POST | health_check | app\api_v2\production\routes.py |
| /data-source/metrics | POST | health_check | app\api_v2\production\routes.py |
| /data-source/status | POST | health_check | app\api_v2\production\routes.py |
| /data-source/switch | POST | health_check | app\api_v2\production\routes.py |
| /data-source/uph-equipment | POST | health_check | app\api_v2\production\routes.py |
| /data-source/validate | POST | health_check | app\api_v2\production\routes.py |
| /data-source/wait-lots | POST | health_check | app\api_v2\production\routes.py |
| /data/<table_name> | POST | resources_health_v2 | app\api_v2\resources\routes.py |
| /data/<table_name> | POST | resources_health_v2 | app\api_v2\resources\routes.py |
| /data/<table_name> | POST | resources_health_v2 | app\api_v2\resources\routes.py |
| /data/<table_name>/<record_id> | POST | resources_health_v2 | app\api_v2\resources\routes.py |
| /data/<table_name>/batch | POST | resources_health_v2 | app\api_v2\resources\routes.py |
| /data/<table_name>/export | POST | resources_health_v2 | app\api_v2\resources\routes.py |
| /data/export | POST | scan_attachments | app\api_v2\orders\semi_auto_api.py |
| /data/preview | POST | scan_attachments | app\api_v2\orders\semi_auto_api.py |
| /database-status | PUT | get_history_data | app\api\routes.py |
| /database/config | POST | health_check | app\api_v2\system\routes.py |
| /database/status | POST | health_check | app\api_v2\system\routes.py |
| /database/test-connection | POST | health_check | app\api_v2\system\routes.py |
| /debug/login-status | GET | health_check | app\api_v2\auth\routes.py |
| /delete/<int:record_id> | GET | get_wip_lot_data | app\api_v2\wip_lot_api.py |
| /device | GET | health_check | app\api_v2\production\priority_api.py |
| /download-summary/<filename> | GET | None | app\api_v2\orders\summary_preview_api.py |
| /duplicate-confirm | POST | None | app\api_v2\orders\optimized_parser_api.py |
| /email-configs/<int:config_id>/get-auth | POST | scan_attachments | app\api_v2\orders\semi_auto_api.py |
| /email-configs/<int:config_id>/save-auth | POST | scan_attachments | app\api_v2\orders\semi_auto_api.py |
| /email-scheduler | POST | health_check | app\api_v2\system\routes.py |
| /embed.min.js | POST | get_embed_script | app\api\dify_proxy.py |
| /ft-summary | GET | None | app\api_v2\orders\order_data_api.py |
| /ft-summary/export | GET | None | app\api_v2\orders\order_data_api.py |
| /generate-engineering-summary | GET | None | app\api_v2\orders\summary_preview_api.py |
| /generate-production-summary | GET | None | app\api_v2\orders\summary_preview_api.py |
| /health | GET | get_wait_lots | app\api\unified_production_api.py |
| /health | GET | health_check | app\api_v2\__init__.py |
| /health | GET | health_check | app\api_v2\auth\routes.py |
| /health | GET | health_check | app\api_v2\production\priority_api.py |
| /health | POST | health_check | app\api_v2\production\routes.py |
| /health | GET | health_check | app\api_v2\production\wait_lots_api.py |
| /health | POST | resources_health_v2 | app\api_v2\resources\routes.py |
| /health | GET | health_check | app\api_v2\system\monitoring.py |
| /health | POST | health_check | app\api_v2\system\routes.py |
| /health-check | GET | None | app\api_v2\system\missing_monitoring.py |
| /jobs | GET | None | app\api\scheduler_api.py |
| /jobs | GET | None | app\api\scheduler_api.py |
| /jobs/<job_id> | GET | None | app\api\scheduler_api.py |
| /jobs/<job_id>/run | GET | None | app\api\scheduler_api.py |
| /logs | GET | None | app\api\scheduler_api.py |
| /lot | GET | health_check | app\api_v2\production\priority_api.py |
| /lots/<lot_id> | GET | get_wait_lots | app\api\unified_production_api.py |
| /lots/<lot_id>/equipment | GET | get_wait_lots | app\api\unified_production_api.py |
| /lots/<lot_id>/priority | GET | get_wait_lots | app\api\unified_production_api.py |
| /mappings | GET | None | app\api_v2\system\database_config.py |
| /mappings | GET | None | app\api_v2\system\database_config.py |
| /match-batches | GET | health_check | app\api_v2\production\priority_api.py |
| /matching-report | GET | health_check | app\api_v2\production\priority_api.py |
| /menu-settings | GET | health_check | app\api_v2\auth\routes.py |
| /metrics | GET | None | app\api_v2\system\missing_monitoring.py |
| /metrics | GET | health_check | app\api_v2\system\monitoring.py |
| /metrics/clear | GET | health_check | app\api_v2\system\monitoring.py |
| /metrics/history | GET | health_check | app\api_v2\system\monitoring.py |
| /migration/status | GET | get_supported_tables | app\api\routes_v3.py |
| /migration/test-page | GET | get_supported_tables | app\api\routes_v3.py |
| /move-to-scheduled | GET | health_check | app\api_v2\production\wait_lots_api.py |
| /navigation | GET | get_supported_tables | app\api\routes_v3.py |
| /order-processing/control/<task_id> | POST | start_order_processing | app\api_v2\production\order_processing.py |
| /order-processing/health | POST | start_order_processing | app\api_v2\production\order_processing.py |
| /order-processing/list | POST | start_order_processing | app\api_v2\production\order_processing.py |
| /order-processing/start | POST | start_order_processing | app\api_v2\production\order_processing.py |
| /order-processing/stats | POST | start_order_processing | app\api_v2\production\order_processing.py |
| /order-processing/status/<task_id> | POST | start_order_processing | app\api_v2\production\order_processing.py |
| /orders | POST | health_check | app\api_v2\production\routes.py |
| /orders | POST | health_check | app\api_v2\production\routes.py |
| /orders/<int:order_id> | POST | health_check | app\api_v2\production\routes.py |
| /orders/classification-rules | PUT | get_history_data | app\api\routes.py |
| /orders/export | POST | health_check | app\api_v2\production\routes.py |
| /orders/parse-excel | PUT | get_history_data | app\api\routes.py |
| /orders/parse-progress/<task_id> | PUT | get_history_data | app\api\routes.py |
| /orders/preview-file | PUT | get_history_data | app\api\routes.py |
| /orders/scan-files | PUT | get_history_data | app\api\routes.py |
| /orders/scan-lot-types | PUT | get_history_data | app\api\routes.py |
| /page/<table_name> | GET | get_supported_tables | app\api\routes_v3.py |
| /parse-excel-optimized | POST | None | app\api_v2\orders\optimized_parser_api.py |
| /parse-progress/<task_id> | POST | None | app\api_v2\orders\optimized_parser_api.py |
| /parsing/validate | POST | scan_attachments | app\api_v2\orders\semi_auto_api.py |
| /performance-stats | POST | None | app\api_v2\orders\optimized_parser_api.py |
| /preview-summary | GET | None | app\api_v2\orders\summary_preview_api.py |
| /priority-settings/upload | POST | health_check | app\api_v2\production\routes.py |
| /priority/device | POST | health_check | app\api_v2\production\routes.py |
| /priority/lot | POST | health_check | app\api_v2\production\routes.py |
| /priority/product | POST | health_check | app\api_v2\production\routes.py |
| /priority/product | POST | health_check | app\api_v2\production\routes.py |
| /processing/<task_id>/pause | POST | scan_attachments | app\api_v2\orders\semi_auto_api.py |
| /processing/<task_id>/status | POST | scan_attachments | app\api_v2\orders\semi_auto_api.py |
| /processing/<task_id>/stop | POST | scan_attachments | app\api_v2\orders\semi_auto_api.py |
| /processing/start | POST | scan_attachments | app\api_v2\orders\semi_auto_api.py |
| /processing/status | POST | scan_attachments | app\api_v2\orders\semi_auto_api.py |
| /product | GET | health_check | app\api_v2\production\priority_api.py |
| /product | GET | health_check | app\api_v2\production\priority_api.py |
| /production/auto-schedule | PUT | get_history_data | app\api\routes.py |
| /production/auto-schedule/stop | PUT | get_history_data | app\api\routes.py |
| /production/batch/upload | PUT | get_history_data | app\api\routes.py |
| /production/delete-file | PUT | get_history_data | app\api\routes.py |
| /production/export-file/<filename> | PUT | get_history_data | app\api\routes.py |
| /production/export-schedule | PUT | get_history_data | app\api\routes.py |
| /production/file-data/<filename> | PUT | get_history_data | app\api\routes.py |
| /production/get-import-path | PUT | get_history_data | app\api\routes.py |
| /production/history-data | PUT | get_history_data | app\api\routes.py |
| /production/import-from-directory | PUT | get_history_data | app\api\routes.py |
| /production/import-progress | PUT | get_history_data | app\api\routes.py |
| /production/imported-files | PUT | get_history_data | app\api\routes.py |
| /production/manual-schedule | PUT | get_history_data | app\api\routes.py |
| /production/save-import-path | PUT | get_history_data | app\api\routes.py |
| /production/save-order | PUT | get_history_data | app\api\routes.py |
| /production/save-priority-done | PUT | get_history_data | app\api\routes.py |
| /production/save-schedule-history | PUT | get_history_data | app\api\routes.py |
| /production/schedule-history | PUT | get_history_data | app\api\routes.py |
| /production/schedules/<int:schedule_id> | PUT | get_history_data | app\api\routes.py |
| /production/schedules/<int:schedule_id> | PUT | get_history_data | app\api\routes.py |
| /schedules | POST | health_check | app\api_v2\production\routes.py |
| /scheduling | GET | get_wait_lots | app\api\unified_production_api.py |
| /scheduling/execute | POST | health_check | app\api_v2\production\routes.py |
| /scheduling/export | POST | health_check | app\api_v2\production\routes.py |
| /scheduling/history | POST | health_check | app\api_v2\production\routes.py |
| /session/refresh | GET | health_check | app\api_v2\auth\routes.py |
| /session/status | GET | health_check | app\api_v2\auth\routes.py |
| /settings | POST | health_check | app\api_v2\system\routes.py |
| /settings | POST | health_check | app\api_v2\system\routes.py |
| /start | GET | None | app\api\scheduler_api.py |
| /statistics | GET | get_wait_lots | app\api\unified_production_api.py |
| /stats | GET | None | app\api_v2\system\dashboard.py |
| /status | GET | None | app\api\scheduler_api.py |
| /status | GET | system_status | app\api_v2\common\__init__.py |
| /stop | GET | None | app\api\scheduler_api.py |
| /summary | GET | get_wip_lot_data | app\api_v2\wip_lot_api.py |
| /summary-data | GET | None | app\api_v2\orders\summary_preview_api.py |
| /tables | GET | get_supported_tables | app\api\routes_v3.py |
| /tables | POST | resources_health_v2 | app\api_v2\resources\routes.py |
| /tables/<table_name>/columns | GET | get_supported_tables | app\api\routes_v3.py |
| /tables/<table_name>/data | GET | get_supported_tables | app\api\routes_v3.py |
| /tables/<table_name>/data | GET | get_supported_tables | app\api\routes_v3.py |
| /tables/<table_name>/data/<record_id> | GET | get_supported_tables | app\api\routes_v3.py |
| /tables/<table_name>/data/<record_id> | GET | get_supported_tables | app\api\routes_v3.py |
| /tables/<table_name>/data/batch | GET | get_supported_tables | app\api\routes_v3.py |
| /tables/<table_name>/export | GET | get_supported_tables | app\api\routes_v3.py |
| /tables/<table_name>/info | GET | get_supported_tables | app\api\routes_v3.py |
| /tables/<table_name>/structure | GET | get_supported_tables | app\api\routes_v3.py |
| /tables/<table_name>/validate | GET | get_supported_tables | app\api\routes_v3.py |
| /test | GET | None | app\api\scheduler_api.py |
| /test-all-database-connections | PUT | get_history_data | app\api\routes.py |
| /test-connection | POST | get_embed_script | app\api\dify_proxy.py |
| /test-database-connection | PUT | get_history_data | app\api\routes.py |
| /test-socketio | POST | scan_attachments | app\api_v2\orders\semi_auto_api.py |
| /test-specs | GET | get_wait_lots | app\api\unified_production_api.py |
| /test-specs/<spec_id> | GET | get_wait_lots | app\api\unified_production_api.py |
| /time | GET | system_status | app\api_v2\common\__init__.py |
| /universal/<table_name> | GET | get_supported_tables | app\api\routes_v3.py |
| /update-classification | GET | None | app\api_v2\orders\summary_preview_api.py |
| /update/<int:record_id> | GET | get_wip_lot_data | app\api_v2\wip_lot_api.py |
| /upload | GET | health_check | app\api_v2\production\wait_lots_api.py |
| /user-filter-presets | PUT | get_history_data | app\api\routes.py |
| /user-filter-presets/<int:preset_id> | PUT | get_history_data | app\api\routes.py |
| /user/activity | GET | health_check | app\api_v2\auth\routes.py |
| /user/info | GET | health_check | app\api_v2\auth\routes.py |
| /users/<username>/permissions | GET | health_check | app\api_v2\auth\routes.py |
| /users/<username>/permissions | GET | health_check | app\api_v2\auth\routes.py |
| /v1/chat-messages | POST | get_embed_script | app\api\dify_proxy.py |
| /v1/conversations | POST | get_embed_script | app\api\dify_proxy.py |
| /v1/conversations/<conversation_id>/messages | POST | get_embed_script | app\api\dify_proxy.py |
| /validate/<table_name> | POST | resources_health_v2 | app\api_v2\resources\routes.py |
| /validate/all | POST | resources_health_v2 | app\api_v2\resources\routes.py |
| /wait-lots | GET | get_wait_lots | app\api\unified_production_api.py |
| /wip-lots | GET | get_wait_lots | app\api\unified_production_api.py |


## 按模块分类

### 其他 (220 个端点)

| 版本 | 端点 | HTTP方法 | 功能 |
| --- | --- | --- | --- |
| v1 | /api/email_attachments | GET | None |
| v1 | /api/email_attachments/<int:attachment_id> | GET | None |
| v1 | /api/email_attachments/<int:attachment_id>/download | GET | None |
| v1 | /api/email_attachments/<int:attachment_id>/process | GET | None |
| v1 | /api/email_attachments/batch_process | GET | None |
| v1 | /api/email_attachments/dashboard | GET | None |
| v1 | /api/email_configs | GET | None |
| v1 | /api/email_configs | GET | None |
| v1 | /api/email_configs/<int:config_id> | GET | None |
| v1 | /api/email_configs/<int:config_id> | GET | None |
| v1 | /api/email_configs/<int:config_id> | GET | None |
| v1 | /api/email_configs/<int:config_id>/fetch | GET | None |
| v1 | /api/email_configs/<int:config_id>/preview | GET | None |
| v1 | /api/email_configs/<int:config_id>/test | GET | None |
| v1 | /api/email_configs/test | GET | None |
| v1 | /api/excel_mappings | GET | None |
| v1 | /api/excel_mappings/<int:mapping_id> | GET | None |
| v1 | /api/excel_mappings/<int:mapping_id> | GET | None |
| v1 | /api/excel_mappings/<int:mapping_id> | GET | None |
| v1 | /api/order_data | GET | None |
| v1 | /api/order_data/<int:order_id> | GET | None |
| v1 | /api/order_data/<int:order_id> | GET | None |
| v1 | /api/order_data/<task_id>/pause | POST | None |
| v1 | /api/order_data/<task_id>/status | POST | None |
| v1 | /api/order_data/<task_id>/stop | POST | None |
| v1 | /api/order_data/download_summary | GET | None |
| v1 | /api/order_data/export | GET | None |
| v1 | /api/order_data/start | POST | None |
| v1 | /api/order_data/stats | GET | None |
| v1 | /api/order_data/stats | POST | None |
| v2 | /api/v2/high-concurrency/config | POST | None |
| v2 | /api/v2/high-concurrency/email-processing/start | POST | None |
| v2 | /api/v2/high-concurrency/email-processing/status | POST | None |
| v2 | /api/v2/high-concurrency/email-processing/stop | POST | None |
| v2 | /api/v2/high-concurrency/performance/metrics | POST | None |
| v2 | /api/v2/high-concurrency/test-data/scan | POST | None |
| 未知 | / | GET | health_check |
| 未知 | / | GET | health_check |
| 未知 | /<int:lot_id> | GET | health_check |
| 未知 | /<int:lot_id> | GET | health_check |
| 未知 | /<int:lot_id> | GET | health_check |
| 未知 | /ai-config | POST | health_check |
| 未知 | /ai-settings | POST | health_check |
| 未知 | /ai/chat | POST | None |
| 未知 | /ai/db_chat | POST | None |
| 未知 | /apply-matches | GET | health_check |
| 未知 | /attachments/<int:attachment_id>/process | POST | scan_attachments |
| 未知 | /attachments/process-batch | POST | scan_attachments |
| 未知 | /attachments/scan | POST | scan_attachments |
| 未知 | /attachments/scan/local | POST | scan_attachments |
| 未知 | /batch-info/<batch_id> | GET | health_check |
| 未知 | /batch-update-classification | GET | None |
| 未知 | /cache/clear | GET | get_supported_tables |
| 未知 | /charts | GET | None |
| 未知 | /check-mysql-databases | PUT | get_history_data |
| 未知 | /check-mysql-databases | POST | health_check |
| 未知 | /classification-stats | GET | None |
| 未知 | /cleanup-tasks | POST | None |
| 未知 | /columns | GET | health_check |
| 未知 | /columns/<table_name> | POST | resources_health_v2 |
| 未知 | /config | POST | get_embed_script |
| 未知 | /config/tables/<table_name> | GET | get_supported_tables |
| 未知 | /configs | GET | None |
| 未知 | /configs | GET | None |
| 未知 | /configs/<int:config_id> | GET | None |
| 未知 | /configs/<int:config_id> | GET | None |
| 未知 | /configs/<int:config_id> | GET | None |
| 未知 | /configs/<int:config_id>/test | GET | None |
| 未知 | /configs/set-default/<int:config_id> | GET | None |
| 未知 | /configs/test | GET | None |
| 未知 | /configs/test-batch | GET | None |
| 未知 | /configs/types | GET | None |
| 未知 | /cp-summary | GET | None |
| 未知 | /cp-summary/export | GET | None |
| 未知 | /create | GET | get_wip_lot_data |
| 未知 | /data | GET | get_wip_lot_data |
| 未知 | /data-source/equipment-status | POST | health_check |
| 未知 | /data-source/metrics | POST | health_check |
| 未知 | /data-source/status | POST | health_check |
| 未知 | /data-source/switch | POST | health_check |
| 未知 | /data-source/uph-equipment | POST | health_check |
| 未知 | /data-source/validate | POST | health_check |
| 未知 | /data-source/wait-lots | POST | health_check |
| 未知 | /data/<table_name> | POST | resources_health_v2 |
| 未知 | /data/<table_name> | POST | resources_health_v2 |
| 未知 | /data/<table_name> | POST | resources_health_v2 |
| 未知 | /data/<table_name>/<record_id> | POST | resources_health_v2 |
| 未知 | /data/<table_name>/batch | POST | resources_health_v2 |
| 未知 | /data/<table_name>/export | POST | resources_health_v2 |
| 未知 | /data/export | POST | scan_attachments |
| 未知 | /data/preview | POST | scan_attachments |
| 未知 | /database-status | PUT | get_history_data |
| 未知 | /database/config | POST | health_check |
| 未知 | /database/status | POST | health_check |
| 未知 | /database/test-connection | POST | health_check |
| 未知 | /debug/login-status | GET | health_check |
| 未知 | /delete/<int:record_id> | GET | get_wip_lot_data |
| 未知 | /device | GET | health_check |
| 未知 | /download-summary/<filename> | GET | None |
| 未知 | /duplicate-confirm | POST | None |
| 未知 | /email-configs/<int:config_id>/get-auth | POST | scan_attachments |
| 未知 | /email-configs/<int:config_id>/save-auth | POST | scan_attachments |
| 未知 | /email-scheduler | POST | health_check |
| 未知 | /embed.min.js | POST | get_embed_script |
| 未知 | /ft-summary | GET | None |
| 未知 | /ft-summary/export | GET | None |
| 未知 | /generate-engineering-summary | GET | None |
| 未知 | /generate-production-summary | GET | None |
| 未知 | /health | GET | get_wait_lots |
| 未知 | /health | GET | health_check |
| 未知 | /health | GET | health_check |
| 未知 | /health | GET | health_check |
| 未知 | /health | POST | health_check |
| 未知 | /health | GET | health_check |
| 未知 | /health | POST | resources_health_v2 |
| 未知 | /health | GET | health_check |
| 未知 | /health | POST | health_check |
| 未知 | /health-check | GET | None |
| 未知 | /jobs | GET | None |
| 未知 | /jobs | GET | None |
| 未知 | /jobs/<job_id> | GET | None |
| 未知 | /jobs/<job_id>/run | GET | None |
| 未知 | /logs | GET | None |
| 未知 | /lot | GET | health_check |
| 未知 | /lots/<lot_id> | GET | get_wait_lots |
| 未知 | /lots/<lot_id>/equipment | GET | get_wait_lots |
| 未知 | /lots/<lot_id>/priority | GET | get_wait_lots |
| 未知 | /mappings | GET | None |
| 未知 | /mappings | GET | None |
| 未知 | /match-batches | GET | health_check |
| 未知 | /matching-report | GET | health_check |
| 未知 | /menu-settings | GET | health_check |
| 未知 | /metrics | GET | None |
| 未知 | /metrics | GET | health_check |
| 未知 | /metrics/clear | GET | health_check |
| 未知 | /metrics/history | GET | health_check |
| 未知 | /migration/status | GET | get_supported_tables |
| 未知 | /migration/test-page | GET | get_supported_tables |
| 未知 | /move-to-scheduled | GET | health_check |
| 未知 | /navigation | GET | get_supported_tables |
| 未知 | /order-processing/control/<task_id> | POST | start_order_processing |
| 未知 | /order-processing/health | POST | start_order_processing |
| 未知 | /order-processing/list | POST | start_order_processing |
| 未知 | /order-processing/start | POST | start_order_processing |
| 未知 | /order-processing/stats | POST | start_order_processing |
| 未知 | /order-processing/status/<task_id> | POST | start_order_processing |
| 未知 | /orders | POST | health_check |
| 未知 | /orders | POST | health_check |
| 未知 | /page/<table_name> | GET | get_supported_tables |
| 未知 | /parse-excel-optimized | POST | None |
| 未知 | /parse-progress/<task_id> | POST | None |
| 未知 | /parsing/validate | POST | scan_attachments |
| 未知 | /performance-stats | POST | None |
| 未知 | /preview-summary | GET | None |
| 未知 | /priority-settings/upload | POST | health_check |
| 未知 | /priority/device | POST | health_check |
| 未知 | /priority/lot | POST | health_check |
| 未知 | /priority/product | POST | health_check |
| 未知 | /priority/product | POST | health_check |
| 未知 | /processing/<task_id>/pause | POST | scan_attachments |
| 未知 | /processing/<task_id>/status | POST | scan_attachments |
| 未知 | /processing/<task_id>/stop | POST | scan_attachments |
| 未知 | /processing/start | POST | scan_attachments |
| 未知 | /processing/status | POST | scan_attachments |
| 未知 | /product | GET | health_check |
| 未知 | /product | GET | health_check |
| 未知 | /schedules | POST | health_check |
| 未知 | /scheduling | GET | get_wait_lots |
| 未知 | /scheduling/execute | POST | health_check |
| 未知 | /scheduling/export | POST | health_check |
| 未知 | /scheduling/history | POST | health_check |
| 未知 | /session/refresh | GET | health_check |
| 未知 | /session/status | GET | health_check |
| 未知 | /settings | POST | health_check |
| 未知 | /settings | POST | health_check |
| 未知 | /start | GET | None |
| 未知 | /statistics | GET | get_wait_lots |
| 未知 | /stats | GET | None |
| 未知 | /status | GET | None |
| 未知 | /status | GET | system_status |
| 未知 | /stop | GET | None |
| 未知 | /summary | GET | get_wip_lot_data |
| 未知 | /summary-data | GET | None |
| 未知 | /tables | GET | get_supported_tables |
| 未知 | /tables | POST | resources_health_v2 |
| 未知 | /tables/<table_name>/columns | GET | get_supported_tables |
| 未知 | /tables/<table_name>/data | GET | get_supported_tables |
| 未知 | /tables/<table_name>/data | GET | get_supported_tables |
| 未知 | /tables/<table_name>/data/<record_id> | GET | get_supported_tables |
| 未知 | /tables/<table_name>/data/<record_id> | GET | get_supported_tables |
| 未知 | /tables/<table_name>/data/batch | GET | get_supported_tables |
| 未知 | /tables/<table_name>/export | GET | get_supported_tables |
| 未知 | /tables/<table_name>/info | GET | get_supported_tables |
| 未知 | /tables/<table_name>/structure | GET | get_supported_tables |
| 未知 | /tables/<table_name>/validate | GET | get_supported_tables |
| 未知 | /test | GET | None |
| 未知 | /test-all-database-connections | PUT | get_history_data |
| 未知 | /test-connection | POST | get_embed_script |
| 未知 | /test-database-connection | PUT | get_history_data |
| 未知 | /test-socketio | POST | scan_attachments |
| 未知 | /test-specs | GET | get_wait_lots |
| 未知 | /test-specs/<spec_id> | GET | get_wait_lots |
| 未知 | /time | GET | system_status |
| 未知 | /universal/<table_name> | GET | get_supported_tables |
| 未知 | /update-classification | GET | None |
| 未知 | /update/<int:record_id> | GET | get_wip_lot_data |
| 未知 | /upload | GET | health_check |
| 未知 | /user-filter-presets | PUT | get_history_data |
| 未知 | /user-filter-presets/<int:preset_id> | PUT | get_history_data |
| 未知 | /user/activity | GET | health_check |
| 未知 | /user/info | GET | health_check |
| 未知 | /users/<username>/permissions | GET | health_check |
| 未知 | /users/<username>/permissions | GET | health_check |
| 未知 | /v1/chat-messages | POST | get_embed_script |
| 未知 | /v1/conversations | POST | get_embed_script |
| 未知 | /v1/conversations/<conversation_id>/messages | POST | get_embed_script |
| 未知 | /validate/<table_name> | POST | resources_health_v2 |
| 未知 | /validate/all | POST | resources_health_v2 |
| 未知 | /wait-lots | GET | get_wait_lots |
| 未知 | /wip-lots | GET | get_wait_lots |

### 生产管理 (21 个端点)

| 版本 | 端点 | HTTP方法 | 功能 |
| --- | --- | --- | --- |
| v1 | /api/production/history-times | PUT | get_history_data |
| 未知 | /production/auto-schedule | PUT | get_history_data |
| 未知 | /production/auto-schedule/stop | PUT | get_history_data |
| 未知 | /production/batch/upload | PUT | get_history_data |
| 未知 | /production/delete-file | PUT | get_history_data |
| 未知 | /production/export-file/<filename> | PUT | get_history_data |
| 未知 | /production/export-schedule | PUT | get_history_data |
| 未知 | /production/file-data/<filename> | PUT | get_history_data |
| 未知 | /production/get-import-path | PUT | get_history_data |
| 未知 | /production/history-data | PUT | get_history_data |
| 未知 | /production/import-from-directory | PUT | get_history_data |
| 未知 | /production/import-progress | PUT | get_history_data |
| 未知 | /production/imported-files | PUT | get_history_data |
| 未知 | /production/manual-schedule | PUT | get_history_data |
| 未知 | /production/save-import-path | PUT | get_history_data |
| 未知 | /production/save-order | PUT | get_history_data |
| 未知 | /production/save-priority-done | PUT | get_history_data |
| 未知 | /production/save-schedule-history | PUT | get_history_data |
| 未知 | /production/schedule-history | PUT | get_history_data |
| 未知 | /production/schedules/<int:schedule_id> | PUT | get_history_data |
| 未知 | /production/schedules/<int:schedule_id> | PUT | get_history_data |

### 订单管理 (8 个端点)

| 版本 | 端点 | HTTP方法 | 功能 |
| --- | --- | --- | --- |
| 未知 | /orders/<int:order_id> | POST | health_check |
| 未知 | /orders/classification-rules | PUT | get_history_data |
| 未知 | /orders/export | POST | health_check |
| 未知 | /orders/parse-excel | PUT | get_history_data |
| 未知 | /orders/parse-progress/<task_id> | PUT | get_history_data |
| 未知 | /orders/preview-file | PUT | get_history_data |
| 未知 | /orders/scan-files | PUT | get_history_data |
| 未知 | /orders/scan-lot-types | PUT | get_history_data |

### 认证授权 (5 个端点)

| 版本 | 端点 | HTTP方法 | 功能 |
| --- | --- | --- | --- |
| 未知 | /auth/users | GET | None |
| 未知 | /auth/users | GET | None |
| 未知 | /auth/users/<username> | GET | None |
| 未知 | /auth/users/<username> | GET | None |
| 未知 | /auth/users/<username> | GET | None |

