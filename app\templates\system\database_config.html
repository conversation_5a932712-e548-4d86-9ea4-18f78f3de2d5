{% extends "base.html" %}

{% block title %}数据库配置管理- AEC-FT ICP
<style>
:root {
    --primary-color: #b72424;
    --secondary-color: #d73027;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
}

/* 文字选中效果优化 */
::selection {
    background-color: rgba(255, 255, 255, 0.9);
    color: var(--primary-color);
}

.card-header ::selection {
    background-color: rgba(255, 255, 255, 0.3);
    color: white;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(183, 36, 36, 0.3);
}

.navbar-brand, .nav-link.active {
    color: var(--primary-color) !important;
}

.card-header {
    background-color: var(--primary-color);
    color: white;
    transition: all 0.3s ease;
}

/* 按钮交互优化 */
.btn {
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
}

.btn-outline-primary:hover {
    box-shadow: 0 4px 12px rgba(183, 36, 36, 0.3);
}

.btn-outline-success:hover {
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

.btn-outline-info:hover {
    box-shadow: 0 4px 12px rgba(23, 162, 184, 0.3);
}

/* 卡片悬浮效果 */
.card {
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
}

.card:hover {
    box-shadow: 0 6px 20px rgba(0,0,0,0.1);
    transform: translateY(-2px);
}

/* 表单元素优化 */
.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(183, 36, 36, 0.25);
}

/* 徽章样式优化 */
.badge {
    transition: all 0.3s ease;
}

.badge:hover {
    transform: scale(1.05);
}

/* 图标动画效果 */
.fa-sync-alt, .fa-exchange-alt {
    transition: transform 0.3s ease;
}

.btn:hover .fa-sync-alt {
    transform: rotate(180deg);
}

.btn:hover .fa-exchange-alt {
    transform: scale(1.1);
}
</style>
{% endblock %}

{% block page_title %}数据库配置管理{% endblock %}

{% block extra_css %}
<style>
    .data-source-card {
        transition: all 0.3s ease;
        border: 2px solid #e9ecef;
        cursor: pointer;
    }
    
    .data-source-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }
    
    .data-source-card.active {
        border-color: #28a745;
        box-shadow: 0 0 0 0.25rem rgba(40, 167, 69, 0.1);
    }
    
    .data-source-card.error {
        border-color: #dc3545;
        box-shadow: 0 0 0 0.25rem rgba(220, 53, 69, 0.1);
    }
    
    .status-indicator {
        position: absolute;
        top: 10px;
        right: 10px;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        transition: all 0.3s ease;
    }
    
    .status-indicator:hover {
        transform: scale(1.2);
    }
    
    .status-indicator.online {
        background-color: #28a745;
        box-shadow: 0 0 6px rgba(40, 167, 69, 0.5);
        animation: pulse-online 2s infinite;
    }
    
    .status-indicator.offline {
        background-color: #dc3545;
        box-shadow: 0 0 6px rgba(220, 53, 69, 0.5);
        animation: pulse-offline 1s infinite;
    }
    
    @keyframes pulse-online {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.7; }
    }
    
    @keyframes pulse-offline {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.3; }
    }
    
    .switch-btn {
        transition: all 0.3s ease;
    }
    
    .switch-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    }
    
    /* 列表组优化 */
    .list-group-item {
        transition: all 0.3s ease;
        border: 1px solid #e9ecef;
    }
    
    .list-group-item:hover {
        background-color: rgba(183, 36, 36, 0.05);
        border-color: var(--primary-color);
    }
    
    /* 警告框优化 */
    .alert {
        transition: all 0.3s ease;
        border-radius: 8px;
    }
    
    .alert:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    }
    
    /* 图标悬浮效果 */
    .fa-database, .fa-file-excel, .fa-cogs, .fa-tachometer-alt, .fa-file-alt, .fa-info-circle {
        transition: all 0.3s ease;
    }
    
    .card-body:hover .fa-database,
    .card-body:hover .fa-file-excel,
    .card-body:hover .fa-cogs,
    .card-body:hover .fa-tachometer-alt,
    .card-body:hover .fa-file-alt,
    .card-body:hover .fa-info-circle {
        transform: scale(1.1) rotate(5deg);
    }
    
    /* AI集成标签页样式 */
    .status-badge {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
        border-radius: 0.375rem;
    }
    
    .setting-description {
        font-size: 0.875rem;
        color: #6c757d;
        margin-top: 0.25rem;
        margin-bottom: 0;
    }
    
    .prompt-editor {
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-size: 0.875rem;
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 0.375rem;
        resize: vertical;
    }
    
    .prompt-editor:focus {
        background-color: #ffffff;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 0.2rem rgba(183, 36, 36, 0.25);
    }
    
    .table-permissions .form-check {
        margin-bottom: 0.75rem;
        padding-left: 1.75rem;
    }
    
    .table-permissions .form-check-label {
        font-size: 0.875rem;
        line-height: 1.5;
    }
    
    .table-permissions code {
        background-color: rgba(183, 36, 36, 0.1);
        color: var(--primary-color);
        padding: 0.125rem 0.25rem;
        border-radius: 0.25rem;
        font-size: 0.8rem;
    }
    
    /* 响应式优化 */
    @media (max-width: 768px) {
        .data-source-card {
            margin-bottom: 15px;
        }
        
        .btn {
            margin-bottom: 10px;
        }
        
        .table-permissions {
            margin-top: 1rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- 页面标题 -->
            <div class="card mb-4" style="background: linear-gradient(135deg, var(--primary-color) 0%, #a01f1f 100%); color: white;">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="mb-2"><i class="fas fa-database me-3"></i>统一数据库配置管理</h2>
                            <p class="mb-0 opacity-75">集中管理MySQL、PostgreSQL等数据库连接配置，统一系统数据源</p>
                        </div>
                        <div>
                            <button class="btn btn-light me-2" onclick="refreshConfigs()">
                                <i class="fas fa-sync-alt me-1"></i>刷新
                            </button>
                            <button class="btn btn-warning me-2" onclick="batchTest()">
                                <i class="fas fa-vial me-1"></i>批量测试
                            </button>
                            <button class="btn btn-success" onclick="addConfig()">
                                <i class="fas fa-plus me-1"></i>新增配置
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 功能说明 -->
            <div class="alert alert-info" style="border-left: 4px solid var(--primary-color);">
                <h6><i class="fas fa-info-circle me-1"></i>功能说明</h6>
                <p class="mb-0">统一管理所有数据库连接信息，支持MySQL、PostgreSQL、SQLite等多种数据库类型。密码采用安全加密存储，支持连接测试和映射关系管理，确保系统数据源的统一性和安全性。</p>
            </div>
            
            <!-- 标签页导航 -->
            <ul class="nav nav-tabs" id="configTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="configs-tab" data-bs-toggle="tab" data-bs-target="#configs" type="button" role="tab">
                        <i class="fas fa-cog me-1"></i>数据库配置
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="mappings-tab" data-bs-toggle="tab" data-bs-target="#mappings" type="button" role="tab">
                        <i class="fas fa-sitemap me-1"></i>映射关系
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="monitor-tab" data-bs-toggle="tab" data-bs-target="#monitor" type="button" role="tab">
                        <i class="fas fa-chart-line me-1"></i>状态监控
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="ai-integration-tab" data-bs-toggle="tab" data-bs-target="#ai-integration" type="button" role="tab">
                        <i class="fas fa-brain me-1"></i>AI集成
                    </button>
                </li>
            </ul>

            <!-- 标签页内容 -->
            <div class="tab-content" id="configTabContent">
                <!-- 数据库配置标签页 -->
                <div class="tab-pane fade show active" id="configs" role="tabpanel">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-database me-2"></i>数据库配置列表
                            </h5>
                        </div>
                        <div class="card-body">
                            <!-- 搜索和筛选 -->
                            <div class="row mb-4">
                                <div class="col-md-4">
                                    <input type="text" class="form-control" id="configSearch" placeholder="搜索配置名称、主机地址...">
                                </div>
                                <div class="col-md-3">
                                    <select class="form-select" id="typeFilter">
                                        <option value="">所有类型</option>
                                        <option value="mysql">MySQL</option>
                                        <option value="postgresql">PostgreSQL</option>
                                        <option value="sqlite">SQLite</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <select class="form-select" id="statusFilter">
                                        <option value="">所有状态</option>
                                        <option value="true">启用</option>
                                        <option value="false">禁用</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <button class="btn btn-outline-primary w-100" onclick="applyFilters()">
                                        <i class="fas fa-search me-1"></i>筛选
                                    </button>
                                </div>
                            </div>

                            <!-- 配置列表 -->
                            <div id="configsList" class="row">
                                <!-- 配置卡片将在这里动态加载 -->
                                <div class="col-12 text-center">
                                    <div class="spinner-border" role="status">
                                        <span class="visually-hidden">加载中...</span>
                                    </div>
                                    <p class="mt-2">正在加载配置...</p>
                                </div>
                            </div>

                            <!-- 分页 -->
                            <nav aria-label="配置分页">
                                <ul class="pagination justify-content-center" id="configsPagination">
                                </ul>
                            </nav>
                        </div>
                    </div>
                </div>

                <!-- 映射关系标签页 -->
                <div class="tab-pane fade" id="mappings" role="tabpanel">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">数据库映射关系</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped" id="mappingsTable">
                                    <thead>
                                        <tr>
                                            <th>映射类型</th>
                                            <th>目标名称</th>
                                            <th>数据库配置</th>
                                            <th>优先级</th>
                                            <th>状态</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <!-- 映射数据将动态加载 -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 状态监控标签页 -->
                <div class="tab-pane fade" id="monitor" role="tabpanel">
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card text-white" style="background: linear-gradient(135deg, var(--primary-color) 0%, #a01f1f 100%);">
                                <div class="card-body text-center">
                                    <div style="font-size: 2rem; font-weight: bold;" id="totalConfigs">0</div>
                                    <div>总配置数</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-white" style="background: linear-gradient(135deg, var(--primary-color) 0%, #a01f1f 100%);">
                                <div class="card-body text-center">
                                    <div style="font-size: 2rem; font-weight: bold;" id="activeConfigs">0</div>
                                    <div>活跃配置</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-white" style="background: linear-gradient(135deg, var(--primary-color) 0%, #a01f1f 100%);">
                                <div class="card-body text-center">
                                    <div style="font-size: 2rem; font-weight: bold;" id="totalMappings">0</div>
                                    <div>映射关系</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-white" style="background: linear-gradient(135deg, var(--primary-color) 0%, #a01f1f 100%);">
                                <div class="card-body text-center">
                                    <div style="font-size: 2rem; font-weight: bold;" id="healthyConfigs">0</div>
                                    <div>健康配置</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">连接状态监控</h5>
                        </div>
                        <div class="card-body">
                            <div id="monitoringData">
                                <!-- 监控数据将动态加载 -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- AI集成标签页 -->
            <div class="tab-pane fade" id="ai-integration" role="tabpanel">
                <div class="row">
                    <!-- AI助手配置 -->
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-robot me-2"></i>AI助手配置
                                </h5>
                            </div>
                            <div class="card-body">
                                <form id="aiIntegrationForm">
                                    <!-- AI功能开关 -->
                                    <div class="mb-4">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="enableAI" name="ai_enabled">
                                            <label class="form-check-label" for="enableAI">启用AI助手</label>
                                            <span class="status-badge ms-2" id="aiStatus">未启用</span>
                                        </div>
                                        <p class="setting-description">控制AI助手的全局开关，关闭后AI相关功能将不可用。</p>
                                    </div>

                                    <!-- 数据库查询功能 -->
                                    <div class="mb-4">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="enableDatabaseQuery" name="database_query_enabled">
                                            <label class="form-check-label" for="enableDatabaseQuery">启用数据库查询功能</label>
                                        </div>
                                        <p class="setting-description">允许AI助手查询MySQL数据库以回答业务相关问题。</p>
                                    </div>

                                    <!-- 智能推荐功能 -->
                                    <div class="mb-4">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="enableSmartRecommendation" name="smart_recommendation_enabled">
                                            <label class="form-check-label" for="enableSmartRecommendation">启用智能推荐</label>
                                        </div>
                                        <p class="setting-description">基于历史数据和当前状态，提供排产和资源分配建议。</p>
                                    </div>
                                    
                                    <!-- 模型参数设置 -->
                                    <div class="mb-4">
                                        <h6 class="mb-3"><i class="fas fa-sliders-h me-2"></i>模型参数</h6>
                                        
                                        <div class="row">
                                            <div class="col-12">
                                                <div class="mb-3">
                                                    <label for="temperature" class="form-label">创造性温度 (Temperature)</label>
                                                    <div class="d-flex align-items-center">
                                                        <input type="range" class="form-range flex-grow-1 me-2" id="temperature" 
                                                               name="temperature" min="0" max="1" step="0.1" value="0.3">
                                                        <span id="temperatureValue" class="badge bg-primary" style="width: 50px; text-align: center;">0.3</span>
                                                    </div>
                                                    <div class="form-text">控制AI回复的创造性，值越低回复越确定性，建议0.1-0.5。</div>
                                                </div>
                                            </div>
                                            <div class="col-12">
                                                <div class="mb-3">
                                                    <label for="maxTokens" class="form-label">最大生成长度</label>
                                                    <input type="number" class="form-control" id="maxTokens" name="max_tokens" 
                                                           min="100" max="4000" step="100" value="1000">
                                                    <div class="form-text">控制AI回复的最大长度，建议800-2000。</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="d-flex gap-2">
                                        <button type="button" class="btn btn-primary" id="saveAIIntegrationBtn">
                                            <i class="fas fa-save me-1"></i>保存配置
                                        </button>
                                        <button type="button" class="btn btn-outline-secondary" id="testAIIntegrationBtn">
                                            <i class="fas fa-flask me-1"></i>测试AI功能
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- 数据库连接状态和API状态 -->
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-plug me-2"></i>连接状态
                                </h5>
                            </div>
                            <div class="card-body">
                                <!-- API连接状态 -->
                                <div class="mb-4">
                                    <h6 class="mb-3"><i class="fas fa-api me-2"></i>API连接状态</h6>
                                    <div class="alert alert-info" id="apiStatus">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <span>
                                                <i class="fas fa-info-circle me-2"></i>API连接状态
                                            </span>
                                            <button type="button" class="btn btn-sm btn-outline-info" id="checkApiStatusBtn">
                                                <i class="fas fa-sync-alt me-1"></i>检查连接
                                            </button>
                                        </div>
                                        <div class="mt-2" id="apiStatusDetails">
                                            正在检查API连接状态...
                                        </div>
                                    </div>
                                </div>

                                <!-- 数据库查询统计 -->
                                <div class="mb-4">
                                    <h6 class="mb-3"><i class="fas fa-chart-bar me-2"></i>查询统计</h6>
                                    <div class="row">
                                        <div class="col-6">
                                            <div class="text-center">
                                                <div class="fs-4 fw-bold text-primary" id="totalQueries">--</div>
                                                <small class="text-muted">总查询次数</small>
                                            </div>
                                        </div>
                                        <div class="col-6">
                                            <div class="text-center">
                                                <div class="fs-4 fw-bold text-success" id="successfulQueries">--</div>
                                                <small class="text-muted">成功查询</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 系统提示词设置 -->
                                <div class="mb-4">
                                    <h6 class="mb-3"><i class="fas fa-file-text me-2"></i>系统提示词</h6>
                                    <div class="mb-3">
                                        <textarea class="form-control prompt-editor" id="systemPrompt" name="system_prompt" rows="6"
                                                  placeholder="定义AI助手的角色、行为和回答风格..."></textarea>
                                        <div class="form-text">定义AI助手在车规芯片终测调度平台中的专业角色和回答风格。</div>
                                    </div>
                                    
                                    <div class="d-flex gap-2">
                                        <button type="button" class="btn btn-sm btn-outline-secondary" id="resetPromptBtn">
                                            <i class="fas fa-undo me-1"></i>恢复默认
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-info" id="previewPromptBtn">
                                            <i class="fas fa-eye me-1"></i>预览效果
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 数据库表权限配置 -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-shield-alt me-2"></i>数据库表权限配置
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    配置AI助手可以访问的数据库表，确保数据安全。
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6 class="mb-3">允许查询的表</h6>
                                        <div class="table-permissions" id="allowedTables">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="table_eqp_status" checked>
                                                <label class="form-check-label" for="table_eqp_status">
                                                    <code>eqp_status</code> - 设备状态表
                                                </label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="table_et_wait_lot" checked>
                                                <label class="form-check-label" for="table_et_wait_lot">
                                                    <code>et_wait_lot</code> - 等待批次表
                                                </label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="table_ct" checked>
                                                <label class="form-check-label" for="table_ct">
                                                    <code>ct</code> - 周期时间表
                                                </label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="table_et_uph_eqp">
                                                <label class="form-check-label" for="table_et_uph_eqp">
                                                    <code>et_uph_eqp</code> - 设备效率表
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <h6 class="mb-3">权限说明</h6>
                                        <ul class="list-group list-group-flush">
                                            <li class="list-group-item">
                                                <strong>只读权限</strong>：AI仅能查询数据，不能修改
                                            </li>
                                            <li class="list-group-item">
                                                <strong>表级控制</strong>：按表粒度控制访问权限
                                            </li>
                                            <li class="list-group-item">
                                                <strong>安全查询</strong>：自动过滤敏感字段
                                            </li>
                                            <li class="list-group-item">
                                                <strong>查询限制</strong>：限制单次查询返回的记录数量
                                            </li>
                                        </ul>
                                    </div>
                                </div>

                                <div class="mt-3">
                                    <button type="button" class="btn btn-primary" id="saveTablePermissionsBtn">
                                        <i class="fas fa-save me-1"></i>保存权限配置
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 配置编辑模态框 -->
<div class="modal fade" id="configModal" tabindex="-1" aria-labelledby="configModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header" style="background-color: var(--primary-color); color: white;">
                <h5 class="modal-title" id="configModalTitle">新增数据库配置</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="configForm">
                    <input type="hidden" id="configId">
                    
                    <!-- 基本信息 -->
                    <div style="margin-bottom: 1.5rem; padding: 1.2rem; border: 1px solid #e9ecef; border-radius: 8px; background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);">
                        <h6 style="margin-bottom: 1rem; color: var(--primary-color); font-weight: 600;">
                            <i class="fas fa-info-circle me-1"></i>基本信息
                        </h6>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="configName" class="form-label">配置名称 *</label>
                                    <input type="text" class="form-control" id="configName" name="name" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="configType" class="form-label">数据库类型 *</label>
                                    <select class="form-select" id="configType" name="db_type" required>
                                        <option value="">请选择...</option>
                                        <option value="mysql">MySQL</option>
                                        <option value="postgresql">PostgreSQL</option>
                                        <option value="sqlite">SQLite</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="configDescription" class="form-label">描述</label>
                            <textarea class="form-control" id="configDescription" name="description" rows="2"></textarea>
                        </div>
                    </div>

                    <!-- 连接信息 -->
                    <div style="margin-bottom: 1.5rem; padding: 1.2rem; border: 1px solid #e9ecef; border-radius: 8px; background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);">
                        <h6 style="margin-bottom: 1rem; color: var(--primary-color); font-weight: 600;">
                            <i class="fas fa-server me-1"></i>连接信息
                        </h6>
                        <div class="row">
                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label for="configHost" class="form-label">主机地址 *</label>
                                    <input type="text" class="form-control" id="configHost" name="host" required>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="configPort" class="form-label">端口 *</label>
                                    <input type="number" class="form-control" id="configPort" name="port" required>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="configUsername" class="form-label">用户名 *</label>
                                    <input type="text" class="form-control" id="configUsername" name="username" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="configPassword" class="form-label">密码 *</label>
                                    <input type="password" class="form-control" id="configPassword" name="password" required>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="configDatabase" class="form-label">数据库名 *</label>
                            <input type="text" class="form-control" id="configDatabase" name="database_name" required>
                        </div>
                    </div>

                    <!-- 测试结果区域 -->
                    <div id="testResultContainer" style="display: none;">
                        <div id="testResult" style="margin-top: 0.75rem; padding: 0.75rem; border-radius: 6px; font-size: 0.9rem; font-weight: 500;">
                            <!-- 测试结果将在这里显示 -->
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-outline-primary" onclick="testConnectionForm()">
                    <i class="fas fa-vial me-1"></i>测试连接
                </button>
                <button type="button" class="btn btn-primary" onclick="saveConfig()">
                    <i class="fas fa-save me-1"></i>保存配置
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Toast 通知 -->
<div class="toast-container position-fixed top-0 end-0 p-3">
    <div id="alertToast" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="toast-header">
            <i class="fas fa-info-circle text-primary me-2"></i>
            <strong class="me-auto">系统通知</strong>
            <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
        </div>
        <div class="toast-body" id="toastMessage">
            <!-- 消息内容 -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 全局变量
let currentConfigs = [];
let currentPage = 1;
let totalPages = 1;
let perPage = 10;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    loadConfigs();
    loadMappings();
    loadMonitoringData();
    loadAIIntegrationConfig();
});

// ===============================
// AI集成功能
// ===============================

// AI配置相关元素
const temperatureSlider = document.getElementById('temperature');
const temperatureValue = document.getElementById('temperatureValue');
const systemPromptTextarea = document.getElementById('systemPrompt');
const saveAIIntegrationBtn = document.getElementById('saveAIIntegrationBtn');
const testAIIntegrationBtn = document.getElementById('testAIIntegrationBtn');
const resetPromptBtn = document.getElementById('resetPromptBtn');
const checkApiStatusBtn = document.getElementById('checkApiStatusBtn');
const saveTablePermissionsBtn = document.getElementById('saveTablePermissionsBtn');

// 默认系统提示词
const defaultSystemPrompt = `你是车规芯片终测智能调度平台的专业AI助手。你的主要职责包括：

1. 基于MySQL数据库的实时数据查询和分析
2. 提供生产排产建议和优化方案
3. 解答设备状态、批次管理、工程配方等相关问题
4. 协助故障诊断和性能优化

工作原则：
- 仅基于数据库中的真实数据回答问题，不编造信息
- 优先使用准确的数据分析结果
- 提供专业的车规芯片终测行业建议
- 保持回答的准确性和实用性`;

// 温度滑块事件
if (temperatureSlider && temperatureValue) {
    temperatureSlider.addEventListener('input', function() {
        temperatureValue.textContent = this.value;
    });
}

// 加载AI集成配置
function loadAIIntegrationConfig() {
    fetch('/api/ai-settings')
        .then(response => response.json())
        .then(data => {
            if (data.database) {
                const dbConfig = data.database;
                
                // 更新AI功能开关
                document.getElementById('enableAI').checked = dbConfig.enabled || false;
                updateStatusBadge('aiStatus', dbConfig.enabled ? 'success' : 'warning', 
                                dbConfig.enabled ? '已启用' : '已禁用');
                
                // 数据库查询功能状态
                const isDatabaseEnabled = dbConfig.type === 'mysql';
                document.getElementById('enableDatabaseQuery').checked = isDatabaseEnabled;
                
                // 智能推荐功能
                document.getElementById('enableSmartRecommendation').checked = dbConfig.prioritize_database || false;
                
                // 模型参数
                if (dbConfig.model) {
                    if (dbConfig.model.temperature !== undefined && temperatureSlider) {
                        temperatureSlider.value = dbConfig.model.temperature;
                        temperatureValue.textContent = dbConfig.model.temperature;
                    }
                    
                    if (dbConfig.model.max_tokens !== undefined) {
                        document.getElementById('maxTokens').value = dbConfig.model.max_tokens;
                    }
                }
                
                // 系统提示词
                if (dbConfig.system_prompt && systemPromptTextarea) {
                    systemPromptTextarea.value = dbConfig.system_prompt;
                } else if (systemPromptTextarea) {
                    systemPromptTextarea.value = defaultSystemPrompt;
                }
            } else {
                // 使用默认值
                document.getElementById('enableAI').checked = true;
                document.getElementById('enableDatabaseQuery').checked = true;
                document.getElementById('enableSmartRecommendation').checked = true;
                if (temperatureSlider) {
                    temperatureSlider.value = 0.3;
                    temperatureValue.textContent = 0.3;
                }
                document.getElementById('maxTokens').value = 1000;
                if (systemPromptTextarea) {
                    systemPromptTextarea.value = defaultSystemPrompt;
                }
                updateStatusBadge('aiStatus', 'success', '已启用');
            }
        })
        .catch(error => {
            console.error('加载AI配置失败:', error);
            updateStatusBadge('aiStatus', 'error', '加载失败');
        });
    
    // 初始检查API状态
    checkApiStatus();
}

// 保存AI集成配置
function saveAIIntegrationConfig() {
    const aiEnabled = document.getElementById('enableAI').checked;
    const dbQueryEnabled = document.getElementById('enableDatabaseQuery').checked;
    const smartRecommendationEnabled = document.getElementById('enableSmartRecommendation').checked;
    
    const config = {
        database: {
            enabled: aiEnabled,
            auto_db_path: "instance/aps.db",
            prioritize_database: smartRecommendationEnabled,
            type: dbQueryEnabled ? "mysql" : "sqlite",
            model: {
                temperature: parseFloat(temperatureSlider?.value || 0.3),
                max_tokens: parseInt(document.getElementById('maxTokens').value || 1000)
            },
            system_prompt: systemPromptTextarea?.value || defaultSystemPrompt
        }
    };
    
    fetch('/api/ai-settings', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(config)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('AI集成配置已保存成功！', 'success');
            updateStatusBadge('aiStatus', aiEnabled ? 'success' : 'warning', 
                            aiEnabled ? '已启用' : '已禁用');
        } else {
            showAlert(data.message || '保存失败', 'danger');
        }
    })
    .catch(error => {
        console.error('保存AI配置失败:', error);
        showAlert('保存失败: ' + error.message, 'danger');
    });
}

// 测试AI功能
function testAIIntegrationFunction() {
    const testResult = document.getElementById('aiTestResult');
    
    fetch('/api/ai-test', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
            type: document.getElementById('enableDatabaseQuery').checked ? 'database' : 'basic'
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success && data.test_result) {
            showAlert('AI功能测试成功！', 'success');
            updateQueryStats(data.test_result);
        } else {
            showAlert('AI功能测试失败: ' + (data.error || '未知错误'), 'danger');
        }
    })
    .catch(error => {
        console.error('测试AI功能失败:', error);
        showAlert('测试失败: ' + error.message, 'danger');
    });
}

// 检查API状态
function checkApiStatus() {
    const statusDiv = document.getElementById('apiStatusDetails');
    
    fetch('/api/ai-status')
        .then(response => response.json())
        .then(data => {
            let statusHTML = '';
            
            if (data.success) {
                statusHTML = `
                    <div class="row">
                        <div class="col-md-6">
                            <div class="d-flex justify-content-between">
                                <span>连接状态:</span>
                                <span class="badge bg-success">正常</span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="d-flex justify-content-between">
                                <span>模型版本:</span>
                                <span class="badge bg-info">${data.model_version || '未知'}</span>
                            </div>
                        </div>
                    </div>
                `;
                
                if (data.database_status) {
                    statusHTML += `
                        <div class="row mt-2">
                            <div class="col-12">
                                <div class="d-flex justify-content-between">
                                    <span>数据库连接:</span>
                                    <span class="badge bg-${data.database_status.connected ? 'success' : 'danger'}">
                                        ${data.database_status.connected ? '已连接' : '未连接'}
                                    </span>
                                </div>
                            </div>
                        </div>
                    `;
                }
            } else {
                statusHTML = `
                    <div class="text-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        ${data.error || 'API连接失败'}
                    </div>
                `;
            }
            
            statusDiv.innerHTML = statusHTML;
        })
        .catch(error => {
            console.error('检查API状态失败:', error);
            statusDiv.innerHTML = `
                <div class="text-danger">
                    <i class="fas fa-times me-2"></i>
                    检查失败: ${error.message}
                </div>
            `;
        });
}

// 更新查询统计
function updateQueryStats(testResult) {
    if (testResult && testResult.details) {
        document.getElementById('totalQueries').textContent = testResult.details.query_count || '--';
        document.getElementById('successfulQueries').textContent = testResult.details.success_count || '--';
    }
}

// 保存表权限配置
function saveTablePermissions() {
    const permissions = {};
    const checkboxes = document.querySelectorAll('#allowedTables input[type="checkbox"]');
    
    checkboxes.forEach(checkbox => {
        const tableName = checkbox.id.replace('table_', '');
        permissions[tableName] = checkbox.checked;
    });
    
    fetch('/api/ai-table-permissions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ permissions: permissions })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('表权限配置已保存成功！', 'success');
        } else {
            showAlert(data.message || '保存失败', 'danger');
        }
    })
    .catch(error => {
        console.error('保存表权限失败:', error);
        showAlert('保存失败: ' + error.message, 'danger');
    });
}

// 状态徽章更新函数
function updateStatusBadge(elementId, type, text) {
    const element = document.getElementById(elementId);
    if (element) {
        element.className = `status-badge ms-2 badge bg-${type}`;
        element.textContent = text;
    }
}

// 绑定AI集成事件
if (saveAIIntegrationBtn) saveAIIntegrationBtn.addEventListener('click', saveAIIntegrationConfig);
if (testAIIntegrationBtn) testAIIntegrationBtn.addEventListener('click', testAIIntegrationFunction);
if (resetPromptBtn) {
    resetPromptBtn.addEventListener('click', function() {
        if (systemPromptTextarea) {
            systemPromptTextarea.value = defaultSystemPrompt;
            showAlert('系统提示词已重置为默认值', 'success');
        }
    });
}
if (checkApiStatusBtn) checkApiStatusBtn.addEventListener('click', checkApiStatus);
if (saveTablePermissionsBtn) saveTablePermissionsBtn.addEventListener('click', saveTablePermissions);

// 加载配置列表
function loadConfigs(page = 1) {
    currentPage = page;
    const search = document.getElementById('configSearch') ? document.getElementById('configSearch').value : '';
    const typeFilter = document.getElementById('typeFilter') ? document.getElementById('typeFilter').value : '';
    const statusFilter = document.getElementById('statusFilter') ? document.getElementById('statusFilter').value : '';
    
    const params = new URLSearchParams({
        page: page,
        per_page: perPage,
        search: search,
        db_type: typeFilter,
        is_active: statusFilter
    });
    
    fetch(`/api/v2/system/database-config/configs?${params}`)
        .then(response => {
            if (response.status === 401 || response.status === 403) {
                // 用户未认证或权限不足，跳转到登录页面
                window.location.href = '/auth/login';
                return;
            }
            if (!response.ok) {
                throw new Error(`HTTP错误: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            if (!data) return; // 如果已重定向，则停止执行
            
            if (data.error) {
                throw new Error(data.error);
            }
            
            currentConfigs = data.configs || [];
            totalPages = data.pagination ? data.pagination.pages : 1;
            renderConfigs(data.configs || []);
            renderPagination(totalPages, page);
        })
        .catch(error => {
            console.error('加载配置失败:', error);
            showAlert('加载配置失败: ' + error.message, 'danger');
            
            // 如果是网络错误或服务器错误，显示错误消息
            const container = document.getElementById('configsList');
            container.innerHTML = `
                <div class="col-12 text-center">
                    <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                    <h5 class="text-warning">加载配置失败</h5>
                    <p class="text-muted">${error.message}</p>
                    <button class="btn btn-primary" onclick="loadConfigs()">
                        <i class="fas fa-refresh me-1"></i>重试
                    </button>
                </div>
            `;
        });
}

// 渲染配置卡片
function renderConfigs(configs) {
    const container = document.getElementById('configsList');
    
    if (!configs || configs.length === 0) {
        container.innerHTML = `
            <div class="col-12 text-center">
                <i class="fas fa-database fa-3x text-muted mb-3"></i>
                <p class="text-muted">暂无配置数据</p>
            </div>
        `;
        return;
    }
    
    container.innerHTML = configs.map(config => {
        const statusBadge = config.is_active ? 
            '<span class="badge bg-success">启用</span>' : 
            '<span class="badge bg-secondary">禁用</span>';
        
        const defaultBadge = config.is_default ? 
            '<span class="badge bg-primary ms-1">默认</span>' : '';
        
        const cardClass = config.is_default ? 'card data-source-card active' : 
                         !config.is_active ? 'card data-source-card inactive' : 'card data-source-card';
        
        return `
            <div class="col-md-6 col-lg-4 mb-3">
                <div class="${cardClass}">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">${config.name}</h6>
                        <div>
                            ${statusBadge}
                            ${defaultBadge}
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="mb-2">
                            <strong>类型:</strong> ${config.db_type.toUpperCase()}
                        </div>
                        <div class="mb-2">
                            <strong>主机:</strong> ${config.host}:${config.port}
                        </div>
                        <div class="mb-2">
                            <strong>数据库:</strong> ${config.database_name}
                        </div>
                        <div class="mb-3">
                            <strong>用户:</strong> ${config.username}
                        </div>
                        ${config.description ? `<p class="text-muted small">${config.description}</p>` : ''}
                        
                        <div class="btn-group-sm d-flex gap-1">
                            <button class="btn btn-outline-primary btn-sm" onclick="testConfig(${config.id})">
                                <i class="fas fa-vial"></i> 测试
                            </button>
                            <button class="btn btn-outline-secondary btn-sm" onclick="editConfig(${config.id})">
                                <i class="fas fa-edit"></i> 编辑
                            </button>
                            <button class="btn btn-outline-danger btn-sm" onclick="deleteConfig(${config.id})">
                                <i class="fas fa-trash"></i> 删除
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }).join('');
}

// 基础函数
function applyFilters() { loadConfigs(1); }
function refreshConfigs() { loadConfigs(currentPage); }
function addConfig() { 
    document.getElementById('configModalTitle').textContent = '新增数据库配置';
    if (document.getElementById('configForm')) {
        document.getElementById('configForm').reset();
    }
    if (document.getElementById('configId')) {
        document.getElementById('configId').value = '';
    }
    if (document.getElementById('testResultContainer')) {
        document.getElementById('testResultContainer').style.display = 'none';
    }
    new bootstrap.Modal(document.getElementById('configModal')).show();
}

function showAlert(message, type = 'info') {
    const toast = document.getElementById('alertToast');
    const toastMessage = document.getElementById('toastMessage');
    
    if (toastMessage) {
        toastMessage.textContent = message;
    }
    const toastHeader = toast.querySelector('.toast-header');
    if (toastHeader) {
        toastHeader.className = `toast-header bg-${type} text-white`;
    }
    
    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();
}

// 简化版的其他函数
function renderPagination(totalPages, currentPage) { 
    // 分页渲染逻辑
    const container = document.getElementById('configsPagination');
    if (container) {
        container.innerHTML = '';
    }
}

function loadMappings() { 
    console.log('开始加载映射关系...');
    
    fetch('/api/v2/system/database-config/mappings')
        .then(response => {
            if (response.status === 401 || response.status === 403) {
                window.location.href = '/auth/login';
                return;
            }
            if (!response.ok) {
                throw new Error(`HTTP错误: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            if (!data) return;
            
            if (data.error) {
                throw new Error(data.error);
            }
            renderMappings(data.mappings || []);
        })
        .catch(error => {
            console.error('加载映射关系失败:', error);
            showAlert('加载映射关系失败: ' + error.message, 'danger');
            
            const tbody = document.querySelector('#mappingsTable tbody');
            tbody.innerHTML = `
                <tr>
                    <td colspan="6" class="text-center text-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        加载映射关系失败: ${error.message}
                        <br>
                        <button class="btn btn-sm btn-primary mt-2" onclick="loadMappings()">
                            <i class="fas fa-refresh me-1"></i>重试
                        </button>
                    </td>
                </tr>
            `;
        });
}

// 渲染映射关系表格
function renderMappings(mappings) {
    const tbody = document.querySelector('#mappingsTable tbody');
    
    if (!mappings || mappings.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="6" class="text-center text-muted">暂无映射关系数据</td>
            </tr>
        `;
        return;
    }
    
    tbody.innerHTML = mappings.map(mapping => {
        const statusBadge = mapping.is_active ? 
            '<span class="badge bg-success">启用</span>' : 
            '<span class="badge bg-secondary">禁用</span>';
        
        const typeColor = mapping.mapping_type === 'table' ? 'primary' : 
                         mapping.mapping_type === 'module' ? 'success' : 'info';
        
        return `
            <tr>
                <td>
                    <span class="badge bg-${typeColor}">${mapping.mapping_type}</span>
                </td>
                <td>
                    <code>${mapping.target_name}</code>
                </td>
                <td>${mapping.database_config_name || '未知配置'}</td>
                <td>
                    <span class="badge bg-outline-secondary">${mapping.priority}</span>
                </td>
                <td>${statusBadge}</td>
                <td>
                    <div class="btn-group-sm">
                        <button class="btn btn-outline-secondary btn-sm" onclick="editMapping(${mapping.id})" title="编辑">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-outline-danger btn-sm" onclick="deleteMapping(${mapping.id})" title="删除">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }).join('');
}

function loadMonitoringData() { 
    // 监控数据加载逻辑
    if (document.getElementById('totalConfigs')) {
        document.getElementById('totalConfigs').textContent = '2';
    }
    if (document.getElementById('activeConfigs')) {
        document.getElementById('activeConfigs').textContent = '2';
    }
    if (document.getElementById('totalMappings')) {
        document.getElementById('totalMappings').textContent = '35';
    }
    if (document.getElementById('healthyConfigs')) {
        document.getElementById('healthyConfigs').textContent = '2';
    }
}

function editConfig(id) {
    fetch(`/api/v2/system/database-config/configs/${id}`)
        .then(response => {
            if (response.status === 401 || response.status === 403) {
                window.location.href = '/auth/login';
                return;
            }
            return response.json();
        })
        .then(data => {
            if (!data) return;
            
            if (data.error) {
                showAlert('获取配置失败: ' + data.error, 'danger');
                return;
            }
            
            // 填充表单
            document.getElementById('configId').value = data.id;
            document.getElementById('configName').value = data.name;
            document.getElementById('configType').value = data.db_type;
            document.getElementById('configHost').value = data.host;
            document.getElementById('configPort').value = data.port;
            document.getElementById('configUsername').value = data.username;
            document.getElementById('configPassword').value = ''; // 不显示密码
            document.getElementById('configDatabase').value = data.database_name;
            document.getElementById('configDescription').value = data.description || '';
            
            // 更新模态框标题
            document.getElementById('configModalTitle').textContent = '编辑数据库配置';
            
            // 隐藏测试结果
            document.getElementById('testResultContainer').style.display = 'none';
            
            // 显示模态框
            new bootstrap.Modal(document.getElementById('configModal')).show();
        })
        .catch(error => {
            console.error('获取配置失败:', error);
            showAlert('获取配置失败: ' + error.message, 'danger');
        });
}
function saveConfig() {
    const form = document.getElementById('configForm');
    const formData = new FormData(form);
    const configId = document.getElementById('configId').value;
    
    // 转换FormData为JSON
    const data = {};
    for (let [key, value] of formData.entries()) {
        data[key] = value;
    }
    
    const url = configId ? 
        `/api/v2/system/database-config/configs/${configId}` : 
        '/api/v2/system/database-config/configs';
    const method = configId ? 'PUT' : 'POST';
    
    fetch(url, {
        method: method,
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => {
        if (response.status === 401 || response.status === 403) {
            window.location.href = '/auth/login';
            return;
        }
        return response.json();
    })
    .then(data => {
        if (!data) return;
        
        if (data.error) {
            showAlert('保存失败: ' + data.error, 'danger');
        } else {
            showAlert(configId ? '配置更新成功!' : '配置创建成功!', 'success');
            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('configModal'));
            modal.hide();
            // 重新加载配置列表
            loadConfigs();
        }
    })
    .catch(error => {
        console.error('保存配置失败:', error);
        showAlert('保存失败: ' + error.message, 'danger');
    });
}
function testConfig(id) {
    fetch(`/api/v2/system/database-config/configs/${id}/test`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => {
        if (response.status === 401 || response.status === 403) {
            window.location.href = '/auth/login';
            return;
        }
        return response.json();
    })
    .then(data => {
        if (!data) return;
        
        if (data.error) {
            showAlert('测试失败: ' + data.error, 'danger');
        } else {
            if (data.success) {
                showAlert('连接测试成功: ' + data.message, 'success');
            } else {
                showAlert('连接测试失败: ' + data.message, 'danger');
            }
        }
    })
    .catch(error => {
        console.error('测试连接失败:', error);
        showAlert('测试失败: ' + error.message, 'danger');
    });
}
function deleteConfig(id) { 
    if (confirm('确定要删除这个配置吗？这个操作不可撤销！')) {
        fetch(`/api/v2/system/database-config/configs/${id}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => {
            if (response.status === 401 || response.status === 403) {
                window.location.href = '/auth/login';
                return;
            }
            return response.json();
        })
        .then(data => {
            if (!data) return;
            
            if (data.error) {
                showAlert('删除失败: ' + data.error, 'danger');
            } else {
                showAlert('配置删除成功!', 'success');
                // 重新加载配置列表
                loadConfigs();
            }
        })
        .catch(error => {
            console.error('删除配置失败:', error);
            showAlert('删除失败: ' + error.message, 'danger');
        });
    }
}
function testConnectionForm() {
    const form = document.getElementById('configForm');
    const formData = new FormData(form);
    
    // 转换FormData为JSON
    const data = {};
    for (let [key, value] of formData.entries()) {
        data[key] = value;
    }
    
    // 验证必填字段
    const requiredFields = ['name', 'db_type', 'host', 'port', 'username', 'database_name'];
    const missingFields = requiredFields.filter(field => !data[field]);
    
    if (missingFields.length > 0) {
        showTestResult(false, `请填写必填字段: ${missingFields.join(', ')}`);
        return;
    }
    
    fetch('/api/v2/system/database-config/configs/test', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => {
        if (response.status === 401 || response.status === 403) {
            window.location.href = '/auth/login';
            return;
        }
        return response.json();
    })
    .then(data => {
        if (!data) return;
        
        if (data.error) {
            showTestResult(false, data.error);
        } else {
            showTestResult(data.success, data.message);
        }
    })
    .catch(error => {
        console.error('测试连接失败:', error);
        showTestResult(false, '测试失败: ' + error.message);
    });
}

function showTestResult(success, message) {
    const container = document.getElementById('testResultContainer');
    const result = document.getElementById('testResult');
    
    container.style.display = 'block';
    result.className = success ? 
        'alert alert-success' : 
        'alert alert-danger';
    result.innerHTML = `
        <i class="fas fa-${success ? 'check-circle' : 'exclamation-triangle'} me-2"></i>
        ${message}
    `;
}
function batchTest() {
    fetch('/api/v2/system/database-config/configs/test-batch', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => {
        if (response.status === 401 || response.status === 403) {
            window.location.href = '/auth/login';
            return;
        }
        return response.json();
    })
    .then(data => {
        if (!data) return;
        
        if (data.error) {
            showAlert('批量测试失败: ' + data.error, 'danger');
        } else {
            const results = data.results || [];
            const successful = results.filter(r => r.success).length;
            const total = results.length;
            
            showAlert(
                `批量测试完成: ${successful}/${total} 个配置连接成功`, 
                successful === total ? 'success' : 'warning'
            );
            
            // 重新加载配置列表以显示最新的测试状态
            loadConfigs();
        }
    })
    .catch(error => {
        console.error('批量测试失败:', error);
        showAlert('批量测试失败: ' + error.message, 'danger');
    });
}

// 映射关系管理函数
function editMapping(id) { 
    showAlert('编辑映射关系功能开发中...', 'info'); 
}

function deleteMapping(id) { 
    if (confirm('确定要删除这个映射关系吗？')) {
        showAlert('删除映射关系功能开发中...', 'info'); 
    }
}
</script>
{% endblock %}
