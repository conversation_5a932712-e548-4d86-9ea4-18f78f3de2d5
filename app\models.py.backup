from datetime import datetime
from werkzeug.security import generate_password_hash, check_password_hash
from flask_login import UserMixin
from app import db, login_manager
import logging
import json
from app.config.menu_config import get_menu_by_id, get_all_menu_ids

# 配置日志
logger = logging.getLogger(__name__)

class UserPermission(db.Model):
    __tablename__ = 'user_permissions'
    __bind_key__ = 'system'  # 绑定到系统数据库
    
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(64), db.ForeignKey('users.username', ondelete='CASCADE'))
    menu_id = db.Column(db.Integer)  # 不再使用外键关联到menu_settings表
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    granted_by = db.Column(db.String(64), comment='权限授予人')

class User(UserMixin, db.Model):
    __tablename__ = 'users'
    __bind_key__ = 'system'  # 绑定到系统数据库
    
    username = db.Column(db.String(64), primary_key=True)
    password_hash = db.Column(db.String(1000))  # 增加长度以匹配迁移后的表结构
    role = db.Column(db.String(20))  # admin/tech/op
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    last_login = db.Column(db.DateTime)
    is_active = db.Column(db.Boolean, default=True)
    
    # 添加权限关联 - 直接使用user_permissions表，不再通过MenuSetting表
    permissions_relation = db.relationship('UserPermission', backref='user', lazy='dynamic', 
                                          cascade='all, delete-orphan')
    
    def set_password(self, password):
        self.password_hash = generate_password_hash(password)
        logger.info(f"为用户 {self.username} 设置密码哈希: {self.password_hash}")
    
    def check_password(self, password):
        if not self.password_hash:
            logger.warning(f"用户 {self.username} 没有密码哈希")
            return False
        
        result = check_password_hash(self.password_hash, password)
        logger.info(f"用户 {self.username} 密码验证结果: {result}")
        return result
    
    @property
    def id(self):
        """为了兼容Flask-Login，提供id属性"""
        return self.username
    
    def get_id(self):
        return self.username
    
    def has_permission(self, menu_id):
        """检查用户是否有权限访问指定菜单"""
        if self.role == 'admin':  # 管理员拥有所有权限
            return True
        
        # 从user_permissions表中查询
        return UserPermission.query.filter_by(
            username=self.username, 
            menu_id=menu_id
        ).first() is not None
    
    def get_permissions(self):
        """获取用户的所有菜单权限ID"""
        if self.role == 'admin':  # 管理员拥有所有权限
            return get_all_menu_ids()
        
        # 从关联表中获取所有menu_id
        permissions = UserPermission.query.filter_by(username=self.username).all()
        return [p.menu_id for p in permissions]
    
    def set_permissions(self, menu_ids):
        """设置用户的菜单权限"""
        if self.role == 'admin':  # 管理员权限不可修改
            logger.info(f"跳过修改管理员 {self.username} 的权限")
            return
        
        # 记录当前的权限
        current_permissions = self.get_permissions()
        logger.info(f"用户 {self.username} 当前权限: {current_permissions}")
        logger.info(f"准备设置新权限: {menu_ids}")
        
        # 清除现有权限
        deleted = UserPermission.query.filter_by(username=self.username).delete()
        logger.info(f"已删除 {deleted} 条现有权限记录")
        
        # 获取所有有效的菜单ID
        all_menu_ids = get_all_menu_ids()
        
        # 添加新权限
        added_count = 0
        for menu_id in menu_ids:
            menu_id = int(menu_id)  # 确保是整数类型
            
            # 检查菜单ID是否有效 (只要在有效ID列表中即可)
            if menu_id in all_menu_ids:
                permission = UserPermission(username=self.username, menu_id=menu_id)
                db.session.add(permission)
                added_count += 1
            else:
                logger.warning(f"忽略无效的菜单ID: {menu_id}")
        
        logger.info(f"为用户 {self.username} 添加了 {added_count} 条新权限记录")

    def update_last_login(self):
        """更新最后登录时间"""
        self.last_login = datetime.utcnow()
        db.session.commit()

class MenuSetting(db.Model):
    """菜单设置 - 已迁移到配置文件，保留模型以兼容旧数据"""
    __tablename__ = 'menu_settings'
    
    id = db.Column(db.Integer, primary_key=True)
    menu_name = db.Column(db.String(128), unique=True)
    # 暂时注释掉外键关系，避免删除用户时的约束问题
    # parent_id = db.Column(db.Integer, db.ForeignKey('menu_settings.id'), nullable=True)
    parent_id = db.Column(db.Integer, nullable=True)
    is_visible = db.Column(db.Boolean, default=True)
    order = db.Column(db.Integer)
    icon = db.Column(db.String(64))
    route = db.Column(db.String(128))
    
    # 暂时注释掉关系定义，避免删除用户时的约束问题
    # children = db.relationship('MenuSetting', backref=db.backref('parent', remote_side=[id]))

# 生产管理相关模型
class Product(db.Model):
    __tablename__ = 'products'
    
    id = db.Column(db.Integer, primary_key=True)
    code = db.Column(db.String(64), unique=True, index=True)
    name = db.Column(db.String(128))
    description = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

class ProductionOrder(db.Model):
    __tablename__ = 'production_orders'
    
    id = db.Column(db.Integer, primary_key=True)
    order_number = db.Column(db.String(64), unique=True, index=True)
    product_id = db.Column(db.Integer, db.ForeignKey('products.id'))
    quantity = db.Column(db.Integer)
    priority = db.Column(db.String(20), default='medium')  # high/medium/low
    status = db.Column(db.String(20))  # pending/in_progress/completed/cancelled
    scheduled_start = db.Column(db.DateTime)
    scheduled_end = db.Column(db.DateTime)
    actual_start = db.Column(db.DateTime)
    actual_end = db.Column(db.DateTime)
    created_by = db.Column(db.String(64), db.ForeignKey('users.username'))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    product = db.relationship('Product', backref='production_orders')
    # 暂时注释掉，因为可能导致删除用户时的外键约束问题
    # creator = db.relationship('User', backref='production_orders')

class ProductionSchedule(db.Model):
    __tablename__ = 'production_schedules'
    
    id = db.Column(db.Integer, primary_key=True)
    production_order_id = db.Column(db.Integer, db.ForeignKey('production_orders.id'))
    resource_id = db.Column(db.Integer, db.ForeignKey('resources.id'))
    start_time = db.Column(db.DateTime)
    end_time = db.Column(db.DateTime)
    status = db.Column(db.String(20))  # scheduled/in_progress/completed/cancelled
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    production_order = db.relationship('ProductionOrder', backref='schedules')
    resource = db.relationship('Resource', backref='schedules')

# 订单管理相关模型
class CustomerOrder(db.Model):
    __tablename__ = 'customer_orders'
    
    id = db.Column(db.Integer, primary_key=True)
    order_number = db.Column(db.String(64), unique=True, index=True)
    customer_name = db.Column(db.String(128))
    status = db.Column(db.String(20))  # new/confirmed/in_production/completed/cancelled
    total_amount = db.Column(db.Float)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

class OrderItem(db.Model):
    __tablename__ = 'order_items'
    
    id = db.Column(db.Integer, primary_key=True)
    order_id = db.Column(db.Integer, db.ForeignKey('customer_orders.id'))
    product_id = db.Column(db.Integer, db.ForeignKey('products.id'))
    quantity = db.Column(db.Integer)
    unit_price = db.Column(db.Float)
    total_price = db.Column(db.Float)
    
    order = db.relationship('CustomerOrder', backref='items')
    product = db.relationship('Product', backref='order_items')

# 资源管理相关模型
class Resource(db.Model):
    """资源基类"""
    __tablename__ = 'resources'
    
    id = db.Column(db.Integer, primary_key=True)
    code = db.Column(db.String(20), unique=True, nullable=False)  # 设备编号
    name = db.Column(db.String(50), nullable=False)  # 设备名称
    type = db.Column(db.String(20), nullable=False)  # 设备类型：sorter/tester/fixture
    status = db.Column(db.String(20), default='available')  # 状态：available/busy/maintenance
    specifications = db.Column(db.Text)  # 设备规格（JSON格式）
    location = db.Column(db.String(50))  # 设备位置
    maintenance_cycle = db.Column(db.Integer)  # 维护周期（天）
    last_maintenance = db.Column(db.DateTime)  # 上次维护时间
    next_maintenance = db.Column(db.DateTime)  # 下次维护时间
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def to_dict(self):
        return {
            'id': self.id,
            'code': self.code,
            'name': self.name,
            'type': self.type,
            'status': self.status,
            'specifications': json.loads(self.specifications) if self.specifications else {},
            'location': self.location,
            'maintenance_cycle': self.maintenance_cycle,
            'last_maintenance': self.last_maintenance.isoformat() if self.last_maintenance else None,
            'next_maintenance': self.next_maintenance.isoformat() if self.next_maintenance else None,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }

class TestSpec(db.Model):
    """测试规范"""
    __tablename__ = 'test_specs'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)  # 规范名称
    version = db.Column(db.String(20), nullable=False)  # 版本号
    status = db.Column(db.String(20), default='draft')  # 状态：draft/active/archived
    description = db.Column(db.Text)  # 规范描述
    parameters = db.Column(db.Text)  # 测试参数（JSON格式）
    owner = db.Column(db.String(50))  # 负责人
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'version': self.version,
            'status': self.status,
            'description': self.description,
            'parameters': json.loads(self.parameters) if self.parameters else [],
            'owner': self.owner,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }

class MaintenanceRecord(db.Model):
    """维护记录"""
    __tablename__ = 'maintenance_records'
    
    id = db.Column(db.Integer, primary_key=True)
    resource_id = db.Column(db.Integer, db.ForeignKey('resources.id'), nullable=False)
    maintenance_type = db.Column(db.String(20), nullable=False)  # 维护类型：routine/repair/calibration
    description = db.Column(db.Text)  # 维护描述
    start_time = db.Column(db.DateTime, nullable=False)  # 开始时间
    end_time = db.Column(db.DateTime)  # 结束时间
    status = db.Column(db.String(20), default='pending')  # 状态：pending/in_progress/completed
    operator = db.Column(db.String(50))  # 操作人
    result = db.Column(db.Text)  # 维护结果
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    def to_dict(self):
        return {
            'id': self.id,
            'resource_id': self.resource_id,
            'maintenance_type': self.maintenance_type,
            'description': self.description,
            'start_time': self.start_time.isoformat(),
            'end_time': self.end_time.isoformat() if self.end_time else None,
            'status': self.status,
            'operator': self.operator,
            'result': self.result,
            'created_at': self.created_at.isoformat()
        }

class ResourceUsageLog(db.Model):
    """资源使用日志"""
    __tablename__ = 'resource_usage_logs'
    
    id = db.Column(db.Integer, primary_key=True)
    resource_id = db.Column(db.Integer, db.ForeignKey('resources.id'), nullable=False)
    order_id = db.Column(db.String(20))  # 关联订单号
    start_time = db.Column(db.DateTime, nullable=False)  # 开始使用时间
    end_time = db.Column(db.DateTime)  # 结束使用时间
    operator = db.Column(db.String(50))  # 操作人
    usage_type = db.Column(db.String(20))  # 使用类型
    details = db.Column(db.Text)  # 使用详情（JSON格式）
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    def to_dict(self):
        return {
            'id': self.id,
            'resource_id': self.resource_id,
            'order_id': self.order_id,
            'start_time': self.start_time.isoformat(),
            'end_time': self.end_time.isoformat() if self.end_time else None,
            'operator': self.operator,
            'usage_type': self.usage_type,
            'details': json.loads(self.details) if self.details else {},
            'created_at': self.created_at.isoformat()
        }

# WIP跟踪相关模型
class WIPRecord(db.Model):
    __tablename__ = 'wip_records'
    
    id = db.Column(db.Integer, primary_key=True)
    production_order_id = db.Column(db.Integer, db.ForeignKey('production_orders.id'))
    product_id = db.Column(db.Integer, db.ForeignKey('products.id'))
    quantity = db.Column(db.Integer)
    status = db.Column(db.String(20))
    location = db.Column(db.String(128))
    recorded_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    production_order = db.relationship('ProductionOrder', backref='wip_records')
    product = db.relationship('Product', backref='wip_records')

# 用户操作日志模型
class UserActionLog(db.Model):
    __tablename__ = 'user_action_logs'
    __bind_key__ = 'system'  # 绑定到系统数据库，与User模型在同一数据库
    
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(64))  # 移除外键约束，避免跨数据库外键问题
    action_type = db.Column(db.String(64))  # login, logout, create, update, delete, etc.
    target_model = db.Column(db.String(64))  # users, products, orders, etc.
    target_id = db.Column(db.String(128))  # ID of the affected record
    details = db.Column(db.Text)  # JSON or text details of the action
    ip_address = db.Column(db.String(64))
    user_agent = db.Column(db.String(256))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # 暂时注释掉，因为可能导致删除用户时的外键约束问题
    # user = db.relationship('User', backref='action_logs')
    
    @classmethod
    def log_action(cls, username, action_type, target_model, target_id=None, details=None, request=None):
        """记录用户操作"""
        log_entry = cls(
            username=username,
            action_type=action_type,
            target_model=target_model,
            target_id=str(target_id) if target_id else None,
            details=details
        )
        
        # 获取请求信息
        if request:
            log_entry.ip_address = request.remote_addr
            log_entry.user_agent = request.user_agent.string
            
        db.session.add(log_entry)
        try:
            db.session.commit()
        except:
            db.session.rollback()
        
        return log_entry

class SystemSetting(db.Model):
    """系统设置"""
    __tablename__ = 'system_settings'
    __bind_key__ = 'system'  # 绑定到系统数据库
    
    id = db.Column(db.Integer, primary_key=True)
    key = db.Column(db.String(64), nullable=False)  # 设置键名
    value = db.Column(db.Text)  # 设置值
    description = db.Column(db.String(256))  # 设置描述
    user_id = db.Column(db.String(64), nullable=True)  # 用户ID，为NULL表示全局设置 (不使用外键，因为user表在不同数据库)
    setting_type = db.Column(db.String(20), default='system')  # 设置类型：system, user, path, etc.
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

# 新增表的模型定义
class CT(db.Model):
    """产品生产周期参考表"""
    __tablename__ = 'ct'
    
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    PRODUCT = db.Column(db.String(50), nullable=True, comment='产品型号')
    STAGE = db.Column(db.String(50), nullable=True, comment='工序')
    CT_VALUE = db.Column(db.Float, nullable=True, comment='周期时间值')
    UNIT = db.Column(db.String(10), nullable=True, comment='单位')
    REMARK = db.Column(db.Text, nullable=True, comment='备注')
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)

    def __repr__(self):
        return f'<CT {self.PRODUCT}-{self.STAGE}>'

class ET_RECIPE_FILE(db.Model):
    """FT设备配方资源表"""
    __tablename__ = 'et_recipe_file'
    
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    DEVICE = db.Column(db.String(50), nullable=True, comment='产品型号')
    CHIP_ID = db.Column(db.String(50), nullable=True, comment='芯片ID')
    PKG_PN = db.Column(db.String(50), nullable=True, comment='封装型号')
    STAGE = db.Column(db.String(50), nullable=True, comment='工序')
    RECIPE_FILE_NAME = db.Column(db.String(100), nullable=True, comment='配方文件名')
    RECIPE_FILE_PATH = db.Column(db.String(200), nullable=True, comment='配方文件路径')
    KIT_PN = db.Column(db.String(50), nullable=True, comment='KIT型号')
    SOCKET_PN = db.Column(db.String(50), nullable=True, comment='Socket型号')
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)

    def __repr__(self):
        return f'<ET_RECIPE_FILE {self.DEVICE}-{self.STAGE}>'

class ET_UPH_EQP(db.Model):
    """产品UPH表"""
    __tablename__ = 'et_uph_eqp'
    
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    DEVICE = db.Column(db.String(50), nullable=True, comment='产品型号')
    PKG_PN = db.Column(db.String(50), nullable=True, comment='封装型号')
    STAGE = db.Column(db.String(50), nullable=True, comment='工序')
    UPH = db.Column(db.Integer, nullable=True, comment='每小时产出')
    SORTER_MODEL = db.Column(db.String(50), nullable=True, comment='分选机型号')
    FAC_ID = db.Column(db.String(50), nullable=True, comment='工厂ID')
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)

    def __repr__(self):
        return f'<ET_UPH_EQP {self.DEVICE}-{self.STAGE}>'

class TCC_INV(db.Model):
    """测试硬件资源表"""
    __tablename__ = 'TCC_INV'
    
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    INVENTORY_ID = db.Column(db.String(50), nullable=True, comment='资源ID')
    INVENTORY_TYPE = db.Column(db.String(50), nullable=True, comment='资源类型')
    INVENTORY_STATUS = db.Column(db.String(50), nullable=True, comment='资源状态')
    LOCATION = db.Column(db.String(50), nullable=True, comment='位置')
    REMARK = db.Column(db.Text, nullable=True, comment='备注')
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)

    def __repr__(self):
        return f'<TCC_INV {self.INVENTORY_ID}>'

class WIP_LOT(db.Model):
    """在制品信息表 - 基于实际数据库结构"""
    __tablename__ = 'wip_lot'
    
    # 主键
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    
    # 核心批次信息
    LOT_ID = db.Column(db.String(50), nullable=False, comment='批次号')
    LOT_TYPE = db.Column(db.String(50), nullable=True, comment='批次类型')
    DET_LOT_TYPE = db.Column(db.String(50), nullable=True, comment='详细批次类型')
    
    # 数量信息
    LOT_QTY = db.Column(db.Integer, nullable=True, comment='批次数量')
    SUB_QTY = db.Column(db.Float, nullable=True, comment='子数量')
    UNIT = db.Column(db.String(20), nullable=True, comment='单位')
    SUB_UNIT = db.Column(db.Float, nullable=True, comment='子单位')
    
    # 状态信息
    WIP_STATE = db.Column(db.String(50), nullable=True, comment='在制品状态')
    PROC_STATE = db.Column(db.String(50), nullable=True, comment='工艺状态')
    HOLD_STATE = db.Column(db.String(50), nullable=True, comment='暂停状态')
    RW_STATE = db.Column(db.String(50), nullable=True, comment='返工状态')
    REPAIR_STATE = db.Column(db.String(50), nullable=True, comment='修复状态')
    QC_STATE = db.Column(db.Float, nullable=True, comment='质检状态')
    
    # 产品信息
    PROD_ID = db.Column(db.String(50), nullable=True, comment='产品ID')
    DEVICE = db.Column(db.String(50), nullable=True, comment='设备/产品型号')
    CHIP_ID = db.Column(db.String(50), nullable=True, comment='芯片ID')
    PKG_PN = db.Column(db.String(50), nullable=True, comment='封装型号')
    
    # 工艺信息
    STAGE = db.Column(db.String(50), nullable=True, comment='工序')
    PROC_RULE_ID = db.Column(db.String(50), nullable=True, comment='工艺规则ID')
    PRP_ID = db.Column(db.String(50), nullable=True, comment='PRP ID')
    FLOW_ID = db.Column(db.String(50), nullable=True, comment='流程ID')
    PRP_VER = db.Column(db.Float, nullable=True, comment='PRP版本')
    FLOW_VER = db.Column(db.Float, nullable=True, comment='流程版本')
    OPER_VER = db.Column(db.Float, nullable=True, comment='操作版本')
    
    # 设备信息
    EQP_ID = db.Column(db.String(50), nullable=True, comment='设备ID')
    AREA_ID = db.Column(db.String(50), nullable=True, comment='区域ID')
    
    # 数量详情
    LOT_IN_QTY = db.Column(db.Integer, nullable=True, comment='批次入库数量')
    LOT_OUT_QTY = db.Column(db.Integer, nullable=True, comment='批次出库数量')
    GOOD_QTY = db.Column(db.Integer, nullable=True, comment='良品数量')
    NG_QTY = db.Column(db.Integer, nullable=True, comment='不良品数量')
    
    # 批次关系
    ROOT_LOT_ID = db.Column(db.String(50), nullable=True, comment='根批次ID')
    PARENT_LOT_ID = db.Column(db.String(50), nullable=True, comment='父批次ID')
    CHILD_LOT_ID = db.Column(db.String(50), nullable=True, comment='子批次ID')
    CUST_LOT_ID = db.Column(db.String(50), nullable=True, comment='客户批次ID')
    
    # 优先级和计划
    HOT_TYPE = db.Column(db.String(50), nullable=True, comment='热点类型/优先级')
    PLAN_START_DATE = db.Column(db.Float, nullable=True, comment='计划开始日期')
    
    # 时间信息
    OPER_CHANGE_TIME = db.Column(db.String(50), nullable=True, comment='操作变更时间')
    JOB_START_TIME = db.Column(db.String(50), nullable=True, comment='作业开始时间')
    JOB_END_TIME = db.Column(db.String(50), nullable=True, comment='作业结束时间')
    RELEASE_TIME = db.Column(db.String(50), nullable=True, comment='释放时间')
    
    # 工厂信息
    FAC_ID = db.Column(db.String(50), nullable=True, comment='工厂ID')
    SUB_FAC = db.Column(db.String(50), nullable=True, comment='子工厂')
    
    # 订单信息
    WORK_ORDER_ID = db.Column(db.String(50), nullable=True, comment='工单ID')
    WORK_ORDER_VER = db.Column(db.Integer, nullable=True, comment='工单版本')
    PO_ID = db.Column(db.String(50), nullable=True, comment='采购单ID')
    
    # 事件信息
    EVENT = db.Column(db.String(50), nullable=True, comment='事件')
    EVENT_KEY = db.Column(db.String(50), nullable=True, comment='事件键')
    EVENT_TIME = db.Column(db.String(50), nullable=True, comment='事件时间')
    EVENT_USER = db.Column(db.String(50), nullable=True, comment='事件用户')
    EVENT_MSG = db.Column(db.String(200), nullable=True, comment='事件消息')
    
    # 创建信息
    created_at = db.Column(db.String(50), nullable=True, comment='创建时间')
    CREATE_USER = db.Column(db.String(50), nullable=True, comment='创建用户')
    
    # 质量信息
    UPH = db.Column(db.Float, nullable=True, comment='每小时产量')
    ORT_QTY = db.Column(db.Float, nullable=True, comment='ORT数量')
    IQC_QTY = db.Column(db.Float, nullable=True, comment='IQC数量')
    
    # 兼容性字段（为了向后兼容）
    
    # 注意：实际数据库有148个字段，这里只定义了主要的字段
    # 其他字段可以通过动态查询获取

    def __repr__(self):
        return f'<WIP_LOT {self.LOT_ID}>'
    
    def to_dict(self):
        """转换为字典，包含所有非空字段"""
        result = {}
        for column in self.__table__.columns:
            value = getattr(self, column.name)
            if value is not None:
                result[column.name] = value
        return result

class ET_WAIT_LOT(db.Model):
    """待排产批次表"""
    __tablename__ = 'et_wait_lot'
    
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    LOT_ID = db.Column(db.String(50), nullable=False, comment='批次号')
    LOT_TYPE = db.Column(db.String(50), nullable=True, comment='批次类型')
    GOOD_QTY = db.Column(db.Integer, nullable=True, comment='良品数量')
    PROD_ID = db.Column(db.String(50), nullable=True, comment='产品ID')
    DEVICE = db.Column(db.String(50), nullable=True, comment='产品型号')
    CHIP_ID = db.Column(db.String(50), nullable=True, comment='芯片ID')
    PKG_PN = db.Column(db.String(50), nullable=True, comment='封装型号')
    PO_ID = db.Column(db.String(50), nullable=True, comment='采购单ID')
    STAGE = db.Column(db.String(50), nullable=True, comment='工序')
    WIP_STATE = db.Column(db.String(50), nullable=True, comment='在制品状态')
    PROC_STATE = db.Column(db.String(50), nullable=True, comment='工艺状态')
    HOLD_STATE = db.Column(db.String(50), nullable=True, comment='暂停状态')
    FLOW_ID = db.Column(db.String(50), nullable=True, comment='流程ID')
    FLOW_VER = db.Column(db.Integer, nullable=True, comment='流程版本')
    RELEASE_TIME = db.Column(db.String(50), nullable=True, comment='释放时间')
    FAC_ID = db.Column(db.String(50), nullable=True, comment='工厂ID')
    created_at = db.Column(db.String(50), nullable=True, comment='创建时间')

    def __repr__(self):
        return f'<ET_WAIT_LOT {self.LOT_ID}>'

class ET_FT_TEST_SPEC(db.Model):
    """FT测试规范表"""
    __tablename__ = 'et_ft_test_spec'
    
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    TEST_SPEC_ID = db.Column(db.String(100), nullable=True, comment='测试规范ID')
    TEST_SPEC_NAME = db.Column(db.String(100), nullable=True, comment='测试规范名称')
    TEST_SPEC_VER = db.Column(db.String(50), nullable=True, comment='测试规范版本')
    STAGE = db.Column(db.String(50), nullable=True, comment='工序')
    TESTER = db.Column(db.String(50), nullable=True, comment='测试机')
    INV_ID = db.Column(db.String(50), nullable=True, comment='库存ID')
    TEST_SPEC_TYPE = db.Column(db.String(50), nullable=True, comment='测试规范类型')
    APPROVAL_STATE = db.Column(db.String(50), nullable=True, comment='审批状态')
    ACTV_YN = db.Column(db.String(10), nullable=True, comment='是否激活')
    PROD_ID = db.Column(db.String(50), nullable=True, comment='产品ID')
    DEVICE = db.Column(db.String(50), nullable=True, comment='产品型号')
    CHIP_ID = db.Column(db.String(50), nullable=True, comment='芯片ID')
    PKG_PN = db.Column(db.String(50), nullable=True, comment='封装型号')
    COMPANY_ID = db.Column(db.String(50), nullable=True, comment='公司ID')
    TEST_AREA = db.Column(db.String(50), nullable=True, comment='测试区域')
    HANDLER = db.Column(db.String(50), nullable=True, comment='分选机')
    TEMPERATURE = db.Column(db.String(50), nullable=True, comment='温度')
    FT_PROGRAM = db.Column(db.String(100), nullable=True, comment='FT程序')
    QA_PROGRAM = db.Column(db.String(100), nullable=True, comment='QA程序')
    GU_PROGRAM = db.Column(db.String(100), nullable=True, comment='GU程序')
    TB_PN = db.Column(db.String(50), nullable=True, comment='TB型号')
    HB_PN = db.Column(db.String(50), nullable=True, comment='HB型号')
    TEST_TIME = db.Column(db.Float, nullable=True, comment='测试时间')
    UPH = db.Column(db.Integer, nullable=True, comment='每小时产量')
    STANDARD_YIELD = db.Column(db.Float, nullable=True, comment='标准良率')
    LOW_YIELD = db.Column(db.Float, nullable=True, comment='低良率')
    DOWN_YIELD = db.Column(db.Float, nullable=True, comment='停机良率')
    ORT_QTY = db.Column(db.Integer, nullable=True, comment='ORT数量')
    REMAIN_QTY = db.Column(db.Integer, nullable=True, comment='剩余数量')
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)

    def __repr__(self):
        return f'<ET_FT_TEST_SPEC {self.TEST_SPEC_ID}>'

class EQP_STATUS(db.Model):
    """设备状态表"""
    __tablename__ = 'eqp_status'
    
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    HANDLER_ID = db.Column(db.String(50), nullable=True, comment='分选机ID')
    TESTER_ID = db.Column(db.String(50), nullable=True, comment='测试机ID')
    STATUS = db.Column(db.String(50), nullable=True, comment='设备状态')
    LOT_ID = db.Column(db.String(50), nullable=True, comment='批次号')
    DEVICE = db.Column(db.String(50), nullable=True, comment='产品型号')
    STAGE = db.Column(db.String(50), nullable=True, comment='工序')
    EQP_TYPE = db.Column(db.String(50), nullable=True, comment='设备类型')
    EQP_CLASS = db.Column(db.String(50), nullable=True, comment='设备类别')
    TEMPERATURE_RANGE = db.Column(db.String(50), nullable=True, comment='温度范围')
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)

    def __repr__(self):
        return f'<EQP_STATUS {self.HANDLER_ID}-{self.STATUS}>'

@login_manager.user_loader
def load_user(username):
    return User.query.get(username)

# 邮件附件自动处理系统相关模型
class EmailConfig(db.Model):
    """邮箱配置"""
    __tablename__ = 'email_configs'
    __bind_key__ = 'system'  # 绑定到系统数据库
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(64), unique=True, nullable=False, comment='配置名称')
    server = db.Column(db.String(128), nullable=False, comment='服务器地址')
    port = db.Column(db.Integer, nullable=False, comment='端口号')
    email = db.Column(db.String(128), nullable=False, comment='邮箱地址')
    password = db.Column(db.String(128), nullable=False, comment='客户端授权码')
    senders = db.Column(db.Text, nullable=True, comment='发件人过滤，多个用分号分隔')
    subjects = db.Column(db.Text, nullable=True, comment='主题关键词过滤，多个用分号分隔')
    check_interval = db.Column(db.Integer, default=60, comment='检查邮件频率(分钟)')
    work_start_time = db.Column(db.String(10), default='08:00', comment='工作开始时间')
    work_end_time = db.Column(db.String(10), default='18:00', comment='工作结束时间')
    enabled = db.Column(db.Boolean, default=False, comment='是否启用')
    download_path = db.Column(db.String(256), nullable=False, comment='附件保存路径')
    use_date_folder = db.Column(db.Boolean, default=True, comment='是否按日期分类保存')
    fetch_days = db.Column(db.Integer, default=10, comment='抓取最近多少天的邮件')
    created_by = db.Column(db.String(64), comment='创建人')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='创建时间')
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='更新时间')
    
    # 暂时注释掉，因为可能导致删除用户时的外键约束问题
    # creator = db.relationship('User', backref='email_configs')
    
    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'server': self.server,
            'port': self.port,
            'email': self.email,
            'senders': self.senders,
            'subjects': self.subjects,
            'check_interval': self.check_interval,
            'work_start_time': self.work_start_time,
            'work_end_time': self.work_end_time,
            'enabled': self.enabled,
            'download_path': self.download_path,
            'use_date_folder': self.use_date_folder,
            'fetch_days': self.fetch_days,
            'created_by': self.created_by,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            'updated_at': self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None
        }

class ExcelMapping(db.Model):
    """Excel字段映射配置"""
    __tablename__ = 'excel_mappings'
    __bind_key__ = 'system'  # 绑定到系统数据库
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(64), unique=True, nullable=False, comment='配置名称')
    sheet_name = db.Column(db.String(64), nullable=True, comment='工作表名称，为空表示第一个工作表')
    start_row = db.Column(db.Integer, default=2, comment='数据起始行，1表示第一行')
    header_row = db.Column(db.Integer, default=1, comment='表头行，1表示第一行')
    field_mappings = db.Column(db.Text, nullable=False, comment='字段映射关系，JSON格式')
    key_fields = db.Column(db.String(256), nullable=False, comment='关键字段，用于识别重复订单，多个用逗号分隔')
    date_format = db.Column(db.String(64), default='%Y-%m-%d', comment='日期格式')
    created_by = db.Column(db.String(64), comment='创建人')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='创建时间')
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='更新时间')
    
    # 暂时注释掉，因为可能导致删除用户时的外键约束问题
    # creator = db.relationship('User', backref='excel_mappings')
    
    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'sheet_name': self.sheet_name,
            'start_row': self.start_row,
            'header_row': self.header_row,
            'field_mappings': json.loads(self.field_mappings) if self.field_mappings else {},
            'key_fields': self.key_fields,
            'date_format': self.date_format,
            'created_by': self.created_by,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            'updated_at': self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None
        }

class EmailAttachment(db.Model):
    """邮件附件记录"""
    __tablename__ = 'email_attachments'
    __bind_key__ = 'system'  # 绑定到系统数据库
    
    id = db.Column(db.Integer, primary_key=True)
    email_config_id = db.Column(db.Integer, db.ForeignKey('email_configs.id'), comment='邮箱配置ID')
    message_id = db.Column(db.String(256), nullable=False, comment='邮件唯一ID')
    sender = db.Column(db.String(128), nullable=False, comment='发件人')
    subject = db.Column(db.String(256), nullable=False, comment='邮件主题')
    receive_date = db.Column(db.DateTime, nullable=False, comment='接收日期')
    filename = db.Column(db.String(256), nullable=False, comment='附件文件名')
    file_path = db.Column(db.String(512), nullable=False, comment='附件保存路径')
    file_size = db.Column(db.Integer, nullable=False, comment='附件大小(字节)')
    processed = db.Column(db.Boolean, default=False, comment='是否已处理')
    process_date = db.Column(db.DateTime, nullable=True, comment='处理日期')
    process_result = db.Column(db.String(64), nullable=True, comment='处理结果')
    process_message = db.Column(db.Text, nullable=True, comment='处理消息')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='创建时间')
    
    email_config = db.relationship('EmailConfig', backref='attachments')
    
    def to_dict(self):
        return {
            'id': self.id,
            'email_config_id': self.email_config_id,
            'message_id': self.message_id,
            'sender': self.sender,
            'subject': self.subject,
            'receive_date': self.receive_date.strftime('%Y-%m-%d %H:%M:%S') if self.receive_date else None,
            'filename': self.filename,
            'file_path': self.file_path,
            'file_size': self.file_size,
            'processed': self.processed,
            'process_date': self.process_date.strftime('%Y-%m-%d %H:%M:%S') if self.process_date else None,
            'process_result': self.process_result,
            'process_message': self.process_message,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None
        }

class OrderData(db.Model):
    """完整的订单数据表 - 包含横向信息和纵向数据的所有字段"""
    __tablename__ = 'order_data'
    
    # 主键和基础信息
    id = db.Column(db.Integer, primary_key=True)
    
    # === 横向信息字段 (来自Excel前13行) ===
    document_type = db.Column(db.String(50), comment='单据类型 (如: 封测外包加工单)')
    document_number = db.Column(db.String(50), comment='单据编号')
    processing_type = db.Column(db.String(100), comment='加工属性 (如: 测试-编带)')
    order_date = db.Column(db.Date, comment='下单日期')
    contractor_name = db.Column(db.String(200), comment='加工承揽商 (如: 无锡市宜欣科技有限公司)')
    contractor_contact = db.Column(db.String(100), comment='承揽商联系人')
    contractor_address = db.Column(db.String(500), comment='承揽商地址')
    contractor_phone = db.Column(db.String(50), comment='承揽商电话')
    contractor_email = db.Column(db.String(100), comment='承揽商Email')
    client_name = db.Column(db.String(200), comment='加工委托方')
    
    # === 纵向表格数据字段 (来自表格行) ===
    order_number = db.Column(db.String(64), nullable=False, index=True, comment='订单号 (如: JHT2506190001)')
    product_name = db.Column(db.String(200), comment='标签名称/产品名称 (如: JW5116FESOP#TRPBF)')
    circuit_name = db.Column(db.String(100), comment='电路名称 (如: JW5116F)')
    chip_name = db.Column(db.String(100), comment='芯片名称')
    wafer_size = db.Column(db.String(20), comment='圆片尺寸 (如: 8)')
    package_qty = db.Column(db.Integer, comment='送包只数')
    package_pieces = db.Column(db.Integer, comment='送包片数')
    diffusion_batch = db.Column(db.String(100), comment='扩散批号')
    wafer_number = db.Column(db.String(100), comment='片号')
    assembly_method = db.Column(db.String(100), comment='装片方式')
    drawing_number = db.Column(db.String(100), comment='图号')
    package_form = db.Column(db.String(100), comment='封装形式')
    stamp_line1 = db.Column(db.String(200), comment='印章第一行')
    stamp_line2 = db.Column(db.String(200), comment='印章第二行')
    stamp_line3 = db.Column(db.String(200), comment='印章第三行')
    other_notes = db.Column(db.Text, comment='其他说明')
    delivery_date = db.Column(db.Date, comment='交期')
    env_requirement = db.Column(db.String(100), comment='产品环保要求')
    msl_requirement = db.Column(db.String(100), comment='MSL要求')
    reliability_requirement = db.Column(db.String(200), comment='可靠性要求')
    print_pin_dot = db.Column(db.Boolean, default=False, comment='是否打印pin点')
    pin_dot_position = db.Column(db.String(100), comment='pin点位置')
    item_code = db.Column(db.String(100), comment='Item Code（ITEM编码）')
    shipping_address = db.Column(db.String(500), comment='出货地址')
    wafer_lot = db.Column(db.String(100), comment='标签 wafer lot')
    order_attribute = db.Column(db.String(100), comment='订单属性')
    
    # === 关键分类和状态字段 ===
    lot_type = db.Column(db.String(50), comment='Lot Type原始值 (如: 量产-P, 试验-E, 小批量-PE)')
    classification = db.Column(db.Enum('engineering', 'production', 'unknown'), default='unknown', comment='用户选择的分类(工程/量产)')
    wafer_id = db.Column(db.String(100), comment='Wafer ID')
    
    # === 扩展的业务字段 ===
    customer = db.Column(db.String(128), comment='客户名称 (从承揽商或委托方提取)')
    product_code = db.Column(db.String(64), comment='产品编码 (从电路名称或产品名称提取)')
    quantity = db.Column(db.Integer, comment='数量 (从送包片数或其他数量字段提取)')
    unit_price = db.Column(db.Numeric(10, 2), comment='单价')
    total_price = db.Column(db.Numeric(10, 2), comment='总价')
    status = db.Column(db.String(20), default='new', comment='订单状态')
    urgent = db.Column(db.Boolean, default=False, comment='是否紧急')
    owner = db.Column(db.String(64), comment='负责人')
    note = db.Column(db.Text, comment='备注')
    
    # === 数据来源和追溯字段 ===
    source_file = db.Column(db.String(512), comment='数据来源文件 (Excel文件名)')
    raw_data = db.Column(db.Text, comment='原始数据JSON (包含所有扫描信息)')
    horizontal_data = db.Column(db.Text, comment='横向信息JSON')
    vertical_data = db.Column(db.Text, comment='纵向表格数据JSON')
    data_row_number = db.Column(db.Integer, comment='在Excel中的数据行号')
    extraction_method = db.Column(db.String(50), default='enhanced_parser', comment='提取方法')
    extraction_info = db.Column(db.Text, comment='提取过程信息')
    
    # === 时间戳字段 ===
    created_by = db.Column(db.String(64), comment='创建人')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='创建时间')
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='更新时间')
    imported_at = db.Column(db.DateTime, comment='导入时间')
    processed_at = db.Column(db.DateTime, comment='处理时间')
    
    def to_dict(self):
        """转换为字典格式"""
        result = {
            'id': self.id,
            
            # 横向信息
            'document_type': self.document_type,
            'document_number': self.document_number,
            'processing_type': self.processing_type,
            'order_date': self.order_date.strftime('%Y-%m-%d') if self.order_date else None,
            'contractor_name': self.contractor_name,
            'contractor_contact': self.contractor_contact,
            'contractor_address': self.contractor_address,
            'contractor_phone': self.contractor_phone,
            'contractor_email': self.contractor_email,
            'client_name': self.client_name,
            
            # 纵向数据
            'order_number': self.order_number,
            'product_name': self.product_name,
            'circuit_name': self.circuit_name,
            'chip_name': self.chip_name,
            'wafer_size': self.wafer_size,
            'package_qty': self.package_qty,
            'package_pieces': self.package_pieces,
            'diffusion_batch': self.diffusion_batch,
            'wafer_number': self.wafer_number,
            'assembly_method': self.assembly_method,
            'drawing_number': self.drawing_number,
            'package_form': self.package_form,
            'delivery_date': self.delivery_date.strftime('%Y-%m-%d') if self.delivery_date else None,
            'item_code': self.item_code,
            'shipping_address': self.shipping_address,
            'wafer_lot': self.wafer_lot,
            'order_attribute': self.order_attribute,
            
            # 分类字段
            'lot_type': self.lot_type,
            'classification': self.classification,
            'wafer_id': self.wafer_id,
            
            # 业务字段
            'customer': self.customer,
            'product_code': self.product_code,
            'quantity': self.quantity,
            'unit_price': float(self.unit_price) if self.unit_price else None,
            'total_price': float(self.total_price) if self.total_price else None,
            'status': self.status,
            'urgent': self.urgent,
            'owner': self.owner,
            'note': self.note,
            
            # 数据来源
            'source_file': self.source_file,
            'data_row_number': self.data_row_number,
            'extraction_method': self.extraction_method,
            
            # 时间戳
            'created_by': self.created_by,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            'updated_at': self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None,
            'imported_at': self.imported_at.strftime('%Y-%m-%d %H:%M:%S') if self.imported_at else None,
            'processed_at': self.processed_at.strftime('%Y-%m-%d %H:%M:%S') if self.processed_at else None
        }
        return result
    
    def get_classification_display(self):
        """获取分类的显示名称"""
        classification_map = {
            'engineering': '工程',
            'production': '量产',
            'unknown': '未分类'
        }
        return classification_map.get(self.classification, '未分类')
    
    def auto_classify_by_lot_type(self):
        """根据Lot Type自动分类"""
        if not self.lot_type:
            return 'unknown'
        
        # 工程类型
        engineering_types = ['试验-E', '新品-E', '工程批', '工程', 'DOE-Q', 'qual-Q']
        # 量产类型
        production_types = ['量产-P', '小批量-PE', '量产批']
        
        if self.lot_type in engineering_types:
            return 'engineering'
        elif self.lot_type in production_types:
            return 'production'
        else:
            return 'unknown'

# ===================== 系统数据库模型 (绑定到 system.db) =====================

class AISettings(db.Model):
    """AI助手设置"""
    __tablename__ = 'ai_settings'
    __bind_key__ = 'system'  # 绑定到系统数据库
    
    id = db.Column(db.Integer, primary_key=True)
    settings = db.Column(db.Text, nullable=False, comment='AI设置JSON')
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, comment='更新时间')
    
    def __repr__(self):
        return f'<AISettings {self.id}>'
    
    def to_dict(self):
        import json
        return {
            'id': self.id,
            'settings': json.loads(self.settings) if self.settings else {},
            'updated_at': self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None
        }

class ProductPriorityConfig(db.Model):
    """产品优先级配置 - 基于最新Excel结构"""
    __tablename__ = 'product_priority_config'
    __bind_key__ = 'system'  # 绑定到系统数据库

    id = db.Column(db.Integer, primary_key=True)
    device = db.Column(db.String(100), nullable=True, comment='DEVICE - 产品名称 (varchar(100))')
    stage = db.Column(db.String(20), nullable=True, comment='STAGE - 工序 (varchar(20))')
    pkg_pn = db.Column(db.String(30), nullable=True, comment='PKG_PN - 封装形式 (varchar(30))')
    chip_id = db.Column(db.String(50), nullable=True, comment='CHIP_ID - 芯片名称 (varchar(50))')
    priority_level = db.Column(db.String(20), default='medium', comment='优先级等级(high/medium/low)')
    priority_order = db.Column(db.Integer, default=999, comment='优先级排序(数字越小优先级越高)')
    lead_time_days = db.Column(db.Integer, default=7, comment='交期天数')
    uph_override = db.Column(db.Integer, nullable=True, comment='UPH覆盖值')
    notes = db.Column(db.Text, nullable=True, comment='备注信息')
    created_by = db.Column(db.String(64), nullable=True, comment='创建人')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='创建时间')
    updated_by = db.Column(db.String(64), nullable=True, comment='更新人')
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='更新时间')

    def __repr__(self):
        return f'<ProductPriorityConfig {self.device}:{self.stage}:{self.priority_level}>'
    
    def to_dict(self):
        return {
            'id': self.id,
            'device': self.device,
            'stage': self.stage,
            'pkg_pn': self.pkg_pn,
            'chip_id': self.chip_id,
            'priority_level': self.priority_level,
            'priority_order': self.priority_order,
            'lead_time_days': self.lead_time_days,
            'uph_override': self.uph_override,
            'notes': self.notes,
            'created_by': self.created_by,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            'updated_by': self.updated_by,
            'updated_at': self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None
        }

# 产品优先级配置模型 - 基于Excel结构
class DevicePriorityConfig(db.Model):
    """产品优先级配置"""
    __tablename__ = 'devicepriorityconfig'
    __bind_key__ = 'system'  # 绑定到系统数据库

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    device = db.Column(db.String(200), nullable=False, comment='DEVICE - 产品名称')
    priority = db.Column(db.String(10), comment='PRIORITY - 优先级')
    from_time = db.Column(db.DateTime, nullable=True, comment='FROM_TIME - 开始时间')
    end_time = db.Column(db.DateTime, nullable=True, comment='END_TIME - 结束时间')
    refresh_time = db.Column(db.DateTime, nullable=True, comment='REFRESH_TIME - 刷新时间')
    user = db.Column(db.String(100), nullable=True, comment='USER - 用户')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='创建时间')
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='更新时间')
    
    def __repr__(self):
        return f'<DevicePriorityConfig {self.device}:{self.priority}>'
    
    def to_dict(self):
        return {
            'id': self.id,
            'device': self.device,
            'priority': self.priority,
            'from_time': self.from_time.strftime('%Y-%m-%d %H:%M:%S') if self.from_time else None,
            'end_time': self.end_time.strftime('%Y-%m-%d %H:%M:%S') if self.end_time else None,
            'refresh_time': self.refresh_time.strftime('%Y-%m-%d %H:%M:%S') if self.refresh_time else None,
            'user': self.user,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            'updated_at': self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None
        }

# 批次优先级配置模型 - 基于Excel结构
class LotPriorityConfig(db.Model):
    """批次优先级配置"""
    __tablename__ = 'lotpriorityconfig'
    __bind_key__ = 'system'  # 绑定到系统数据库

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    device = db.Column(db.String(200), nullable=False, comment='DEVICE - 产品名称')
    stage = db.Column(db.String(100), nullable=True, comment='STAGE - 工序')
    priority = db.Column(db.String(10), comment='PRIORITY - 优先级')
    refresh_time = db.Column(db.DateTime, nullable=True, comment='REFRESH_TIME - 刷新时间')
    user = db.Column(db.String(100), nullable=True, comment='USER - 用户')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='创建时间')
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='更新时间')

    def __repr__(self):
        return f'<LotPriorityConfig {self.device}:{self.priority}>'
    
    def to_dict(self):
        return {
            'id': self.id,
            'device': self.device,
            'stage': self.stage,
            'priority': self.priority,
            'refresh_time': self.refresh_time.strftime('%Y-%m-%d %H:%M:%S') if self.refresh_time else None,
            'user': self.user,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            'updated_at': self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None
        }

class UserFilterPresets(db.Model):
    """用户过滤器预设"""
    __tablename__ = 'user_filter_presets'
    __bind_key__ = 'system'  # 绑定到系统数据库
    
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(50), nullable=False, comment='用户名')
    preset_name = db.Column(db.String(100), nullable=False, comment='预设名称')
    page_type = db.Column(db.String(50), nullable=False, comment='页面类型')
    filters = db.Column(db.Text, nullable=False, comment='过滤器配置JSON')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='创建时间')
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='更新时间')
    
    def __repr__(self):
        return f'<UserFilterPresets {self.username}:{self.preset_name}>'
    
    def to_dict(self):
        import json
        return {
            'id': self.id,
            'username': self.username,
            'preset_name': self.preset_name,
            'page_type': self.page_type,
            'filters': json.loads(self.filters) if self.filters else {},
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            'updated_at': self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None
        }

class Settings(db.Model):
    """系统全局设置"""
    __tablename__ = 'settings'
    __bind_key__ = 'system'  # 绑定到系统数据库
    
    id = db.Column(db.Integer, primary_key=True)
    key = db.Column(db.String(100), unique=True, nullable=False, comment='设置键')
    value = db.Column(db.Text, nullable=True, comment='设置值')
    description = db.Column(db.String(255), nullable=True, comment='设置描述')
    data_type = db.Column(db.String(20), default='string', comment='数据类型')
    category = db.Column(db.String(50), nullable=True, comment='设置分类')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='创建时间')
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='更新时间')
    
    def __repr__(self):
        return f'<Settings {self.key}:{self.value}>'
    
    def to_dict(self):
        return {
            'id': self.id,
            'key': self.key,
            'value': self.value,
            'description': self.description,
            'data_type': self.data_type,
            'category': self.category,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            'updated_at': self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None
        }

class SchedulingTasks(db.Model):
    """调度任务配置"""
    __tablename__ = 'scheduling_tasks'
    __bind_key__ = 'system'  # 绑定到系统数据库
    
    id = db.Column(db.Integer, primary_key=True)
    task_name = db.Column(db.String(100), nullable=False, comment='任务名称')
    task_type = db.Column(db.String(50), nullable=False, comment='任务类型')
    schedule_config = db.Column(db.Text, nullable=False, comment='调度配置JSON')
    enabled = db.Column(db.Boolean, default=True, comment='是否启用')
    last_run = db.Column(db.DateTime, nullable=True, comment='最后运行时间')
    next_run = db.Column(db.DateTime, nullable=True, comment='下次运行时间')
    created_by = db.Column(db.String(64), nullable=True, comment='创建人')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='创建时间')
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='更新时间')
    
    def __repr__(self):
        return f'<SchedulingTasks {self.task_name}>'
    
    def to_dict(self):
        import json
        return {
            'id': self.id,
            'task_name': self.task_name,
            'task_type': self.task_type,
            'schedule_config': json.loads(self.schedule_config) if self.schedule_config else {},
            'enabled': self.enabled,
            'last_run': self.last_run.strftime('%Y-%m-%d %H:%M:%S') if self.last_run else None,
            'next_run': self.next_run.strftime('%Y-%m-%d %H:%M:%S') if self.next_run else None,
            'created_by': self.created_by,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            'updated_at': self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None
        }

class DatabaseInfo(db.Model):
    """数据库信息"""
    __tablename__ = 'database_info'
    __bind_key__ = 'system'  # 绑定到系统数据库
    
    id = db.Column(db.Integer, primary_key=True)
    db_name = db.Column(db.String(100), nullable=False, comment='数据库名称')
    db_type = db.Column(db.String(50), nullable=False, comment='数据库类型')
    connection_info = db.Column(db.Text, nullable=True, comment='连接信息JSON')
    description = db.Column(db.String(255), nullable=True, comment='描述')
    status = db.Column(db.String(20), default='active', comment='状态')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='创建时间')
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='更新时间')
    
    def __repr__(self):
        return f'<DatabaseInfo {self.db_name}>'
    
    def to_dict(self):
        import json
        return {
            'id': self.id,
            'db_name': self.db_name,
            'db_type': self.db_type,
            'connection_info': json.loads(self.connection_info) if self.connection_info else {},
            'description': self.description,
            'status': self.status,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            'updated_at': self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None
        }

class MigrationLog(db.Model):
    """数据迁移日志"""
    __tablename__ = 'migration_log'
    __bind_key__ = 'system'  # 绑定到系统数据库
    
    id = db.Column(db.Integer, primary_key=True)
    migration_name = db.Column(db.String(100), nullable=False, comment='迁移名称')
    migration_type = db.Column(db.String(50), nullable=False, comment='迁移类型')
    source_db = db.Column(db.String(100), nullable=True, comment='源数据库')
    target_db = db.Column(db.String(100), nullable=True, comment='目标数据库')
    status = db.Column(db.String(20), nullable=False, comment='状态')
    start_time = db.Column(db.DateTime, nullable=False, comment='开始时间')
    end_time = db.Column(db.DateTime, nullable=True, comment='结束时间')
    records_processed = db.Column(db.Integer, default=0, comment='处理记录数')
    error_message = db.Column(db.Text, nullable=True, comment='错误信息')
    details = db.Column(db.Text, nullable=True, comment='详细信息JSON')
    created_by = db.Column(db.String(64), nullable=True, comment='执行人')
    
    def __repr__(self):
        return f'<MigrationLog {self.migration_name}:{self.status}>'
    
    def to_dict(self):
        import json
        return {
            'id': self.id,
            'migration_name': self.migration_name,
            'migration_type': self.migration_type,
            'source_db': self.source_db,
            'target_db': self.target_db,
            'status': self.status,
            'start_time': self.start_time.strftime('%Y-%m-%d %H:%M:%S') if self.start_time else None,
            'end_time': self.end_time.strftime('%Y-%m-%d %H:%M:%S') if self.end_time else None,
            'records_processed': self.records_processed,
            'error_message': self.error_message,
            'details': json.loads(self.details) if self.details else {},
            'created_by': self.created_by
        }

# ============== APScheduler调度器相关模型 ==============

class SchedulerJob(db.Model):
    """调度器任务配置表 - 存储在MySQL aps_system数据库"""
    __tablename__ = 'scheduler_jobs'
    __bind_key__ = 'system'  # 绑定到系统数据库
    
    id = db.Column(db.String(255), primary_key=True)
    name = db.Column(db.String(255), nullable=False, comment='任务名称')
    job_type = db.Column(db.String(100), nullable=False, comment='任务类型')
    func = db.Column(db.String(255), nullable=False, comment='执行函数')
    args = db.Column(db.Text, comment='位置参数(JSON格式)')
    kwargs = db.Column(db.Text, comment='关键字参数(JSON格式)')
    trigger = db.Column(db.String(50), nullable=False, comment='触发器类型')
    trigger_args = db.Column(db.Text, comment='触发器参数(JSON格式)')
    enabled = db.Column(db.Boolean, default=True, comment='是否启用')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='创建时间')
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='更新时间')
    created_by = db.Column(db.String(100), comment='创建者')
    description = db.Column(db.Text, comment='任务描述')
    
    # 关联的日志记录
    logs = db.relationship('SchedulerJobLog', backref='job', lazy='dynamic', cascade='all, delete-orphan')
    
    def __repr__(self):
        return f'<SchedulerJob {self.name}>'
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'name': self.name,
            'job_type': self.job_type,
            'func': self.func,
            'args': self.args,
            'kwargs': self.kwargs,
            'trigger': self.trigger,
            'trigger_args': self.trigger_args,
            'enabled': self.enabled,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'created_by': self.created_by,
            'description': self.description
        }

class SchedulerJobLog(db.Model):
    """调度器任务执行日志表 - 存储在MySQL aps_system数据库"""
    __tablename__ = 'scheduler_job_logs'
    __bind_key__ = 'system'  # 绑定到系统数据库
    
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    job_id = db.Column(db.String(255), db.ForeignKey('scheduler_jobs.id'), nullable=False, comment='任务ID')
    job_name = db.Column(db.String(255), comment='任务名称')
    start_time = db.Column(db.DateTime, nullable=False, comment='开始时间')
    end_time = db.Column(db.DateTime, comment='结束时间')
    status = db.Column(db.String(50), nullable=False, comment='执行状态')
    result = db.Column(db.Text, comment='执行结果')
    error = db.Column(db.Text, comment='错误信息')
    duration = db.Column(db.Numeric(10, 3), comment='执行时长(秒)')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='创建时间')
    
    # 添加索引
    __table_args__ = (
        db.Index('idx_job_id', 'job_id'),
        db.Index('idx_start_time', 'start_time'),
        db.Index('idx_status', 'status'),
    )
    
    def __repr__(self):
        return f'<SchedulerJobLog {self.job_name} - {self.status}>'
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'job_id': self.job_id,
            'job_name': self.job_name,
            'start_time': self.start_time.isoformat() if self.start_time else None,
            'end_time': self.end_time.isoformat() if self.end_time else None,
            'status': self.status,
            'result': self.result,
            'error': self.error,
            'duration': float(self.duration) if self.duration else None,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }

class SchedulerConfig(db.Model):
    """调度器配置表 - 存储在MySQL aps_system数据库"""
    __tablename__ = 'scheduler_config'
    __bind_key__ = 'system'  # 绑定到系统数据库
    
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    key = db.Column(db.String(255), unique=True, nullable=False, comment='配置键')
    value = db.Column(db.Text, comment='配置值')
    description = db.Column(db.String(500), comment='配置描述')
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='更新时间')
    updated_by = db.Column(db.String(100), comment='更新者')
    
    def __repr__(self):
        return f'<SchedulerConfig {self.key}>'
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'key': self.key,
            'value': self.value,
            'description': self.description,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'updated_by': self.updated_by
        }
    
    @classmethod
    def get_config(cls, key, default=None):
        """获取配置值"""
        config = cls.query.filter_by(key=key).first()
        return config.value if config else default
    
    @classmethod
    def set_config(cls, key, value, description=None, updated_by=None):
        """设置配置值"""
        config = cls.query.filter_by(key=key).first()
        if config:
            config.value = value
            config.updated_by = updated_by
            if description:
                config.description = description
        else:
            config = cls(
                key=key, 
                value=value, 
                description=description,
                updated_by=updated_by
            )
            db.session.add(config)
        
        db.session.commit()
        return config

class LotTypeClassificationRule(db.Model):
    """Lot Type分类规则表"""
    __tablename__ = 'lot_type_classification_rules'
    
    id = db.Column(db.Integer, primary_key=True)
    lot_type = db.Column(db.String(50), nullable=False, unique=True, comment='Lot Type原始值')
    classification = db.Column(db.Enum('engineering', 'production', 'unknown'), nullable=False, comment='分类结果')
    description = db.Column(db.String(200), comment='规则描述')
    created_by = db.Column(db.String(64), comment='创建人')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='创建时间')
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='更新时间')
    
    def __repr__(self):
        return f'<LotTypeClassificationRule {self.lot_type}:{self.classification}>'
    
    def to_dict(self):
        return {
            'id': self.id,
            'lot_type': self.lot_type,
            'classification': self.classification,
            'description': self.description,
            'created_by': self.created_by,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            'updated_at': self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None
        }
    
    @classmethod
    def get_classification(cls, lot_type):
        """根据Lot Type获取分类"""
        if not lot_type:
            return 'unknown'
        
        rule = cls.query.filter_by(lot_type=lot_type).first()
        return rule.classification if rule else 'unknown'
    
    @classmethod
    def bulk_classify(cls, lot_types):
        """批量分类Lot Types"""
        if not lot_types:
            return {}
        
        rules = cls.query.filter(cls.lot_type.in_(lot_types)).all()
        classification_map = {rule.lot_type: rule.classification for rule in rules}
        
        # 为未找到规则的lot_type设置默认值
        for lot_type in lot_types:
            if lot_type not in classification_map:
                classification_map[lot_type] = 'unknown'
        
        return classification_map

# ===================== 系统数据库模型 (绑定到 system.db) =====================

# 导入CP订单模型
try:
    from .cp_order_data import CpOrderData
    print("✅ 成功导入CP订单模型")
except ImportError as e:
    print(f"⚠️  无法导入CP订单模型: {e}")
    # 如果模型文件不存在，定义一个临时的空模型避免错误
    class CpOrderData(db.Model):
        __tablename__ = 'cp_order_data'
        id = db.Column(db.Integer, primary_key=True)