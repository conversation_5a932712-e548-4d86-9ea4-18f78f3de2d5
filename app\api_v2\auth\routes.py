from flask import jsonify, request, current_app
from flask_login import login_required, current_user
from . import auth_bp
from app import db
from app.models import User, UserActionLog, UserPermission
from app.decorators import admin_required, permission_required
from app.config.menu_config import MENU_CONFIG, MENU_ID_MAP
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

@auth_bp.route('/health')
def health_check():
    """健康检查端点"""
    return jsonify({
        'status': 'ok',
        'service': 'auth_v2',
        'timestamp': datetime.now().isoformat()
    })

@auth_bp.route('/user/info')
@login_required
def get_user_info():
    """获取当前用户信息"""
    try:
        return jsonify({
            'id': current_user.username,  # 使用username作为id
            'username': current_user.username,
            'email': getattr(current_user, 'email', ''),
            'role': getattr(current_user, 'role', 'user'),
            'last_login': getattr(current_user, 'last_login', None),
            'is_active': current_user.is_active
        })
    except Exception as e:
        logger.error(f"获取用户信息失败: {str(e)}")
        return jsonify({'error': '获取用户信息失败'}), 500

@auth_bp.route('/users/<username>/permissions', methods=['GET'])
@login_required
def get_user_permissions(username):
    """获取用户权限列表"""
    try:
        # 检查权限：只有管理员或用户本人可以查看权限
        if current_user.role != 'admin' and current_user.username != username:
            return jsonify({'error': '权限不足'}), 403
            
        # 验证用户是否存在
        user = User.query.filter_by(username=username).first()
        if not user:
            return jsonify({'error': '用户不存在'}), 404
        
        # 如果是admin用户，返回所有权限ID
        if user.role == 'admin':
            # 获取所有可用的权限ID (1-40)
            all_permissions = list(range(1, 41))
            logger.info(f"Admin用户 {username} 拥有所有权限: {all_permissions}")
            return jsonify(all_permissions)
        
        # 获取用户的权限 - 修复字段名
        permissions = UserPermission.query.filter_by(username=username).all()
        permission_ids = [p.menu_id for p in permissions]  # 改为menu_id
        
        logger.info(f"用户 {username} 的权限: {permission_ids}")
        return jsonify(permission_ids)
        
    except Exception as e:
        logger.error(f"获取用户权限失败: {str(e)}")
        return jsonify({'error': '获取用户权限失败'}), 500

@auth_bp.route('/users/<username>/permissions', methods=['PUT'])
@login_required
def update_user_permissions(username):
    """更新用户权限"""
    try:
        # 检查权限：只有管理员可以修改权限
        if current_user.role != 'admin':
            return jsonify({'error': '只有管理员可以修改用户权限'}), 403
            
        # 验证用户是否存在
        user = User.query.filter_by(username=username).first()
        if not user:
            return jsonify({'error': '用户不存在'}), 404
        
        # 不能修改admin用户的权限
        if user.role == 'admin':
            return jsonify({'error': '不能修改管理员用户的权限'}), 403
        
        data = request.get_json()
        if not data or 'permissions' not in data:
            return jsonify({'error': '缺少权限数据'}), 400
        
        permissions = data['permissions']
        if not isinstance(permissions, list):
            return jsonify({'error': '权限数据格式错误'}), 400
        
        # 验证权限ID有效性
        valid_permission_ids = list(range(1, 41))  # 1-40，包含所有菜单权限
        invalid_permissions = [p for p in permissions if p not in valid_permission_ids]
        if invalid_permissions:
            return jsonify({'error': f'无效的权限ID: {invalid_permissions}'}), 400
        
        # 删除用户现有权限
        UserPermission.query.filter_by(username=username).delete()
        
        # 添加新权限 - 修复字段名
        for permission_id in permissions:
            user_permission = UserPermission(
                username=username,
                menu_id=permission_id  # 改为menu_id
            )
            db.session.add(user_permission)
        
        db.session.commit()
        
        # 记录操作日志
        UserActionLog.log_action(
            username=current_user.username,
            action_type='update_permissions',
            target_model='user_permissions',
            target_id=username,
            details={
                'permissions': permissions,
                'updated_by': current_user.username,
                'timestamp': datetime.now().isoformat()
            },
            request=request
        )
        
        logger.info(f"用户 {username} 权限已更新: {permissions}")
        return jsonify({
            'success': True,
            'message': '权限更新成功',
            'permissions': permissions
        })
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"更新用户权限失败: {str(e)}")
        return jsonify({'error': '更新用户权限失败'}), 500

@auth_bp.route('/session/status')
def check_session_status():
    """检查会话状态 - 不需要登录即可访问，用于诊断"""
    try:
        from flask import session
        
        session_info = {
            'is_authenticated': current_user.is_authenticated,
            'session_permanent': session.permanent,
            'session_keys': list(session.keys()) if session else [],
            'has_user_id': '_user_id' in session if session else False,
            'timestamp': datetime.now().isoformat()
        }
        
        if current_user.is_authenticated:
            session_info.update({
                'user_id': current_user.username,  # 使用username作为id
                'username': current_user.username,
                'role': getattr(current_user, 'role', 'unknown')
            })
        
        return jsonify({
            'success': True,
            'session': session_info
        })
    except Exception as e:
        logger.error(f"检查会话状态失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@auth_bp.route('/session/refresh', methods=['POST'])
@login_required
def refresh_session():
    """刷新用户会话"""
    try:
        from flask import session
        from flask_login import login_user
        
        # 重新登录用户以刷新会话
        login_user(current_user, remember=True)
        
        # 记录会话刷新操作
        UserActionLog.log_action(
            username=current_user.username,
            action_type='session_refresh',
            target_model='session',
            target_id=current_user.username,
            details={'timestamp': datetime.now().isoformat()},
            request=request
        )
        
        return jsonify({
            'success': True,
            'message': '会话已刷新',
            'user': {
                'username': current_user.username,
                'role': getattr(current_user, 'role', 'user')
            },
            'timestamp': datetime.now().isoformat()
        })
    except Exception as e:
        logger.error(f"刷新会话失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@auth_bp.route('/debug/login-status')
def debug_login_status():
    """调试登录状态 - 提供详细的登录状态信息"""
    try:
        from flask import session, request
        
        debug_info = {
            'request_info': {
                'method': request.method,
                'url': request.url,
                'remote_addr': request.remote_addr,
                'user_agent': str(request.user_agent),
                'headers': dict(request.headers)
            },
            'session_info': {
                'exists': bool(session),
                'permanent': session.permanent if session else False,
                'keys': list(session.keys()) if session else [],
                'user_id_in_session': '_user_id' in session if session else False
            },
            'current_user_info': {
                'is_authenticated': current_user.is_authenticated,
                'is_active': current_user.is_active if hasattr(current_user, 'is_active') else False,
                'is_anonymous': current_user.is_anonymous,
                'username': getattr(current_user, 'username', None),
                'id': getattr(current_user, 'id', None)
            },
            'app_config': {
                'login_view': current_app.config.get('LOGIN_VIEW'),
                'session_lifetime': str(current_app.config.get('PERMANENT_SESSION_LIFETIME')),
                'secret_key_set': bool(current_app.config.get('SECRET_KEY'))
            },
            'timestamp': datetime.now().isoformat()
        }
        
        return jsonify({
            'success': True,
            'debug': debug_info
        })
    except Exception as e:
        logger.error(f"调试登录状态失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@auth_bp.route('/user/activity')
@login_required
def get_user_activity():
    """获取用户活动日志"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        
        activities = UserActionLog.query.filter_by(
            username=current_user.username  # 使用username而不是user_id
        ).order_by(
            UserActionLog.created_at.desc()
        ).paginate(
            page=page, 
            per_page=per_page,
            error_out=False
        )
        
        return jsonify({
            'items': [{
                'id': activity.id,
                'action': activity.action,
                'description': activity.description,
                'ip_address': activity.ip_address,
                'user_agent': activity.user_agent,
                'created_at': activity.created_at.isoformat()
            } for activity in activities.items],
            'total': activities.total,
            'pages': activities.pages,
            'current_page': activities.page
        })
    except Exception as e:
        logger.error(f"获取用户活动日志失败: {str(e)}")
        return jsonify({'error': '获取活动日志失败'}), 500

@auth_bp.route('/menu-settings', methods=['GET'])
@login_required
def get_menu_settings():
    """获取菜单设置 - v2版本"""
    try:
        # 使用配置文件而不是从数据库获取
        result = []
        
        # 处理菜单项，添加ID映射
        def process_menu_item(item, parent_id=None):
            menu_id = MENU_ID_MAP.get(item['code'])
            menu_data = {
                'id': menu_id,
                'name': item['name'],
                'parent_id': parent_id,
                'is_visible': True,
                'order': item['order'],
                'icon': item['icon'],
                'route': item['route']
            }
            result.append(menu_data)
            
            # 递归处理子菜单
            for child in item.get('children', []):
                process_menu_item(child, menu_id)
        
        # 处理所有顶级菜单
        for menu in MENU_CONFIG:
            process_menu_item(menu)
        
        logger.info(f"菜单设置加载成功，共 {len(result)} 个菜单项")
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"获取菜单设置失败: {str(e)}")
        return jsonify({'error': '获取菜单设置失败'}), 500