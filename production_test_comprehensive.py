#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
APS排产系统生产环境测试脚本
真实验证改进的排产逻辑
"""

import sys
import os
import time
import json
import logging
from datetime import datetime, timedelta
import pandas as pd
import numpy as np

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入必要模块
from app.services.data_source_manager import DataSourceManager
from app.services.improved_scheduling_service import ImprovedSchedulingService

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'logs/production_test_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ProductionTestSuite:
    """生产环境测试套件"""
    
    def __init__(self):
        self.data_manager = DataSourceManager()
        self.scheduler = ImprovedSchedulingService()
        self.test_results = {}
        
    def run_all_tests(self):
        """运行所有测试"""
        logger.info("=" * 80)
        logger.info("开始APS排产系统生产环境测试")
        logger.info(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info("=" * 80)
        
        try:
            # 1. 数据连接测试
            self.test_database_connectivity()
            
            # 2. 算法正确性测试
            self.test_algorithm_correctness()
            
            # 3. 生成测试报告
            report = self.generate_final_report()
            return report
            
        except Exception as e:
            logger.error(f"测试过程中发生错误: {e}")
            raise
    
    def test_database_connectivity(self):
        """测试数据库连接"""
        logger.info("\n1. 测试数据库连接...")
        
        test_result = {
            'test_name': '数据库连接测试',
            'start_time': datetime.now(),
            'status': 'RUNNING'
        }
        
        try:
            # 测试MySQL连接
            mysql_tables = ['ET_WAIT_LOT', 'EQP_STATUS', 'ET_UPH_EQP']
            mysql_status = {}
            
            for table in mysql_tables:
                try:
                    data = self.data_manager.get_table_data(table)
                    mysql_status[table] = {
                        'status': 'SUCCESS',
                        'record_count': len(data),
                        'error': None
                    }
                    logger.info(f"  ✓ {table}: {len(data)}条记录")
                except Exception as e:
                    mysql_status[table] = {
                        'status': 'ERROR',
                        'record_count': 0,
                        'error': str(e)
                    }
                    logger.error(f"  ✗ {table}: 连接失败 - {e}")
            
            test_result.update({
                'end_time': datetime.now(),
                'status': 'SUCCESS',
                'mysql_status': mysql_status
            })
            
            logger.info("数据库连接测试完成")
            
        except Exception as e:
            test_result.update({
                'end_time': datetime.now(),
                'status': 'ERROR',
                'error': str(e)
            })
            logger.error(f"数据库连接测试失败: {e}")
        
        self.test_results['database_connectivity'] = test_result
    
    def test_algorithm_correctness(self):
        """测试算法正确性"""
        logger.info("\n2. 测试算法正确性...")
        
        test_result = {
            'test_name': '算法正确性测试',
            'start_time': datetime.now(),
            'status': 'RUNNING'
        }
        
        try:
            algorithms = ['deadline', 'product', 'value', 'intelligent']
            algorithm_results = {}
            
            for algorithm in algorithms:
                logger.info(f"  测试{algorithm}算法...")
                
                start_time = time.time()
                result = self.scheduler.execute_scheduling(algorithm)
                end_time = time.time()
                
                # 分析结果质量
                quality_metrics = self._analyze_scheduling_quality(result, algorithm)
                
                algorithm_results[algorithm] = {
                    'execution_time': end_time - start_time,
                    'total_lots': len(result),
                    'quality_metrics': quality_metrics,
                    'first_10_lots': result[:10] if result else []
                }
                
                logger.info(f"    {algorithm}算法执行完成，耗时: {algorithm_results[algorithm]['execution_time']:.2f}秒")
                logger.info(f"    处理批次数: {algorithm_results[algorithm]['total_lots']}")
            
            # 对比不同算法的效果
            comparison_results = self._compare_algorithms(algorithm_results)
            
            test_result.update({
                'end_time': datetime.now(),
                'status': 'SUCCESS',
                'algorithm_results': algorithm_results,
                'comparison_results': comparison_results
            })
            
            logger.info("算法正确性测试完成")
            
        except Exception as e:
            test_result.update({
                'end_time': datetime.now(),
                'status': 'ERROR',
                'error': str(e)
            })
            logger.error(f"算法正确性测试失败: {e}")
        
        self.test_results['algorithm_correctness'] = test_result
    
    def _analyze_scheduling_quality(self, result, algorithm):
        """分析排产质量"""
        if not result:
            return {'error': '排产结果为空'}
        
        quality_metrics = {}
        
        # 设备利用率分析
        equipment_usage = {}
        total_hours = 0
        
        for item in result:
            handler_id = item.get('HANDLER_ID', 'UNKNOWN')
            estimated_hours = item.get('estimated_hours', 0)
            
            if handler_id not in equipment_usage:
                equipment_usage[handler_id] = 0
            equipment_usage[handler_id] += estimated_hours
            total_hours += estimated_hours
        
        if equipment_usage:
            avg_usage = total_hours / len(equipment_usage)
            usage_values = list(equipment_usage.values())
            usage_variance = np.var(usage_values) if len(usage_values) > 1 else 0
            
            quality_metrics['equipment_usage'] = {
                'total_equipment': len(equipment_usage),
                'avg_usage_hours': avg_usage,
                'usage_variance': usage_variance,
                'equipment_distribution': equipment_usage
            }
        
        # 匹配质量分析
        match_scores = [item.get('match_score', 0) for item in result if 'match_score' in item]
        if match_scores:
            quality_metrics['match_quality'] = {
                'avg_match_score': np.mean(match_scores),
                'min_match_score': np.min(match_scores),
                'max_match_score': np.max(match_scores),
                'high_quality_matches': len([s for s in match_scores if s >= 80])
            }
        
        return quality_metrics
    
    def _compare_algorithms(self, algorithm_results):
        """对比不同算法"""
        comparison = {}
        
        # 执行时间对比
        execution_times = {alg: results['execution_time'] for alg, results in algorithm_results.items()}
        fastest_algorithm = min(execution_times, key=execution_times.get)
        
        comparison['performance'] = {
            'execution_times': execution_times,
            'fastest_algorithm': fastest_algorithm
        }
        
        # 匹配质量对比
        match_qualities = {}
        for alg, results in algorithm_results.items():
            quality = results.get('quality_metrics', {}).get('match_quality', {})
            if quality:
                match_qualities[alg] = quality.get('avg_match_score', 0)
        
        if match_qualities:
            best_quality_algorithm = max(match_qualities, key=match_qualities.get)
            comparison['quality'] = {
                'match_qualities': match_qualities,
                'best_quality_algorithm': best_quality_algorithm
            }
        
        return comparison
    
    def generate_final_report(self):
        """生成最终测试报告"""
        logger.info("\n3. 生成测试报告...")
        
        # 计算总体测试结果
        total_tests = len(self.test_results)
        successful_tests = len([r for r in self.test_results.values() if r['status'] == 'SUCCESS'])
        
        overall_status = 'PASS' if successful_tests == total_tests else 'FAIL'
        success_rate = (successful_tests / total_tests * 100) if total_tests > 0 else 0
        
        # 生成报告
        report = {
            'test_summary': {
                'test_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'total_tests': total_tests,
                'successful_tests': successful_tests,
                'failed_tests': total_tests - successful_tests,
                'success_rate': success_rate,
                'overall_status': overall_status
            },
            'test_details': self.test_results,
            'recommendations': self._generate_recommendations()
        }
        
        # 保存报告
        report_filename = f'logs/production_test_report_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
        with open(report_filename, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2, default=str)
        
        # 输出报告摘要
        logger.info("=" * 80)
        logger.info("测试报告摘要")
        logger.info("=" * 80)
        logger.info(f"总测试项: {total_tests}")
        logger.info(f"成功测试: {successful_tests}")
        logger.info(f"失败测试: {total_tests - successful_tests}")
        logger.info(f"成功率: {success_rate:.1f}%")
        logger.info(f"总体状态: {overall_status}")
        logger.info(f"详细报告: {report_filename}")
        
        # 输出建议
        recommendations = self._generate_recommendations()
        logger.info(f"\n建议: {recommendations}")
        
        return report
    
    def _generate_recommendations(self):
        """生成建议"""
        successful_tests = len([r for r in self.test_results.values() if r['status'] == 'SUCCESS'])
        total_tests = len(self.test_results)
        success_rate = (successful_tests / total_tests * 100) if total_tests > 0 else 0
        
        if success_rate >= 95:
            return "✅ 系统通过所有关键测试，改进的排产算法工作正常，建议投入生产使用"
        elif success_rate >= 85:
            return "⚠️ 系统基本满足要求，建议修复少量问题后投入使用"
        else:
            return "❌ 系统存在重大问题，不建议投入生产使用，需要重新设计"

def main():
    """主函数"""
    # 确保日志目录存在
    os.makedirs('logs', exist_ok=True)
    
    try:
        # 创建测试套件并运行
        test_suite = ProductionTestSuite()
        report = test_suite.run_all_tests()
        
        # 根据测试结果设置退出码
        exit_code = 0 if report['test_summary']['overall_status'] == 'PASS' else 1
        return exit_code
        
    except Exception as e:
        logger.error(f"测试执行失败: {e}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code) 