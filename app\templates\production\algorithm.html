{% extends "base.html" %}

{% block title %}手动排产与优先级管理
<style>
:root {
    --primary-color: #b72424;
    --secondary-color: #d73027;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
}

.navbar-brand, .nav-link.active {
    color: var(--primary-color) !important;
}

.card-header {
    background-color: var(--primary-color);
    color: white;
}
</style>
{% endblock %}

{% block extra_css %}
<style>
    .preview-area {
        margin-bottom: 1rem;
        padding: 1rem;
        background-color: #f8f9fa;
        border-radius: 4px;
    }
    .table-responsive {
        max-height: 600px;
        overflow-y: auto;
    }
    /* 添加表格列宽控制 */
    .table th {
        white-space: nowrap;
        min-width: 100px;
        position: relative;
        padding-right: 20px;
    }
    .table td {
        white-space: nowrap;
    }
    /* 选择行样式 */
    .selectable-row {
        cursor: pointer;
    }
    .selectable-row.selected {
        background-color: #fff1f0 !important; /* 浅红色背景 */
    }
    .selectable-row:hover {
        background-color: #f8f9fa;
    }
    /* 筛选输入框样式 */
    .filter-input {
        width: 100%;
        padding: 2px 5px;
        margin: 2px 0;
        font-size: 0.875rem;
        border: 1px solid #dee2e6;
        border-radius: 3px;
    }
    /* 排序图标样式 */
    .sort-icon {
        position: absolute;
        right: 5px;
        cursor: pointer;
        color: #999;
    }
    .sort-icon.active {
        color: #b72424; /* 红色主题色 */
    }
    /* 筛选行样式 */
    .filter-row th {
        padding: 4px;
        background-color: #f8f9fa;
    }
    /* 批量操作按钮样式 */
    .batch-actions {
        margin-bottom: 10px;
    }
    .batch-actions .btn {
        margin-right: 5px;
    }
    /* 键盘快捷键提示 */
    .shortcut-hint {
        font-size: 0.8rem;
        color: #6c757d;
        margin-left: 5px;
    }
    /* 修改表格字体大小和行高 */
    .table {
        font-size: 0.8rem;
        line-height: 1.2;
    }
    
    /* 固定第一列（复选框列）*/
    .table-responsive {
        position: relative;
    }
    
    .table th:first-child,
    .table td:first-child {
        position: sticky;
        left: 0;
        z-index: 2;
        background-color: #fff;
        border-right: 1px solid #dee2e6;
    }
    
    /* 当表头固定时，确保第一列表头的背景色 */
    .table th:first-child {
        background-color: #f8f9fa;
        z-index: 3;
    }
    
    /* 悬停时保持背景色 */
    .table tr:hover td:first-child {
        background-color: #f8f9fa;
    }
    
    /* 选中行时的第一列样式 */
    .table tr.selected td:first-child {
        background-color: #fff1f0;
    }
    
    /* 调整单元格内边距 */
    .table td, 
    .table th {
        padding: 0.3rem 0.5rem;
        white-space: nowrap;
    }
    
    /* 调整复选框大小 */
    .form-check-input {
        width: 0.8rem;
        height: 0.8rem;
        margin-top: 0.2rem;
    }

    /* 修改主要按钮颜色 */
    .btn-primary {
        background-color: #b72424;
        border-color: #b72424;
    }
    .btn-primary:hover {
        background-color: #b72424;
        border-color: #b72424;
    }
    .btn-primary:focus {
        background-color: #b72424;
        border-color: #b72424;
        box-shadow: 0 0 0 0.25rem rgba(245, 34, 45, 0.25);
    }

    /* 修改分页激活状态颜色 */
    .page-item.active .page-link {
        background-color: #b72424;
        border-color: #b72424;
    }
    .page-link {
        color: #b72424;
    }
    .page-link:hover {
        color: #b72424;
    }

    /* 修改徽章颜色 */
    .badge.bg-primary {
        background-color: #b72424 !important;
    }

    /* 修改链接颜色 */
    a {
        color: #b72424;
    }
    a:hover {
        color: #b72424;
    }


</style>
{% endblock %}

{% block content %}
<div class="card mb-4">
  <div class="card-header">
    <div class="d-flex justify-content-between align-items-center">
      <span>算法权重配置</span>
      <div class="d-flex align-items-center">
        <label for="strategySelector" class="form-label me-2 mb-0">排产策略:</label>
        <select class="form-select form-select-sm" id="strategySelector" style="width: auto;">
          <option value="intelligent">🧠 智能综合策略</option>
          <option value="deadline">📅 交期优先策略</option>
          <option value="product">📦 产品优先策略</option>
          <option value="value">💰 产值优先策略</option>
        </select>
      </div>
    </div>
  </div>
  <div class="card-body">
    <div class="alert alert-info" id="strategyDescription">
      <i class="fas fa-info-circle me-2"></i>
      <span id="strategyDescText">平衡各项指标的综合策略，适用于大多数排产场景</span>
    </div>
    <form id="weights-form">
      <div class="form-row">
        <div class="form-group col-md-4">
          <label for="tech_match_weight">技术匹配度权重(%)</label>
          <input type="number" class="form-control" id="tech_match_weight" name="tech_match_weight" min="0" max="100" step="1">
        </div>
        <div class="form-group col-md-4">
          <label for="load_balance_weight">负载均衡权重(%)</label>
          <input type="number" class="form-control" id="load_balance_weight" name="load_balance_weight" min="0" max="100" step="1">
        </div>
        <div class="form-group col-md-4">
          <label for="deadline_weight">交期紧迫度权重(%)</label>
          <input type="number" class="form-control" id="deadline_weight" name="deadline_weight" min="0" max="100" step="1">
        </div>
        <div class="form-group col-md-4">
          <label for="value_efficiency_weight">产值效率权重(%)</label>
          <input type="number" class="form-control" id="value_efficiency_weight" name="value_efficiency_weight" min="0" max="100" step="1">
        </div>
        <div class="form-group col-md-4">
          <label for="business_priority_weight">业务优先级权重(%)</label>
          <input type="number" class="form-control" id="business_priority_weight" name="business_priority_weight" min="0" max="100" step="1">
        </div>
      </div>
      <button type="submit" class="btn btn-primary">保存权重</button>
    </form>
  </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(function() {
  var currentStrategy = 'intelligent';

  // 策略描述映射
  var strategyDescriptions = {
    'intelligent': '平衡各项指标的综合策略，适用于大多数排产场景',
    'deadline': '优先考虑交期紧迫度，确保按时交付',
    'product': '优先考虑技术匹配度，提高产品质量',
    'value': '优先考虑产值效率，最大化经济效益'
  };

  // 加载指定策略的权重配置
  function loadWeights(strategy) {
    $.getJSON('/api/production/algorithm-weights?strategy=' + strategy, function(resp) {
      if (resp.success) {
        var w = resp.weights;
        for (var key in w) {
          if ($('#' + key).length) {
            $('#' + key).val(w[key]);
          }
        }
        // 更新策略描述
        $('#strategyDescText').text(strategyDescriptions[strategy] || '');
        console.log('✅ 已加载策略权重:', strategy, w);
      } else {
        alert('加载权重失败: ' + resp.message);
      }
    }).fail(function() {
      alert('网络错误，无法加载权重配置');
    });
  }

  // 策略选择器变化事件
  $('#strategySelector').change(function() {
    currentStrategy = $(this).val();
    loadWeights(currentStrategy);
  });

  // 初始化加载默认策略权重
  loadWeights(currentStrategy);

  // 保存权重
  $('#weights-form').submit(function(e) {
    e.preventDefault();
    var payload = {
      strategy: currentStrategy
    };
    $(this).serializeArray().forEach(function(item) {
      payload[item.name] = parseFloat(item.value);
    });

    // 验证权重总和
    var totalWeight = payload.tech_match_weight + payload.load_balance_weight +
                     payload.deadline_weight + payload.value_efficiency_weight +
                     payload.business_priority_weight;

    if (Math.abs(totalWeight - 100) > 0.01) {
      alert('权重总和必须为100%，当前为' + totalWeight.toFixed(2) + '%');
      return;
    }

    $.ajax({
      url: '/api/production/algorithm-weights',
      type: 'POST',
      contentType: 'application/json',
      data: JSON.stringify(payload),
      success: function(resp) {
        if (resp.success) {
          alert('✅ 策略 "' + currentStrategy + '" 的权重配置已保存');
          // 重新加载权重以确保数据一致性
          loadWeights(currentStrategy);
        } else {
          alert('❌ 保存失败: ' + resp.message);
        }
      },
      error: function(xhr) {
        var errorMsg = '保存出错';
        try {
          var errorResp = JSON.parse(xhr.responseText);
          errorMsg = errorResp.message || errorMsg;
        } catch(e) {
          errorMsg = xhr.responseText || errorMsg;
        }
        alert('❌ ' + errorMsg);
      }
    });
  });
});
</script>
{% endblock %}