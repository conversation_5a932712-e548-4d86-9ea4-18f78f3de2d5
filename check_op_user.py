#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from app.models import User

def check_op_user():
    """检查用户op的信息"""
    app, socketio = create_app()
    
    with app.app_context():
        op_user = User.query.filter_by(username='op').first()
        if op_user:
            print(f'用户op的角色: {op_user.role}')
            print(f'用户op密码验证op: {op_user.check_password("op")}')
            print(f'用户op密码验证123: {op_user.check_password("123")}')
        else:
            print('用户op不存在')

if __name__ == '__main__':
    check_op_user() 