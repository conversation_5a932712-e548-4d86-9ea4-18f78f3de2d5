# -*- coding: utf-8 -*-
"""
APScheduler统一调度器服务
管理所有定时任务的创建、启动、停止、监控功能

Author: AI Assistant
Date: 2025-06-13
"""

import json
import logging
import traceback
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any

from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.cron import CronTrigger
from apscheduler.triggers.interval import IntervalTrigger
from apscheduler.triggers.date import DateTrigger
from apscheduler.jobstores.sqlalchemy import SQLAlchemyJobStore
from apscheduler.executors.pool import ThreadPoolExecutor
from apscheduler.events import EVENT_JOB_EXECUTED, EVENT_JOB_ERROR, EVENT_JOB_MISSED
import pytz

from flask import current_app
from app import db
from app.models import SchedulerJob, SchedulerJobLog, SchedulerConfig

# 设置日志
logger = logging.getLogger(__name__)

class APSchedulerService:
    """APScheduler统一调度器服务"""
    
    def __init__(self, app=None):
        self.scheduler = None
        self.app = app
        self.is_running = False
        
        if app is not None:
            self.init_app(app)
    
    def init_app(self, app):
        """初始化Flask应用"""
        self.app = app
        
        # 添加配置默认值
        app.config.setdefault('SCHEDULER_TIMEZONE', 'Asia/Shanghai')
        app.config.setdefault('SCHEDULER_EXECUTORS_DEFAULT_MAX_WORKERS', 100)
        app.config.setdefault('SCHEDULER_JOB_DEFAULTS_COALESCE', True)
        app.config.setdefault('SCHEDULER_JOB_DEFAULTS_MAX_INSTANCES', 10)
        
        # 在应用上下文中延迟初始化
        with app.app_context():
            self._setup_scheduler()
    
    def _setup_scheduler(self):
        """设置调度器配置"""
        try:
            # 从数据库获取配置
            timezone = SchedulerConfig.get_config('timezone', 'Asia/Shanghai')
            max_workers = int(SchedulerConfig.get_config('max_workers', '100'))
            coalesce = SchedulerConfig.get_config('coalesce', 'true').lower() == 'true'
            max_instances = int(SchedulerConfig.get_config('max_instances', '10'))
            misfire_grace_time = int(SchedulerConfig.get_config('job_defaults_misfire_grace_time', '30'))
            
            # 配置作业存储 - 使用MySQL系统数据库(aps_system)
            system_db_uri = self.app.config['SQLALCHEMY_BINDS']['system']
            jobstores = {
                'default': SQLAlchemyJobStore(
                    url=system_db_uri,
                    tablename='apscheduler_jobs'
                )
            }
            
            # 配置执行器
            executors = {
                'default': ThreadPoolExecutor(max_workers=max_workers),
            }
            
            # 作业默认配置
            job_defaults = {
                'coalesce': coalesce,
                'max_instances': max_instances,
                'misfire_grace_time': misfire_grace_time
            }
            
            # 创建调度器
            self.scheduler = BackgroundScheduler(
                jobstores=jobstores,
                executors=executors,
                job_defaults=job_defaults,
                timezone=pytz.timezone(timezone)
            )
            
            # 添加事件监听器
            self.scheduler.add_listener(self._job_listener, EVENT_JOB_EXECUTED | EVENT_JOB_ERROR | EVENT_JOB_MISSED)
            
            logger.info(f"APScheduler调度器配置完成: timezone={timezone}, max_workers={max_workers}")
            
        except Exception as e:
            logger.error(f"设置调度器失败: {e}")
            raise
    
    def start(self):
        """启动调度器"""
        if self.scheduler and not self.is_running:
            try:
                # 检查是否启用
                enabled = SchedulerConfig.get_config('scheduler_enabled', 'true').lower() == 'true'
                if not enabled:
                    logger.info("调度器被配置为禁用状态，跳过启动")
                    return False
                
                self.scheduler.start()
                self.is_running = True
                
                # 加载已配置的任务
                self._load_jobs_from_database()
                
                logger.info("APScheduler调度器启动成功")
                return True
                
            except Exception as e:
                logger.error(f"启动调度器失败: {e}")
                return False
        
        return False
    
    def stop(self, wait=True):
        """停止调度器"""
        if self.scheduler and self.is_running:
            try:
                self.scheduler.shutdown(wait=wait)
                self.is_running = False
                logger.info("APScheduler调度器停止成功")
                return True
            except Exception as e:
                logger.error(f"停止调度器失败: {e}")
                return False
        return False
    
    # ===== 兼容性方法 =====
    @property
    def running(self):
        """兼容旧调度器的running属性"""
        return self.is_running
    
    def add_config(self, config):
        """兼容旧调度器的add_config方法"""
        logger.info(f"兼容性调用: add_config for {getattr(config, 'name', 'unknown')}")
        # 这里可以根据需要实现具体逻辑，或者只是记录日志
        return True
    
    def _load_jobs_from_database(self):
        """从数据库加载已配置的任务"""
        try:
            jobs = SchedulerJob.query.filter_by(enabled=True).all()
            loaded_count = 0
            
            for job_config in jobs:
                try:
                    # 解析参数
                    args = json.loads(job_config.args) if job_config.args else []
                    kwargs = json.loads(job_config.kwargs) if job_config.kwargs else {}
                    trigger_args = json.loads(job_config.trigger_args) if job_config.trigger_args else {}
                    
                    # 创建触发器
                    trigger = self._create_trigger(job_config.trigger, trigger_args)
                    
                    # 获取执行函数
                    func = self._get_function_by_name(job_config.func)
                    
                    # 添加任务到调度器
                    self.scheduler.add_job(
                        func=func,
                        trigger=trigger,
                        args=args,
                        kwargs=kwargs,
                        id=job_config.id,
                        name=job_config.name,
                        replace_existing=True
                    )
                    
                    loaded_count += 1
                    logger.debug(f"任务加载成功: {job_config.name}")
                    
                except Exception as e:
                    logger.error(f"加载任务失败 {job_config.name}: {e}")
            
            logger.info(f"从数据库加载了 {loaded_count} 个任务")
            
        except Exception as e:
            logger.error(f"从数据库加载任务失败: {e}")
    
    def _create_trigger(self, trigger_type: str, trigger_args: dict):
        """创建触发器"""
        if trigger_type == 'cron':
            return CronTrigger(**trigger_args)
        elif trigger_type == 'interval':
            return IntervalTrigger(**trigger_args)
        elif trigger_type == 'date':
            return DateTrigger(**trigger_args)
        else:
            raise ValueError(f"不支持的触发器类型: {trigger_type}")
    
    def _get_function_by_name(self, func_name: str):
        """根据函数名获取函数对象"""
        # 这里需要导入所有可用的任务函数
        from app.utils.task_executors import (
            auto_schedule_task,
            data_sync_task,
            email_attachment_task,
            system_maintenance_task,
            test_task
        )
        
        function_map = {
            'auto_schedule_task': auto_schedule_task,
            'data_sync_task': data_sync_task,
            'email_attachment_task': email_attachment_task,
            'system_maintenance_task': system_maintenance_task,
            'test_task': test_task
        }
        
        if func_name not in function_map:
            raise ValueError(f"未找到函数: {func_name}")
        
        return function_map[func_name]
    
    def _job_listener(self, event):
        """任务执行事件监听器"""
        try:
            job_id = event.job_id
            job_name = event.scheduled_run_time
            
            # 查找任务配置
            job_config = SchedulerJob.query.filter_by(id=job_id).first()
            job_name = job_config.name if job_config else job_id
            
            # 创建日志记录
            log_entry = SchedulerJobLog(
                job_id=job_id,
                job_name=job_name,
                start_time=event.scheduled_run_time,
                status='running'
            )
            
            if hasattr(event, 'exception'):
                # 任务执行出错
                log_entry.status = 'error'
                log_entry.error = str(event.exception)
                log_entry.end_time = datetime.utcnow()
                
                if hasattr(event, 'traceback'):
                    log_entry.error += f"\n{event.traceback}"
                
                logger.error(f"任务执行失败 {job_name}: {event.exception}")
                
            elif hasattr(event, 'retval'):
                # 任务执行成功
                log_entry.status = 'completed'
                log_entry.result = json.dumps(event.retval) if event.retval else None
                log_entry.end_time = datetime.utcnow()
                
                logger.info(f"任务执行成功: {job_name}")
            
            else:
                # 任务错过执行
                log_entry.status = 'missed'
                log_entry.end_time = datetime.utcnow()
                log_entry.error = "任务错过执行时间"
                
                logger.warning(f"任务错过执行: {job_name}")
            
            # 计算执行时长
            if log_entry.end_time and log_entry.start_time:
                duration = (log_entry.end_time - log_entry.start_time).total_seconds()
                log_entry.duration = duration
            
            # 保存日志
            db.session.add(log_entry)
            db.session.commit()
            
        except Exception as e:
            logger.error(f"记录任务执行日志失败: {e}")
    
    # ==================== 任务管理API ==================== 
    
    def add_job(self, job_id: str, name: str, func_name: str, 
               trigger_type: str, trigger_args: dict,
               args: list = None, kwargs: dict = None,
               description: str = None, created_by: str = None) -> bool:
        """添加新任务"""
        try:
            # 检查任务是否已存在
            existing_job = SchedulerJob.query.filter_by(id=job_id).first()
            if existing_job:
                logger.warning(f"任务ID已存在: {job_id}")
                return False
            
            # 创建数据库记录
            job_config = SchedulerJob(
                id=job_id,
                name=name,
                job_type='custom',
                func=func_name,
                args=json.dumps(args) if args else None,
                kwargs=json.dumps(kwargs) if kwargs else None,
                trigger=trigger_type,
                trigger_args=json.dumps(trigger_args),
                description=description,
                created_by=created_by,
                enabled=True
            )
            
            db.session.add(job_config)
            db.session.commit()
            
            # 如果调度器正在运行，添加到调度器
            if self.is_running:
                trigger = self._create_trigger(trigger_type, trigger_args)
                func = self._get_function_by_name(func_name)
                
                self.scheduler.add_job(
                    func=func,
                    trigger=trigger,
                    args=args or [],
                    kwargs=kwargs or {},
                    id=job_id,
                    name=name,
                    replace_existing=True
                )
            
            logger.info(f"任务添加成功: {name}")
            return True
            
        except Exception as e:
            logger.error(f"添加任务失败: {e}")
            db.session.rollback()
            return False
    
    def remove_job(self, job_id: str) -> bool:
        """删除任务"""
        try:
            # 从调度器删除
            if self.is_running and self.scheduler.get_job(job_id):
                self.scheduler.remove_job(job_id)
            
            # 从数据库删除
            job_config = SchedulerJob.query.filter_by(id=job_id).first()
            if job_config:
                db.session.delete(job_config)
                db.session.commit()
                logger.info(f"任务删除成功: {job_id}")
                return True
            else:
                logger.warning(f"任务不存在: {job_id}")
                return False
                
        except Exception as e:
            logger.error(f"删除任务失败: {e}")
            db.session.rollback()
            return False
    
    def pause_job(self, job_id: str) -> bool:
        """暂停任务"""
        try:
            if self.is_running and self.scheduler.get_job(job_id):
                self.scheduler.pause_job(job_id)
                
                # 更新数据库状态
                job_config = SchedulerJob.query.filter_by(id=job_id).first()
                if job_config:
                    job_config.enabled = False
                    db.session.commit()
                
                logger.info(f"任务暂停成功: {job_id}")
                return True
            return False
            
        except Exception as e:
            logger.error(f"暂停任务失败: {e}")
            return False
    
    def resume_job(self, job_id: str) -> bool:
        """恢复任务"""
        try:
            if self.is_running and self.scheduler.get_job(job_id):
                self.scheduler.resume_job(job_id)
                
                # 更新数据库状态
                job_config = SchedulerJob.query.filter_by(id=job_id).first()
                if job_config:
                    job_config.enabled = True
                    db.session.commit()
                
                logger.info(f"任务恢复成功: {job_id}")
                return True
            return False
            
        except Exception as e:
            logger.error(f"恢复任务失败: {e}")
            return False
    
    def get_job_status(self, job_id: str) -> Optional[Dict[str, Any]]:
        """获取任务状态"""
        try:
            # 从数据库获取配置
            job_config = SchedulerJob.query.filter_by(id=job_id).first()
            if not job_config:
                return None
            
            # 从调度器获取运行时状态
            scheduler_job = None
            if self.is_running:
                scheduler_job = self.scheduler.get_job(job_id)
            
            # 获取最近的执行日志
            latest_log = SchedulerJobLog.query.filter_by(job_id=job_id)\
                                            .order_by(SchedulerJobLog.start_time.desc())\
                                            .first()
            
            return {
                'config': job_config.to_dict(),
                'scheduler_status': {
                    'next_run_time': scheduler_job.next_run_time.isoformat() if scheduler_job and scheduler_job.next_run_time else None,
                    'pending': scheduler_job is not None
                },
                'latest_execution': latest_log.to_dict() if latest_log else None
            }
            
        except Exception as e:
            logger.error(f"获取任务状态失败: {e}")
            return None
    
    def get_all_jobs(self) -> List[Dict[str, Any]]:
        """获取所有任务状态"""
        try:
            jobs = SchedulerJob.query.all()
            result = []
            
            for job_config in jobs:
                job_status = self.get_job_status(job_config.id)
                if job_status:
                    result.append(job_status)
            
            return result
            
        except Exception as e:
            logger.error(f"获取所有任务失败: {e}")
            return []
    
    def run_job_now(self, job_id: str) -> bool:
        """立即执行任务"""
        try:
            if self.is_running:
                job = self.scheduler.get_job(job_id)
                if job:
                    job.modify(next_run_time=datetime.now())
                    logger.info(f"任务手动触发成功: {job_id}")
                    return True
            return False
            
        except Exception as e:
            logger.error(f"手动触发任务失败: {e}")
            return False
    
    def get_execution_logs(self, job_id: str = None, limit: int = 100, status: str = None) -> List[Dict[str, Any]]:
        """获取执行日志"""
        try:
            query = SchedulerJobLog.query
            
            if job_id:
                query = query.filter_by(job_id=job_id)
            
            if status:
                query = query.filter_by(status=status)
            
            logs = query.order_by(SchedulerJobLog.start_time.desc()).limit(limit).all()
            
            return [log.to_dict() for log in logs]
            
        except Exception as e:
            logger.error(f"获取执行日志失败: {e}")
            return []

# 全局调度器实例
scheduler_service = APSchedulerService() 