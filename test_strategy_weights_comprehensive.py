#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全面测试策略权重配置功能
"""

import requests
import json
import time

# API基础URL
BASE_URL = "http://127.0.0.1:5000"
LOGIN_URL = f"{BASE_URL}/auth/login"
WEIGHTS_API_URL = f"{BASE_URL}/api/production/algorithm-weights"
STRATEGIES_API_URL = f"{BASE_URL}/api/production/algorithm-strategies"

def login_and_get_session():
    """登录并获取会话"""
    session = requests.Session()
    
    # 首先获取登录页面
    login_page = session.get(f"{BASE_URL}/auth/login")
    
    # 登录
    login_data = {
        'username': 'admin',
        'password': 'admin'
    }
    
    response = session.post(LOGIN_URL, data=login_data, allow_redirects=False)
    
    if response.status_code in [200, 302]:
        print("✅ 登录成功")
        return session
    else:
        print(f"❌ 登录失败: {response.status_code}")
        return None

def test_get_strategies(session):
    """测试获取策略列表"""
    print("\n📋 测试获取策略列表...")
    
    try:
        response = session.get(STRATEGIES_API_URL)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 获取策略列表成功")
            print(f"策略数量: {len(data.get('strategies', []))}")
            for strategy in data.get('strategies', []):
                print(f"  - {strategy['value']}: {strategy['label']}")
            return data.get('strategies', [])
        else:
            print(f"❌ 获取策略列表失败: {response.status_code}")
            return []
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return []

def test_get_strategy_weights(session, strategy):
    """测试获取指定策略的权重配置"""
    print(f"\n📊 测试获取 {strategy} 策略权重...")
    
    try:
        response = session.get(f"{WEIGHTS_API_URL}?strategy={strategy}")
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 获取 {strategy} 策略权重成功")
            weights = data.get('weights', {})
            print(f"权重配置:")
            print(f"  - 技术匹配度: {weights.get('tech_match_weight', 0)}%")
            print(f"  - 负载均衡: {weights.get('load_balance_weight', 0)}%")
            print(f"  - 交期紧迫度: {weights.get('deadline_weight', 0)}%")
            print(f"  - 产值效率: {weights.get('value_efficiency_weight', 0)}%")
            print(f"  - 业务优先级: {weights.get('business_priority_weight', 0)}%")
            
            # 验证权重总和
            total = sum([
                weights.get('tech_match_weight', 0),
                weights.get('load_balance_weight', 0),
                weights.get('deadline_weight', 0),
                weights.get('value_efficiency_weight', 0),
                weights.get('business_priority_weight', 0)
            ])
            print(f"  - 权重总和: {total}%")
            
            return data
        else:
            print(f"❌ 获取 {strategy} 策略权重失败: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return None

def test_save_strategy_weights(session, strategy, custom_weights):
    """测试保存指定策略的权重配置"""
    print(f"\n💾 测试保存 {strategy} 策略权重...")
    
    payload = {
        'strategy': strategy,
        **custom_weights
    }
    
    try:
        response = session.post(
            WEIGHTS_API_URL,
            json=payload,
            headers={'Content-Type': 'application/json'}
        )
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 保存 {strategy} 策略权重成功")
            return True
        else:
            print(f"❌ 保存 {strategy} 策略权重失败: {response.status_code}")
            print(f"响应内容: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def test_strategy_weight_isolation(session):
    """测试策略权重隔离性"""
    print("\n🔒 测试策略权重隔离性...")
    
    # 为不同策略设置不同的权重
    test_configs = {
        'intelligent': {
            'tech_match_weight': 25.0,
            'load_balance_weight': 25.0,
            'deadline_weight': 25.0,
            'value_efficiency_weight': 15.0,
            'business_priority_weight': 10.0
        },
        'deadline': {
            'tech_match_weight': 10.0,
            'load_balance_weight': 10.0,
            'deadline_weight': 60.0,
            'value_efficiency_weight': 10.0,
            'business_priority_weight': 10.0
        },
        'product': {
            'tech_match_weight': 50.0,
            'load_balance_weight': 15.0,
            'deadline_weight': 15.0,
            'value_efficiency_weight': 10.0,
            'business_priority_weight': 10.0
        },
        'value': {
            'tech_match_weight': 10.0,
            'load_balance_weight': 10.0,
            'deadline_weight': 15.0,
            'value_efficiency_weight': 55.0,
            'business_priority_weight': 10.0
        }
    }
    
    # 保存所有策略的权重
    for strategy, weights in test_configs.items():
        success = test_save_strategy_weights(session, strategy, weights)
        if not success:
            print(f"❌ 策略 {strategy} 权重保存失败")
            return False
        time.sleep(0.5)  # 避免请求过快
    
    # 验证每个策略的权重是否正确保存
    print("\n🔍 验证策略权重隔离性...")
    all_correct = True
    
    for strategy, expected_weights in test_configs.items():
        actual_data = test_get_strategy_weights(session, strategy)
        if actual_data and actual_data.get('success'):
            actual_weights = actual_data.get('weights', {})
            
            # 检查关键权重是否匹配
            key_weight = None
            if strategy == 'deadline':
                key_weight = 'deadline_weight'
            elif strategy == 'product':
                key_weight = 'tech_match_weight'
            elif strategy == 'value':
                key_weight = 'value_efficiency_weight'
            else:
                key_weight = 'tech_match_weight'
            
            expected_value = expected_weights[key_weight]
            actual_value = actual_weights.get(key_weight, 0)
            
            if abs(expected_value - actual_value) < 0.01:
                print(f"✅ 策略 {strategy} 权重隔离正确")
            else:
                print(f"❌ 策略 {strategy} 权重隔离失败: 期望 {expected_value}, 实际 {actual_value}")
                all_correct = False
        else:
            print(f"❌ 无法验证策略 {strategy} 的权重")
            all_correct = False
    
    return all_correct

def main():
    """主测试函数"""
    print("🧪 开始全面测试策略权重配置功能...")
    
    # 登录
    session = login_and_get_session()
    if not session:
        print("❌ 无法登录，测试终止")
        return
    
    # 等待一下确保登录完成
    time.sleep(1)
    
    # 测试获取策略列表
    strategies = test_get_strategies(session)
    
    # 测试获取每个策略的默认权重
    print("\n📊 测试获取各策略默认权重...")
    for strategy_info in strategies:
        strategy = strategy_info['value']
        test_get_strategy_weights(session, strategy)
        time.sleep(0.5)
    
    # 测试策略权重隔离性
    isolation_success = test_strategy_weight_isolation(session)
    
    # 测试无效权重数据
    print("\n🚫 测试无效权重数据...")
    invalid_weights = {
        'strategy': 'intelligent',
        'tech_match_weight': 50.0,
        'load_balance_weight': 30.0,
        'deadline_weight': 30.0,
        'value_efficiency_weight': 20.0,
        'business_priority_weight': 20.0  # 总和150%
    }
    
    try:
        response = session.post(
            WEIGHTS_API_URL,
            json=invalid_weights,
            headers={'Content-Type': 'application/json'}
        )
        
        if response.status_code == 400:
            print("✅ 正确拒绝了无效权重数据")
            invalid_test_success = True
        else:
            print(f"❌ 应该拒绝无效数据但没有: {response.status_code}")
            invalid_test_success = False
    except Exception as e:
        print(f"❌ 测试无效数据时出错: {e}")
        invalid_test_success = False
    
    # 总结
    print("\n📊 测试总结:")
    print(f"  - 策略列表获取: {'✅' if strategies else '❌'}")
    print(f"  - 策略权重隔离: {'✅' if isolation_success else '❌'}")
    print(f"  - 无效数据验证: {'✅' if invalid_test_success else '❌'}")
    
    if strategies and isolation_success and invalid_test_success:
        print("\n🎉 所有测试通过！策略权重配置功能正常工作。")
        print("\n✨ 功能特性验证:")
        print("  ✅ 支持多种排产策略（智能综合、交期优先、产品优先、产值优先）")
        print("  ✅ 每个策略拥有独立的权重配置")
        print("  ✅ 权重配置数据验证正常")
        print("  ✅ 前后端API集成正常")
    else:
        print("\n⚠️ 部分测试失败，请检查相关功能。")

if __name__ == "__main__":
    main()
