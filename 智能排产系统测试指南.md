# 🧠 智能排产系统前端测试指南

## 📋 测试环境准备

### 1. 访问地址
```
http://127.0.0.1:5000/production/semi-auto
```

### 2. 数据源确认
在测试前，请确保以下数据表有数据：
- ✅ **ET_WAIT_LOT**: 待排产批次 (应有 171 个批次)
- ✅ **EQP_STATUS**: 设备状态 (应有 65 台设备)
- ✅ **ET_FT_TEST_SPEC**: 测试规范 (应有 527 条记录)
- ✅ **et_recipe_file**: 设备配方 (应有 610 条记录)
- ✅ **ET_UPH_EQP**: 产能数据 (应有 1759 条记录)

## 🚀 智能排产测试步骤

### 第一步：选择排产策略
在"手动排产"区域，你会看到：

```
排产策略：
🧠 智能综合策略 (推荐) ← 默认选择
📅 交期优先策略
📦 产品优先策略  
💰 产值优先策略

优化目标：
⚖️ 均衡优化 (推荐) ← 默认选择
⏱️ 最小化完工时间
🚀 最大化效率
```

### 第二步：执行智能排产
1. 点击 **"🧠 手动排产"** 按钮
2. 观察进度条显示排产进度
3. 等待算法完成（预计 45-60 秒）

### 第三步：验证排产结果

#### 📊 统计信息验证
排产完成后，查看统计面板：
- **总批次数**: 应显示 ~171
- **已排产批次**: 应显示 ~118 (排产成功率约 69%)
- **排产策略**: 应显示 "🧠 智能综合"
- **执行时间**: 应显示 ~45-60秒

#### 📋 排产结果表格验证

**重点观察的字段：**
- **优先级**: 按1,2,3...顺序排列
- **设备ID**: 不再是简单的H01,H02,H03循环，而是智能匹配
- **匹配类型**: 
  - 🟢 完全匹配 (最佳)
  - 🔵 KIT匹配 (很好)
  - 🟡 小改机 (需要更换配件)
  - ⚪ 大改机 (需要重新配置)
  - 🔵 空闲设备 (可配置)
- **综合评分**: 0-100分，分数越高越优先
- **改机时间**: 0-120分钟，时间越短越好

#### 🎯 典型成功案例预期

**烘箱设备** (ABAK-O-001-3004):
- 应分配到 BAKING2 工序的批次
- STATUS为空字符串，但算法能正常识别

**编带机** (如 ATSR-S-003-3080):
- 应分配到 LSTR 工序的批次
- 纯编带工序，不需要测试

**测试机** (如 HCHC-C-020-6800):
- 应分配到 HOT-FT, COLD-FT 等测试工序
- 有完整的KIT配置信息

**烧录机** (如 HANK-C-001-5800):
- 应分配到 TRIM-FT 等编程工序
- 配置为IPS5800S

### 第四步：对比验证

#### ❌ 原始错误算法特征
- 所有批次按 H01→H02→H03... 循环分配
- 不考虑设备类型兼容性
- 无技术匹配评分

#### ✅ 新智能算法特征
- 基于DEVICE+STAGE进行技术匹配
- 五维度综合评分 (技术25% + 负载20% + 交期25% + 产值20% + 优先级10%)
- 设备类型严格区分 (测试机/烧录机/烘箱/编带机)
- 优先选择已配置正确KIT的设备

## 🔍 详细测试案例

### 案例1：YX2500002000批次验证
**预期结果**：
- 需求：JWQ5103CSQFNAT_TR0 + HOT-FT
- 应选择：HCHC-C-020-6800 (已配置CKC-QFN2X3-0.85-2X4-HB)
- 匹配类型：完全匹配或KIT匹配
- 综合评分：应该较高 (>60分)

### 案例2：设备类型验证
**烘箱任务**：
- 批次包含 BAKING2 工序
- 应分配给 ABAK-O-001-3004
- 不应分配给测试机或烧录机

**测试任务**：
- 批次包含 HOT-FT, COLD-FT 等
- 应分配给测试机 (HCHC-C-xxx-6800)
- 不应分配给烧录机

## 🚨 异常情况处理

### 如果排产失败
1. 检查网络连接
2. 查看浏览器控制台错误信息
3. 确认数据表是否有数据
4. 检查服务器日志

### 如果结果异常
1. 对比排产前后的设备分配差异
2. 检查匹配类型是否合理
3. 验证评分逻辑是否正常

## 🎉 测试完成标准

✅ 排产成功率 ≥ 65%
✅ 设备分配不再是简单循环
✅ 匹配类型显示正确
✅ 综合评分合理 (有差异化)
✅ 设备类型兼容性正确
✅ 执行时间 < 60秒

---

**🔗 准备好了吗？**
访问 http://127.0.0.1:5000/production/semi-auto 开始测试！

如发现任何问题，请记录具体的错误信息和异常批次，我们将进一步优化算法。 