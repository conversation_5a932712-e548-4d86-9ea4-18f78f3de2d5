#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
页面管理脚本 - 11个表页面的统一管理工具
支持生成、测试、部署、监控等功能
"""

import os
import sys
import json
import argparse
import logging
from datetime import datetime

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.utils.page_generator import PageGenerator, generate_all_pages
from app.utils.page_tester import PageTester, test_all_pages
from app.config.table_configs import TABLE_CONFIGS

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('page_management.log', encoding='utf-8')
    ]
)

logger = logging.getLogger(__name__)

class PageManager:
    """页面管理器 - 统一管理11个表页面"""
    
    def __init__(self):
        self.target_tables = [
            'eqp_status', 'et_uph_eqp', 'et_ft_test_spec', 'ct', 'tcc_inv',
            'wip_lot', 'et_wait_lot', 'et_recipe_file', 'devicepriorityconfig',
            'lotpriorityconfig', 'lotprioritydone'
        ]
        self.generator = PageGenerator()
        self.tester = PageTester()
    
    def status(self):
        """显示当前状态"""
        print("📊 APS 11个表页面管理状态")
        print("=" * 50)
        
        # 检查配置状态
        print("\n🔧 配置状态:")
        configured_count = 0
        for table_name in self.target_tables:
            if table_name in TABLE_CONFIGS:
                configured_count += 1
                status = "✅ 已配置"
            else:
                status = "❌ 未配置"
            print(f"  {table_name:<20} {status}")
        
        print(f"\n配置完成度: {configured_count}/{len(self.target_tables)} ({configured_count/len(self.target_tables)*100:.1f}%)")
        
        # 检查路由状态
        print("\n🛣️ 路由状态:")
        print("  所有表都使用通用路由: /api/v3/universal/<table_name>")
        
        # 检查模板状态
        template_path = "app/templates/resources/universal_resource_v3.html"
        if os.path.exists(template_path):
            print(f"\n📄 模板状态: ✅ {template_path} 存在")
        else:
            print(f"\n📄 模板状态: ❌ {template_path} 不存在")
    
    def generate(self, table_name=None):
        """生成页面配置"""
        if table_name:
            print(f"🚀 生成 {table_name} 页面配置...")
            result = self.generator.generate_single_page(table_name)
            print(json.dumps(result, ensure_ascii=False, indent=2))
        else:
            print("🚀 批量生成所有页面配置...")
            result = self.generator.generate_all_pages()
            
            print(f"\n📊 生成结果:")
            print(f"  成功: {result['summary']['success']}")
            print(f"  失败: {result['summary']['failed']}")
            print(f"  总计: {result['summary']['total']}")
            
            if result['errors']:
                print(f"\n❌ 失败详情:")
                for error in result['errors']:
                    print(f"  {error['table']}: {error['error']}")
    
    def test(self, table_name=None, base_url="http://localhost:5000"):
        """测试页面功能"""
        if table_name:
            print(f"🧪 测试 {table_name} 页面功能...")
            result = self.tester.test_single_page(table_name)
            print(json.dumps(result, ensure_ascii=False, indent=2))
        else:
            print("🧪 批量测试所有页面功能...")
            result = self.tester.test_all_pages()
            
            # 生成报告
            report = self.tester.generate_test_report(result)
            print(report)
            
            # 保存详细结果
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            result_file = f"test_results_{timestamp}.json"
            with open(result_file, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            print(f"\n📄 详细测试结果已保存到: {result_file}")
    
    def deploy(self):
        """部署页面（检查部署状态）"""
        print("🚀 检查部署状态...")
        
        # 检查必要文件
        required_files = [
            "app/templates/resources/universal_resource_v3.html",
            "app/config/table_configs.py",
            "app/api/routes_v3.py",
            "app/utils/page_generator.py",
            "app/utils/page_tester.py"
        ]
        
        missing_files = []
        for file_path in required_files:
            if not os.path.exists(file_path):
                missing_files.append(file_path)
        
        if missing_files:
            print("❌ 部署检查失败，缺少文件:")
            for file_path in missing_files:
                print(f"  - {file_path}")
            return False
        
        print("✅ 所有必要文件都存在")
        
        # 检查配置完整性
        missing_configs = []
        for table_name in self.target_tables:
            if table_name not in TABLE_CONFIGS:
                missing_configs.append(table_name)
        
        if missing_configs:
            print("⚠️ 以下表缺少配置:")
            for table_name in missing_configs:
                print(f"  - {table_name}")
        else:
            print("✅ 所有表配置都存在")
        
        print("\n🎯 部署清单:")
        print("1. ✅ 通用模板已创建")
        print("2. ✅ 表配置系统已建立")
        print("3. ✅ 路由配置已更新")
        print("4. ✅ 页面生成工具已创建")
        print("5. ✅ 测试工具已创建")
        
        return True
    
    def export_config(self, output_file=None):
        """导出配置"""
        if not output_file:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"table_configs_export_{timestamp}.json"
        
        export_data = {
            'timestamp': datetime.now().isoformat(),
            'tables': self.target_tables,
            'configs': TABLE_CONFIGS
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 配置已导出到: {output_file}")
        return output_file
    
    def validate(self):
        """验证配置和页面"""
        print("🔍 验证配置和页面...")
        
        issues = []
        
        # 验证配置完整性
        for table_name in self.target_tables:
            config = TABLE_CONFIGS.get(table_name)
            if not config:
                issues.append(f"❌ {table_name}: 缺少配置")
                continue
            
            # 检查必要字段
            required_fields = ['title', 'icon', 'description', 'business_key']
            for field in required_fields:
                if field not in config:
                    issues.append(f"❌ {table_name}: 缺少字段 {field}")
        
        # 验证路由配置
        routes_file = "app/api/routes_v3.py"
        if os.path.exists(routes_file):
            with open(routes_file, 'r', encoding='utf-8') as f:
                content = f.read()
                if 'get_table_config' not in content:
                    issues.append("❌ routes_v3.py: 未使用新的配置系统")
        else:
            issues.append("❌ routes_v3.py: 文件不存在")
        
        # 验证模板
        template_file = "app/templates/resources/universal_resource_v3.html"
        if os.path.exists(template_file):
            with open(template_file, 'r', encoding='utf-8') as f:
                content = f.read()
                if 'table_config' not in content:
                    issues.append("❌ universal_resource_v3.html: 未使用配置系统")
        else:
            issues.append("❌ universal_resource_v3.html: 文件不存在")
        
        if issues:
            print("发现以下问题:")
            for issue in issues:
                print(f"  {issue}")
            return False
        else:
            print("✅ 所有验证都通过")
            return True

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="APS 11个表页面管理工具")
    parser.add_argument('command', choices=['status', 'generate', 'test', 'deploy', 'export', 'validate'],
                       help='要执行的命令')
    parser.add_argument('--table', help='指定表名（用于generate和test命令）')
    parser.add_argument('--url', default='http://localhost:5000', help='测试用的基础URL')
    parser.add_argument('--output', help='输出文件路径（用于export命令）')
    
    args = parser.parse_args()
    
    manager = PageManager()
    
    try:
        if args.command == 'status':
            manager.status()
        elif args.command == 'generate':
            manager.generate(args.table)
        elif args.command == 'test':
            manager.test(args.table, args.url)
        elif args.command == 'deploy':
            manager.deploy()
        elif args.command == 'export':
            manager.export_config(args.output)
        elif args.command == 'validate':
            manager.validate()
    except KeyboardInterrupt:
        print("\n⚠️ 操作被用户中断")
    except Exception as e:
        logger.error(f"❌ 执行失败: {e}")
        print(f"❌ 执行失败: {e}")

if __name__ == "__main__":
    main()
