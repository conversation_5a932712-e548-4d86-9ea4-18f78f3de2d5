2025-06-17 21:23:20,241 ERROR: 检查会话状态失败: 'User' object has no attribute 'id' [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\api_v2\auth\routes.py:63]
2025-06-17 21:23:20,320 INFO: 从MySQL获取到 184 条测试规范数据 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:439]
2025-06-17 21:23:20,323 INFO: 🔬 从MySQL获取到 184 条测试规范数据 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:247]
2025-06-17 21:24:01,310 INFO: 尝试登录用户: admin [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\auth\routes.py:24]
2025-06-17 21:24:01,314 INFO: 找到用户: admin, 角色: admin, 密码哈希: pbkdf2:sha256:600000$5b3oIzxKyFuBWizM$eaaab87f30981563f8519ad8cb7e8669fa5b6280e439fcd17b1845cdd1c4f56c [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\auth\routes.py:33]
2025-06-17 21:24:01,876 INFO: 密码验证结果: True [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\auth\routes.py:35]
2025-06-17 21:24:01,876 INFO: 用户 admin 登录成功 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\auth\routes.py:42]
2025-06-17 21:24:03,917 INFO: 从MySQL获取到 62 条设备状态数据 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:534]
2025-06-17 21:24:03,917 INFO: 🏭 从MySQL获取到 62 条设备状态数据 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:295]
2025-06-17 21:24:08,016 INFO: 从MySQL获取到 62 条设备状态数据 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:534]
2025-06-17 21:24:08,016 INFO: 🏭 从MySQL获取到 62 条设备状态数据 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:295]
2025-06-17 21:24:10,644 INFO: 从MySQL获取到 184 条测试规范数据 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:439]
2025-06-17 21:24:10,646 INFO: 🔬 从MySQL获取到 184 条测试规范数据 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:247]
2025-06-17 21:24:14,875 INFO: 从MySQL获取到 184 条测试规范数据 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:439]
2025-06-17 21:24:14,877 INFO: 🔬 从MySQL获取到 184 条测试规范数据 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:247]
2025-06-17 21:24:17,453 INFO: 从MySQL获取到 1759 条UPH数据 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:577]
2025-06-17 21:24:17,454 INFO: ⚡ 从MySQL获取到 1759 条UPH数据 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:319]
2025-06-17 21:24:21,584 INFO: 从MySQL获取到 1759 条UPH数据 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:577]
2025-06-17 21:24:21,586 INFO: ⚡ 从MySQL获取到 1759 条UPH数据 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:319]
2025-06-17 21:24:24,171 INFO: 从MySQL获取到 124 条TCC_INV数据 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:840]
2025-06-17 21:24:28,283 INFO: 从MySQL获取到 124 条TCC_INV数据 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:840]
2025-06-17 21:24:30,842 INFO: 从MySQL获取到 1000 条CT数据 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:868]
2025-06-17 21:24:34,955 INFO: 从MySQL获取到 1000 条CT数据 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:868]
2025-06-17 21:24:37,525 INFO: 📊 从MySQL获取到 171 条待排产批次数据 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:221]
2025-06-17 21:24:41,607 INFO: 📊 从MySQL获取到 171 条待排产批次数据 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:221]
2025-06-17 21:24:44,170 INFO: 从MySQL获取到 2 条已排产批次数据 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:1510]
2025-06-17 21:24:48,281 INFO: 从MySQL获取到 2 条已排产批次数据 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:1510]
2025-06-17 21:24:50,849 INFO: 从MySQL获取到 11 条产品优先级配置数据 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:1387]
2025-06-17 21:24:54,976 ERROR: 创建devicepriorityconfig记录失败: (1366, "Incorrect integer value: 'high' for column 'priority' at row 1") [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:1117]
2025-06-17 21:24:57,021 INFO: 从MySQL获取到 11 条产品优先级配置数据 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:1387]
2025-06-17 21:24:59,568 INFO: 从MySQL获取到 19 条批次优先级配置数据 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:1417]
2025-06-17 21:25:03,651 ERROR: 创建lotpriorityconfig记录失败: (1366, "Incorrect integer value: 'medium' for column 'priority' at row 1") [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:1117]
2025-06-17 21:25:05,719 INFO: 从MySQL获取到 19 条批次优先级配置数据 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:1417]
2025-06-17 21:25:10,315 INFO: 尝试登录用户: admin [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\auth\routes.py:24]
2025-06-17 21:25:10,317 INFO: 找到用户: admin, 角色: admin, 密码哈希: pbkdf2:sha256:600000$5b3oIzxKyFuBWizM$eaaab87f30981563f8519ad8cb7e8669fa5b6280e439fcd17b1845cdd1c4f56c [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\auth\routes.py:33]
2025-06-17 21:25:10,861 INFO: 密码验证结果: True [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\auth\routes.py:35]
2025-06-17 21:25:10,862 INFO: 用户 admin 登录成功 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\auth\routes.py:42]
2025-06-17 21:25:12,907 ERROR: 创建devicepriorityconfig记录失败: (1366, "Incorrect integer value: 'high' for column 'priority' at row 1") [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:1117]
2025-06-17 21:25:14,976 ERROR: 创建lotpriorityconfig记录失败: (1366, "Incorrect integer value: 'medium' for column 'priority' at row 1") [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:1117]
2025-06-17 21:25:19,080 INFO: 尝试登录用户: admin [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\auth\routes.py:24]
2025-06-17 21:25:19,083 INFO: 找到用户: admin, 角色: admin, 密码哈希: pbkdf2:sha256:600000$5b3oIzxKyFuBWizM$eaaab87f30981563f8519ad8cb7e8669fa5b6280e439fcd17b1845cdd1c4f56c [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\auth\routes.py:33]
2025-06-17 21:25:19,617 INFO: 密码验证结果: True [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\auth\routes.py:35]
2025-06-17 21:25:19,618 INFO: 用户 admin 登录成功 [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\auth\routes.py:42]
2025-06-17 21:25:21,659 ERROR: 创建devicepriorityconfig记录失败: (1366, "Incorrect integer value: 'high' for column 'priority' at row 1") [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:1117]
2025-06-17 21:25:23,918 ERROR: 创建devicepriorityconfig记录失败: (1366, "Incorrect integer value: 'medium' for column 'priority' at row 1") [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:1117]
2025-06-17 21:25:26,177 ERROR: 创建devicepriorityconfig记录失败: (1366, "Incorrect integer value: 'low' for column 'priority' at row 1") [in D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.12\app\services\data_source_manager.py:1117]
