#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

print("🔍 检查ET_WAIT_LOT表中的DUE_DATE数据...")

try:
    from app.services.data_source_manager import DataSourceManager
    
    dm = DataSourceManager()
    
    # 获取待排产批次数据
    result = dm.get_table_data('ET_WAIT_LOT', per_page=200)
    
    if not result.get('success'):
        print("❌ 无法获取ET_WAIT_LOT数据")
        exit(1)
    
    wait_lots = result.get('data', [])
    print(f"📊 获取到 {len(wait_lots)} 条待排产批次数据")
    
    # 检查字段结构
    if wait_lots:
        first_lot = wait_lots[0]
        print(f"\n📋 数据字段列表:")
        for key in sorted(first_lot.keys()):
            value = first_lot.get(key, '')
            print(f"  {key}: {value}")
    
    # 检查DUE_DATE字段
    print(f"\n=== 🗓️ DUE_DATE字段分析 ===")
    
    due_date_stats = {
        'has_due_date': 0,
        'empty_due_date': 0,
        'null_due_date': 0,
        'valid_dates': []
    }
    
    for lot in wait_lots:
        due_date = lot.get('DUE_DATE')
        
        if due_date is None:
            due_date_stats['null_due_date'] += 1
        elif due_date == '' or due_date.strip() == '':
            due_date_stats['empty_due_date'] += 1
        else:
            due_date_stats['has_due_date'] += 1
            due_date_stats['valid_dates'].append(due_date)
    
    print(f"✅ 有交期数据: {due_date_stats['has_due_date']} 个批次")
    print(f"❌ 空交期数据: {due_date_stats['empty_due_date']} 个批次")
    print(f"❓ NULL交期数据: {due_date_stats['null_due_date']} 个批次")
    
    if due_date_stats['valid_dates']:
        print(f"\n📅 有效交期样本:")
        for i, date in enumerate(due_date_stats['valid_dates'][:10]):  # 显示前10个
            print(f"  {date}")
        if len(due_date_stats['valid_dates']) > 10:
            print(f"  ... 还有 {len(due_date_stats['valid_dates'])-10} 个")
    
    # 检查其他可能的交期字段
    print(f"\n=== 🔍 查找其他可能的交期字段 ===")
    
    date_related_fields = []
    for key in first_lot.keys():
        if any(date_word in key.upper() for date_word in ['DATE', 'TIME', 'DUE', 'DELIVERY', 'DEADLINE']):
            date_related_fields.append(key)
    
    if date_related_fields:
        print(f"发现可能的日期相关字段:")
        for field in date_related_fields:
            sample_value = first_lot.get(field, '')
            print(f"  {field}: {sample_value}")
    else:
        print("❌ 未发现其他日期相关字段")
    
    # 检查批次优先级相关字段
    print(f"\n=== 🎯 批次优先级相关字段 ===")
    priority_fields = []
    for key in first_lot.keys():
        if any(priority_word in key.upper() for priority_word in ['PRIORITY', 'URGENT', 'LEVEL', 'RANK']):
            priority_fields.append(key)
    
    if priority_fields:
        print(f"发现优先级相关字段:")
        for field in priority_fields:
            sample_value = first_lot.get(field, '')
            print(f"  {field}: {sample_value}")
    else:
        print("❌ 未发现优先级相关字段")
    
    print(f"\n✅ DUE_DATE数据检查完成！")
    
except Exception as e:
    print(f"❌ 检查失败: {e}")
    import traceback
    traceback.print_exc() 