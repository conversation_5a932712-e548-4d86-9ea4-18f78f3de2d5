# 🎯 智能排产系统完善计划 (APS 2.0)

## 📋 **总体目标**
实现基于真实业务逻辑的智能排产算法，支持多维度评分和用户自定义权重配置。

## 🚀 **Phase 1: 核心算法重构 (优先级: 🔥高)**
*预估时间: 5-7天*

### **Task 1.1: 配置匹配引擎开发** ⭐ **当前任务**
**负责人**: AI Assistant  
**预估时间**: 2天  
**状态**: 🔄 进行中

#### **子任务:**
- [ ] **1.1.1** 实现 DEVICE+STAGE → ET_FT_TEST_SPEC 查询逻辑
  - 筛选 APPROVAL_STATE = 'Released' 的记录
  - 获取 HB_PN, TB_PN, HANDLER_CONFIG 信息
- [ ] **1.1.2** 实现 DEVICE+STAGE+PKG_PN → ET_RECIPE_FILE 查询逻辑  
  - 获取 KIT_PN, SOCKET_PN 信息
- [ ] **1.1.3** 设备配置匹配算法
  - 完全匹配检测
  - 小改机检测 (HB/TB不同，KIT相同)
  - 大改机检测 (HB/TB/KIT都不同)
- [ ] **1.1.4** 改机时间计算
  ```python
  改机时间计算 = {
      '小改机': HB更换(15min) + TB更换(10min) + 校准(20min) = 45min,
      '大改机': 小改机(45min) + KIT更换(45min) + 首件确认(30min) = 120min
  }
  ```

#### **验收标准:**
- 能准确识别批次的配置需求
- 正确计算设备匹配度和改机时间
- 通过单元测试覆盖率 ≥ 90%

---

### **Task 1.2: 多维度评分系统**
**预估时间**: 2天  
**依赖**: Task 1.1 完成

#### **子任务:**
- [ ] **1.2.1** 技术匹配度评分 (权重25%)
  ```python
  技术匹配分 = {
      '完全匹配': 100分,
      '小改机': 80分,  
      '大改机': 60分,
      '不匹配': 0分
  }
  ```
- [ ] **1.2.2** 负载均衡评分 (权重20%)
  ```python
  负载评分 = 100 - (当前负载+改机时间+加工时间) / 最大负载 * 100
  ```
- [ ] **1.2.3** 交期紧迫度评分 (权重25%)
  ```python
  紧迫度分 = {
      '超期风险': 150分,  # 剩余时间 < 0
      '极紧急': 120分,    # 剩余时间 < 8h
      '紧急': 100分,      # 剩余时间 < 24h  
      '正常': 80分,       # 剩余时间 < 72h
      '宽松': 60分        # 剩余时间 >= 72h
  }
  ```
- [ ] **1.2.4** 产值效率评分 (权重20%)
  ```python
  产值评分 = min(100, (批次产值/加工时间) / 基准产值率 * 100)
  ```
- [ ] **1.2.5** 业务优先级评分 (权重10%)
  ```python
  优先级分 = {
      '产品优先级设定值': devicepriorityconfig表,
      '批次优先级设定值': lotpriorityconfig表,  
      'FIFO评分': 基于工单号的先进先出
  }
  ```

---

### **Task 1.3: 同产品续排优化**
**预估时间**: 1天  
**依赖**: Task 1.2 完成

#### **子任务:**
- [ ] **1.3.1** 设备当前产品状态跟踪
- [ ] **1.3.2** 同产品续排加分机制 (+20分)
- [ ] **1.3.3** 换线成本最小化算法

---

### **Task 1.4: 综合排产算法**
**预估时间**: 2天  
**依赖**: Task 1.3 完成

#### **子任务:**
- [ ] **1.4.1** 综合评分计算引擎
  ```python
  综合评分 = (
      技术匹配分 * tech_weight +
      负载均衡分 * load_weight + 
      交期紧迫度分 * deadline_weight +
      产值效率分 * value_weight +
      业务优先级分 * priority_weight
  ) + 续排加分
  ```
- [ ] **1.4.2** 动态权重配置支持
- [ ] **1.4.3** 批次分配优化算法
- [ ] **1.4.4** 负载更新和重计算机制

#### **验收标准:**
- 排产结果不再是简单的循环分配
- 同产品优先续排到相同设备
- 综合评分逻辑准确
- 支持不同权重配置

---

## 💾 **Phase 2: 数据层支持 (优先级: 🟡中)**
*预估时间: 2-3天*

### **Task 2.1: 数据库结构扩展**
**预估时间**: 1天

#### **子任务:**
- [ ] **2.1.1** 创建 scheduling_config 配置表
  ```sql
  CREATE TABLE scheduling_config (
      id INT PRIMARY KEY AUTO_INCREMENT,
      config_name VARCHAR(100) NOT NULL,
      user_id VARCHAR(50),
      tech_match_weight DECIMAL(5,2) DEFAULT 25.00,
      load_balance_weight DECIMAL(5,2) DEFAULT 20.00,
      deadline_weight DECIMAL(5,2) DEFAULT 25.00,
      value_efficiency_weight DECIMAL(5,2) DEFAULT 20.00,
      business_priority_weight DECIMAL(5,2) DEFAULT 10.00,
      minor_changeover_time INT DEFAULT 45,
      major_changeover_time INT DEFAULT 120,
      urgent_threshold INT DEFAULT 8,
      normal_threshold INT DEFAULT 24,
      critical_threshold INT DEFAULT 72,
      is_active BOOLEAN DEFAULT TRUE,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
  );
  ```
- [ ] **2.1.2** 初始化默认配置数据
- [ ] **2.1.3** 数据库迁移脚本

---

### **Task 2.2: 配置管理API**
**预估时间**: 1-2天

#### **子任务:**
- [ ] **2.2.1** 配置CRUD接口
  - GET /api/scheduling/config - 获取配置
  - POST /api/scheduling/config - 保存配置
  - PUT /api/scheduling/config/{id} - 更新配置
  - DELETE /api/scheduling/config/{id} - 删除配置
- [ ] **2.2.2** 预设模板接口
  - GET /api/scheduling/templates - 获取所有模板
  - POST /api/scheduling/templates/{name}/apply - 应用模板
- [ ] **2.2.3** 配置验证接口
  - POST /api/scheduling/config/validate - 验证配置合法性

---

## 🎨 **Phase 3: 前端配置界面 (优先级: 🟡中)**
*预估时间: 3-4天*

### **Task 3.1: 权重配置页面**
**预估时间**: 2天

#### **子任务:**
- [ ] **3.1.1** 权重滑块组件开发
  - 实时权重调整
  - 权重总和验证 (必须=100%)
  - 最小值/最大值限制
- [ ] **3.1.2** 预设策略模板
  - 交期敏感型: deadline(35%) + value(15%) + tech(25%) + load(15%) + priority(10%)
  - 利润导向型: value(35%) + deadline(15%) + tech(25%) + load(15%) + priority(10%)
  - 效率优先型: load(30%) + tech(35%) + deadline(20%) + value(10%) + priority(5%)
  - 平衡策略: 各维度均衡分配
- [ ] **3.1.3** 高级参数设置
  - 改机时间配置
  - 交期阈值设置
  - 权重边界设置

---

### **Task 3.2: 配置管理界面**
**预估时间**: 1-2天

#### **子任务:**
- [ ] **3.2.1** 配置保存/加载功能
- [ ] **3.2.2** 配置历史记录
- [ ] **3.2.3** 配置导入/导出
- [ ] **3.2.4** 实时预览效果

---

## 📊 **Phase 4: 可视化与优化 (优先级: 🟢低)**
*预估时间: 3-4天*

### **Task 4.1: 排产结果可视化**
**预估时间**: 2天

#### **子任务:**
- [ ] **4.1.1** 设备负载甘特图
- [ ] **4.1.2** 改机时间统计图  
- [ ] **4.1.3** 交期达成率分析
- [ ] **4.1.4** 产值效率对比

---

### **Task 4.2: 算法优化分析**
**预估时间**: 1-2天

#### **子任务:**
- [ ] **4.2.1** A/B测试功能
- [ ] **4.2.2** 配置效果对比
- [ ] **4.2.3** 最优配置推荐
- [ ] **4.2.4** 敏感性分析

---

## 🎯 **里程碑计划**

| 里程碑 | 完成日期 | 交付物 | 验收标准 |
|--------|----------|--------|----------|
| **M1: 核心算法** | Day 7 | 智能排产算法 | 不再是循环分配，同产品续排 |
| **M2: 数据支持** | Day 10 | 配置管理API | 支持权重动态配置 |  
| **M3: 前端界面** | Day 14 | 配置管理页面 | 用户可自主调整权重 |
| **M4: 系统完善** | Day 18 | 完整系统 | 生产环境可用 |

---

## 🔧 **当前行动**

### **立即开始 Task 1.1.1: 配置匹配引擎开发**

我现在就开始重构 `improved_scheduling_service.py`，实现真正的业务逻辑：

1. **DEVICE+STAGE 查询 ET_FT_TEST_SPEC**
2. **获取 HB/TB/HANDLER_CONFIG 配置**
3. **DEVICE+STAGE+PKG_PN 查询 ET_RECIPE_FILE**
4. **实现设备配置匹配算法**

## 📝 **进度跟踪**

- [ ] Task 1.1 - 配置匹配引擎 (🔄 进行中)
- [ ] Task 1.2 - 多维度评分系统  
- [ ] Task 1.3 - 同产品续排优化
- [ ] Task 1.4 - 综合排产算法

---

*文档创建时间: 2025-06-26*  
*最后更新: 2025-06-26* 