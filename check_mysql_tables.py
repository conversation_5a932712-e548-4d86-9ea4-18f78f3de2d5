#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

print("检查MySQL数据库中的实际表名...")

try:
    from app.services.data_source_manager import DataSourceManager
    
    dm = DataSourceManager()
    
    # 尝试不同的表名变体
    table_variants = {
        'ET_FT_TEST_SPEC': ['ET_FT_TEST_SPEC', 'et_ft_test_spec'],
        'ET_RECIPE_FILE': ['ET_RECIPE_FILE', 'et_recipe_file'],
        'ET_WAIT_LOT': ['ET_WAIT_LOT', 'et_wait_lot'],
        'EQP_STATUS': ['EQP_STATUS', 'eqp_status'],
        'ET_UPH_EQP': ['ET_UPH_EQP', 'et_uph_eqp'],
        'devicepriorityconfig': ['devicepriorityconfig', 'DEVICEPRIORITYCONFIG'],
        'lotpriorityconfig': ['lotpriorityconfig', 'LOTPRIORITYCONFIG'],
        'lotprioritydone': ['lotprioritydone', 'LOTPRIORITYDONE']
    }
    
    print(f"\n=== MySQL表名检查结果 ===")
    
    working_tables = {}
    
    for logical_name, variants in table_variants.items():
        print(f"\n🔍 检查 {logical_name}:")
        found = False
        
        for variant in variants:
            try:
                result = dm.get_table_data(variant)
                if result.get('success'):
                    data = result.get('data', [])
                    print(f"  ✅ {variant}: {len(data)} 条数据")
                    working_tables[logical_name] = variant
                    found = True
                    
                    # 显示字段信息
                    if data and len(data) > 0:
                        fields = list(data[0].keys())
                        print(f"     字段数: {len(fields)}")
                        
                        # 显示关键字段
                        if logical_name == 'ET_FT_TEST_SPEC':
                            key_fields = ['DEVICE', 'STAGE', 'PKG_PN', 'HB_PN', 'TB_PN', 'APPROVAL_STATE']
                            available = [f for f in key_fields if f in fields]
                            print(f"     关键字段: {available}")
                            
                        elif logical_name == 'ET_RECIPE_FILE':
                            recipe_fields = [f for f in fields if any(keyword in f.upper() for keyword in ['KIT', 'SOCKET', 'RECIPE'])]
                            print(f"     配方相关字段: {recipe_fields}")
                            
                        elif logical_name == 'EQP_STATUS':
                            eqp_fields = ['DEVICE', 'HANDLER_ID', 'TESTER_ID', 'HB_PN', 'TB_PN', 'KIT_PN', 'STATUS']
                            available = [f for f in eqp_fields if f in fields]
                            print(f"     设备字段: {available}")
                    
                    break  # 找到一个可用的就停止
                else:
                    print(f"  ❌ {variant}: 访问失败")
                    
            except Exception as e:
                print(f"  ❌ {variant}: 异常 - {str(e)}")
        
        if not found:
            print(f"  ⚠️  {logical_name}: 所有变体都无法访问")
    
    print(f"\n=== 可用表名映射 ===")
    for logical, actual in working_tables.items():
        print(f"{logical} -> {actual}")
    
    # 专门检查ET_RECIPE_FILE的数据内容
    if 'ET_RECIPE_FILE' in working_tables:
        print(f"\n=== ET_RECIPE_FILE 数据分析 ===")
        recipe_table = working_tables['ET_RECIPE_FILE']
        result = dm.get_table_data(recipe_table)
        if result.get('success'):
            data = result.get('data', [])[:3]  # 取前3条
            for idx, record in enumerate(data):
                print(f"第{idx+1}条记录:")
                for key, value in record.items():
                    if any(keyword in key.upper() for keyword in ['DEVICE', 'STAGE', 'KIT', 'SOCKET', 'PKG']):
                        print(f"  {key}: {value}")
                print()
                
except Exception as e:
    print(f"错误: {e}")
    import traceback
    traceback.print_exc() 