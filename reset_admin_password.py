#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重置admin用户密码
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models import User
from werkzeug.security import generate_password_hash

def reset_admin_password():
    """重置admin用户密码为admin"""
    app, _ = create_app()
    
    with app.app_context():
        # 查找admin用户
        admin_user = User.query.filter_by(username='admin').first()
        
        if admin_user:
            # 重置密码为admin123
            new_password = 'admin'
            admin_user.password_hash = generate_password_hash(new_password)
            db.session.commit()
            
            print(f"✅ admin用户密码已重置为: {new_password}")
            print(f"   新密码哈希: {admin_user.password_hash[:50]}...")
        else:
            print("❌ 未找到admin用户")

if __name__ == '__main__':
    reset_admin_password() 