import pandas as pd

def check_excel_columns():
    """检查Excel文件的实际列名"""
    
    files = [
        {
            'path': r"D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.23.5\Excellist2025.06.05\devicepriorityconfig.xlsx",
            'name': 'devicepriorityconfig'
        },
        {
            'path': r"D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.23.5\Excellist2025.06.05\lotpriorityconfig.xlsx", 
            'name': 'lotpriorityconfig'
        }
    ]
    
    for file_info in files:
        print(f"\n=== {file_info['name']}.xlsx ===")
        try:
            df = pd.read_excel(file_info['path'])
            print(f"实际列名: {list(df.columns)}")
            print(f"列数: {len(df.columns)}")
            print(f"行数: {len(df)}")
            
            # 显示前几行数据
            if len(df) > 0:
                print("前3行数据:")
                for i, row in df.head(3).iterrows():
                    print(f"  第{i+1}行: {dict(row)}")
                    
        except Exception as e:
            print(f"读取失败: {e}")

if __name__ == "__main__":
    check_excel_columns()
