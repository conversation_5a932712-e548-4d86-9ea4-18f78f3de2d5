#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
API接口分析工具

此工具用于扫描项目中的所有API端点，并生成清单报告，帮助接口清理和重构
"""

import os
import re
import json
import logging
from datetime import datetime
from collections import defaultdict

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('api_analysis.log')
    ]
)
logger = logging.getLogger('api_analyzer')

class APIAnalyzer:
    def __init__(self, base_dir='.'):
        self.base_dir = base_dir
        self.api_paths = [
            'app/api',
            'app/api_v2',
        ]
        self.endpoints = []
        self.route_pattern = re.compile(r'@(?:bp|api_v\d+|auth_bp|.*?_bp)\.route\([\'"]([^\'"]*)[\'"]')
        self.stats = defaultdict(int)
    
    def find_python_files(self, directory):
        """查找目录下所有Python文件"""
        python_files = []
        for root, _, files in os.walk(os.path.join(self.base_dir, directory)):
            for file in files:
                if file.endswith('.py'):
                    python_files.append(os.path.join(root, file))
        return python_files
    
    def extract_endpoints(self, file_path):
        """从Python文件中提取API端点"""
        file_endpoints = []
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # 提取蓝图前缀
            blueprint_prefix = None
            bp_match = re.search(r'Blueprint\([^,]+,\s*[^,]+,\s*url_prefix=[\'"]([^\'"]*)[\'"]', content)
            if bp_match:
                blueprint_prefix = bp_match.group(1)
                
            # 提取所有路由
            matches = self.route_pattern.finditer(content)
            
            for match in matches:
                endpoint = match.group(1)
                
                # 找出路由对应的方法名
                method_name = None
                method_match = re.search(rf'@.*?route\([\'"{re.escape(endpoint)}[\'"].*?\)\s*?def\s+([a-zA-Z0-9_]+)', content, re.DOTALL)
                if method_match:
                    method_name = method_match.group(1)
                
                # 获取HTTP方法
                methods = []
                methods_match = re.search(rf'@.*?route\([\'"{re.escape(endpoint)}[\'"].*?methods=\[(.*?)\]', content, re.DOTALL)
                if methods_match:
                    methods_str = methods_match.group(1)
                    methods = [m.strip('\'\" ') for m in methods_str.split(',')]
                else:
                    methods = ['GET']  # 默认为GET方法
                
                # 获取所在文件的相对路径
                rel_path = os.path.relpath(file_path, self.base_dir)
                
                # 生成完整URL路径
                full_path = endpoint
                if blueprint_prefix and not endpoint.startswith('/'):
                    if blueprint_prefix.endswith('/') and endpoint.startswith('/'):
                        full_path = f"{blueprint_prefix}{endpoint[1:]}"
                    else:
                        full_path = f"{blueprint_prefix}/{endpoint}"
                
                # 分析端点版本
                version = "未知"
                if '/api/v2/' in full_path:
                    version = "v2"
                elif '/api/v3/' in full_path:
                    version = "v3"
                elif '/api/' in full_path:
                    version = "v1"
                
                # 分析模块
                module = "其他"
                if '/production/' in full_path:
                    module = "生产管理"
                elif '/orders/' in full_path:
                    module = "订单管理"
                elif '/resources/' in full_path:
                    module = "资源管理"
                elif '/system/' in full_path:
                    module = "系统管理"
                elif '/auth/' in full_path:
                    module = "认证授权"
                
                # 记录统计信息
                self.stats[f"版本_{version}"] += 1
                self.stats[f"模块_{module}"] += 1
                
                # 添加到结果
                file_endpoints.append({
                    'endpoint': full_path,
                    'method': ', '.join(methods),
                    'function': method_name,
                    'file': rel_path,
                    'version': version,
                    'module': module
                })
                
            logger.info(f"已从 {file_path} 提取 {len(file_endpoints)} 个API端点")
            return file_endpoints
            
        except Exception as e:
            logger.error(f"处理文件 {file_path} 时出错: {e}")
            return []
    
    def analyze(self):
        """分析所有API路径中的端点"""
        for api_path in self.api_paths:
            logger.info(f"正在分析 {api_path} 目录...")
            files = self.find_python_files(api_path)
            
            for file in files:
                endpoints = self.extract_endpoints(file)
                self.endpoints.extend(endpoints)
        
        # 基本统计
        self.stats['总端点数'] = len(self.endpoints)
        logger.info(f"分析完成，共发现 {len(self.endpoints)} 个API端点")
        
        return self.endpoints
    
    def save_report(self, output_dir='docs/api_audit'):
        """生成并保存分析报告"""
        os.makedirs(output_dir, exist_ok=True)
        
        # 保存JSON格式的完整数据
        json_path = os.path.join(output_dir, 'api_endpoints.json')
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(self.endpoints, f, ensure_ascii=False, indent=2)
        
        # 保存统计摘要
        stats_path = os.path.join(output_dir, 'api_stats.json')
        with open(stats_path, 'w', encoding='utf-8') as f:
            json.dump(dict(self.stats), f, ensure_ascii=False, indent=2)
        
        # 生成Markdown格式的报告
        md_path = os.path.join(output_dir, 'api_endpoints_report.md')
        with open(md_path, 'w', encoding='utf-8') as f:
            f.write(f"# API端点分析报告\n\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            f.write("## 统计摘要\n\n")
            for key, value in self.stats.items():
                f.write(f"- **{key}**: {value}\n")
            
            f.write("\n## 按版本分类\n\n")
            versions = sorted(set(e['version'] for e in self.endpoints))
            for version in versions:
                version_endpoints = [e for e in self.endpoints if e['version'] == version]
                f.write(f"### {version} 版本 ({len(version_endpoints)} 个端点)\n\n")
                f.write("| 端点 | HTTP方法 | 功能 | 所在文件 |\n")
                f.write("| --- | --- | --- | --- |\n")
                for endpoint in sorted(version_endpoints, key=lambda x: x['endpoint']):
                    f.write(f"| {endpoint['endpoint']} | {endpoint['method']} | {endpoint['function']} | {endpoint['file']} |\n")
                f.write("\n")
            
            f.write("\n## 按模块分类\n\n")
            modules = sorted(set(e['module'] for e in self.endpoints))
            for module in modules:
                module_endpoints = [e for e in self.endpoints if e['module'] == module]
                f.write(f"### {module} ({len(module_endpoints)} 个端点)\n\n")
                f.write("| 版本 | 端点 | HTTP方法 | 功能 |\n")
                f.write("| --- | --- | --- | --- |\n")
                for endpoint in sorted(module_endpoints, key=lambda x: (x['version'], x['endpoint'])):
                    f.write(f"| {endpoint['version']} | {endpoint['endpoint']} | {endpoint['method']} | {endpoint['function']} |\n")
                f.write("\n")
        
        logger.info(f"报告已保存到：{output_dir}")
        return md_path, json_path, stats_path

def main():
    analyzer = APIAnalyzer()
    analyzer.analyze()
    md_path, json_path, stats_path = analyzer.save_report()
    
    print(f"分析完成:")
    print(f"- Markdown报告: {md_path}")
    print(f"- JSON数据: {json_path}")
    print(f"- 统计数据: {stats_path}")

if __name__ == "__main__":
    main() 