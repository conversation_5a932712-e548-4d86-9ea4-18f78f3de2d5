{"table_name": "unified_test_specs", "columns": [{"name": "id", "type": "INTEGER", "default": null, "comment": null, "nullable": false, "autoincrement": true}, {"name": "TEST_SPEC_ID", "type": "VARCHAR(50) COLLATE \"utf8mb4_unicode_ci\"", "default": null, "comment": "测试规范ID", "nullable": false}, {"name": "TEST_SPEC_NAME", "type": "VARCHAR(100) COLLATE \"utf8mb4_unicode_ci\"", "default": null, "comment": "测试规范名称", "nullable": false}, {"name": "TEST_SPEC_VER", "type": "VARCHAR(20) COLLATE \"utf8mb4_unicode_ci\"", "default": null, "comment": "版本号", "nullable": false}, {"name": "DEVICE", "type": "VARCHAR(50) COLLATE \"utf8mb4_unicode_ci\"", "default": null, "comment": "产品型号", "nullable": true}, {"name": "CHIP_ID", "type": "VARCHAR(50) COLLATE \"utf8mb4_unicode_ci\"", "default": null, "comment": "芯片ID", "nullable": true}, {"name": "PKG_PN", "type": "VARCHAR(50) COLLATE \"utf8mb4_unicode_ci\"", "default": null, "comment": "封装型号", "nullable": true}, {"name": "PROD_ID", "type": "VARCHAR(50) COLLATE \"utf8mb4_unicode_ci\"", "default": null, "comment": "产品ID", "nullable": true}, {"name": "STAGE", "type": "VARCHAR(50) COLLATE \"utf8mb4_unicode_ci\"", "default": null, "comment": "工序", "nullable": true}, {"name": "TESTER", "type": "VARCHAR(50) COLLATE \"utf8mb4_unicode_ci\"", "default": null, "comment": "测试机", "nullable": true}, {"name": "HANDLER", "type": "VARCHAR(50) COLLATE \"utf8mb4_unicode_ci\"", "default": null, "comment": "分选机", "nullable": true}, {"name": "TEST_SPEC_TYPE", "type": "VARCHAR(50) COLLATE \"utf8mb4_unicode_ci\"", "default": null, "comment": "测试规范类型", "nullable": true}, {"name": "TEST_AREA", "type": "VARCHAR(50) COLLATE \"utf8mb4_unicode_ci\"", "default": null, "comment": "测试区域", "nullable": true}, {"name": "TEMPERATURE", "type": "VARCHAR(50) COLLATE \"utf8mb4_unicode_ci\"", "default": null, "comment": "测试温度", "nullable": true}, {"name": "TB_PN", "type": "VARCHAR(50) COLLATE \"utf8mb4_unicode_ci\"", "default": null, "comment": "测试板型号", "nullable": true}, {"name": "HB_PN", "type": "VARCHAR(50) COLLATE \"utf8mb4_unicode_ci\"", "default": null, "comment": "分选板型号", "nullable": true}, {"name": "KIT_PN", "type": "VARCHAR(50) COLLATE \"utf8mb4_unicode_ci\"", "default": null, "comment": "KIT型号", "nullable": true}, {"name": "SOCKET_PN", "type": "VARCHAR(50) COLLATE \"utf8mb4_unicode_ci\"", "default": null, "comment": "Socket型号", "nullable": true}, {"name": "TESTER_CONFIG", "type": "VARCHAR(100) COLLATE \"utf8mb4_unicode_ci\"", "default": null, "comment": "测试机配置", "nullable": true}, {"name": "FT_PROGRAM", "type": "VARCHAR(100) COLLATE \"utf8mb4_unicode_ci\"", "default": null, "comment": "FT程序", "nullable": true}, {"name": "QA_PROGRAM", "type": "VARCHAR(100) COLLATE \"utf8mb4_unicode_ci\"", "default": null, "comment": "QA程序", "nullable": true}, {"name": "GU_PROGRAM", "type": "VARCHAR(100) COLLATE \"utf8mb4_unicode_ci\"", "default": null, "comment": "GU程序", "nullable": true}, {"name": "FT_PROGRAM_PATH", "type": "VARCHAR(200) COLLATE \"utf8mb4_unicode_ci\"", "default": null, "comment": "FT程序路径", "nullable": true}, {"name": "QA_PROGRAM_PATH", "type": "VARCHAR(200) COLLATE \"utf8mb4_unicode_ci\"", "default": null, "comment": "QA程序路径", "nullable": true}, {"name": "GU_PROGRAM_PATH", "type": "VARCHAR(200) COLLATE \"utf8mb4_unicode_ci\"", "default": null, "comment": "GU程序路径", "nullable": true}, {"name": "UPH", "type": "INTEGER", "default": null, "comment": "每小时产量", "nullable": true, "autoincrement": false}, {"name": "TEST_TIME", "type": "FLOAT", "default": null, "comment": "测试时间", "nullable": true}, {"name": "STANDARD_YIELD", "type": "FLOAT", "default": null, "comment": "标准良率", "nullable": true}, {"name": "LOW_YIELD", "type": "FLOAT", "default": null, "comment": "低良率", "nullable": true}, {"name": "HIGH_YIELD", "type": "FLOAT", "default": null, "comment": "高良率", "nullable": true}, {"name": "DOWN_YIELD", "type": "FLOAT", "default": null, "comment": "停机良率", "nullable": true}, {"name": "ORT_QTY", "type": "INTEGER", "default": null, "comment": "ORT数量", "nullable": true, "autoincrement": false}, {"name": "REMAIN_QTY", "type": "INTEGER", "default": null, "comment": "剩余数量", "nullable": true, "autoincrement": false}, {"name": "status", "type": "VARCHAR(20) COLLATE \"utf8mb4_unicode_ci\"", "default": "'draft'", "comment": "状态", "nullable": true}, {"name": "APPROVAL_STATE", "type": "VARCHAR(20) COLLATE \"utf8mb4_unicode_ci\"", "default": null, "comment": "审批状态", "nullable": true}, {"name": "ACTV_YN", "type": "VARCHAR(1) COLLATE \"utf8mb4_unicode_ci\"", "default": "'Y'", "comment": "是否激活", "nullable": true}, {"name": "APPROVE_USER", "type": "VARCHAR(50) COLLATE \"utf8mb4_unicode_ci\"", "default": null, "comment": "审批人", "nullable": true}, {"name": "APPROVE_TIME", "type": "VARCHAR(50) COLLATE \"utf8mb4_unicode_ci\"", "default": null, "comment": "审批时间", "nullable": true}, {"name": "TEST_ENG", "type": "VARCHAR(50) COLLATE \"utf8mb4_unicode_ci\"", "default": null, "comment": "测试工程师", "nullable": true}, {"name": "FAC_ID", "type": "VARCHAR(50) COLLATE \"utf8mb4_unicode_ci\"", "default": null, "comment": "工厂ID", "nullable": true}, {"name": "SUB_FAC", "type": "VARCHAR(50) COLLATE \"utf8mb4_unicode_ci\"", "default": null, "comment": "子工厂", "nullable": true}, {"name": "COMPANY_ID", "type": "VARCHAR(50) COLLATE \"utf8mb4_unicode_ci\"", "default": null, "comment": "公司ID", "nullable": true}, {"name": "test_parameters", "type": "TEXT COLLATE \"utf8mb4_unicode_ci\"", "default": null, "comment": "测试参数JSON", "nullable": true}, {"name": "extended_data", "type": "TEXT COLLATE \"utf8mb4_unicode_ci\"", "default": null, "comment": "扩展数据JSON", "nullable": true}, {"name": "source_table", "type": "VARCHAR(20) COLLATE \"utf8mb4_unicode_ci\"", "default": null, "comment": "数据来源表", "nullable": true}, {"name": "migration_status", "type": "VARCHAR(20) COLLATE \"utf8mb4_unicode_ci\"", "default": "'pending'", "comment": "迁移状态", "nullable": true}, {"name": "data_version", "type": "INTEGER", "default": "'1'", "comment": "数据版本", "nullable": true, "autoincrement": false}, {"name": "created_at", "type": "DATETIME", "default": "CURRENT_TIMESTAMP", "comment": "创建时间", "nullable": true}, {"name": "updated_at", "type": "DATETIME", "default": "CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP", "comment": "更新时间", "nullable": true}, {"name": "created_by", "type": "VARCHAR(64) COLLATE \"utf8mb4_unicode_ci\"", "default": null, "comment": "创建人", "nullable": true}, {"name": "updated_by", "type": "VARCHAR(64) COLLATE \"utf8mb4_unicode_ci\"", "default": null, "comment": "更新人", "nullable": true}], "backup_time": "2025-06-16T20:18:37.499498"}