import pandas as pd

# 检查devicepriorityconfig.xlsx
try:
    file_path = r"D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.23.5\Excellist2025.06.05\devicepriorityconfig.xlsx"
    df = pd.read_excel(file_path)
    print("=== devicepriorityconfig.xlsx ===")
    print("列名:", list(df.columns))
    print("行数:", len(df))
    print("前3行数据:")
    print(df.head(3))
    print("\n数据类型:")
    print(df.dtypes)
    print("\n空值统计:")
    print(df.isnull().sum())
except Exception as e:
    print("读取devicepriorityconfig.xlsx失败:", e)

print("\n" + "="*50 + "\n")

# 检查lotpriorityconfig.xlsx
try:
    file_path = r"D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.23.5\Excellist2025.06.05\lotpriorityconfig.xlsx"
    df = pd.read_excel(file_path)
    print("=== lotpriorityconfig.xlsx ===")
    print("列名:", list(df.columns))
    print("行数:", len(df))
    print("前3行数据:")
    print(df.head(3))
    print("\n数据类型:")
    print(df.dtypes)
    print("\n空值统计:")
    print(df.isnull().sum())
except Exception as e:
    print("读取lotpriorityconfig.xlsx失败:", e)
