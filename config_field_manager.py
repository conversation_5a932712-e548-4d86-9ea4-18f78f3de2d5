#!/usr/bin/env python3
"""
字段配置管理工具
用于生成、验证和管理动态字段配置文件
"""

import json
import os
import sys
import pymysql
from datetime import datetime
from typing import Dict, List

class FieldConfigManager:
    """字段配置管理器"""
    
    def __init__(self):
        self.db_configs = {
            'aps': {
                'host': 'localhost',
                'user': 'root',
                'password': 'WWWwww123!',
                'database': 'aps',
                'charset': 'utf8mb4'
            },
            'aps_system': {
                'host': 'localhost',
                'user': 'root',
                'password': 'WWWwww123!',
                'database': 'aps_system',
                'charset': 'utf8mb4'
            }
        }
        
        self.table_database_mapping = {
            'devicepriorityconfig': 'aps_system',
            'lotpriorityconfig': 'aps_system',
            'device_priority_config': 'aps_system',
            'lot_priority_config': 'aps_system',
        }
    
    def discover_all_tables(self) -> Dict:
        """发现所有表和字段"""
        all_tables = {}
        
        for db_name, db_config in self.db_configs.items():
            print(f"🔍 正在扫描数据库: {db_name}")
            
            try:
                connection = pymysql.connect(**db_config)
                
                with connection.cursor() as cursor:
                    # 获取所有表
                    cursor.execute("SHOW TABLES")
                    tables = [row[0] for row in cursor.fetchall()]
                    
                    for table in tables:
                        print(f"  📋 扫描表: {table}")
                        table_info = self._discover_table_structure(table, db_config)
                        table_info['database'] = db_name
                        all_tables[table] = table_info
                
                connection.close()
                
            except Exception as e:
                print(f"❌ 扫描数据库失败 {db_name}: {e}")
                continue
        
        return all_tables
    
    def _discover_table_structure(self, table_name: str, db_config: Dict) -> Dict:
        """发现表结构"""
        try:
            connection = pymysql.connect(**db_config)
            
            with connection.cursor() as cursor:
                # 获取表结构
                cursor.execute(f"DESCRIBE {table_name}")
                columns_info = cursor.fetchall()
                
                # 获取列详细信息
                cursor.execute(f"""
                    SELECT COLUMN_NAME, COLUMN_COMMENT, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT
                    FROM information_schema.COLUMNS 
                    WHERE TABLE_SCHEMA = '{db_config["database"]}' AND TABLE_NAME = '{table_name}'
                    ORDER BY ORDINAL_POSITION
                """)
                columns_detail = cursor.fetchall()
            
            connection.close()
            
            # 构建字段信息
            fields = []
            field_details = {}
            primary_key = None
            datetime_fields = []
            
            for col_info in columns_info:
                field_name = col_info[0]
                data_type = col_info[1]
                is_nullable = col_info[2]
                key_type = col_info[3]
                default_value = col_info[4]
                
                fields.append(field_name)
                
                # 确定主键
                if key_type == 'PRI' and not primary_key:
                    primary_key = field_name
                
                # 识别日期时间字段
                if any(dt_type in data_type.lower() for dt_type in ['timestamp', 'datetime', 'date', 'time']):
                    datetime_fields.append(field_name)
                elif any(pattern in field_name.lower() for pattern in ['time', 'date', 'created', 'updated']):
                    datetime_fields.append(field_name)
                
                field_details[field_name] = {
                    'data_type': data_type,
                    'nullable': is_nullable == 'YES',
                    'key_type': key_type,
                    'default': default_value
                }
            
            # 添加列注释
            for col_detail in columns_detail:
                field_name = col_detail[0]
                if field_name in field_details:
                    field_details[field_name]['comment'] = col_detail[1] or ""
                    field_details[field_name]['mysql_type'] = col_detail[2]
            
            return {
                'fields': fields,
                'field_details': field_details,
                'primary_key': primary_key or 'id',
                'business_key': self._guess_business_key(fields),
                'datetime_fields': datetime_fields,
                'field_count': len(fields),
                'discovered_at': datetime.now().isoformat()
            }
            
        except Exception as e:
            print(f"❌ 发现表结构失败 - {table_name}: {e}")
            return {}
    
    def _guess_business_key(self, fields: List[str]) -> str:
        """智能猜测业务主键"""
        business_key_patterns = ['LOT_ID', 'DEVICE', 'TEST_SPEC_ID', 'HANDLER_ID']
        
        for pattern in business_key_patterns:
            if pattern in fields:
                return pattern
        
        # 寻找包含ID的字段（排除自增ID）
        for field in fields:
            if 'ID' in field.upper() and field.lower() != 'id':
                return field
        
        return 'id'
    
    def generate_config_file(self, output_path: str = "instance/field_config.json"):
        """生成配置文件"""
        print("🚀 开始生成字段配置文件...")
        
        # 发现所有表
        all_tables = self.discover_all_tables()
        
        if not all_tables:
            print("❌ 没有发现任何表")
            return
        
        # 构建配置结构
        config = {
            "meta": {
                "version": "1.0",
                "generated_at": datetime.now().isoformat(),
                "description": "自动生成的动态字段映射配置文件",
                "total_tables": len(all_tables)
            },
            "tables": {},
            "field_types": {
                "datetime_patterns": ["time", "date", "created", "updated"],
                "id_patterns": ["id", "key"],
                "numeric_patterns": ["qty", "count", "priority", "uph"]
            },
            "display_rules": {
                "hidden_fields": ["created_at", "updated_at", "mysql_hash", "excel_override"],
                "readonly_fields": ["id", "create_time", "CREATE_TIME"],
                "required_fields": []
            }
        }
        
        # 处理每个表
        for table_name, table_info in all_tables.items():
            print(f"📝 处理表: {table_name} ({table_info['field_count']}个字段)")
            
            config["tables"][table_name] = {
                "display_name": table_name,
                "database": table_info.get('database', 'aps'),
                "primary_key": table_info.get('primary_key', 'id'),
                "business_key": table_info.get('business_key', 'id'),
                "fields": table_info.get('fields', []),
                "datetime_fields": table_info.get('datetime_fields', []),
                "field_count": table_info.get('field_count', 0),
                "auto_discovered": True,
                "last_discovery": table_info.get('discovered_at'),
                
                # 表级别的显示规则覆盖
                "hidden_fields": [
                    f for f in table_info.get('fields', []) 
                    if any(pattern in f.lower() for pattern in ['hash', 'override', 'sync'])
                ],
                "readonly_fields": [
                    f for f in table_info.get('fields', [])
                    if f.lower() in ['id'] or 'create_time' in f.lower()
                ],
                "required_fields": [
                    table_info.get('business_key', 'id')
                ] if table_info.get('business_key') != 'id' else []
            }
        
        # 保存配置文件
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 配置文件生成成功: {output_path}")
        print(f"📊 统计信息:")
        print(f"   - 总表数: {len(all_tables)}")
        print(f"   - 总字段数: {sum(t.get('field_count', 0) for t in all_tables.values())}")
        print(f"   - 配置文件大小: {os.path.getsize(output_path)} 字节")
    
    def validate_config(self, config_path: str = "instance/field_config.json"):
        """验证配置文件"""
        print(f"🔍 验证配置文件: {config_path}")
        
        if not os.path.exists(config_path):
            print("❌ 配置文件不存在")
            return
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
        except Exception as e:
            print(f"❌ 读取配置文件失败: {e}")
            return
        
        tables = config.get('tables', {})
        validation_results = []
        
        for table_name, table_config in tables.items():
            print(f"🔧 验证表: {table_name}")
            
            # 获取数据库实际字段
            database = table_config.get('database', 'aps')
            db_config = self.db_configs.get(database)
            
            if not db_config:
                print(f"  ❌ 无效的数据库配置: {database}")
                continue
            
            try:
                actual_structure = self._discover_table_structure(table_name, db_config)
                config_fields = set(table_config.get('fields', []))
                actual_fields = set(actual_structure.get('fields', []))
                
                missing_fields = actual_fields - config_fields
                extra_fields = config_fields - actual_fields
                common_fields = config_fields & actual_fields
                
                match_rate = len(common_fields) / len(actual_fields) * 100 if actual_fields else 0
                
                result = {
                    'table': table_name,
                    'match_rate': round(match_rate, 2),
                    'config_count': len(config_fields),
                    'actual_count': len(actual_fields),
                    'missing_count': len(missing_fields),
                    'extra_count': len(extra_fields),
                    'status': 'excellent' if match_rate == 100 else 'good' if match_rate > 90 else 'poor'
                }
                
                validation_results.append(result)
                
                print(f"  📊 匹配率: {match_rate:.1f}% ({result['status']})")
                if missing_fields:
                    print(f"  ⚠️ 缺失字段: {len(missing_fields)}个")
                if extra_fields:
                    print(f"  ⚠️ 多余字段: {len(extra_fields)}个")
                    
            except Exception as e:
                print(f"  ❌ 验证失败: {e}")
        
        # 输出总结
        print("\n📈 验证总结:")
        excellent_count = sum(1 for r in validation_results if r['status'] == 'excellent')
        good_count = sum(1 for r in validation_results if r['status'] == 'good')
        poor_count = sum(1 for r in validation_results if r['status'] == 'poor')
        
        print(f"   - 完美匹配: {excellent_count}个表")
        print(f"   - 良好匹配: {good_count}个表")
        print(f"   - 需要修复: {poor_count}个表")
        
        avg_match_rate = sum(r['match_rate'] for r in validation_results) / len(validation_results) if validation_results else 0
        print(f"   - 平均匹配率: {avg_match_rate:.1f}%")
    
    def compare_approaches(self):
        """比较硬编码和动态配置方法"""
        print("📊 方案对比分析:")
        print()
        
        # 硬编码方案分析
        print("🔧 硬编码方案 (当前data_source_manager.py):")
        hardcoded_file = "app/services/data_source_manager.py"
        if os.path.exists(hardcoded_file):
            with open(hardcoded_file, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.count('\n')
                mapping_lines = content.count("':")
                print(f"   - 文件大小: {len(content)} 字符")
                print(f"   - 代码行数: {lines}")
                print(f"   - 映射条目: ~{mapping_lines}")
        
        print("   ✅ 优势: 性能高、简单直接、稳定")
        print("   ❌ 劣势: 维护困难、重复冗余、扩展性差")
        print()
        
        # 动态配置方案分析
        print("🚀 动态配置方案 (推荐):")
        print("   ✅ 优势:")
        print("     • 自动发现字段，无需手动维护")
        print("     • 配置文件驱动，灵活可控")
        print("     • 支持字段验证和不一致检测")
        print("     • 缓存机制保证性能")
        print("     • 支持表级别和全局规则")
        print("     • 易于扩展和版本控制")
        print()
        print("   ⚠️ 劣势:")
        print("     • 初次启动需要发现过程")
        print("     • 增加了一层抽象复杂性")
        print("     • 需要数据库连接权限")
        print()
        
        # 推荐迁移策略
        print("🔄 推荐迁移策略:")
        print("1. 生成初始配置文件")
        print("2. 验证字段映射完整性")
        print("3. 逐步替换现有API")
        print("4. 保留硬编码作为备用")
        print("5. 完全切换后清理旧代码")

def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("用法:")
        print("  python config_field_manager.py generate    # 生成配置文件")
        print("  python config_field_manager.py validate    # 验证配置文件")
        print("  python config_field_manager.py compare     # 方案对比")
        return
    
    manager = FieldConfigManager()
    command = sys.argv[1]
    
    if command == 'generate':
        manager.generate_config_file()
    elif command == 'validate':
        manager.validate_config()
    elif command == 'compare':
        manager.compare_approaches()
    else:
        print(f"未知命令: {command}")

if __name__ == '__main__':
    main() 